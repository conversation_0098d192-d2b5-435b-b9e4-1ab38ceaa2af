/* eslint-env node */
require('@rushstack/eslint-patch/modern-module-resolution')

module.exports = {
    root: true,
    extends: [
        'plugin:vue/vue3-essential',
        'eslint:recommended',
        '@vue/eslint-config-typescript'
    ],
    env: {
        'browser': true,
        'node': true
    },
    // parser: '@typescript-eslint/parser',
    parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module'
    },
    plugins: [
        '@typescript-eslint'
    ],
    rules: {
        // 4空格
        'indent': ['error', 4, { 'SwitchCase': 1 }],

        // 关闭未使用的变量检查
        'no-unused-vars': 'off',
        '@typescript-eslint/no-unused-vars': ['off'],

        // 关闭any类型检查
        '@typescript-eslint/no-explicit-any': ['off'],

        // 最后一个属性后面不加逗号
        'comma-dangle': ['error', 'never'],

        // 箭头函数空格
        'arrow-spacing': ['error', { 'before': true, 'after': true }],

        // 强制使用单引号
        'quotes': ['error', 'single'],

        // 不允许使用分号结尾
        'semi': ['error', 'never'],
        '@typescript-eslint/semi': ['error', 'never'],

        // 禁止不必要的额外分号
        '@typescript-eslint/no-extra-semi': ['error'],

        // 在接口和类型字面量中禁止使用分号
        '@typescript-eslint/member-delimiter-style': ['error', {
            'multiline': {
                'delimiter': 'none', // 多行时不使用分隔符
                'requireLast': false
            },
            'singleline': {
                'delimiter': 'comma', // 单行时使用逗号作为分隔符
                'requireLast': false
            }
        }],

        // 花括号前后空格
        'object-curly-spacing': ['error', 'always'],

        // 强制在注释中 // 或 /* 使用一致的空格
        'spaced-comment': ['error', 'always'],

        // 强制在关键字前后使用一致的空格
        'keyword-spacing': ['error', { 'before': true, 'after': true }],

        // 禁止属性前有空白
        'no-whitespace-before-property': 'error',

        // 强制在逗号前后使用一致的空格
        'comma-spacing': ['error', { 'before': false, 'after': true }],

        // 优先使用数组和对象解构
        'prefer-destructuring': ['error', { 'array': true, 'object': true }, { 'enforceForRenamedProperties': false }],

        // 关闭组件命名规则
        'vue/multi-word-component-names': ['off'],

        // 关闭未使用的组件检测
        'vue/no-unused-components': ['off'],

        // 关闭无效的HTML属性检测
        'vue/valid-attribute-name': ['off'],

        // 关闭无用属性检测
        'vue/no-useless-template-attributes': ['off']
    },
    ignorePatterns: ['src/**/*.test.ts', 'src/frontend/generated/*']
}
