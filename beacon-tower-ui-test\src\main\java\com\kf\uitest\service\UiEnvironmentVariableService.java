package com.kf.uitest.service;

import com.kf.uitest.entity.UiTestEnvironmentVariable;

import java.util.List;
import java.util.Map;

public interface UiEnvironmentVariableService {

    /**
     * 获取环境变量
     */
    UiTestEnvironmentVariable getVariable(Long environmentId, String name);

    /**
     * 获取环境所有变量
     */
    List<UiTestEnvironmentVariable> listVariables(Long environmentId);

    /**
     * 获取环境变量Map
     */
    Map<String, Object> getVariablesMap(Long environmentId);

    /**
     * 更新环境变量
     */
    void updateVariable(Long environmentId, String name, String value);

    /**
     * 删除环境变量
     */
    void deleteVariable(Long environmentId, String name);
}