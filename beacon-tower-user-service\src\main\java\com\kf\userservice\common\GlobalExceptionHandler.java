package com.kf.userservice.common;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;


@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(BindException.class)
    public ResponseDoMain bindExceptionHandler(HttpServletRequest request, BindException e) {
        String method = request.getMethod();
        String errMsg = e.getMessage();
        log.error("方法 {}出现异常：{}", method, errMsg);
        return ResponseDoMain.failure(e.getBindingResult().getAllErrors().get(0).getDefaultMessage());
    }

    @ExceptionHandler(Exception.class)
    public ResponseDoMain myExceptionHandler(HttpServletRequest request, final Exception e) {
        String method = request.getMethod();
        String errMsg = e.getMessage();
        log.error("方法 {}出现异常：{}", method, errMsg, e);
        return ResponseDoMain.failure(errMsg);
    }

}
