package com.kf.userservice.controller;

import com.kf.userservice.common.RequireHeader;
import com.kf.userservice.common.ResponseDoMain;
import com.kf.userservice.entity.TMenu;
import com.kf.userservice.service.MenuService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/menu")
public class TMenuController {

    @Resource
    private MenuService menuService;

    /**
     * 查询当前用户的所有菜单
     *
     * @return 所有菜单
     */
    @RequireHeader("userId")
    @GetMapping("/route")
    public ResponseDoMain getRoute(HttpServletRequest request) {
        return ResponseDoMain.success(this.menuService.queryMenuByUserId(request.getHeader("userId")));
    }

}

