#commons
error_lang_invalid=語言參數錯誤
cannot_be_null=不能為空
number=第
row=行
error=出錯
delete_fail=刪除失敗
start_engine_fail=啟動失敗
upload_fail=文件上傳失敗
invalid_parameter=非法的參數
name_already_exists=該名稱已經存在
resource_not_exist=資源不存在或已刪除
read_permission_file_fail=讀取權限文件失敗!
#user related
user_email_already_exists=用戶郵箱已存在
user_id_is_null=用戶ID不能為空
user_name_is_null=用戶名不能為空
user_email_is_null=用戶郵箱不能為空
password_is_null=密碼不能為空
user_id_already_exists=用戶id已存在
password_modification_failed=舊密碼輸入錯誤，請重新輸入
cannot_delete_current_user=無法刪除當前登錄用戶
connection_failed=連接失敗
connection_timeout=連接超時
user_already_exists=該用戶已存在於當前成員列表中
cannot_remove_current=無法移除當前登錄用戶
login_fail=登錄失敗
password_is_incorrect=用戶名或密碼不正確
user_not_exist=用戶不存在：
user_has_been_disabled=用戶已被禁用
excessive_attempts=操作頻繁
user_locked=用戶被鎖定
user_expires=用戶過期
not_authorized=未經授權
user_apikey_limit=您最多可創建5個API Key
please_logout_current_user=請先登出當前用戶
#attachment upload
test_case_attachment_upload_not_found=無法上傳附件，未找到相關用例：
issues_attachment_upload_not_found=無法上傳附件，未找到相關缺陷：
# issue template copy
target_issue_template_not_checked=無法複製，未選中目標項目
source_issue_template_is_empty=複製錯誤，源項目為空
#load test
edit_load_test_not_found=無法編輯測試，未找到測試：
run_load_test_not_found=無法運行測試，未找到測試：
run_load_test_file_not_found=無法運行測試，無法獲取測試文件元信息，測試ID：
run_load_test_file_content_not_found=無法運行測試，無法獲取測試文件內容，測試ID：
run_load_test_file_init_error=無法運行測試，請前往 [系統設置-系統-系統參數設置] 檢查當前站點配置，詳情見 https://metersphere.io/docs/v2.x/faq/load_test/#url
load_test_is_running=測試正在運行, 請等待
load_test_kafka_invalid=Kafka 不可用，請檢查配置
cannot_edit_load_test_running=不能修改正在運行的測試
test_not_found=測試不存在:
test_not_running=測試未運行
load_test_already_exists=測試名稱不能重復
load_test_name_length=測試名稱長度超過限製
no_nodes_message=沒有節點信息
duplicate_node_ip=節點 IP 重復
duplicate_node_port=節點 Port 重復
duplicate_node_ip_port=節點 IP、Port 重復
max_thread_insufficient=並發用戶數超額
related_case_del_fail_prefix=已關聯到
related_case_del_fail_suffix=測試用例，請先解除關聯
jmx_content_valid=JMX 內容無效，請檢查
container_delete_fail=容器由於網絡原因停止失敗，請重試
load_test_report_file_not_exist=當前報告沒有JTL文件，請等待或重新執行以便獲取
#workspace
workspace_name_is_null=工作空間名不能為空
workspace_name_already_exists=工作空間名已存在
workspace_does_not_belong_to_user=當前工作空間不屬於當前用戶
workspace_not_exists=工作空間不存在
#test resource pool
test_resource_pool_id_is_null=資源池ID不能為空
test_resource_pool_name_is_null=資源池名稱不能為空
test_resource_pool_name_already_exists=資源池名稱已存在
load_test=性能測試
test_resource_pool_is_use=正在使用此資源池，無法刪除
only_one_k8s=只能添加一個 K8S
test_resource_pool_not_exists=測試資源池不存在
test_resource_pool_invalid=當前測試使用的資源池處於禁用狀態
#project
project_name_is_null=項目名稱不能為空
project_name_already_exists=項目名稱已存在
project_file_already_exists=項目下該文件已經存在
project_file_in_use=占用文件，無法刪除。
#organization
organization_name_is_null=組織名不能為空
organization_name_already_exists=組織名已存在
organization_does_not_belong_to_user=當前組織不屬於當前用戶
organization_id_is_null=組織 ID 不能為空
#api
api_load_script_error=讀取腳本失敗
illegal_xml_format=不合法的 XML 格式
api_report_is_null="測試報告是未生成，無法更新"
api_test_environment_already_exists="已存在該名稱的環境配置"
api_test=接口測試
api_versions_update_http=該接口存在多版本，不允許修改請求類型或路徑，請新建接口
api_versions_update=該接口存在多版本，不允許修改名稱，請新建接口
api_versions_create=該接口已存在，請在版本處創建新版本
#test case
test_case_node_level=層
test_case_node_level_tip=模塊樹最大深度為
test_case_module_not_null=所屬模塊不能為空
test_case_create_module_fail=創建模塊失敗
test_case_import_template_name=測試用例模版
test_case_import_template_sheet=模版
module_not_null=所屬模塊不能為空格
user_not_exists=該項目下無該用戶
test_case_already_exists=該項目下已存在該測試用例
parse_data_error=解析數據出錯
missing_header_information=缺少頭部信息
test_case_exist=該項目下已存在用例：
node_deep_limit=節點深度不超過8層！
before_delete_plan=該計劃下存在關聯測試用例，請先取消關聯！
incorrect_format=格式錯誤
test_case_name=用例名稱
test_case_type=用例類型
test_case_maintainer=維護人
test_case_priority=用例等級
test_case_prerequisite=前置條件
test_case_remark=備註
test_case_step_desc=步驟描述
test_case_step_result=預期結果
test_case_module=所屬模塊
test_case=功能用例
user=用戶
user_import_template_name=用戶導入模板
user_import_template_sheet=模版
user_import_format_wrong=格式錯誤
user_import_id_is_repeat=ID重復
user_import_email_is_repeat=E-mail重復
user_import_organization_not_fond=組織未找到
user_import_workspace_not_fond=工作空間未找到
org_admin=組織管理員
org_member=組織成員
test_manager=測試經理
tester=測試成員
read_only_user=只讀用戶
module=模塊
tag_tip_pattern=標簽之間以分號或者逗號隔開
preconditions_optional=前置條件選填
remark_optional=備註選填
do_not_modify_header_order=請勿修改表頭順序
module_created_automatically=若無該模塊將自動創建
options=選項
options_yes=是
options_no=否
required=必填
password_format_is_incorrect=有效密碼：8-30位，英文大小寫字母+數字+特殊字符（可選）
please_input_project_member=請填寫該項目下的相關人員ID
test_case_report_template_repeat=同一工作空間下不能存在同名模版
plan_name_already_exists=測試計劃名稱已存在
test_case_already_exists_excel=文件中存在多條相同用例
test_case_module_already_exists=同層級下已存在該模塊名稱
functional_method_tip=功能測試不支持自動方式
custom_num_is_exist=用例自定義ID已存在
custom_num_is_not_exist=用例自定義ID不存在
id_required=ID必填
id_repeat_in_table=表格內ID重復
step_model_tip=步驟描述填寫 STEP,文本描述請填寫 TEXT (非必填)
case_status_not_exist=用例狀態必須為未開始（Prepare）、進行中（Underway）、已完成（Completed）
tapd_project_not_exist=關聯的TAPD項目ID不存在
zentao_get_project_builds_fail=獲取影響版本錯誤
#ldap
ldap_url_is_null=LDAP地址為空
ldap_dn_is_null=LDAP綁定DN為空
ldap_ou_is_null=LDAP參數OU為空
ldap_password_is_null=LDAP密碼為空
ldap_connect_fail=連接LDAP失敗
ldap_connect_fail_user=連接LDAP失敗，綁定的DN或密碼錯誤
ldap_user_filter_is_null=LDAP用戶過濾器為空
ldap_user_mapping_is_null=LDAP用戶屬性映射為空
authentication_failed=用戶認證失敗,用戶名或密碼錯誤
user_not_found_or_not_unique=用戶不存在或者不唯一
ldap_authentication_not_enabled=LDAP認證未啟用
login_fail_ou_error=登錄失敗，請檢查用戶OU
login_fail_filter_error=登錄失敗，請檢查用戶過濾器
check_ldap_mapping=檢查LDAP屬性映射
ldap_mapping_value_null=LDAP用戶屬性映射字段為空值
#quota
quota_project_excess_ws_resource_pool=項目的資源池不能超過工作空間的資源池範圍
quota_performance_excess_project=性能測試數量超過項目限額
quota_max_threads_excess_project=最大並發數超過項目限額
quota_duration_excess_project=壓測時長超過項目限額
quota_member_excess_project=成員數超過項目配額
quota_project_excess_project=項目數超過工作空間配額
import_xmind_count_error=思維導圖導入用例數量不能超過 800 條
license_valid_license_error=授權認證失敗
import_xmind_not_found=未找到測試用例
test_review_task_notice=測試評審任務通知
swagger_url_scheduled_import_notification=swagger_url定時導入通知
swagger_parse_error=Swagger 解析失敗，請確認文件格式是否正確！
swagger_parse_error_with_auth=Swagger 解析失敗，請確認認證信息是否正確或文件格式是否正確！
test_track.length_less_than=標題過長，字數必須小於
# check owner
check_owner_project=當前用戶沒有操作此項目的權限
check_owner_test=當前用戶沒有操作此測試的權限
check_owner_case=當前用戶沒有操作此用例的權限
check_owner_plan=當前用戶沒有操作此計劃的權限
check_owner_review=當前用戶沒有操作此評審的權限
check_owner_comment=當前用戶沒有操作此評論的權限
upload_content_is_null=導入內容為空
test_plan_notification=測試計劃通知
task_notification_=定時任務結果通知
api_definition_url_not_repeating=介面請求地址已經存在
api_definition_name_not_repeating=同一模塊下相同的名稱-url組合已存在
api_definition_name_already_exists=同一模塊下介面名稱不能重複
api_definition_module=模塊路徑為
api_definition_name=介面
task_notification_jenkins=jenkins任務通知
task_notification=任務通知
message_task_already_exists=任務接收人已經存在
#automation
automation_name_already_exists=同一個項目同一模塊下，場景名稱不能重復
automation_name=場景
automation_exec_info=沒有測試步驟，無法執行
delete_check_reference_by=被場景引用
not_execute=未執行
execute_not_pass=未通過
execute_pass=通過
import_fail_custom_num_exists=導入失敗，自定義ID已存在
automation_versions_update=該場景存在多版本，不允許修改名稱，請新建場景
automation_versions_create=該場景已存在，請在版本處創建新版本
#authsource
authsource_name_already_exists=認證源名稱已經存在
custom_field_already=工作空間下已存在該字段：
expect_name_exists=預期名稱已存在
ssl_password_error=認證密碼錯誤，請重新輸入密碼
ssl_file_error=認證文件加載失敗，請檢查認證文件
#log
api_definition=接口定義
api_definition_case=接口定義用例
api_automation=接口自動化
api_automation_schedule=接口自動化-定時任務
api_automation_report=測試報告
track_test_case=測試用例
track_test_case_review=用例評審
track_test_plan=測試計劃
track_test_plan_schedule=測試計劃-定時任務
track_bug=缺陷管理
track_report=報告
performance_test=性能測試
performance_test_report=性能測試報告
system_user=系統-用戶
system_organization=系統-組織
system_workspace=工作空間
system_test_resource=系統-測試資源池
system_parameter_setting=系統-系統參數設置
system_quota_management=系統-配額管理
system_authorization_management=系統-授權管理
organization_member=組織-成員
organization_workspace=組織-工作空間
workspace_service_integration=工作空間-服務集成
workspace_message_settings=工作空間-消息設置
workspace_member=工作空間-成員
workspace_template_settings_field=工作空間-模版設置-自定義字段
workspace_template_settings_case=工作空間-模版設置-用例模版
workspace_template_settings_issue=工作空間-模版設置-缺陷模版
project_project_manager=項目-項目管理
project_project_member=項目-成員
project_project_jar=項目-JAR包管理
project_environment_setting=項目-環境設置
project_file_management=項目-文件管理
personal_information_personal_settings=個人信息-個人設置
personal_information_apikeys=個人信息-API Keys
auth_title=系統認證
group_permission=用戶組與權限
test_case_status_prepare=未開始
test_case_status_running=進行中
test_case_status_finished=已完成
connection_expired=連接已失效，請重新獲取
# track home
api_case=接口用例
performance_case=性能用例
scenario_case=場景用例
test_case_status_error=失敗
test_case_status_success=成功
test_case_status_trash=廢棄
test_case_status_saved=已保存
create_user=創建人
test_case_status=用例狀態
id_not_rightful=ID 不合法
project_reference_multiple_plateform=項目指向多個第三方平臺
# mock
mock_warning=未找到匹配的Mock期望
#项目报告
enterprise_test_report=項目報告
count=統計
cannot_find_project=未找到測試項目
project_repeatable_is_false=項目未配置URL可重複
#环境组
null_environment_group_name=環境組名稱不存在
environment_group_name=環境組名稱
environment_group_exist=已存在
environment_group_has_duplicate_project=每個項目只能選擇一個環境！
environment_group=環境組
#误报库
error_report_library=誤報庫
issue_jira_info_error=請檢查服務集成信息或Jira項目ID
error_code_is_unique=錯誤代碼不可重複
no_version_exists=不存在版本！請先創建項目的版本
jira_auth_error=賬號名或密碼(Token)錯誤
jira_auth_url_error=測試連接失敗，請檢查Jira地址是否正確
#ui 指令校驗
is_null=不能為空
url_is_null=URL 參數不能為空
locator_is_null=元素定位參數不能有空
coord=坐標
input_content=輸入內容
subitem_type=子選項類型
subitem=子選項值
varname=變量名
attributeName=屬性名
expression=表達式
times=循環次數
command=步驟
extract_type=提取信息類型
cmdValidation=斷言
cmdValidateValue=斷言值
cmdValidateText=彈窗文本
cmdValidateDropdown=下拉框
cmdValidateElement=元素斷言
cmdValidateTitle=網頁標題
cmdOpen=打開網頁
cmdSelectWindow=切換窗口
cmdSetWindowSize=設置窗口大小
cmdSelectFrame=選擇內嵌網頁
cmdDialog=彈窗操作
cmdDropdownBox=下拉框操作
submit=提交表單
cmdSetItem=設置選項
cmdWaitElement=等待元素
cmdInput=輸入操作
cmdMouseClick=鼠標點擊
cmdMouseMove=鼠標移動
cmdMouseDrag=鼠標拖拽
cmdTimes=次數循環
cmdForEach=ForEach 循環
cmdWhile=While 循環
cmdIf=If
cmdElse=Else
cmdElseIf=ElseIf
close=關閉網頁
cmdExtraction=數據提取
cmdExtractWindow=提取窗口信息
cmdExtractElement=提取元素信息
tcp_mock_not_unique=該TCP端口號已被使用
report_warning=報告類型和報告ID不能為空
report_type_error=報告類型錯誤
serial=串行
parallel=並行
csv_no_exist=CSV檔案不存在
update_scenario=更新了場景
scenario_update_notice=接口自動化通知
create_scenario=新建了場景
scenario_create_notice=接口自動化通知
update_api=更新了接口定義
api_update_notice=接口更新通知
create_api=新建了接口定義
api_create_notice=接口新建通知
create_api_case=新建了接口用例
api_case_create_notice=接口用例新建通知
update_api_case=更新了接口用例
api_case_update_notice=接口用例更新通知
error_xml_struct=錯誤的xml數據
case_name_is_already_exist=用例名稱不能重復
file_format_does_not_meet_requirements=文件格式不符合要求
url_is_not_valid=URL 格式不正确!
scenario_step_parsing_error_check=場景步驟解析錯誤，檢查是否包含插件步驟!
pre_processor_env=全局前置腳本
post_processor_env=全局後置腳本
scenario_warning=場景包含挿件步驟，挿件已删除不能匯出