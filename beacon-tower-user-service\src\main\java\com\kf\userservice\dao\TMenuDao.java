package com.kf.userservice.dao;

import com.kf.userservice.entity.TMenu;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TMenuDao {
    /**
     * 通过ID查询单条数据
     *
     * @param userId 用户ID
     * @return 实例对象
     */
    List<TMenu> queryMenuByUserId(String userId);

    /**
     * 通过ID查询单条数据
     *
     * @param menuId 主键
     * @return 实例对象
     */
    TMenu queryById(Long menuId);

    /**
     * 统计总行数
     *
     * @param tMenu 查询条件
     * @return 总行数
     */
    long count(TMenu tMenu);

    /**
     * 新增数据
     *
     * @param tMenu 实例对象
     * @return 影响行数
     */
    int insert(TMenu tMenu);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<TMenu> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<TMenu> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<TMenu> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<TMenu> entities);

    /**
     * 修改数据
     *
     * @param tMenu 实例对象
     * @return 影响行数
     */
    int update(TMenu tMenu);

    /**
     * 通过主键删除数据
     *
     * @param menuId 主键
     * @return 影响行数
     */
    int deleteById(Long menuId);

}

