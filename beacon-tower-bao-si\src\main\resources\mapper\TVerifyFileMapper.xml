<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kf.baosi.dao.TVerifyFileMapper">
    <resultMap id="VerifyDocumentListDTOResultMap" type="com.kf.baosi.dto.VerifyDocumentListDTO">
        <result property="id" column="id"/>
        <result property="fileName" column="file_name"/>
        <result property="fileType" column="file_type"/>
        <result property="testPlanKey" column="test_plan_key"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isComplete" column="is_complete"/>
        <result property="errorMsg" column="error_message"/>
    </resultMap>

    <select id="getVerifyDocumentList" resultMap="VerifyDocumentListDTOResultMap">
        SELECT v.id,
               v.file_name,
               v.file_type,
               v.test_plan_key,
               v.create_time,
               v.update_time,
               v.is_complete,
               v.error_message
        FROM t_verify_file v
        WHERE v.user_id = #{userId}
          AND v.status = 0
        <if test="fileName != null and !fileName.isEmpty()">
            AND v.file_name LIKE CONCAT('%', #{fileName}, '%')
        </if>
        <if test="fileType != null and !fileType.isEmpty()">
            AND v.file_type = #{fileType}
        </if>
        <if test="isComplete != null and !isComplete.isEmpty()">
            AND v.is_complete = #{isComplete}
        </if>
        order by v.create_time desc
    </select>
</mapper>