package com.kf.accuratetest.service.impl;

import com.kf.accuratetest.dao.InterfaceAnnotationDao;
import com.kf.accuratetest.dao.InterfacePageDao;
import com.kf.accuratetest.dao.UserInterfaceAnnotationTaskDao;
import com.kf.accuratetest.dto.TInterfaceAnnotationDTO;
import com.kf.accuratetest.entity.InterfaceAnnotation;
import com.kf.accuratetest.entity.InterfacePage;
import com.kf.accuratetest.entity.UserInterfaceAnnotationTask;
import com.kf.accuratetest.service.InterfaceAnnotationService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class InterfaceAnnotationServiceImpl implements InterfaceAnnotationService {
    @Resource
    private InterfaceAnnotationDao interfaceAnnotationDao;

    @Resource
    private InterfacePageDao interfacePageDao;

    @Resource
    private UserInterfaceAnnotationTaskDao userInterfaceAnnotationTaskDao;

    /**
     * 通过ID查询列表
     *
     * @param taskId 任务ID
     * @return 实例对象
     */
    @Override
    public List<TInterfaceAnnotationDTO> queryById(String taskId) {
        List<InterfaceAnnotation> list = this.interfaceAnnotationDao.queryById(taskId);
        //将实体类转换为DTO
        List<TInterfaceAnnotationDTO> dtoList = list.stream()
                .map(interfaceAnnotation -> {
                    TInterfaceAnnotationDTO tInterfaceAnnotationDTO = new TInterfaceAnnotationDTO();
                    tInterfaceAnnotationDTO.setInterfaceName(interfaceAnnotation.getInterfaceName());
                    tInterfaceAnnotationDTO.setInterfaceDesc(interfaceAnnotation.getInterfaceDesc());
                    tInterfaceAnnotationDTO.setInterfaceNotes(interfaceAnnotation.getInterfaceNotes());
                    return tInterfaceAnnotationDTO;
                })
                .collect(Collectors.toList());

        UserInterfaceAnnotationTask userInterfaceAnnotationTask = userInterfaceAnnotationTaskDao.queryByTaskId(taskId);
        if (userInterfaceAnnotationTask == null) {
            return dtoList;
        }
        String userId = userInterfaceAnnotationTask.getUserId();
        List<InterfacePage> interfacePageList = interfacePageDao.queryAllByUserId(userId);
        //如果查到用户配置的页面信息为空，则直接返回
        if (interfacePageList == null || interfacePageList.isEmpty()) {
            return dtoList;
        }
        //将页面信息按照接口名称分组
        Map<String, List<String>> map = new HashMap<>();
        for (InterfacePage interfacePage : interfacePageList) {
            String interfaceName = interfacePage.getInterfaceName();
            String pageUrl = interfacePage.getPageUrl();
            map.computeIfAbsent(interfaceName, k -> new ArrayList<>()).add(pageUrl);
        }
        //将页面信息放入dto中
        for (TInterfaceAnnotationDTO tInterfaceAnnotationDTO : dtoList) {
            String interfaceName = tInterfaceAnnotationDTO.getInterfaceName();
            if (map.containsKey(interfaceName)) {
                List<String> pageUrlList = map.get(interfaceName);
                tInterfaceAnnotationDTO.setPageUrl(pageUrlList);
            }
        }
        return dtoList;
    }

    /**
     * 新增数据
     *
     * @param map 实例对象
     * @return 实例对象
     */
    @Override
    public int insert(Map<String, List<String>> map, String taskId) {
        List<InterfaceAnnotation> list = new ArrayList<>();
        //遍历map
        for (Map.Entry<String, List<String>> entry : map.entrySet()) {
            String key = entry.getKey();
            //获取value
            List<String> value = entry.getValue();
            InterfaceAnnotation interfaceAnnotation = new InterfaceAnnotation();
            interfaceAnnotation.setTaskId(taskId);
            interfaceAnnotation.setInterfaceName(key);
            interfaceAnnotation.setInterfaceDesc(value.get(0));
            interfaceAnnotation.setInterfaceNotes(value.get(1));
            interfaceAnnotation.setCreateTime(new Date());
            interfaceAnnotation.setUpdateTime(new Date());
            interfaceAnnotation.setIsDeleted(0);
            list.add(interfaceAnnotation);
//            if (list.size() == 20) {
//
//                tInterfaceAnnotationDao.insertBatch(list);
//                list.clear();
//            }
        }
        return this.interfaceAnnotationDao.insertBatch(list);
    }

    @Override
    public int queryCount(String taskId) {
        InterfaceAnnotation interfaceAnnotation = new InterfaceAnnotation();
        interfaceAnnotation.setTaskId(taskId);
        return (int) interfaceAnnotationDao.count(interfaceAnnotation);
    }

}
