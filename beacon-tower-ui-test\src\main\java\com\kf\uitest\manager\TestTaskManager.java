package com.kf.uitest.manager;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Component
public class TestTaskManager {

    // 自增ID，用于生成 executionId
    private final AtomicLong idGenerator = new AtomicLong(1);

    // 保存当前活跃的执行任务
    private final Map<Long, TestExecutionStatus> activeTasks = new ConcurrentHashMap<>();

    /**
     * 创建一个新的任务状态对象并放入 activeTasks
     */
    public TestExecutionStatus createNewExecution() {
        Long executionId = idGenerator.getAndIncrement();
        TestExecutionStatus status = new TestExecutionStatus(executionId);
        activeTasks.put(executionId, status);
        return status;
    }

    /**
     * 获取任务状态
     */
    public TestExecutionStatus getExecutionStatus(Long executionId) {
        return activeTasks.get(executionId);
    }

    /**
     * 标记任务终止并尝试关闭浏览器
     */
    public void stopExecution(Long executionId) {
        TestExecutionStatus status = activeTasks.get(executionId);
        if (status != null) {
            // 设置停止标志
            status.getStopFlag().set(true);
            log.info("Stop flag set for executionId {}", executionId);

            // 可以在这里进一步关闭浏览器
            if (status.getBrowser() != null) {
                try {
                    status.getBrowser().close();
                } catch (Exception e) {
                    log.error("Error closing browser for executionId {}: {}", executionId, e.getMessage());
                }
            }
        }
    }

    /**
     * 移除任务（在任务执行完毕后）
     */
    public void removeExecution(Long executionId) {
        activeTasks.remove(executionId);
    }
}
