package com.kf.baosi.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public class JsonUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final Logger log = LoggerFactory.getLogger(JsonUtil.class);

    // 配置 ObjectMapper
    static {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    /**
     * 将对象转换为 JSON 字符串
     */
    public static String toJson(Object obj) {
        if (obj == null) return null;
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            // 使用log打印异常信息
            log.error("Failed to convert object to JSON", e);
            return null;
        }
    }

    /**
     * 从 JSON 字符串解析为指定类型的对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (json == null || clazz == null) return null;
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            // 使用log打印异常信息
            log.error("Failed to convert JSON to object", e);
            return null;
        }
    }

    /**
     * 从 JSON 字符串解析为 List<T>
     */
    public static <T> List<T> fromJsonToList(String json, Class<T> elementType) {
        if (json == null || elementType == null) return null;
        try {
            JavaType javaType = objectMapper.getTypeFactory().constructCollectionType(List.class, elementType);
            return objectMapper.readValue(json, javaType);
        } catch (JsonProcessingException e) {
            // 使用log打印异常信息
            log.error("Failed to convert JSON to List", e);
            return null;
        }
    }

    /**
     * 从 JSON 字符串解析为 Map<K,V>
     */
    public static <K, V> Map<K, V> fromJsonToMap(String json, Class<K> keyType, Class<V> valueType) {
        if (json == null || keyType == null || valueType == null) return null;
        try {
            JavaType javaType = objectMapper.getTypeFactory().constructMapType(Map.class, keyType, valueType);
            return objectMapper.readValue(json, javaType);
        } catch (JsonProcessingException e) {
            // 使用log打印异常信息
            log.error("Failed to convert JSON to Map", e);
            return null;
        }
    }

    /**
     * 将对象转换为格式化的 JSON 字符串
     */
    public static String toPrettyJson(Object obj) {
        if (obj == null) return null;
        try {
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            // 使用log打印异常信息
            log.error("Failed to convert object to pretty JSON", e);
            return null;
        }
    }

    /**
     * 从 JSON 字符串解析指定字段的值
     */
    public static String getFieldFromJson(String json, String field) {
        if (json == null || field == null) return null;
        try {
            JsonNode jsonNode = objectMapper.readTree(json);
            return jsonNode.get(field).asText();
        } catch (Exception e) {
            log.error("Failed to extract field from JSON", e);
            return null;
        }
    }

    /**
     * 从 JSON 字符串解析指定的嵌套字段的值
     */
    public static String getNestedFieldFromJson(String json, String... fields) {
        if (json == null || fields == null || fields.length == 0) return null;
        try {
            JsonNode jsonNode = objectMapper.readTree(json);
            for (String field : fields) {
                if (jsonNode != null) {
                    jsonNode = jsonNode.get(field);
                } else {
                    return null;
                }
            }
            return jsonNode != null ? jsonNode.asText() : null;
        } catch (Exception e) {
            log.error("Failed to extract nested field from JSON", e);
            return null;
        }
    }

}
