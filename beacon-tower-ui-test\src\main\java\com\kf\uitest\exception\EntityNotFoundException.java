package com.kf.uitest.exception;

import lombok.Getter;

public class EntityNotFoundException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    private String entityType;
    private Object entityId;

    public EntityNotFoundException(String message) {
        super(message);
    }

    public EntityNotFoundException(String entityType, Object entityId) {
        super(String.format("%s not found with id: %s", entityType, entityId));
        this.entityType = entityType;
        this.entityId = entityId;
    }

    public EntityNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public EntityNotFoundException(String entityType, Object entityId, Throwable cause) {
        super(String.format("%s not found with id: %s", entityType, entityId), cause);
        this.entityType = entityType;
        this.entityId = entityId;
    }

}