/**
 * AI测试服务类型定义
 */

import type { AxiosResponse } from 'axios'

// ==================== AI测试服务类型定义 ====================

export type AITestTaskStatus = 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'PARTIAL_SUCCESS'
export type AITestStageName = 'recognize' | 'extraction' | 'structured' | 'transformer'
export type AITestStageStatus = 'SUCCESS' | 'FAILED' | 'TIMEOUT'

export interface AITestStartRequest {
    userId?: string
    ids: string[]
    uatBaseUrl?: string
    testBaseUrl?: string
    enableAiEvaluation?: boolean
    concurrentLimit?: number
    timeoutSeconds?: number
    disableChunking?: boolean
}

export interface AITestListRequest {
    current: number
    size: number
    userId?: string
    taskId?: string
    overallStatus?: AITestTaskStatus
    startTime?: string
    endTime?: string
}

export interface AITestStartResponse {
    taskId: string
    message: string
    progressUrl: string
}

export interface AITestTask {
    id: number
    taskId: string
    userId: string
    comparisonIds: string
    overallStatus: AITestTaskStatus
    overallScore?: number
    overallAiEvaluation?: string
    createTime: string
    updateTime: string
    startTime?: string
    endTime?: string
    processingTime?: number
    errorMessage?: string
    stages?: AITestStageResult[]
}

export interface AITestStageResult {
    id: number
    comparisonId: number
    dataId: string
    stageName: AITestStageName
    dataType: string
    stageStatus: AITestStageStatus
    uatData?: string
    testData?: string
    diffResult?: string
    aiEvaluation?: string
    stageScore?: number
    processingTime?: number
    errorMessage?: string
    createTime: string
    updateTime: string
}

export interface AITestConfig {
    defaultUatUrl: string
    defaultTestUrl: string
    supportedStages: string
}

export interface SSEEventData {
    type: 'connected' | 'progress' | 'ai-evaluation' | 'ai-result' | 'stage-complete' | 'task-complete' | 'error'
    taskId: string
    dataId?: string
    stageName?: AITestStageName
    progress?: number
    message?: string
    data?: any
    timestamp: number
}

export interface SSEConnectionConfig {
    taskId: string
    reconnectInterval?: number
    maxReconnectAttempts?: number
    timeout?: number
}

export type SSEConnectionStatus = 'CONNECTING' | 'CONNECTED' | 'DISCONNECTED' | 'ERROR'

export interface SSEEventCallbacks {
    onConnected?: () => void
    onProgress?: (data: SSEEventData) => void
    onAiEvaluation?: (data: SSEEventData) => void
    onAiResult?: (data: SSEEventData) => void
    onStageComplete?: (data: SSEEventData) => void
    onTaskComplete?: (data: SSEEventData) => void
    onError?: (error: Error) => void
    onDisconnected?: () => void
}

// ==================== API响应类型 ====================

export type AITestStartApiResponse = AxiosResponse<IResponse<AITestStartResponse>>
export type AITestListApiResponse = AxiosResponse<IResponse<ITable<AITestTask>>>
export type AITestDetailApiResponse = AxiosResponse<IResponse<AITestTask>>
export type AITestConfigApiResponse = AxiosResponse<IResponse<AITestConfig>>
export type AITestHealthApiResponse = AxiosResponse<IResponse<string>>
