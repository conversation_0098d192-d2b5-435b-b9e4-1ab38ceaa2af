package com.kf.uitest.enums;

import lombok.Getter;

/**
 * 元素定位方式枚举
 */
@Getter
public enum LocatorType {
    ID("ID", "通过元素ID定位"),
    NAME("NAME", "通过元素name属性定位"),
    CLASS_NAME("CLASS_NAME", "通过元素class属性定位"),
    CSS_SELECTOR("CSS_SELECTOR", "通过CSS选择器定位"),
    XPATH("XPATH", "通过XPath表达式定位"),
    LINK_TEXT("LINK_TEXT", "通过链接文本完全匹配定位"),
    PARTIAL_LINK_TEXT("PARTIAL_LINK_TEXT", "通过链接文本部分匹配定位"),
    TAG_NAME("TAG_NAME", "通过标签名定位"),
    ACCESSIBILITY_ID("ACCESSIBILITY_ID", "通过Accessibility ID定位（移动端）"),
    IOS_PREDICATE("IOS_PREDICATE", "通过iOS Predicate定位（iOS）"),
    IOS_CLASS_CHAIN("IOS_CLASS_CHAIN", "通过iOS Class Chain定位（iOS）"),
    ANDROID_UI_AUTOMATOR("ANDROID_UI_AUTOMATOR", "通过UIAutomator定位（Android）"),
    CUSTOM("CUSTOM", "自定义定位方式");

    private final String name;
    private final String description;

    LocatorType(String name, String description) {
        this.name = name;
        this.description = description;
    }
} 