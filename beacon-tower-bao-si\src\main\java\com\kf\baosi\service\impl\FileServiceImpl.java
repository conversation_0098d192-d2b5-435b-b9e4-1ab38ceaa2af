package com.kf.baosi.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.SecureUtil;
import com.kf.baosi.dao.TFileMapper;
import com.kf.baosi.dto.FSFileParamsDTO;
import com.kf.baosi.entity.DataToJiraParam;
import com.kf.baosi.entity.TFile;
import com.kf.baosi.service.FileService;
import com.kf.baosi.utils.JsonUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.*;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MimeType;
import org.springframework.util.MimeTypeUtils;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

import static com.kf.baosi.utils.JsonUtil.getFieldFromJson;

@Slf4j
@Service
public class FileServiceImpl implements FileService {

    //addId
    private static final String FS_APP_ID = "beacon-tower";

    private static final String FS_URL = "http://file.test.com";

    // 获取一次性上传token
    private static final String FS_PA_TOKEN_UPLOAD = FS_URL + "/file/pa/token/upload";

    // 批量上传文件
    private static final String FS_BATCH_UPLOAD = "/file/batchUpload";

    // 下载FS文件
    private static final String FS_DOWNLOAD = "/file/getFile";


    @Resource
    private TFileMapper tFileMapper;

    @Resource
    ResourceLoader resourceLoader;

    @Resource
    private RestTemplate restTemplate;


    @Value("${file.upload.dir}")
    private String uploadFilePath;

    @Value("${file.upload.fs-url}")
    private String fsUrl;

    @Override
    public TFile uploadFile(MultipartFile file, String userId) {
        String simpleUUID = IdUtil.simpleUUID();
        // 提取文件信息
        String originalFilename = file.getOriginalFilename();
        String fileName = originalFilename != null ? originalFilename : "unknown";
        String fileSuffix = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            fileSuffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        String filePath = uploadFilePath + "/" + simpleUUID + fileSuffix;
        File fileTempObj = new File(filePath);
        // 判断filePath是否存在，如果不存在就创建一个文件夹
        if (!fileTempObj.getParentFile().exists()) {
            fileTempObj.getParentFile().mkdirs();
        }
        try {
            //写入文件
            file.transferTo(fileTempObj);
        } catch (Exception e) {
            log.error("发生错误:{}", e.getMessage());
            throw new RuntimeException(e);
        }
        TFile tFile = new TFile();
        tFile.setId(simpleUUID);
        tFile.setUserId(userId);
        tFile.setFileName(fileName);
        tFile.setFilePath(filePath);
        tFile.setFileSize(FileUtil.size(fileTempObj));
        tFile.setFileMd5(SecureUtil.md5(fileTempObj));
        tFile.setFileSuffix(fileSuffix);
        tFile.setCreateTime(new Date());
        tFile.setUpdateTime(new Date());
        tFile.insert();
        return tFile;
    }

    @Override
    public ResponseEntity<byte[]> readFileAsBytes(String fileId) {
        // 查询文件信息
        TFile file = tFileMapper.queryById(fileId);
        if (file == null) {
            throw new RuntimeException("文件不存在");
        }

        // 检查文件是否为图片
        if (!isImage(file.getFilePath())) {
            throw new RuntimeException("文件不是图片");
        }

        // 获取文件路径并读取内容
        Path path = Paths.get(file.getFilePath()).normalize();
        try {
            byte[] bytes = Files.readAllBytes(path);

            // 获取文件的 MediaType
            MediaType mediaType = getFileMediaType(file.getFilePath());

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(mediaType);
            headers.setContentDispositionFormData("inline", file.getFileName());

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(bytes);
        } catch (IOException e) {
            throw new RuntimeException("文件读取失败：" + e.getMessage(), e);
        }
    }


    public boolean isImage(String filePath) {
        MediaType mediaType = getFileMediaType(filePath);
        return mediaType != null && mediaType.getType().equalsIgnoreCase("image");
    }

    public MediaType getFileMediaType(String filePath) {
        Path path = Paths.get(filePath).normalize();
        try {
            String mimeType = Files.probeContentType(path);
            if (mimeType != null) {
                return MediaType.parseMediaType(mimeType);
            }
        } catch (IOException ignored) {

        }
        return MediaType.APPLICATION_OCTET_STREAM;
    }

    /**
     * 通过ID查询单条Xml数据
     *
     * @param userId 用户ID
     * @return Json
     */
    @Override
    public List<TFile> queryXmlByUserId(String userId, String suffixName) {
        return this.tFileMapper.queryBySuffixUserId(userId, suffixName);
    }

    @Override
    public List<DataToJiraParam> queryJoinedByBySuffixUserId(String userId, String fileSuffix) {
        return this.tFileMapper.queryJoinedByBySuffixUserId(userId, fileSuffix);
    }

    /**
     * 新增数据
     *
     * @param file xml文件
     * @return Json
     */
    @Override
    public int xmlInsert(MultipartFile file, String userId) {
        List<TFile> files = tFileMapper.queryBySuffixUserId(userId, ".xml");
        //判断是否已经上传过文件
        if (!files.isEmpty()) {
            //遍历文件列表，删除文件
            for (TFile tFile : files) {
                tFile.setIsDeleted(1);
                tFile.setUpdateTime(new Date());
                tFileMapper.update(tFile);
            }
        }
        String simpleUUID = IdUtil.simpleUUID();
        //文件名
        String fileName = file.getOriginalFilename();
        String suffixName = fileName.substring(fileName.lastIndexOf("."));
        log.info("用户ID：{}，上传文件:{}", userId, fileName);
        String filePath = uploadFilePath + "/" + simpleUUID + suffixName;
        File fileTempObj = new File(filePath);
        //判断filePath是否存在，如果不存在就创建一个文件夹
        if (!fileTempObj.getParentFile().exists()) {
            fileTempObj.getParentFile().mkdirs();
        }

        try {
            //写入文件
            file.transferTo(fileTempObj);
        } catch (Exception e) {
            log.error("发生错误:{}", e.getMessage());
            throw new RuntimeException(e);
        }
        TFile tFile = new TFile();
        tFile.setId(simpleUUID);
        tFile.setUserId(userId);
        tFile.setFileName(fileName);
        tFile.setFilePath(filePath);
        tFile.setFileSize(FileUtil.size(fileTempObj));
        tFile.setFileMd5(SecureUtil.md5(fileTempObj));
        tFile.setFileSuffix(suffixName);
        tFile.setCreateTime(new Date());
        tFile.setUpdateTime(new Date());
        return tFileMapper.insert(tFile);
    }

    @Override
    public int update(String fileId) {
        TFile tFile = new TFile();
        tFile.setId(fileId);
        tFile.setIsDeleted(1);
        tFile.setUpdateTime(new Date());
        return tFileMapper.update(tFile);
    }


    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteFileById(String id) {
        TFile file = tFileMapper.queryById(id);
        update(id);
        //判断file是否为空
        if (file == null) {
            throw new RuntimeException("文件不存在");
        }
        return FileUtil.del(file.getFilePath());
    }

    /**
     * 通过Id下载文件
     */
    @Override
    public void downloadFile(String fileId, HttpServletResponse response) {
        TFile file = tFileMapper.queryById(fileId);
        if (file == null) {
            throw new RuntimeException("文件不存在");
        }

        Path filePath = Paths.get(file.getFilePath());
        if (!Files.exists(filePath) || !Files.isReadable(filePath)) {
            throw new RuntimeException("文件不存在或不可读");
        }

        String mimeType;
        try {
            mimeType = Files.probeContentType(filePath);
        } catch (IOException e) {
            log.error("获取文件的MIME类型失败：{}", e.getMessage());
            mimeType = "application/octet-stream";
        }
        if (mimeType == null) {
            mimeType = "application/octet-stream";
        }
        response.setContentType(mimeType);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Expose-Headers", HttpHeaders.CONTENT_DISPOSITION);

        try {
            String originalFileName = file.getFileName();
            String encodedFileName = URLEncoder.encode(originalFileName, StandardCharsets.UTF_8);
            encodedFileName = encodedFileName.replaceAll("\\+", "%20");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + encodedFileName);

            try (InputStream in = Files.newInputStream(filePath);
                 OutputStream out = response.getOutputStream()) {
                IOUtils.copyLarge(in, out);
                response.flushBuffer();
            }
        } catch (IOException e) {
            log.error("文件下载失败：{}", e.getMessage());
            throw new RuntimeException("文件下载失败");
        }
    }

    /**
     * 下载xMind文件
     */
    @Override
    public void downloadXMindTemplateFile(HttpServletRequest req, HttpServletResponse response) {
        String FileName = "TestCase.xmind";
        //读取resources下的文件
        org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:static/" + FileName);
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Expose-Headers", HttpHeaders.CONTENT_DISPOSITION);
//        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + FileName);
        try {
//            fIleOutputStream(Files.newInputStream(Paths.get(resource.getFile().getPath())), response);
            String excelName = URLEncoder.encode(FileName, StandardCharsets.UTF_8);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + excelName);
            fIleOutputStream(resource.getInputStream(), response);
        } catch (IOException e) {
            log.error("文件下载失败：{}", e.getMessage());
            throw new RuntimeException("文件下载失败");
        }
    }

    /**
     * 下载xMind文件
     */
    @Override
    public void downloadInterfacePageTemplateFile(HttpServletRequest req, HttpServletResponse response) {
        String FileName = "页面与接口的关系维护-模板.xlsx";
        //读取resources下的文件
        org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:static/" + FileName);
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Expose-Headers", HttpHeaders.CONTENT_DISPOSITION);
        try {
            String excelName = URLEncoder.encode(FileName, StandardCharsets.UTF_8);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + excelName);
            fIleOutputStream(resource.getInputStream(), response);
        } catch (IOException e) {
            log.error("文件下载失败：{}", e.getMessage());
            throw new RuntimeException("文件下载失败");
        }
    }

    @Override
    public TFile queryFileById(String fileId) {
        return tFileMapper.queryById(fileId);
    }

    @Override
    public String getTokenForFSUpload() {
        try {
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(FS_PA_TOKEN_UPLOAD);
            Map<String, Object> params = Map.of("appId", FS_APP_ID);
            params.forEach(builder::queryParam);
            String urlWithParams = builder.toUriString();
            ResponseEntity<String> response = restTemplate.getForEntity(urlWithParams, String.class);
            return getFieldFromJson(response.getBody(), "data");
        } catch (Exception e) {
            log.error("获取fs_token失败：{}", e.getMessage());
            throw new RuntimeException("获取fs_token失败");
        }
    }

    @Retryable(
            retryFor = {Exception.class},
            backoff = @Backoff(delay = 30000, multiplier = 2)
    )
    @Override
    public String uploadFileToFS(FSFileParamsDTO fsFileParams) {
        HttpHeaders headers = new HttpHeaders();
        // 设置请求头multipart
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity<>(JsonUtil.toJson(fsFileParams), headers);
        ResponseEntity<String> response = restTemplate.postForEntity(String.format(fsUrl + FS_BATCH_UPLOAD), httpEntity, String.class);
        String responseBody = response.getBody();
        log.info("上传文件到FS结果:{}", responseBody);
        // 使用工具类中的方法获取嵌套字段的值
        return JsonUtil.getNestedFieldFromJson(responseBody, "data", "fileId");
    }

    @Retryable(
            retryFor = {Exception.class},
            backoff = @Backoff(delay = 30000, multiplier = 2)
    )
    @Override
    public Map<String, String> batchUploadFileToFS(Map<String, String> fsFileParamsMap) {
        String url = fsUrl + FS_BATCH_UPLOAD;
        LinkedMultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        Map<String, String> resultMap = new LinkedHashMap<>();
        body.add("appId", "beacon-tower");
        body.add("processor", "resources");
        log.info("批量上传文件到FS: {}", fsFileParamsMap);
        for (Map.Entry<String, String> entry : fsFileParamsMap.entrySet()) {
            body.add("file", new FileSystemResource(entry.getValue()));
            resultMap.put(entry.getKey(), null);
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        HttpEntity<LinkedMultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        ResponseEntity<Map> response = restTemplate.postForEntity(url, requestEntity, Map.class);
        log.info("批量上传文件到FS结果: {}", response.getBody());
        if (response.getStatusCode() == HttpStatus.OK) {
            List<Map<String, String>> files = (List<Map<String, String>>) response.getBody().get("data");
            if (files != null) {
                for (Map<String, String> file : files) {
                    String originFileName = file.get("originFileName");
                    String fileId = file.get("fileId");
                    if (resultMap.containsKey(originFileName)) {
                        resultMap.put(originFileName, fileId);
                    }
                }
            }
            return resultMap;
        } else {
            throw new RuntimeException("文件上传失败: " + response.getStatusCode());
        }
    }

    public void fIleOutputStream(InputStream inputStream, HttpServletResponse response) {
        try {
            OutputStream outputStream = response.getOutputStream();
            byte[] buffer = new byte[4096];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }
            outputStream.flush();
            outputStream.close();
            inputStream.close();
        } catch (FileNotFoundException e) {
            throw new RuntimeException("文件不存在");
        } catch (IOException e) {
            log.error("文件下载失败：{}", e.getMessage());
            throw new RuntimeException("文件下载失败");
        }
    }

    @Override
    public void getFsFile(String fileId, String fileName, HttpServletResponse response) {
        log.info("下载文件的请求参数fileName:{}", fileName);

        String urlWithParams = String.format("%s?fileId=%s&zipFileName=%s", fsUrl + FS_DOWNLOAD, fileId, fileName);
        log.info("下载文件的请求参数:{}", urlWithParams);

        try {
            ResponseEntity<byte[]> res = restTemplate.getForEntity(urlWithParams, byte[].class);
            if (res.getStatusCode().is2xxSuccessful() && res.getBody() != null) {
                HttpHeaders headers = res.getHeaders();
                String contentType = headers.getContentType() != null ? headers.getContentType().toString() : MediaType.APPLICATION_OCTET_STREAM_VALUE;
                log.info("文件的ContentType: {}", contentType);

                // 获取文件后缀，尝试从 Content-Disposition 中获取文件名
                String finalFileName = extractFileNameFromHeaders(headers, fileName);
                String fileExtension = getFileExtensionByContentType(contentType);
                if (!finalFileName.contains(".")) {
                    finalFileName += fileExtension;
                }

                response.setContentType(contentType);

                // 使用 ContentDisposition 构建文件名
                ContentDisposition contentDisposition = ContentDisposition.attachment()
                        .filename(finalFileName, StandardCharsets.UTF_8)
                        .build();
                log.info("Content-Disposition: {}", contentDisposition.toString());
                response.setHeader(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());
                try (OutputStream out = response.getOutputStream()) {
                    out.write(res.getBody());
                    response.flushBuffer();
                    log.info("文件下载成功: {}", finalFileName);
                }
            } else {
                log.error("下载文件失败: HTTP状态码 {}，body：{}", res.getStatusCode(), res.getBody());
                throw new RuntimeException("下载失败");
            }
        } catch (IOException e) {
            log.error("文件下载失败：{}", e.getMessage(), e);
            throw new RuntimeException("文件下载失败", e);
        }
    }

    private String extractFileNameFromHeaders(HttpHeaders headers, String defaultFileName) {
        String contentDisposition = headers.getFirst(HttpHeaders.CONTENT_DISPOSITION);
        if (contentDisposition != null) {
            ContentDisposition disposition = ContentDisposition.parse(contentDisposition);
            String fileName = disposition.getFilename();
            if (fileName != null) {
                try {
                    return URLDecoder.decode(fileName, StandardCharsets.UTF_8);
                } catch (IllegalArgumentException e) {
                    log.warn("文件名解码失败，使用默认文件名: {}", fileName);
                    return fileName;
                }
            }
        }
        return defaultFileName;
    }

    private String getFileExtensionByContentType(String contentType) {
        MimeType mimeType = MimeTypeUtils.parseMimeType(contentType);
        String subtype = mimeType.getSubtype();
        return subtype != null && !subtype.equals("*") ? "." + subtype : "";
    }


    @Recover
    public String recover(HttpServerErrorException e, FSFileParamsDTO fsFileParams) {
        log.error("上传文件失败，已达到最大重试次数，错误信息: {}", e.getMessage());
        // 处理最终失败的逻辑，比如返回默认值或抛出自定义异常
        throw new RuntimeException("上传文件失败");
    }
}


