package com.kf.userservice.dao;

import com.kf.userservice.entity.TUserRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TUserRoleDao {

    /**
     * 通过ID查询单条数据
     *
     * @param userId
     * @return 实例对象
     */
    TUserRole queryById(Long userId);

    /**
     * 通过ID查询数据 一个用户多个角色
     *
     * @param userId
     * @return 实例对象
     */
    List<TUserRole> queryAllById(Long userId);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    List<TUserRole> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param tUserRole 实例对象
     * @return 对象列表
     */
    List<TUserRole> queryAll(TUserRole tUserRole);

    /**
     * 新增数据
     *
     * @param tUserRole 实例对象
     * @return 影响行数
     */
    int insert(TUserRole tUserRole);

    /**
     * 修改数据
     *
     * @param tUserRole 实例对象
     * @return 影响行数
     */
//    int update(TUserRole tUserRole);

    /**
     * 通过主键删除数据
     *
     * @param userId 主键
     * @return 影响行数
     */
    int deleteById(Long userId);

//    int updateUserRole(Long userId, @Param("roles") List<String> roles);

}