package com.kf.accuratetest.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@TableName("t_interface_annotation")
public class InterfaceAnnotation implements Serializable {
    private static final long serialVersionUID = 700426499123445700L;
    /**
     * 主键
     */
    private String taskId;

    /**
     * 接口名称
     */
    private String interfaceName;
    /**
     * 接口名称
     */
    private String interfaceDesc;
    /**
     * 接口名称
     */
    private String interfaceNotes;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 是否删除  0未删除 1删除
     */
    private Integer isDeleted;

}

