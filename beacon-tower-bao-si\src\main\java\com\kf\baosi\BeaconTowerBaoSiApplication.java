package com.kf.baosi;


import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.retry.annotation.EnableRetry;

@MapperScan({"com.kf.baosi.dao"})
@EnableDiscoveryClient
@SpringBootApplication
@EnableRetry
public class BeaconTowerBaoSiApplication {

    public static void main(String[] args) {
        SpringApplication.run(BeaconTowerBaoSiApplication.class, args);
    }

}
