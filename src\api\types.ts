/**
 * API 全局通用类型定义
 * 只包含接口返回值的通用定义
 */

import type { AxiosResponse } from 'axios'

// 全局类型声明
export { }
declare global {
    interface IResponse<T = any> {
        code: number
        message: string
        isSuccess: boolean
        data: T
    }

    interface IObject<T> {
        [index: string]: T
    }

    interface ITable<T = any> {
        data: Array<T>
        total: number
        page: number
        size: number
    }

    interface ImportMetaEnv {
        VITE_APP_TITLE: string
        VITE_PORT: number
        VITE_PROXY: string
    }
}
