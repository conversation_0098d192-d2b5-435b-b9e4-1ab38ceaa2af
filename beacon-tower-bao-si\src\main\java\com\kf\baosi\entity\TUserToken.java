package com.kf.baosi.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_user_token")
public class TUserToken extends Model<TUserToken> {
    @Serial
    private static final long serialVersionUID = 1L;
    // 主键
    @TableId
    private String userId;
    // jira用户名
    private String jiraUserName;
    // jira令牌
    private String jiraToken;

    @Override
    public Serializable pkVal() {
        return this.userId;
    }

}

