package com.kf.aitest.service;

import com.kf.aitest.service.impl.PromptTemplateServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 提示词优化测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class PromptOptimizationTest {

    @Autowired
    private PromptTemplateServiceImpl promptTemplateService;

    @Test
    public void testOptimizedPromptTemplates() {
        System.out.println("=== 测试优化后的提示词模板 ===");
        
        // 测试所有四个阶段的提示词模板
        String[] stages = {"recognize", "extraction", "structured", "transformer"};
        
        for (String stage : stages) {
            System.out.printf("\n--- 测试 %s 阶段提示词 ---\n", stage);
            
            try {
                String template = promptTemplateService.getPromptTemplate(stage);
                
                // 检查是否包含优化后的关键内容
                boolean hasUATTESTFormat = template.contains("UAT环境") && template.contains("TEST环境");
                boolean hasScoringRules = template.contains("评分规则") || template.contains("同类错误只计算一次");
                boolean hasDetailedFormat = template.contains("明确列出") || template.contains("具体内容");
                
                System.out.printf("模板长度: %d 字符\n", template.length());
                System.out.printf("包含UAT/TEST格式要求: %s\n", hasUATTESTFormat ? "✅" : "❌");
                System.out.printf("包含评分规则: %s\n", hasScoringRules ? "✅" : "❌");
                System.out.printf("包含详细格式要求: %s\n", hasDetailedFormat ? "✅" : "❌");
                
                // 验证关键优化点
                assert hasUATTESTFormat : stage + "阶段应该包含UAT/TEST环境格式要求";
                assert hasScoringRules : stage + "阶段应该包含评分规则";
                assert hasDetailedFormat : stage + "阶段应该包含详细格式要求";
                
                System.out.printf("%s 阶段提示词优化验证通过 ✅\n", stage);
                
            } catch (Exception e) {
                System.err.printf("测试 %s 阶段失败: %s\n", stage, e.getMessage());
                throw e;
            }
        }
        
        System.out.println("\n=== 所有提示词模板优化验证完成 ===");
    }
    
    @Test
    public void testSpecificOptimizations() {
        System.out.println("=== 测试具体优化内容 ===");
        
        // 测试structured阶段的特殊优化（针对quantitativeCompare问题）
        String structuredTemplate = promptTemplateService.getPromptTemplate("structured");
        
        System.out.println("\n--- 测试structured阶段特殊优化 ---");
        boolean hasQuantitativeCompareRule = structuredTemplate.contains("quantitativeCompare") 
                || structuredTemplate.contains("相同字段的多个实例差异");
        boolean hasFieldLevelEvaluation = structuredTemplate.contains("字段级别评估") 
                || structuredTemplate.contains("按字段类型进行评估");
        
        System.out.printf("包含quantitativeCompare规则: %s\n", hasQuantitativeCompareRule ? "✅" : "❌");
        System.out.printf("包含字段级别评估: %s\n", hasFieldLevelEvaluation ? "✅" : "❌");
        
        // 测试所有阶段的通用优化
        String[] stages = {"recognize", "extraction", "structured", "transformer"};
        
        for (String stage : stages) {
            String template = promptTemplateService.getPromptTemplate(stage);
            
            System.out.printf("\n--- 测试 %s 阶段通用优化 ---\n", stage);
            
            // 检查是否包含"不重复扣分"相关内容
            boolean hasNoRepeatScoring = template.contains("不重复扣分") 
                    || template.contains("只作为一个问题") 
                    || template.contains("只影响一次评分");
            
            // 检查是否要求明确列出两个环境的内容
            boolean hasExplicitContent = template.contains("明确列出") 
                    && template.contains("UAT环境") 
                    && template.contains("TEST环境");
            
            System.out.printf("包含不重复扣分规则: %s\n", hasNoRepeatScoring ? "✅" : "❌");
            System.out.printf("要求明确列出环境内容: %s\n", hasExplicitContent ? "✅" : "❌");
            
            assert hasNoRepeatScoring : stage + "阶段应该包含不重复扣分规则";
            assert hasExplicitContent : stage + "阶段应该要求明确列出环境内容";
        }
        
        System.out.println("\n=== 具体优化内容验证完成 ===");
    }
    
    @Test
    public void testPromptStructure() {
        System.out.println("=== 测试提示词结构完整性 ===");
        
        String[] stages = {"recognize", "extraction", "structured", "transformer"};
        String[] requiredSections = {"任务描述", "评估维度", "输出格式要求", "评分规则", "注意事项"};
        
        for (String stage : stages) {
            String template = promptTemplateService.getPromptTemplate(stage);
            
            System.out.printf("\n--- 测试 %s 阶段结构完整性 ---\n", stage);
            
            for (String section : requiredSections) {
                boolean hasSection = template.contains(section);
                System.out.printf("包含 %s: %s\n", section, hasSection ? "✅" : "❌");
                
                if (!hasSection && !"评分规则".equals(section)) {
                    // 评分规则是新增的，可能不是所有模板都有相同的标题
                    assert false : stage + "阶段应该包含" + section + "部分";
                }
            }
            
            // 检查输出格式是否包含评分要求
            boolean hasScoring = template.contains("评分") && template.contains("0-100");
            System.out.printf("包含评分要求: %s\n", hasScoring ? "✅" : "❌");
            assert hasScoring : stage + "阶段应该包含0-100评分要求";
        }
        
        System.out.println("\n=== 提示词结构完整性验证完成 ===");
    }
}
