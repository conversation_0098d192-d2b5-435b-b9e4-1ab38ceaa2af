<template>
  <span
      @click="openDefectDialog(testCaseRow, requirementKey)"
      :style="defectStyle"
      class="defect-span"
  >
    {{ defectInfo.count }}
  </span>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { JiraTestCaseRunStep } from '@/api/layout'

// 定义接收的 props
const props = defineProps<{
    testCaseKey: string
    testCaseRow: JiraTestCaseRunStep
    requirementKey: string
    getTotalDefects: (testCaseKey: string, requirementKey: string) => { count: number, isRed: boolean }
    openDefectDialog: (testCaseRow: JiraTestCaseRunStep, requirementKey: string) => void
}>()

const { testCaseKey, requirementKey, getTotalDefects, openDefectDialog, testCaseRow } = props

// 计算缺陷信息
const defectInfo = computed(() => getTotalDefects(testCaseKey, requirementKey))

// 计算样式
const defectStyle = computed(() => ({
    color: defectInfo.value.isRed ? 'red' : 'green',
    fontWeight: 'bold',
    fontSize: '16px'
}))
</script>

<style lang="postcss" scoped>
.defect-span {
    cursor: pointer;
}
</style>
