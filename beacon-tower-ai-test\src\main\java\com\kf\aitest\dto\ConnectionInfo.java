package com.kf.aitest.dto;

import com.kf.aitest.enums.ConnectionStatus;
import lombok.Data;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicLong;

/**
 * SSE连接信息
 */
@Data
public class ConnectionInfo {
    
    /**
     * SSE发射器
     */
    private SseEmitter emitter;
    
    /**
     * 连接状态
     */
    private ConnectionStatus status;
    
    /**
     * 连接创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveTime;
    
    /**
     * 消息队列
     */
    private final ConcurrentLinkedQueue<CachedMessage> messageQueue = new ConcurrentLinkedQueue<>();
    
    /**
     * 消息序号生成器
     */
    private final AtomicLong sequenceGenerator = new AtomicLong(0);
    
    /**
     * 最大缓存消息数量
     */
    private static final int MAX_CACHED_MESSAGES = 1000;
    
    public ConnectionInfo() {
        this.createTime = LocalDateTime.now();
        this.lastActiveTime = LocalDateTime.now();
        this.status = ConnectionStatus.PENDING;
    }
    
    /**
     * 添加消息到缓存队列
     */
    public void addMessage(String eventName, Object data) {
        // 如果队列已满，移除最旧的消息
        if (messageQueue.size() >= MAX_CACHED_MESSAGES) {
            messageQueue.poll();
        }
        
        CachedMessage message = new CachedMessage(eventName, data, sequenceGenerator.incrementAndGet());
        messageQueue.offer(message);
        this.lastActiveTime = LocalDateTime.now();
    }
    
    /**
     * 获取并清空所有缓存消息
     */
    public ConcurrentLinkedQueue<CachedMessage> drainMessages() {
        ConcurrentLinkedQueue<CachedMessage> messages = new ConcurrentLinkedQueue<>(messageQueue);
        messageQueue.clear();
        return messages;
    }
    
    /**
     * 获取缓存消息数量
     */
    public int getMessageCount() {
        return messageQueue.size();
    }
    
    /**
     * 更新最后活跃时间
     */
    public void updateLastActiveTime() {
        this.lastActiveTime = LocalDateTime.now();
    }
}
