package com.kf.aitest.service;

import com.kf.aitest.dto.ConnectionInfo;
import com.kf.aitest.enums.ConnectionStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ComparisonProgressManager测试类
 */
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
class ComparisonProgressManagerTest {

    @Autowired
    private ComparisonProgressManager progressManager;

    private String testTaskId;

    @BeforeEach
    void setUp() {
        testTaskId = "test-" + UUID.randomUUID().toString();
    }

    @Test
    void testInitializeTask() {
        // 测试任务初始化
        progressManager.initializeTask(testTaskId);
        
        // 验证连接信息已创建
        SseEmitter emitter = progressManager.getEmitter(testTaskId);
        assertNull(emitter, "初始化时不应该有SSE连接");
        
        // 验证连接统计
        String stats = progressManager.getConnectionStats();
        assertNotNull(stats);
        assertTrue(stats.contains("等待:1"), "应该有一个等待状态的连接");
    }

    @Test
    void testCreateEmitter() {
        // 先初始化任务
        progressManager.initializeTask(testTaskId);
        
        // 创建SSE连接
        SseEmitter emitter = progressManager.createEmitter(testTaskId);
        
        assertNotNull(emitter, "应该成功创建SSE连接");
        
        // 验证连接状态
        String stats = progressManager.getConnectionStats();
        assertTrue(stats.contains("已连接:1"), "应该有一个已连接状态的连接");
    }

    @Test
    void testMessageCaching() throws InterruptedException {
        // 初始化任务但不创建连接
        progressManager.initializeTask(testTaskId);
        
        // 初始化进度（这会发送消息到缓存）
        progressManager.initProgress(testTaskId, 5);
        
        // 验证消息被缓存
        String stats = progressManager.getConnectionStats();
        assertTrue(stats.contains("缓存消息:"), "应该有缓存消息");
        
        // 创建连接，应该会发送缓存的消息
        SseEmitter emitter = progressManager.createEmitter(testTaskId);
        assertNotNull(emitter);
        
        // 等待消息发送完成
        Thread.sleep(100);
        
        // 验证连接状态
        String finalStats = progressManager.getConnectionStats();
        assertTrue(finalStats.contains("已连接:1"), "连接应该处于已连接状态");
    }

    @Test
    void testProgressUpdates() {
        // 初始化任务和连接
        progressManager.initializeTask(testTaskId);
        progressManager.initProgress(testTaskId, 3);
        SseEmitter emitter = progressManager.createEmitter(testTaskId);
        
        assertNotNull(emitter);
        
        // 测试进度更新
        progressManager.updateCurrentId(testTaskId, "id1", 0);
        progressManager.updateCurrentStage(testTaskId, "recognize", 0);
        progressManager.completeStage(testTaskId, "recognize");
        
        // 验证连接仍然活跃
        String stats = progressManager.getConnectionStats();
        assertTrue(stats.contains("已连接:1"), "连接应该保持活跃");
    }

    @Test
    void testConnectionTimeout() throws InterruptedException {
        // 创建一个短超时的连接进行测试
        progressManager.initializeTask(testTaskId);
        SseEmitter emitter = progressManager.createEmitter(testTaskId);
        
        assertNotNull(emitter);
        
        // 模拟连接超时
        emitter.onTimeout(() -> {
            System.out.println("连接超时回调被触发");
        });
        
        // 验证初始状态
        String stats = progressManager.getConnectionStats();
        assertTrue(stats.contains("已连接:1"), "应该有一个已连接的连接");
    }

    @Test
    void testErrorHandling() {
        // 初始化任务
        progressManager.initializeTask(testTaskId);
        
        // 发送错误
        progressManager.sendError(testTaskId, "测试错误消息");
        
        // 验证错误处理
        String stats = progressManager.getConnectionStats();
        // 错误发送后连接应该被清理或标记为断开
        assertNotNull(stats);
    }

    @Test
    void testMultipleConnections() {
        String taskId1 = "test1-" + UUID.randomUUID().toString();
        String taskId2 = "test2-" + UUID.randomUUID().toString();
        
        // 创建多个连接
        progressManager.initializeTask(taskId1);
        progressManager.initializeTask(taskId2);
        
        SseEmitter emitter1 = progressManager.createEmitter(taskId1);
        SseEmitter emitter2 = progressManager.createEmitter(taskId2);
        
        assertNotNull(emitter1);
        assertNotNull(emitter2);
        assertNotEquals(emitter1, emitter2);
        
        // 验证连接统计
        String stats = progressManager.getConnectionStats();
        assertTrue(stats.contains("已连接:2"), "应该有两个已连接的连接");
    }

    @Test
    void testConnectionStats() {
        // 测试连接统计功能
        String initialStats = progressManager.getConnectionStats();
        assertNotNull(initialStats);
        assertTrue(initialStats.contains("连接统计"), "应该包含连接统计信息");
        
        // 创建一些连接
        progressManager.initializeTask(testTaskId);
        progressManager.initProgress(testTaskId, 1);
        
        String statsWithPending = progressManager.getConnectionStats();
        assertTrue(statsWithPending.contains("等待:1"), "应该显示等待状态的连接");
        
        // 建立连接
        progressManager.createEmitter(testTaskId);
        
        String statsWithConnected = progressManager.getConnectionStats();
        assertTrue(statsWithConnected.contains("已连接:1"), "应该显示已连接状态的连接");
    }
}
