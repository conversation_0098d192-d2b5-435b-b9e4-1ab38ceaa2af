<template>
    <el-dialog
        v-model="localVisible"
        :title="`${testCaseKey} 创建缺陷`"
        width="60%"
        @closed="handleClosed"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @drop="handleDrop"
        @dragover.prevent
        @paste="handlePaste"
    >
        <el-form :model="form" ref="formRef" label-width="100px" class="defect-form" :rules="rules">
            <!-- 标题 -->
            <el-form-item label="缺陷标题" prop="summary">
                <el-input v-model="form.summary" placeholder="请输入标题"></el-input>
            </el-form-item>

            <!-- 主体内容 -->
            <el-row :gutter="0">
                <!-- 左侧列 -->
                <el-col :span="14">
                    <!-- 重现步骤 -->
                    <el-form-item label="重现步骤" prop="reproductionSteps">
                        <el-input
                            type="textarea"
                            v-model="form.reproductionSteps"
                            placeholder="请输入重现步骤"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                        ></el-input>
                    </el-form-item>

                    <!-- 期望结果 -->
                    <el-form-item label="期望结果" prop="expectedResult">
                        <el-input
                            type="textarea"
                            v-model="form.expectedResult"
                            placeholder="请输入期望结果"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                        ></el-input>
                    </el-form-item>

                    <!-- 实际结果 -->
                    <el-form-item label="实际结果" prop="actualResult">
                        <el-input
                            type="textarea"
                            v-model="form.actualResult"
                            placeholder="请输入实际结果"
                            :autosize="{ minRows: 4, maxRows: 4 }"
                        ></el-input>
                    </el-form-item>
                    <!-- 描述（富文本编辑器） -->
                    <el-form-item label="描述" prop="description">
                        <div class="editor" ref="editorContainer" @click="focusEditor">
                            <EditorContent :editor="editor" class="editor-content" />
                        </div>
                    </el-form-item>
                </el-col>
                <!-- 右侧列 -->
                <el-col :span="10">
                    <!-- 缺陷实际负责人 -->
                    <el-form-item label="实际负责人" prop="defectAssignee">
                        <el-select
                            v-model="form.defectAssignee"
                            placeholder="请输入缺陷实际负责人"
                            multiple
                            filterable
                            clearable
                            remote
                            :reserve-keyword="false"
                            :remote-method="filterJiraUsers"
                            :loading="loading"
                        >
                            <el-option
                                v-for="user in filteredJiraUsers"
                                :key="user.name"
                                :label="user.displayName"
                                :value="user.name"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- 研发负责人 -->
                    <el-form-item label="研发负责人" prop="developerInCharge">
                        <el-select
                            v-model="form.developerInCharge"
                            placeholder="请输入研发负责人"
                            filterable
                            clearable
                            remote
                            :reserve-keyword="false"
                            :remote-method="filterJiraUsersForDeveloperInCharge"
                            :loading="loading"
                        >
                            <el-option
                                v-for="user in filteredJiraUsersForDeveloperInCharge"
                                :key="user.name"
                                :label="user.displayName"
                                :value="user.name"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- 严重程度 -->
                    <el-form-item label="严重程度" prop="severity">
                        <el-select v-model="form.severity" placeholder="请选择严重程度">
                            <el-option label="一般" :value="10135"></el-option>
                            <el-option label="致命" :value="10133"></el-option>
                            <el-option label="严重" :value="10134"></el-option>
                            <el-option label="提示" :value="10136"></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- 版本 -->
                    <el-form-item label="修复版本" prop="fixVersions">
                        <el-select
                            v-model="form.fixVersions"
                            multiple
                            placeholder="请选择版本"
                            filterable
                            clearable
                            :reserve-keyword="false"
                            :loading="versionLoading"
                        >
                            <el-option
                                v-for="version in availableVersions"
                                :key="version"
                                :label="version"
                                :value="version"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- Bug原因分类 -->
                    <el-form-item label="原因分类" prop="bugReason">
                        <el-select v-model="form.bugReason" placeholder="请选择Bug原因分类">
                            <el-option label="无" :value="-1"></el-option>
                            <el-option label="需求描述缺漏" :value="12559"></el-option>
                            <el-option label="开发编码缺陷" :value="12560"></el-option>
                            <el-option label="外部供应商缺陷" :value="12563"></el-option>
                            <el-option label="数据问题缺陷" :value="12564"></el-option>
                            <el-option label="环境配置缺陷" :value="12767"></el-option>
                            <el-option label="技术设计缺陷(性能等)" :value="13649"></el-option>
                            <el-option label="UI或UX还原缺陷" :value="14061"></el-option>
                            <el-option label="缺少用例或漏测" :value="14661"></el-option>
                            <el-option label="历史代码缺陷" :value="14659"></el-option>
                            <el-option label="重复的 bug" :value="13650"></el-option>
                            <el-option label="设计稿描述缺漏" :value="14691"></el-option>
                            <el-option label="公共组件缺陷" :value="14692"></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- 优先级 -->
                    <el-form-item label="优先级" prop="priority">
                        <el-select v-model="form.priority" placeholder="请选择优先级">
                            <el-option label="P0 - Highest" :value="1"></el-option>
                            <el-option label="P1 - High" :value="2"></el-option>
                            <el-option label="P2 - Medium" :value="3"></el-option>
                            <el-option label="P3 - Low" :value="4"></el-option>
                            <el-option label="P4 - Lowest" :value="5"></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- Bug复现情况 -->
                    <el-form-item label="复现情况" prop="bugReproduction">
                        <el-select v-model="form.bugReproduction" placeholder="请选择Bug复现情况">
                            <el-option label="无" :value="-1"></el-option>
                            <el-option label="必现" :value="12051"></el-option>
                            <el-option label="偶现" :value="12052"></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- 模块 -->
                    <el-form-item label="模块" prop="components">
                        <el-select
                            v-model="form.components"
                            multiple
                            placeholder="请选择模块"
                            filterable
                            clearable
                            :reserve-keyword="false"
                        >
                            <el-option
                                v-for="component in componentsForProject"
                                :key="component"
                                :label="component"
                                :value="component"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- 附件 -->
                    <el-form-item label="附件" prop="attachments">
                        <div class="attachments-list">
                            <!-- 附件上传组件 -->
                            <el-upload
                                :http-request="customRequest"
                                :on-error="handleUploadError"
                                :file-list="fileList"
                                multiple
                                list-type="text"
                                :on-remove="handleRemoveFile"
                            >
                                <el-button type="primary">点击上传附件</el-button>
                            </el-upload>
                        </div>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <!-- 操作按钮 -->
        <template #footer>
            <el-button v-prevent-default type="text" @click="handleCancel">取消</el-button>
            <el-button v-prevent-default type="primary" @click="submitForm" :loading="loading">确定</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onBeforeUnmount, watch } from 'vue'
import { useEditor, EditorContent, Extension, Editor } from '@tiptap/vue-3'
import { Decoration, DecorationSet } from '@tiptap/pm/view'
import { Plugin } from '@tiptap/pm/state'
import StarterKit from '@tiptap/starter-kit'
import { Image as TiptapImage } from '@tiptap/extension-image'
import {
    createBug,
    getAllVersionForProject,
    getComponentsForProject,
    previewImage,
    uploadFile
} from '@/api/layout'
import { ElMessage } from 'element-plus'
import { useDefectAssigneeStore } from '@/stores/defectAssigneeStore'
import type { FormInstance } from 'element-plus/lib/components'
import { validate } from '@/utils/formExtend'

export interface JiraUser {
    self: string
    key: string
    name: string
    emailAddress: string
    avatarUrls: {
        '48x48': string
        '24x24': string
        '16x16': string
        '32x32': string
    }
    displayName: string
    displayNamePinyin: string
}

// 定义接收的 props
const props = defineProps<{
    jiraToken: string
    visible: boolean
    requirementKey: string
    projectKey: string
    testCaseKey: string
    runId: string
    stepId: string
    fixVersions: string[]
    components: string[]
    jiraUsers: JiraUser[]
}>()

// 在 setup 函数中
const defectAssigneeStore = useDefectAssigneeStore()
const rules = reactive({
    summary: [
        { required: true, message: '缺陷标题为必填项', trigger: 'blur' }
    ],
    severity: [
        { required: true, message: '严重程度为必填项', trigger: 'change' }
    ],
    priority: [
        { required: true, message: '优先级为必填项', trigger: 'change' }
    ],
    defectAssignee: [
        { type: 'array', required: true, message: '实际负责人为必填项', trigger: 'change' }
    ],
    developerInCharge: [
        { required: true, message: '研发负责人为必填项', trigger: 'change' }
    ],
    fixVersions: [
        { type: 'array', required: true, message: '修复版本为必填项', trigger: 'change' }
    ]
})
// 定义发出的事件
const emit = defineEmits<{
    (e: 'close'): void
    (e: 'save', payload: {
        requirementKey: string
        testCaseKey: string
        bugInfo: BugInfo
    }): void
}>()

interface BugInfo {
    key: string
    status: string
}

// 对话框的可见状态
const localVisible = ref(props.visible)

// 表单引用
const formRef = ref<FormInstance>()
// 表单数据
const form = reactive({
    projectKey: props.projectKey,
    summary: '',
    severity: 10134,
    priority: 3,
    bugReason: 12560,
    bugReproduction: 12051,
    reproductionSteps: '',
    expectedResult: '',
    actualResult: '',
    description: '',
    defectAssignee: [] as string[],
    developerInCharge: '',
    components: props.components,
    fixVersions: props.fixVersions,
    attachmentFileIds: [] as string[],
    runId: props.runId,
    stepId: props.stepId
})

// 用于存储图片的映射关系
const imageFileDataMap = new Map<string, { id: string, fileName: string }>()

// 自定义扩展，用于高亮选中的图片
const SelectedImageHighlight = Extension.create({
    name: 'selectedImageHighlight',

    addProseMirrorPlugins() {
        return [
            new Plugin({
                props: {
                    decorations(state) {
                        const { selection } = state
                        const { empty, from, to } = selection

                        if (empty) {
                            return null
                        }

                        const decorations: Decoration[] = []

                        // 遍历选区内的节点，查找图片节点
                        state.doc.nodesBetween(from, to, (node, pos) => {
                            if (node.type.name === 'image') {
                                decorations.push(
                                    Decoration.node(pos, pos + node.nodeSize, {
                                        class: 'selected-image' // 自定义类名
                                    })
                                )
                            }
                        })

                        return DecorationSet.create(state.doc, decorations)
                    }
                }
            })
        ]
    }
})

const handleImageDelete = (imageUrl: string) => {
    console.log('删除图片:', imageUrl)
    const fileData = imageFileDataMap.get(imageUrl)
    if (fileData) {
        fileList.value = fileList.value.filter(file => file.id !== fileData.id)
        imageFileDataMap.delete(imageUrl)
        form.attachmentFileIds = form.attachmentFileIds.filter(id => id !== fileData.id)
    }
}
// 扩展 Image 以支持 id 属性
const Image = TiptapImage.extend({
    addAttributes() {
        return {
            ...this.parent?.(),
            id: {
                default: null,
                parseHTML: element => element.getAttribute('data-id'),
                renderHTML: attributes => {
                    if (!attributes.id) {
                        return {}
                    }
                    return {
                        'data-id': attributes.id
                    }
                }
            }
        }
    }
})
// 富文本编辑器实例
const editor = useEditor({
    extensions: [
        StarterKit,
        Image,
        SelectedImageHighlight
    ],
    content: '',
    editorProps: {
        attributes: {
            class: 'editor-instance'
        }
    },
    onUpdate: ({ editor }) => {
        const htmlContent = editor.getHTML()

        // 使用正则表达式查找所有图片的 src
        const imgRegex = /<img[^>]+src="([^">]+)"/g
        const currentImages: string[] = []
        let match

        // 迭代匹配的结果，收集所有图片的 src
        while ((match = imgRegex.exec(htmlContent)) !== null) {
            currentImages.push(match[1])
        }

        // 检查 imageFileDataMap，找出已删除的图片
        imageFileDataMap.forEach((_, src) => {
            if (!currentImages.includes(src)) {
                handleImageDelete(src)
            }
        })
    }
})

// 在组件卸载时销毁编辑器实例
onBeforeUnmount(() => {
    editor.value?.destroy()
})

// 递归处理节点函数
function processNode(node: Node, isLast: boolean): string {
    let result = ''
    if (node.nodeName === 'P') {
        // 处理 P 标签的子节点
        node.childNodes.forEach(child => {
            result += processNode(child, true) // 递归处理子节点
        })
        if (!isLast) {
            result += '\r\n\r\n' // 段落结束，添加双换行
        }
    } else if (node.nodeName === 'IMG') {
        // 获取图片的 src
        const src = (node as HTMLElement).getAttribute('src') || ''
        const fileData = imageFileDataMap.get(src)
        if (fileData) {
            result += `!${fileData.fileName}!`
        } else {
            // 如果没有找到对应的 fileData，可以根据需要处理
            result += `![image](${src})`
        }
        if (!isLast) {
            result += '\r\n\r\n'
        }
    } else if (node.nodeType === Node.TEXT_NODE) {
        // 文本节点
        result += node.textContent || ''
        if (!isLast) {
            result += '\r\n\r\n'
        }
    } else {
        // 处理其他类型的节点
        node.childNodes.forEach(child => {
            result += processNode(child, true) // 递归处理子节点
        })
        if (!isLast) {
            result += '\r\n\r\n'
        }
    }
    return result
}

// 提交表单
const submitForm = async () => {
    if (!formRef.value) return
    const isValid = await validate(formRef, false)
    if (!isValid) {
        return
    }
    loading.value = true
    try {
        const htmlContent = editor.value?.getHTML() || ''
        const parser = new DOMParser()
        const doc = parser.parseFromString(htmlContent, 'text/html')
        let processedContent = ''

        const childNodes = Array.from(doc.body.childNodes)
        const totalNodes = childNodes.length

        childNodes.forEach((node, index) => {
            const isLast = index === totalNodes - 1
            processedContent += processNode(node, isLast)
        })

        form.description = processedContent

        // 获取当前选择的实际负责人
        const selectedDefectAssigneesNames = form.defectAssignee
        // 从 props.jiraUsers 中找到对应的用户信息
        const selectedDefectAssignees = props.jiraUsers.filter(user => selectedDefectAssigneesNames.includes(user.name))
        // 将实际负责人追加到缓存中，避免重复
        selectedDefectAssignees.forEach(user => {
            const exists = defectAssigneeStore.cachedDefectAssignees.some(cachedUser => cachedUser.name === user.name)
            if (!exists) {
                defectAssigneeStore.cachedDefectAssignees.push(user)
            }
        })
        const res = await createBug(props.jiraToken, form)
        if (res.data.isSuccess) {
            emit('save', {
                requirementKey: props.requirementKey,
                testCaseKey: props.testCaseKey,
                bugInfo: res.data.data
            })
            ElMessage.success('缺陷创建成功')
            emit('close')
            formRef.value?.resetFields()
        } else {
            ElMessage.error('缺陷创建失败: ' + res.data.message)
        }
    } finally {
        loading.value = false
    }

}

watch(
    () => form.defectAssignee,
    newVal => {
        if (newVal && newVal.length > 0) {
            [form.developerInCharge] = newVal
            // 更新研发负责人的选项列表，确保包含当前选中的值
            const selectedUser = props.jiraUsers.find(user => user.name === form.developerInCharge)
            filteredJiraUsersForDeveloperInCharge.value = selectedUser ? [selectedUser] : []
        } else {
            form.developerInCharge = ''
            filteredJiraUsersForDeveloperInCharge.value = []
        }
    }
)

// 处理对话框关闭
const handleClosed = () => {
    emit('close')
}
interface FileData {
    id: string
    fileName: string
}
// 附件文件列表
const fileList = ref([] as FileData[])

const handleUploadError = (error: any, file: any, fileList: any) => {
    console.error('附件上传失败', error)
}

// 获取编辑器容器的引用
const editorContainer = ref<HTMLElement | null>(null)
// 点击编辑器容器时聚焦编辑器
const focusEditor = () => {
    editor.value?.commands.focus()
}
// 可用的版本列表和加载状态
const availableVersions = ref<string[]>([])
const versionLoading = ref(false)
const getAllVersion = async () => {
    const res = await getAllVersionForProject(props.projectKey, props.jiraToken)
    availableVersions.value = res.data.data as string[]
}
getAllVersion()

const componentsForProject = ref<string[]>([])
const getComponents = async () => {
    const res = await getComponentsForProject(props.jiraToken, props.projectKey)
    if (res.data.isSuccess) {
        componentsForProject.value = res.data.data
    } else {
        ElMessage.error('获取组件列表失败 ' + res.data.message)
    }
}
getComponents()

const loading = ref(false)
const filteredJiraUsers = ref<JiraUser[]>(defectAssigneeStore.cachedDefectAssignees)

const filterJiraUsers = (query: string) => {
    if (query !== '') {
        loading.value = true
        const q = query.toLowerCase()
        filteredJiraUsers.value = props.jiraUsers.filter(user => {
            return (
                user.displayName.includes(query) || // 匹配中文名部分
                user.name.toLowerCase().includes(q) ||
                user.emailAddress.toLowerCase().includes(q) ||
                user.displayNamePinyin.toLowerCase().includes(q)
            )
        })
        loading.value = false
    } else {
        if (form.defectAssignee.length > 0) {
            filteredJiraUsers.value = []
        } else {
            // 当查询为空时，显示缓存的用户
            filteredJiraUsers.value = defectAssigneeStore.cachedDefectAssignees
        }
    }
}
const filteredJiraUsersForDeveloperInCharge = ref<JiraUser[]>([])

const filterJiraUsersForDeveloperInCharge = (query: string) => {
    if (query !== '') {
        loading.value = true
        const q = query.toLowerCase()
        filteredJiraUsersForDeveloperInCharge.value = props.jiraUsers.filter(user => {
            return (
                user.displayName.includes(query) ||
                user.name.toLowerCase().includes(q) ||
                user.emailAddress.toLowerCase().includes(q) ||
                user.displayNamePinyin.toLowerCase().includes(q)
            )
        })
        loading.value = false
    } else {
        // 当查询为空时，确保包含当前选中的研发负责人
        if (form.developerInCharge) {
            const selectedUser = props.jiraUsers.find(user => user.name === form.developerInCharge)
            filteredJiraUsersForDeveloperInCharge.value = selectedUser ? [selectedUser] : []
        } else {
            filteredJiraUsersForDeveloperInCharge.value = []
        }
    }
}
const generateUniqueFileName = (originalName: string): string => {
    const date = new Date()
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    const randomNum = Math.floor(Math.random() * 1000000) // 生成6位随机数

    const extension = originalName.substring(originalName.lastIndexOf('.'))
    return `${year}-${month}-${day}-${hours}-${minutes}-${seconds}-${randomNum}${extension}`
}

const handleDrop = (event: DragEvent) => {
    event.stopPropagation()
    event.preventDefault()
    const files = Array.from(event.dataTransfer?.files || [])
    files.forEach((file: File) => uploadFileToServer(file, false))
}

const handlePaste = (event: ClipboardEvent) => {
    const target = event.target as HTMLElement
    console.log('粘贴事件目标:', target)

    const items = event.clipboardData?.items || []
    console.log('粘贴的项:', items)

    for (let i = 0; i < items.length; i++) {
        const item = items[i]
        console.log(`正在处理第 ${i + 1} 个项, kind: ${item.kind}`)

        if (item.kind === 'file') {
            const file = item.getAsFile()
            console.log(`第 ${i + 1} 个项是文件, 文件对象:`, file)

            if (file) {
                // 检查粘贴目标是否在编辑器容器内
                const isInEditorContainer = editorContainer.value?.contains(target)
                console.log(`文件粘贴目标是否在编辑器容器内: ${isInEditorContainer}`)

                if (isInEditorContainer) {
                    // 在编辑器容器内
                    console.log(`在编辑器容器内，文件类型: ${file.type}`)
                    if (file.type.startsWith('image/')) {
                        // 上传图片
                        console.log('文件是图片，开始上传图片')
                        uploadFileToServer(file, true)
                    } else {
                        // 如果不是图片，直接返回
                        console.log('文件不是图片，阻止默认粘贴行为')
                        event.preventDefault() // 阻止默认粘贴行为
                        return // 直接返回，不处理
                    }
                } else {
                    // 在编辑器容器外，支持所有文件类型
                    console.log('不在编辑器容器内，开始上传文件')
                    event.preventDefault() // 阻止默认粘贴
                    uploadFileToServer(file, false) // 调用上传函数
                }
            } else {
                console.log(`第 ${i + 1} 个项无法获取为文件，item.getAsFile() 返回: ${file}`)
            }
        } else {
            console.log(`第 ${i + 1} 个项不是文件，kind: ${item.kind}`)
        }
    }
}
const uploadFileToServer = async (file: File, isPreviewImage: boolean) => {
    console.log('开始上传文件，文件:', file)
    const fileName = file.type.startsWith('image/') ? generateUniqueFileName(file.name) : file.name
    console.log(`文件类型: ${file.type}, 使用的文件名: ${fileName}`)

    const formData = new FormData()
    formData.append('file', new File([file], fileName))
    console.log('构建的 FormData:', formData)

    try {
        const response = await uploadFile(formData)

        if (response.data.isSuccess) {
            const fileData = response.data.data

            const uploadedFile = {
                id: fileData.id,
                name: fileData.fileName,
                fileName: fileData.fileName
            }

            // 添加文件 ID 到表单附件文件 ID 数组
            form.attachmentFileIds.push(uploadedFile.id)

            // 更新文件列表
            fileList.value = [...fileList.value, uploadedFile]

            if (isPreviewImage && editor.value) {
                const imageUrl = previewImage(uploadedFile.id)
                // 插入图片
                editor.value.chain().focus().setImage({ src: imageUrl, id: uploadedFile.id } as any).run()

                // 存储图片的映射
                imageFileDataMap.set(imageUrl, { id: fileData.id, fileName: fileData.fileName })
            } else {
                console.log('该文件不是图片或没有编辑器对象，不进行预览')
            }

        } else {
            console.error('文件上传失败，响应的错误信息:', response.data)
        }

    } catch (error) {
        console.error('文件上传异常:', error)
    }
}

// 附件列表 点击删除
const handleRemoveFile = (file: FileData) => {
    // 从文件列表中删除
    fileList.value = fileList.value.filter(item => item.id !== file.id)
    // 从 attachmentFileIds 中删除对应的 ID
    form.attachmentFileIds = form.attachmentFileIds.filter(id => id !== file.id)
    // 检查 imageFileDataMap，删除与该文件相关的图片
    imageFileDataMap.forEach((fileData, imageUrl) => {
        if (fileData.id === file.id) {
            // 删除对应的图片
            handleImageDelete(imageUrl)
            // 从编辑器中删除该图片
            if (editor.value) {
                deleteImageNodeById(editor.value, file.id)
            }
        }
    })
}

/**
 * 根据图片的唯一 ID 从编辑器中删除图片节点
 * @param editor - Tiptap 编辑器实例
 * @param id - 图片的唯一 ID
 */
const deleteImageNodeById = (editor: Editor, id: string): void => {
    const { state, commands } = editor
    let found = false

    state.doc.descendants((node, pos) => {
        if (node.type.name === 'image' && node.attrs.id === id) {
            commands.deleteRange({ from: pos, to: pos + node.nodeSize })
            found = true
            return false // 停止遍历
        }
    })
}
const customRequest = async (options: any) => {
    const { file } = options
    uploadFileToServer(file, false)
}
// 处理取消
const handleCancel = () => {
    // 重置表单
    formRef.value?.resetFields()
    emit('close')
}
</script>

<style scoped>
.defect-form {
    min-height: 60vh;
    max-height: 60vh;
    overflow-y: auto;
}

::v-deep(.el-dialog__footer) {
    padding: 0;
}

/* 自定义编辑器样式 */
.editor {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px;
    width: 100%;
    height: 250px;
    min-height: 200px; /* 设置最小高度 */
    background-color: #fff;
    overflow-y: auto; /* 添加垂直滚动条 */
}
.editor-content {
    height: 100%; /* 确保内容区域有足够高度 */
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; /* 与 el-input 相同的字体 */
    font-size: 14px; /* 与 el-input 相同的字体大小 */
    line-height: 1.5; /* 与 el-input 相同的行高 */
}
/* 取消 ProseMirror 在焦点时的黑框 */
::v-deep(.editor-content.ProseMirror-focused) {
    box-shadow: none !important; /* 取消阴影 */
    border: none !important;     /* 取消边框 */
    outline: none !important;    /* 取消轮廓 */
}

/* 如果仍有其他样式影响，可以进一步覆盖 */
::v-deep(.ProseMirror-focused) {
    box-shadow: none !important;
    border: none !important;
    outline: none !important;
}
::v-deep(.editor-content img ){
    max-width: 85%;
    height: auto;
    border: 1px solid #dcdfe6;
}

::v-deep(.selected-image) {
    border: 2px solid #409EFF; /* 蓝色边框 */
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2); /* 阴影效果 */
    border-radius: 4px; /* 圆角（可选） */
}

/* 可选：调整图片在选中时的样式 */
::v-deep(.selected-image img) {
    display: block;
}
::v-deep(.attachments-list) {
    width: 100%;
}

</style>
