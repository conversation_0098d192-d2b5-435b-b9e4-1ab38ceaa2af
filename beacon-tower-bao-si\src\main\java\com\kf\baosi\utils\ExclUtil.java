package com.kf.baosi.utils;

import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class ExclUtil {

    private static final Logger logger = LoggerFactory.getLogger(ExclUtil.class);


    /**
     * 响应给httpResponse
     *
     * @param response
     * @param fileName
     * @param headList
     * @param rows
     */
    public static void write2Response(HttpServletResponse response, String fileName, List<String> headList, List<ArrayList<String>> rows) {
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        response.setContentType("application/octet-stream;charset=UTF-8");
        try (OutputStream outputStream = response.getOutputStream(); Workbook workbook = write(headList, rows)) {
            workbook.write(outputStream);
        } catch (Exception e) {
            logger.error("生成excel失败", e);
        }
    }

    /**
     * 读取excel
     *
     * @param inputStream
     * @return
     */
    public static ExcelReaderBuilder read(InputStream inputStream) {
        return new ExcelReaderBuilder(inputStream);
    }

    public static ExcelReaderBuilder read(File file) {
        return new ExcelReaderBuilder(file);
    }

    public static CsvReaderBuilder readCSV(File file) {
        return new CsvReaderBuilder(file);
    }

    private static Workbook write(List<String> headList, List<ArrayList<String>> rows) {
        ExcelWriterBuilder excelWriterBuilder = new ExcelWriterBuilder();
        return excelWriterBuilder.sheetName("数据").head(new ArrayList<>(headList)).data(new ArrayList<>(rows)).build();
    }

    private static class ExcelWriterBuilder {

        private final SXSSFWorkbook workbook;

        private final SXSSFSheet sheet;

        private final CellStyle headStyle;

        public ExcelWriterBuilder() {
            workbook = new SXSSFWorkbook();
            sheet = workbook.createSheet();
            headStyle = workbook.createCellStyle();
            Font font = workbook.createFont();
            font.setBold(true);
            headStyle.setFont(font);
        }

        /**
         * sheet页的名称
         *
         * @param sheetName
         * @return
         */
        public ExcelWriterBuilder sheetName(String sheetName) {
            workbook.setSheetName(0, sheetName);
            return this;
        }

        /**
         * 设置excel头，强制使用ArrayList，在使用get方法时，时间复杂度为O（1）
         *
         * @param headList
         * @return
         */
        public ExcelWriterBuilder head(ArrayList<String> headList) {
            SXSSFRow headRow = sheet.createRow(0);
            for (int i = 0, length = headList.size(); i < length; i++) {
                sheet.setColumnWidth(i, 6000);
                SXSSFCell cell = headRow.createCell(i);
                cell.setCellType(CellType.STRING);
                cell.setCellStyle(headStyle);
                cell.setCellValue(headList.get(i));
            }
            return this;
        }

        public ExcelWriterBuilder data(ArrayList<ArrayList<String>> dataList) {
            for (int i = 0, sizei = dataList.size(); i < sizei; i++) {
                SXSSFRow sheetRow = sheet.createRow(1 + i);
                ArrayList<String> rowList = dataList.get(i);
                for (int j = 0, sizej = rowList.size(); j < sizej; j++) {
                    SXSSFCell sheetRowCell = sheetRow.createCell(j);
                    sheetRowCell.setCellType(CellType.STRING);
                    sheetRowCell.setCellValue(rowList.get(j));
                }
            }
            return this;
        }

        public Workbook build() {
            return workbook;
        }

    }

    public static class ExcelReaderBuilder {

//        private XSSFWorkbook workbook;
//        private HSSFWorkbook workbook1;
        private Workbook workbook;

        private Sheet sheet;

        public ExcelReaderBuilder(InputStream inputStream) {
            try {

                // TODO 暂未支持 HSSFWorkbook 数据,即xls后缀Excl
                // workbook = WorkbookFactory.create(inputStream);
                workbook = new XSSFWorkbook(inputStream);
                sheet = workbook.getSheetAt(0);
            } catch (Exception e) {

                logger.error(e.getMessage(), e);

            }
        }

        public ExcelReaderBuilder(File file) {
            String path = file.getPath();
            String ext =path.substring(path.lastIndexOf("."));

            FileInputStream inputStream;
            try {
                inputStream = new FileInputStream(file);
                if (".xls".equals(ext)) {
                    workbook = new HSSFWorkbook(inputStream);
                } else if (".xlsx".equals(ext)) {
                    workbook = new XSSFWorkbook(inputStream);
                }
                sheet = workbook.getSheetAt(0);

            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }

        }

        public List<String> head() {
            Row row = sheet.getRow(0);
            return getRowData(row);
        }

        private List<String> getRowData(Row row) {
            if (row == null) {
                return Collections.emptyList();
            }
            Cell cell;
            List<String> dataList = new ArrayList<>();
            int cellNum = row.getLastCellNum();
            for (int i = 0; i < cellNum; i++) {
                cell = row.getCell(i);
                if (cell != null && CellType.STRING != cell.getCellType()) {

                    cell.setCellType(CellType.STRING);
                }
                dataList.add(cell == null ? null : cell.getStringCellValue());
            }
            return dataList;
        }

        public List<List<String>> data() {
            Row row;
            List<List<String>> list = new ArrayList<>();
            int rowNum = sheet.getPhysicalNumberOfRows();
            for (int i = 1; i < rowNum; i++) {
                row = sheet.getRow(i);
                List<String> rowData = getRowData(row);
                list.add(rowData);
            }
            return list;
        }

        public List<Map<String, String>> dataMap() {
            List<String> head = head();
            List<List<String>> data = data();
            List<Map<String, String>> dataMap = new LinkedList<>();
            for (List<String> row : data) {
                Map<String, String> rowMap = new HashMap<>(row.size());
                for (int i = 0, rowSize = row.size(); i < rowSize; i++) {
                    rowMap.put(head.get(i), row.get(i));
                }
                dataMap.add(rowMap);
            }
            return dataMap;
        }

    }

    public static class CsvReaderBuilder {

        private final CsvData csvData;


        public CsvReaderBuilder(File file) {
            csvData = CsvUtil.getReader().read(file);
        }

        public List<CsvRow> data() {
            return csvData.getRows();

        }

        public List<Map<String, String>> dataMap() {
            // 默认第一行为表头
            List<String> rawList = csvData.getRow(0).getRawList();

            return IntStream.range(1, csvData.getRowCount())
                    .mapToObj(i -> {
                        CsvRow row = csvData.getRow(i);
                        Map<String, String> rowData = new LinkedHashMap<>();
                        IntStream.range(0, row.size())
                                .forEach(j -> rowData.put(rawList.get(j), row.get(j)));
                        return rowData;
                    })
                    .collect(Collectors.toList());

        }
    }

    public static void main(String[] args) {

//        CsvUtil.getReader(new FileReader("C:\\Users\\<USER>\\Desktop\\上传文件\\4.81测试用例 - 副本.xlsx").read();

//        CsvReaderBuilder readCSV = ExclUtil.readCSV(new File("C:\\Users\\<USER>\\Desktop\\上传文件\\4.81测试用例.csv"
//        ));
//        readCSV.dataMap();

//        ExclUtil.read(new File("C:\\Users\\<USER>\\Desktop\\上传文件\\DocTypeImport_Template0719xlsx -平台.xlsx")).dataMap();
        CsvReaderBuilder read = ExclUtil.readCSV(new File("C:\\Users\\<USER>\\Desktop\\上传文件\\4.81测试用例.csv"));
        read.data().get(0).getFieldMap();
//        File file = new File("C:\\Users\\<USER>\\Desktop\\上传文件\\4.81测试用例 - 副本.xlsx");
//        InputStream inputStream;
//
//        try {
//            inputStream = new FileInputStream(file);
//
//            ExcelReaderBuilder read = ExclUtil.read(inputStream);
//            ExclUtil.readCSV()
//            read.dataMap();
//
//            inputStream.close();
//
//
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }


    }
}



