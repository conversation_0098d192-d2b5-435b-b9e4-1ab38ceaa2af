<template>
    <div class='table-search flex flex-col'>
        <div ref='searchEl' class='table-search-form'>
            <el-row :gutter='15' class='clear-both'>
                <el-col :span='24'>
                    <card-list :show-header='true' title='搜索' type='keyvalue'>
                        <template #btn>
                            <el-button-group>
                                <el-button icon='el-icon-search' size='small' @click='submit'>搜索</el-button>
                            </el-button-group>
                        </template>
                        <template #keyvalue>
                            <el-form ref='refForm' :model='form' class='card-list-form' size='small'>
                                <el-row :gutter='15'>
                                    <card-list-item prop='objInfo' width='100px'>
                                        <template #key>项目</template>
                                        <template #value>
                                            <el-input v-model='form.projectName'
                                                      placeholder='请输入项目'/>
                                        </template>
                                    </card-list-item>
                                    <card-list-item prop='pageUrl' width='100px'>
                                        <template #key>页面入口</template>
                                        <template #value>
                                            <el-input v-model='form.pageUrl'
                                                      placeholder='请输入页面入口'/>
                                        </template>
                                    </card-list-item>
                                    <card-list-item prop='interfaceName' width='100px'>
                                        <template #key>接口名称</template>
                                        <template #value>
                                            <el-input v-model='form.interfaceName'
                                                      placeholder='请输入接口名称'/>
                                        </template>
                                    </card-list-item>
                                </el-row>
                            </el-form>
                        </template>
                    </card-list>
                </el-col>
            </el-row>
        </div>

        <div class='flex justify-between items-center mb-2 '>
            <div>
                <el-button-group>
                    <el-button v-prevent-default type='primary' @click='batchImport'>批量导入</el-button>
                    <el-button v-prevent-default type='primary' @click='newAdd'>新增</el-button>
                </el-button-group>
            </div>
            <el-button type='text' @click='toggleSearch'>
                搜索
                <el-icon>
                    <el-icon-arrow-up v-if='isShow'/>
                    <el-icon-arrow-down v-else/>
                </el-icon>
            </el-button>
        </div>
        <el-table :data='tableData.data' stripe style="width:100%">
            <el-table-column label='页面入口' prop='pageUrl' width="'calc(30% - 1350px)'"/>
            <el-table-column label='接口名称' prop='interfaceName' width="'calc(20% - 200px)'"/>
            <el-table-column label='所属项目' prop='projectName' width='250'/>
            <el-table-column label='创建时间' prop='createTime' width='250'/>
            <el-table-column label='更新时间' prop='updateTime' width='250'/>
            <el-table-column label='启用状态' prop='status' width='150'>
                <template #default="{ row }">
                    <el-switch v-model='row.status' :before-change="() => handleBeforeChange(row)" :loading="row.loading"
                               active-color='#1890FF'
                               inactive-color='#ff4949'/>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label='操作' prop='operation' width=" 150">
                <template #default="{ row }">
                    <div class="button-container">
                        <el-button v-prevent-default plain size="small" type="primary"
                                   @click="() => editInterface(row)">编辑
                        </el-button>
                        <el-popconfirm cancel-button-text="取消" confirm-button-text="确定" title="确定删除吗？"
                                       width="220"
                                       @confirm="() => deleteInterface(row.id)">
                            <template #reference>
                                <el-button v-prevent-default plain size="small" type="danger">删除</el-button>
                            </template>
                        </el-popconfirm>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-dialog v-model="addDialogVisible" :close-on-click-modal="false" :close-on-press-escape="false" class="dialogClass"
                   title="新增" width="28%">
            <template #>
                <el-form ref="addRef" :model="addForm" :rules="addRule" label-width="80px">
                    <el-form-item label="页面入口" prop="pageUrl">
                        <el-input v-model="addForm.pageUrl"></el-input>
                    </el-form-item>
                    <el-form-item label="接口名称" prop="interfaceName">
                        <el-input v-model="addForm.interfaceName"></el-input>
                    </el-form-item>
                    <el-form-item label="所属项目">
                        <el-input v-model="addForm.projectName"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer style="text-align:right;">
                <el-button @click="cancelAddForm">取消</el-button>
                <el-button type="primary" @click="addEditForm">保存</el-button>
            </template>
        </el-dialog>
        <el-dialog v-model="editDialogVisible" :close-on-click-modal="false" :close-on-press-escape="false" class="dialogClass"
                   title="编辑" width="28%">
            <template #>
                <el-form ref="editRef" :model="editForm" :rules="editRule" label-width="80px">
                    <el-form-item label="页面入口" prop="pageUrl">
                        <el-input v-model="editForm.pageUrl"></el-input>
                    </el-form-item>
                    <el-form-item label="接口名称" prop="interfaceName">
                        <el-input v-model="editForm.interfaceName"></el-input>
                    </el-form-item>
                    <el-form-item label="所属项目">
                        <el-input v-model="editForm.projectName"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer style="text-align:right;">
                <el-button @click="cancelEditForm">取消</el-button>
                <el-button type="primary" @click="saveEditForm">保存</el-button>
            </template>
        </el-dialog>
        <el-dialog v-model="batchImportDialogVisible" :close-on-click-modal="false" :close-on-press-escape="false"
                   class="dialogClass" title="批量导入" width="45%" @close="handleClose">
            <template v-if="active === 0" #>
                <el-steps :active="active" finish-status="success" simple style="margin-bottom: 10px;margin-top: 0px;">
                    <el-step title="上传"/>
                    <el-step title="预览"/>
                    <el-step title="导入"/>
                </el-steps>
                <div class="upload-container" @dragenter.prevent @dragover.prevent @drop.prevent="handleUpload">
                    <i class="el-icon-upload"></i>
                    <span>将文件拖到此处或点击上传</span>
                    <input ref="fileInput" accept=".xls,.xlsx" class="color" style="visibility: hidden" type="file"
                           @change="uploadFile" @dragenter.prevent @dragover.prevent @drop.prevent/>
                    <el-button class="color" size="small" type="primary" @click="chooseFile">点击上传</el-button>
                    <p>{{ fileName }}</p>
                </div>
                <div>
                    <button class="downloadButton" @click="downloadTemplateFile">下载模板</button>
                    <p>支持文件格式：.xls .xlsx</p>
                    <p>增量导入数据</p>
                </div>
            </template>
            <template v-if="active === 1">
                <el-steps :active="active" finish-status="success" simple style="margin-bottom: 10px;margin-top: 0px;">
                    <el-step title="上传"/>
                    <el-step title="预览"/>
                    <el-step title="导入"/>
                </el-steps>
                <el-table :data='batchImportTableData' height="350" stripe style="width:100%">
                    <el-table-column label='页面入口' prop='pageUrl'/>
                    <el-table-column label='接口名称' prop='interfaceName'/>
                    <el-table-column label='所属项目' prop='projectName'/>
                    <el-table-column label='错误信息' prop='msg'/>
                </el-table>
            </template>
            <template #footer style="text-align:right;">
                <el-button v-if="active === 0" @click="cancelBatchImportForm">取消</el-button>
                <el-button v-if="active === 0" :disabled="isNextDisabled" type="primary"
                           @click="() => active = 1">下一步
                </el-button>
                <el-button v-if="active === 1" @click="() => active = 0">上一步</el-button>
                <el-button v-if="active === 1" :disabled="isSaveDisabled" type="primary"
                           @click="saveBatchImportData">保存
                </el-button>
            </template>
        </el-dialog>
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
                       :small="small" :total="total" layout="total, sizes, prev, pager, next, jumper"
                       @size-change="handleSizeChange"
                       @current-change="handleCurrentChange"/>
    </div>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import CardList from '@/components/CardList/CardList.vue'
import CardListItem from '@/components/CardList/CardListItem.vue'
import {
    addInterfacePage,
    checkFileData,
    deleteInterfacePage,
    editInterfacePage,
    getInterfacePageList,
    getInterfacePageTemplateFile,
    incrementalAdd,
    type InterfacePage
} from '@/api/layout'
import { slide } from '@/utils/animate'
import { useLayoutStore } from '@/stores/modules/layout'
import { validateField } from '@/utils/formExtend'

const { color } = useLayoutStore().getSetting
const isShow = ref(false)
let currentPage = ref(1)
let pageSize = ref(10)
const total = ref(0)
const small = ref(false)
const refForm = ref(null)
const searchEl = ref(null)
const addRef = ref()
const editRef = ref()
const active = ref(0)
const fileName = ref('')
const batchImportDialogVisible = ref(false)
const isNextDisabled = ref(true)
const isSaveDisabled = ref(false)
// 上传 导出 下载模板
const toggleSearch = () => {
    isShow.value = !isShow.value
    slide(searchEl, isShow.value)
}
const handleSizeChange = (val: number) => {
    pageSize.value = val
    submit()
}
const handleCurrentChange = (val: number) => {
    currentPage.value = val
    submit()
}

interface ISearchForm {
    projectName: string
    interfaceName: string
    pageUrl: string
    createTime: string
    updateTime: string
}

// 搜索表单
const form: ISearchForm = reactive({
    projectName: '',
    interfaceName: '',
    pageUrl: '',
    createTime: '',
    updateTime: ''

})
const editRule = reactive({
    pageUrl: [{ required: true, message: '请输入页面入口' }],
    interfaceName: [{ required: true, message: '请输入接口名称' }]
})
const addRule = reactive({
    pageUrl: [{ required: true, message: '请输入页面入口' }],
    interfaceName: [{ required: true, message: '请输入接口名称' }]
})

interface data {
    id: number
    projectName: string
    interfaceName: string
    pageUrl: string
    createTime: string
    updateTime: string
    status: boolean
    loading: boolean
}

// 表格数据
let tableData: ITable<data> = reactive({
    data: [],
    total: 0,
    page: 1,
    size: 10
})

interface batchImportData {
    pageUrl: string
    interfaceName: string
    projectName: string
    msg: string
}

const batchImportTableData: Array<batchImportData> = reactive([])
const submit = async () => {
    const { projectName, interfaceName, pageUrl } = form
    const InterfacePage: InterfacePage = {
        projectName: projectName,
        interfaceName: interfaceName,
        pageUrl: pageUrl,
        current: currentPage.value,
        size: pageSize.value
    }
    const result = await getInterfacePageList(InterfacePage)
    if (!result.data.isSuccess) {
        ElMessage.error(result.data.message)
        return
    }
    total.value = result.data.data.total
    tableData.data = []
    result.data.data.pageList.forEach((item: any) => {
        tableData.data.push({
            id: item.id,
            projectName: item.projectName,
            interfaceName: item.interfaceName,
            pageUrl: item.pageUrl,
            createTime: item.createTime,
            updateTime: item.updateTime,
            status: item.status == '0',
            loading: false
        })
    })
}
submit()
const editDialogVisible = ref(false)
const addDialogVisible = ref(false)
const editForm = reactive({
    id: 0,
    projectName: '',
    interfaceName: '',
    pageUrl: '',
    status: false
})

const editInterface = (data: data) => {
    editForm.id = data.id
    editForm.interfaceName = data.interfaceName
    editForm.pageUrl = data.pageUrl
    editForm.projectName = data.projectName
    editForm.status = data.status
    editDialogVisible.value = true
}
const saveEditForm = async () => {
    if (!await validateField(editRef, ['pageUrl', 'interfaceName'])) return
    const InterfacePageParam = {
        id: editForm.id,
        projectName: editForm.projectName,
        interfaceName: editForm.interfaceName,
        pageUrl: editForm.pageUrl
    }
    const res = await editInterfacePage(InterfacePageParam)
    if (res.data.isSuccess) {
        ElMessage.success('编辑成功')
        editDialogVisible.value = false
        submit()
    } else {
        ElMessage.error(res.data.message)
    }
}
const addForm = reactive({
    projectName: '',
    interfaceName: '',
    pageUrl: ''
})
const addEditForm = async () => {
    // 校验必填项
    if (!await validateField(addRef, ['pageUrl', 'interfaceName'])) return
    const InterfacePageParam = {
        projectName: addForm.projectName,
        interfaceName: addForm.interfaceName,
        pageUrl: addForm.pageUrl
    }
    const res = await addInterfacePage(InterfacePageParam)
    if (res.data.isSuccess) {
        ElMessage.success('保存成功')
        addDialogVisible.value = false
        submit()
    } else {
        ElMessage.error(res.data.message)
    }
}
const cancelEditForm = () => {
    editDialogVisible.value = false
}
const cancelAddForm = () => {
    addDialogVisible.value = false
}
// 删除
const deleteInterface = async (id: number) => {
    const res = await deleteInterfacePage(id)
    if (res.data.isSuccess) {
        ElMessage.success('删除成功')
        submit()
    } else {
        ElMessage.error(res.data.message)
    }
}
const loading = ref(false)
const handleBeforeChange = async (row: data) => {
    row.loading = true
    try {
        const InterfacePageParam = {
            id: row.id,
            projectName: row.projectName,
            interfaceName: row.interfaceName,
            pageUrl: row.pageUrl,
            status: row.status ? 1 : 0
        }
        const res = await editInterfacePage(InterfacePageParam)
        if (!res.data.isSuccess) {
            ElMessage.error(res.data.message)
            return false
        } else {
            ElMessage.success('修改成功')
            // 将res的值赋值给row
            row.updateTime = res.data.data.updateTime
            return true
        }
    } catch (error) {
        console.log(error)
    } finally {
        row.loading = false
    }
}
const newAdd = () => {
    addDialogVisible.value = true
    // 重置表单
    nextTick(() => {
        addRef.value.resetFields()
    })
}
const batchImport = () => {
    fileName.value = ''
    active.value = 0
    batchImportTableData.splice(0, batchImportTableData.length)
    batchImportDialogVisible.value = true
}

const chooseFile = () => {
    const fileInput = document.querySelector('input[type=file]') as HTMLInputElement
    fileInput.click()
}

const handleUpload = (event: DragEvent) => {
    const file = event.dataTransfer?.files[0] as File
    upload(file)
}

const uploadFile = (event: Event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (file) {
        upload(file)
    }
}

const upload = async (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    const res = await checkFileData(formData)
    if (res.data.isSuccess) {
        ElMessage.success('上传成功')
        isNextDisabled.value = false
        batchImportTableData.splice(0, batchImportTableData.length)
        batchImportTableData.push(...res.data.data)
        fileName.value = file.name
        // batchImportTableData的数据的msg只要有一个不为空，就不能点击下一步
        for (let i = 0; i < batchImportTableData.length; i++) {
            if (batchImportTableData[i].msg !== '') {
                isSaveDisabled.value = true
                break
            }
        }
    } else {
        ElMessage.error(res.data.message)
    }
    // 清空input的value值
    const fileInput = document.querySelector('input[type=file]') as HTMLInputElement
    fileInput.value = ''

}
const handleClose = () => {
    isNextDisabled.value = true
    isSaveDisabled.value = false
}
const cancelBatchImportForm = () => {
    batchImportDialogVisible.value = false
}
const saveBatchImportData = async () => {
    const res = await incrementalAdd(batchImportTableData)
    if (res.data.isSuccess) {
        ElMessage.success('导入成功')
        active.value = 2
        batchImportDialogVisible.value = false
        submit()
    } else {
        ElMessage.error(res.data.message)

    }
}
const downloadTemplateFile = async () => {
    try {
        const res: any = await getInterfacePageTemplateFile()
        const contentDisposition = res.headers['content-disposition']
        const matches = contentDisposition.match('filename=([^;\n\r]+)')
        let fileName = matches ? matches[1] : 'unknown'
        fileName = decodeURIComponent(fileName)
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
    } catch (err) {
        // if (err instanceof Error) {
        //     console.log('出错啦', err.message)
        // }
    }
}
</script>
<style lang="postcss" scoped>
.table-search-form {
    overflow: hidden;
    height: 0;
}

::v-deep(.el-card__header) {
    padding: 7px 15px;
}

::v-deep(.el-button) {
    padding: 4px 6px;
    border-radius: 3px;
}

.card-list-header {
    height: 28px;
    line-height: 28px;
}

.card-list-body {
    list-style: square;

    & > li {
        list-style: square;
        display: flex;
        padding: 3px 0;
        align-items: center;
        justify-content: space-between;

        & > .card-list-mark {
            color: #888;
        }

        & > .card-list-text {
            padding-right: 10px;
            color: #666;

            & > a:hover {
                color: v-bind(color.primary);
            }

            & > span.card-list-item-circle {
                display: inline-block;
                width: 5px;
                height: 5px;
                border-radius: 50%;
                background-color: #666;
                margin-right: 10px;
            }

            &.nowrap {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;

                & > span.card-list-item-circle {
                    transform: translateY(-50%);
                }
            }

            &.wrap {
                display: flex;
                align-items: center;

                & > span.card-list-item-circle {
                    min-width: 5px;
                    min-height: 5px;
                    max-width: 5px;
                    max-height: 5px;
                }
            }
        }
    }
}

::v-deep(.el-dialog__body) {
    padding-bottom: 5px;
    padding-top: 15px;
}

.upload-container {
    margin-bottom: 10px;
    border: 0.5px dashed #c2c2c2;
    border-radius: 5px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: 16px;
    font-weight: normal;
    height: 160px;
    transition: all 0.3s ease-in-out;
}

.upload-container:hover {
    cursor: pointer;
    border-style: dashed;
    border-color: v-bind(color.primary);
    background-color: #f5f7fa;
    color: v-bind(color.primary);
}

.color {
    border-color: v-bind(color.primary);
    background-color: v-bind(color.primary);
}

p {
    font-size: 15px;
    font-family: 'Roboto', sans-serif;
}

.downloadButton {
    color: #1890FF;
    /* 将字体颜色设置为蓝色 */
    margin-bottom: 3px;
    /* 设置底部边距为10像素 */
    text-decoration: none;
    /* 移除默认下划线 */
}

.downloadButton:hover {
    text-decoration: underline;
    /* 在鼠标悬停时添加下划线 */
}

.button-container {
    display: flex;
    flex-wrap: wrap; /* 允许按钮换行 */
}

.button-container .el-button {
    margin: 2px 3px;
}
</style>
