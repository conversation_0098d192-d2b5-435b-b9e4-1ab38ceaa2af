<template>
    <ul class="list-inline">
        <li>
            <span @click='show = true' class="link-black">如何添加钉钉机器人？务必点我看下~</span>
            <open-window v-model:show='show' :is-show='show' title='配置钉钉群机器人'>
                <p style="font-weight: bold;">
                    注意:Git地址不是必填的，如果不填，每一次分析结果都会推送到后面的Webhook地址
                </p>
                <p style="font-weight: bold;">
                    如果填写了Git地址，只会将该Git地址的分析结果推送到后面的Webhook地址
                </p>
                <p>1.点击钉钉群右上角的群设置，找到“智能群助手”进入。</p>
                <img :src='ding1' alt='ding1'><br>
                <p>2.点击“添加机器人”，在群机器人列表中继续点击“添加机器人”。</p>
                <img :src='ding2' alt='ding2'><br>
                <p>3.点击自定义添加，进入自定义机器人设置页面，安全设置只勾选自定义关键词，内容输入“受影响的接口”。</p>
                <img :src='ding3' alt='ding3'><br>
                <p>4.机器人的名字根据自身喜好或不填，设置完成后，将Webhook地址填到输入框，保存即可。</p>
                <template #btn>
                    <el-button @click='show = false'>我知道了</el-button>
                </template>
            </open-window>
        </li>
    </ul>
</template>

<script setup lang='ts'>
import { ref } from 'vue'
import OpenWindow from '@/components/OpenWindow/index.vue'
import ding1 from '@/assets/img/ding/ding1.png'
import ding2 from '@/assets/img/ding/ding2.png'
import ding3 from '@/assets/img/ding/ding3.png'

const show = ref(false)
</script>

<style scoped>
img {
    display: block;
    margin: 0 auto;
    height: 400px;
    width: 500px;
    border: 1px solid #ddd
}

p {
    padding: 12px;
    font-size: 15px;
    font-family: 'Roboto', sans-serif;
}

li {
    display: inline-block;
    padding-right: 5px;
    padding-left: 5px;
    font-size: 13px;
}

.list-inline {
    padding-top: 10px;
    padding-left: 0;
    margin-left: -5px;
    list-style: none;
}

.link-black {

    &:hover,
    &:focus {
        color: #999;
        cursor: pointer;
    }
}
</style>
