<template>
    <el-collapse v-model="activeName" accordion>
        <el-collapse-item title="重置密码" name="reset-password">
            <Post description="">
                <SystemUserResetPassword/>
            </Post>
        </el-collapse-item>
        <el-collapse-item title="登录JIRA" name="jira-login">
            <Post>
                <JiraUserLogin/>
            </Post>
        </el-collapse-item>
    </el-collapse>
</template>

<script setup lang='ts'>
import SystemUserResetPassword from '@/components/UserConfig/SystemUserResetPassword.vue'
import JiraUserLogin from '@/components/UserConfig/JiraUserLogin.vue'
import Post from '@/components/UserConfig/Post.vue'
import { onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const activeName = ref('')
onMounted(() => {
    if (route.query.from === 'jiraLogin') {
        activeName.value = 'jira-login'
    }
})
</script>

<style scoped>
::v-deep(.el-collapse-item__content) {
    padding-bottom: 10px;
}
</style>
