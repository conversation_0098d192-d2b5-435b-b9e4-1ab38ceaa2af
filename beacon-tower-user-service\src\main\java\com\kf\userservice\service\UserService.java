package com.kf.userservice.service;

import com.kf.userservice.entity.TUser;

import java.util.List;

public interface UserService {

    /**
     * 通过ID查询单条数据
     *
     * @param userId 主键
     * @return 实例对象
     */
    TUser queryById(String userId);

    /**
     * 通过name查询单条数据
     *
     * @param username 主键
     * @return 实例对象
     */
    TUser queryByUserName(String username);

    /**
     * 通过email查询单条数据
     *
     * @param email 邮箱
     * @return 实例对象
     */
    TUser queryByEmail(String email);

    List<TUser> queryByUser(TUser user);

    /**
     * 新增数据
     *
     * @param tUser 实例对象
     * @return 实例对象
     */
    TUser insert(TUser tUser);

    /**
     * 创建数据
     *
     * @param tUser 实例对象
     * @return 实例对象
     */
    TUser insertUser(TUser tUser);

    /**
     * 修改数据
     *
     * @param tUser 实例对象
     */
    int update(TUser tUser);

    /**
     * 通过主键删除数据
     *
     * @param userId 主键
     * @return 是否成功
     */
    boolean deleteById(Long userId);

    String getRoleById(Long userId);

    boolean deleteByIds(String ids);

    boolean updatePwd(String id, String oldPwd, String newPwd);
}