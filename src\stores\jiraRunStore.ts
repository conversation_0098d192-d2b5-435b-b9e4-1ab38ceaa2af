import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

export const useJiraRunStore = defineStore('jiraRun', () => {
    const projectKey = ref('')
    const projectName = ref('')
    const testPlanKey = ref('')
    const testPlanName = ref('')
    const testCycleId = ref('')
    const testCycleName = ref('')

    // 从 localStorage 初始化状态
    const savedState = localStorage.getItem('jiraRunStore')
    if (savedState) {
        const parsedState = JSON.parse(savedState)
        projectKey.value = parsedState.projectKey || ''
        projectName.value = parsedState.projectName || ''
        testPlanKey.value = parsedState.testPlanKey || ''
        testPlanName.value = parsedState.testPlanName || ''
        testCycleId.value = parsedState.testCycleId || ''
        testCycleName.value = parsedState.testCycleName || ''
    }

    // 监听状态变化并保存到 localStorage
    watch(
        () => ({ projectKey: projectKey.value, projectName: projectName.value, testPlanKey: testPlanKey.value, testPlanName: testPlanName.value, testCycleId: testCycleId.value, testCycleName: testCycleName.value }),
        (state) => {
            localStorage.setItem('jiraRunStore', JSON.stringify(state))
        },
        { deep: true }
    )

    return { projectKey, projectName, testPlanKey, testPlanName, testCycleId, testCycleName }
})
