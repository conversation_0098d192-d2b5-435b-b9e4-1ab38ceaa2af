package com.kf.aitest.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.kf.aitest.service.DataChunkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据分片处理服务实现类
 */
@Slf4j
@Service
public class DataChunkServiceImpl implements DataChunkService {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 默认最大token数（估算）
     */
    private static final int DEFAULT_MAX_TOKENS = 8000;
    
    /**
     * 每个字符大约对应的token数（粗略估算）
     */
    private static final double CHARS_PER_TOKEN = 4.0;
    
    @Override
    public boolean needsChunking(Object uatData, Object testData, int maxTokens) {
        try {
            String uatJson = objectMapper.writeValueAsString(uatData);
            String testJson = objectMapper.writeValueAsString(testData);
            
            int totalChars = uatJson.length() + testJson.length();
            int estimatedTokens = (int) (totalChars / CHARS_PER_TOKEN);
            
            log.debug("数据大小评估: 总字符数={}, 估算token数={}, 限制={}", 
                    totalChars, estimatedTokens, maxTokens);
            
            return estimatedTokens > maxTokens;
            
        } catch (Exception e) {
            log.error("评估数据大小失败", e);
            return false;
        }
    }
    
    @Override
    public ChunkResult chunkData(Object uatData, Object testData, int maxTokens) {
        try {
            if (!needsChunking(uatData, testData, maxTokens)) {
                // 不需要分片，返回单个分片
                DataChunk singleChunk = new DataChunk(uatData, testData, 1, 1, "完整数据");
                return new ChunkResult(Collections.singletonList(singleChunk), false);
            }
            
            // 需要分片处理
            List<DataChunk> chunks = performChunking(uatData, testData, maxTokens);
            return new ChunkResult(chunks, true);
            
        } catch (Exception e) {
            log.error("数据分片失败", e);
            // 失败时返回原始数据作为单个分片
            DataChunk fallbackChunk = new DataChunk(uatData, testData, 1, 1, "分片失败-原始数据");
            return new ChunkResult(Collections.singletonList(fallbackChunk), false);
        }
    }
    
    @Override
    public String mergeChunkResults(List<ChunkEvaluationResult> chunkResults, String stageName) {
        if (chunkResults == null || chunkResults.isEmpty()) {
            return "无评估结果";
        }
        
        if (chunkResults.size() == 1) {
            return chunkResults.get(0).getEvaluation();
        }
        
        StringBuilder mergedResult = new StringBuilder();
        mergedResult.append("## ").append(getStageDisplayName(stageName)).append("分片评估汇总\n\n");
        
        // 添加各分片的评估结果
        for (ChunkEvaluationResult result : chunkResults) {
            mergedResult.append("### 分片 ").append(result.getChunkIndex())
                       .append("/").append(chunkResults.size())
                       .append(" - ").append(result.getChunkDescription()).append("\n");
            mergedResult.append("**评分**: ").append(result.getScore()).append("/100\n");
            mergedResult.append(result.getEvaluation()).append("\n\n");
        }
        
        // 添加综合分析
        mergedResult.append("## 综合分析\n");
        mergedResult.append(generateOverallAnalysis(chunkResults, stageName));
        
        return mergedResult.toString();
    }
    
    @Override
    public Integer calculateOverallScore(List<ChunkEvaluationResult> chunkResults) {
        if (chunkResults == null || chunkResults.isEmpty()) {
            return 0;
        }
        
        // 计算加权平均分（可以根据分片大小调整权重，这里使用简单平均）
        double totalScore = chunkResults.stream()
                .mapToInt(result -> result.getScore() != null ? result.getScore() : 0)
                .average()
                .orElse(0.0);
        
        return (int) Math.round(totalScore);
    }
    
    /**
     * 执行实际的数据分片
     */
    private List<DataChunk> performChunking(Object uatData, Object testData, int maxTokens) throws Exception {
        List<DataChunk> chunks = new ArrayList<>();
        
        JsonNode uatNode = objectMapper.valueToTree(uatData);
        JsonNode testNode = objectMapper.valueToTree(testData);
        
        if (uatNode.isArray() && testNode.isArray()) {
            // 数组类型数据分片
            chunks.addAll(chunkArrayData(uatNode, testNode, maxTokens));
        } else if (uatNode.isObject() && testNode.isObject()) {
            // 对象类型数据分片
            chunks.addAll(chunkObjectData(uatNode, testNode, maxTokens));
        } else {
            // 其他类型，按字符串长度分片
            chunks.addAll(chunkStringData(uatData, testData, maxTokens));
        }
        
        return chunks;
    }
    
    /**
     * 数组数据分片
     */
    private List<DataChunk> chunkArrayData(JsonNode uatArray, JsonNode testArray, int maxTokens) throws Exception {
        List<DataChunk> chunks = new ArrayList<>();
        
        int uatSize = uatArray.size();
        int testSize = testArray.size();
        int maxSize = Math.max(uatSize, testSize);
        
        // 估算每个元素的平均大小
        int avgElementSize = estimateAverageElementSize(uatArray, testArray);
        int elementsPerChunk = Math.max(1, (int) (maxTokens * CHARS_PER_TOKEN / avgElementSize));
        
        for (int i = 0; i < maxSize; i += elementsPerChunk) {
            int endIndex = Math.min(i + elementsPerChunk, maxSize);
            
            ArrayNode uatChunk = objectMapper.createArrayNode();
            ArrayNode testChunk = objectMapper.createArrayNode();
            
            // 提取UAT数据分片
            for (int j = i; j < endIndex && j < uatSize; j++) {
                uatChunk.add(uatArray.get(j));
            }
            
            // 提取TEST数据分片
            for (int j = i; j < endIndex && j < testSize; j++) {
                testChunk.add(testArray.get(j));
            }
            
            String description = String.format("数组元素 %d-%d", i + 1, endIndex);
            int chunkIndex = chunks.size() + 1;
            int totalChunks = (maxSize + elementsPerChunk - 1) / elementsPerChunk;
            
            chunks.add(new DataChunk(uatChunk, testChunk, chunkIndex, totalChunks, description));
        }
        
        return chunks;
    }
    
    /**
     * 对象数据分片
     */
    private List<DataChunk> chunkObjectData(JsonNode uatObject, JsonNode testObject, int maxTokens) throws Exception {
        List<DataChunk> chunks = new ArrayList<>();
        
        // 获取所有字段名
        Set<String> allFields = new HashSet<>();
        uatObject.fieldNames().forEachRemaining(allFields::add);
        testObject.fieldNames().forEachRemaining(allFields::add);
        
        List<String> fieldList = new ArrayList<>(allFields);
        
        // 按字段分组分片
        int fieldsPerChunk = Math.max(1, estimateFieldsPerChunk(uatObject, testObject, maxTokens));
        
        for (int i = 0; i < fieldList.size(); i += fieldsPerChunk) {
            int endIndex = Math.min(i + fieldsPerChunk, fieldList.size());
            
            ObjectNode uatChunk = objectMapper.createObjectNode();
            ObjectNode testChunk = objectMapper.createObjectNode();
            
            List<String> chunkFields = fieldList.subList(i, endIndex);
            
            for (String field : chunkFields) {
                if (uatObject.has(field)) {
                    uatChunk.set(field, uatObject.get(field));
                }
                if (testObject.has(field)) {
                    testChunk.set(field, testObject.get(field));
                }
            }
            
            String description = String.format("字段组 %s", String.join(", ", chunkFields));
            int chunkIndex = chunks.size() + 1;
            int totalChunks = (fieldList.size() + fieldsPerChunk - 1) / fieldsPerChunk;
            
            chunks.add(new DataChunk(uatChunk, testChunk, chunkIndex, totalChunks, description));
        }
        
        return chunks;
    }
    
    /**
     * 字符串数据分片
     */
    private List<DataChunk> chunkStringData(Object uatData, Object testData, int maxTokens) throws Exception {
        String uatStr = objectMapper.writeValueAsString(uatData);
        String testStr = objectMapper.writeValueAsString(testData);
        
        int maxChars = (int) (maxTokens * CHARS_PER_TOKEN / 2); // 除以2因为有两份数据
        
        List<DataChunk> chunks = new ArrayList<>();
        int maxLength = Math.max(uatStr.length(), testStr.length());
        
        for (int i = 0; i < maxLength; i += maxChars) {
            int endIndex = Math.min(i + maxChars, maxLength);
            
            String uatChunk = i < uatStr.length() ? 
                uatStr.substring(i, Math.min(endIndex, uatStr.length())) : "";
            String testChunk = i < testStr.length() ? 
                testStr.substring(i, Math.min(endIndex, testStr.length())) : "";
            
            String description = String.format("字符 %d-%d", i + 1, endIndex);
            int chunkIndex = chunks.size() + 1;
            int totalChunks = (maxLength + maxChars - 1) / maxChars;
            
            chunks.add(new DataChunk(uatChunk, testChunk, chunkIndex, totalChunks, description));
        }
        
        return chunks;
    }
    
    /**
     * 估算数组元素的平均大小
     */
    private int estimateAverageElementSize(JsonNode uatArray, JsonNode testArray) throws Exception {
        int totalSize = 0;
        int count = 0;
        
        // 采样前几个元素估算平均大小
        int sampleSize = Math.min(5, Math.max(uatArray.size(), testArray.size()));
        
        for (int i = 0; i < sampleSize; i++) {
            if (i < uatArray.size()) {
                totalSize += objectMapper.writeValueAsString(uatArray.get(i)).length();
                count++;
            }
            if (i < testArray.size()) {
                totalSize += objectMapper.writeValueAsString(testArray.get(i)).length();
                count++;
            }
        }
        
        return count > 0 ? totalSize / count : 1000; // 默认1000字符
    }
    
    /**
     * 估算每个分片应包含的字段数
     */
    private int estimateFieldsPerChunk(JsonNode uatObject, JsonNode testObject, int maxTokens) throws Exception {
        int totalFields = 0;
        int totalSize = 0;
        
        Iterator<Map.Entry<String, JsonNode>> uatFields = uatObject.fields();
        while (uatFields.hasNext()) {
            Map.Entry<String, JsonNode> entry = uatFields.next();
            totalSize += objectMapper.writeValueAsString(entry.getValue()).length();
            totalFields++;
        }
        
        Iterator<Map.Entry<String, JsonNode>> testFields = testObject.fields();
        while (testFields.hasNext()) {
            Map.Entry<String, JsonNode> entry = testFields.next();
            totalSize += objectMapper.writeValueAsString(entry.getValue()).length();
            totalFields++;
        }
        
        if (totalFields == 0) return 1;
        
        int avgFieldSize = totalSize / totalFields;
        int maxChars = (int) (maxTokens * CHARS_PER_TOKEN);
        
        return Math.max(1, maxChars / avgFieldSize);
    }
    
    /**
     * 生成综合分析
     */
    private String generateOverallAnalysis(List<ChunkEvaluationResult> chunkResults, String stageName) {
        StringBuilder analysis = new StringBuilder();
        
        // 统计信息
        int totalChunks = chunkResults.size();
        double avgScore = chunkResults.stream()
                .mapToInt(r -> r.getScore() != null ? r.getScore() : 0)
                .average().orElse(0.0);
        
        long highScoreCount = chunkResults.stream()
                .mapToInt(r -> r.getScore() != null ? r.getScore() : 0)
                .filter(score -> score >= 80)
                .count();
        
        analysis.append(String.format("**分片统计**: 共 %d 个分片，平均得分 %.1f 分，其中 %d 个分片得分≥80分\n\n", 
                totalChunks, avgScore, highScoreCount));
        
        // 质量评估
        if (avgScore >= 90) {
            analysis.append("**质量评估**: 优秀 - 各分片数据质量均较高，整体表现优异\n");
        } else if (avgScore >= 80) {
            analysis.append("**质量评估**: 良好 - 大部分分片数据质量良好，少量问题需要关注\n");
        } else if (avgScore >= 70) {
            analysis.append("**质量评估**: 一般 - 数据质量中等，存在一些需要改进的问题\n");
        } else {
            analysis.append("**质量评估**: 较差 - 数据质量存在较多问题，需要重点关注和改进\n");
        }
        
        return analysis.toString();
    }
    
    /**
     * 获取阶段显示名称
     */
    private String getStageDisplayName(String stageName) {
        switch (stageName.toLowerCase()) {
            case "recognize": return "识别阶段";
            case "extraction": return "提取阶段";
            case "structured": return "结构化阶段";
            case "transformer": return "转换阶段";
            default: return stageName;
        }
    }
}
