package com.kf.uitest.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class UiTestEnvironmentUpdateRequest {
    @NotNull(message = "环境ID不能为空")
    private Long id;
    private String environmentName;
    private String browserType;
    private Integer isHeadless;
    private String description;
    private VariableUpdates variables;
    private List<Long> delete;

    @Data
    public static class VariableUpdates {
        @Valid
        private List<VariableCreateRequest> create;
        @Valid
        private List<VariableUpdateRequest> update;
        private List<Long> delete;
    }

    @Data
    public static class VariableCreateRequest {
        private String variableName;
        private String variableValue;
        private String description;
        private Long environmentId;
    }

    @Data
    public static class VariableUpdateRequest {
        @NotNull(message = "变量ID不能为空")
        private Long id;
        private String variableName;
        private String variableValue;
        private String description;
    }
} 