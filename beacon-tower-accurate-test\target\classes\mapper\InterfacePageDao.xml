<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kf.accuratetest.dao.InterfacePageDao">
    <resultMap id="BaseResultMap" type="com.kf.accuratetest.entity.InterfacePage">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
        <result property="interfaceName" column="interface_name" jdbcType="VARCHAR"/>
        <result property="pageUrl" column="page_url" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
        id, user_id, project_name, interface_name, page_url, create_time, update_time, status, is_deleted
        from t_interface_page
        where id = #{id}
    </select>

    <select id="queryAllByUserId" resultMap="BaseResultMap">
        select
        id, user_id, project_name, interface_name, page_url, create_time, update_time, status, is_deleted
        from t_interface_page
        where user_id = #{userId}
    </select>


    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
        id, user_id, project_name, interface_name, page_url, create_time, update_time, status, is_deleted
        from t_interface_page
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="projectName != null and projectName != ''">
                and project_name = #{projectName}
            </if>
            <if test="interfaceName != null and interfaceName != ''">
                and interface_name = #{interfaceName}
            </if>
            <if test="pageUrl != null and pageUrl != ''">
                and page_url = #{pageUrl}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select id,
               user_id,
               project_name,
               interface_name,
               page_url,
               create_time,
               update_time,
               status,
               is_deleted
        from k_base.t_interface_page
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="projectName != null and projectName != ''">
                and project_name = #{projectName}
            </if>
            <if test="interfaceName != null and interfaceName != ''">
                and interface_name = #{interfaceName}
            </if>
            <if test="pageUrl != null and pageUrl != ''">
                and page_url = #{pageUrl}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
    </select>
    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from t_interface_page
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="projectName != null and projectName != ''">
                and project_name = #{projectName}
            </if>
            <if test="interfaceName != null and interfaceName != ''">
                and interface_name = #{interfaceName}
            </if>
            <if test="pageUrl != null and pageUrl != ''">
                and page_url = #{pageUrl}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into t_interface_page(user_id, project_name, interface_name, page_url, create_time, update_time, status,
        is_deleted)
        values (#{userId}, #{projectName}, #{interfaceName}, #{pageUrl}, #{createTime}, #{updateTime}, #{status},
        #{isDeleted})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into t_interface_page(user_id, project_name, interface_name, page_url, create_time, update_time, status,
        is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.projectName}, #{entity.interfaceName}, #{entity.pageUrl}, #{entity.createTime},
            #{entity.updateTime}, #{entity.status}, #{entity.isDeleted})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into t_interface_page(user_id, project_name, interface_name, page_url, create_time, update_time, status,
        is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.projectName}, #{entity.interfaceName}, #{entity.pageUrl}, #{entity.createTime},
            #{entity.updateTime}, #{entity.status}, #{entity.isDeleted})
        </foreach>
        on duplicate key update
        user_id = values(user_id),
        project_name = values(project_name),
        interface_name = values(interface_name),
        page_url = values(page_url),
        create_time = values(create_time),
        update_time = values(update_time),
        status = values(status),
        is_deleted = values(is_deleted)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update t_interface_page
        <set>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="projectName != null and projectName != ''">
                project_name = #{projectName},
            </if>
            <if test="interfaceName != null and interfaceName != ''">
                interface_name = #{interfaceName},
            </if>
            <if test="pageUrl != null and pageUrl != ''">
                page_url = #{pageUrl},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from t_interface_page where id = #{id}
    </delete>
</mapper>

