package com.kf.accuratetest.dao;

import com.kf.accuratetest.entity.RepositoryInfo;

import java.util.List;

public interface RepositoryInfoDao {
    int deleteByPrimaryKey(Integer id);

    int insert(RepositoryInfo record);

    int insertSelective(RepositoryInfo record);

    RepositoryInfo selectByPrimaryKey(int id);

    int updateByPrimaryKeySelective(RepositoryInfo record);

    int updateByPrimaryKey(RepositoryInfo record);

    List<RepositoryInfo> selectByRepositoryAndBranch(RepositoryInfo record);

    int updateByRepositoryAndBranch(RepositoryInfo record);

    int updateCompileByRepositoryPath(RepositoryInfo record);
}