package com.kf.baosi.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kf.baosi.dao.TVerifyFileMapper;
import com.kf.baosi.dao.VerifyFieldContentMapper;
import com.kf.baosi.dto.VerifyDocumentListDTO;
import com.kf.baosi.entity.VerifyFieldContent;
import com.kf.baosi.enums.CompressionDocxLevelEnum;
import com.kf.baosi.enums.VerifyDocument;
import com.kf.baosi.service.FileService;
import com.kf.baosi.service.verifyDocumentService;
import com.kf.baosi.utils.JsonUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.kf.baosi.utils.compressorUtil.compressDocx;

@Slf4j
@Service
public class verifyDocumentServiceImpl implements verifyDocumentService {

    private static final String TRIAL_OS_URL = "http://trialos.test.com/api/word-template-plus";

    private static final String WORD_TEMPLATE_PLUS_ASYNC_HANDLE = TRIAL_OS_URL + "/wordTemplatePlus/asyncHandle";

    private static final String WORD_TEMPLATE_PLUS_TASK_INFO = TRIAL_OS_URL + "/wordTemplatePlus/taskInfo";

    private static final String WORD_TEMPLATE_PLUS_DOWNLOAD = TRIAL_OS_URL + "/wordTemplatePlus/downLoad";

    @Resource
    TVerifyFileMapper tVerifyFileMapper;

    @Resource
    VerifyFieldContentMapper verifyFieldContentMapper;

    @Resource
    FileService fileService;

    @Resource
    private RestTemplate restTemplate;

    @Value("${file.upload.dir}")
    private String uploadFilePath;

    @Override
    public IPage<VerifyDocumentListDTO> getVerifyDocumentList(String userId, String fileName, String fileType, String isComplete, int current, int size) {
        //设置分页参数，框架会自动处理分页
        Page<VerifyDocumentListDTO> page = new Page<>(current, size);
        return tVerifyFileMapper.getVerifyDocumentList(userId, fileName, fileType, isComplete, page);
    }

    @Override
    public String wordTemplatePlusHandle(Map<String, Object> params) {
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(params);
        log.info("请求模板填充参数:{}", JsonUtil.toJson(params));

        ResponseEntity<String> response = restTemplate.postForEntity(WORD_TEMPLATE_PLUS_ASYNC_HANDLE, entity, String.class);
        String body = response.getBody();
        log.info("请求模板填充结果:{}", body);

        // Parse the response body into a Map
        Map<String, Object> responseMap = JsonUtil.fromJson(body, Map.class);
        if (responseMap == null || !"true".equals(String.valueOf(responseMap.get("success")))) {
            throw new RuntimeException("处理失败");
        }

        return String.valueOf(responseMap.get("data"));
    }

    @Override
    public String wordTemplatePlusTaskInfo(Map<String, Object> params) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(WORD_TEMPLATE_PLUS_TASK_INFO);
        params.forEach(builder::queryParam);
        String urlWithParams = builder.toUriString();
        ResponseEntity<String> response = restTemplate.getForEntity(urlWithParams, String.class);
        String body = response.getBody();
        log.info("通过taskId查询任务结果:{}", body);
        return body;
    }

    @Override
    public void getFile(String fileId, String fileName, HttpServletResponse response) {
        fileService.getFsFile(fileId, fileName, response);
    }

    private String extractFilename(String fileName) {
        String defaultFilename = "downloadedFile.docx";
        if (fileName != null && fileName.contains("filename=")) {
            return fileName.split("filename=")[1].replace("\"", "");
        }
        return defaultFilename;
    }

    @Override
    public void fileCompress(MultipartFile file, HttpServletResponse response) {
        String originalFileName = file.getOriginalFilename();
        String contentType = file.getContentType();

        // 生成随机文件名，避免重复
        String randomUUID = IdUtil.simpleUUID();
        String newFileName = randomUUID + "-" +  originalFileName;

        // 存储路径
        String filePath = uploadFilePath + File.separator + newFileName;
        // 存储上传的文件
        File originalFile = new File(filePath);
        try {
            // 将MultipartFile转换成File并保存到服务器
            file.transferTo(originalFile);
        } catch (IOException e) {
            log.error("文件存储失败: {}", e.getMessage(), e);
            return; // 文件存储失败，直接返回
        }

        // 压缩后的文件路径
        File compressedFile = new File(uploadFilePath, "compressed_" + newFileName);

        // 如果原始文件存储成功，则进行压缩
        compressDocx(originalFile.getAbsolutePath(), compressedFile.getAbsolutePath(), CompressionDocxLevelEnum.getEnumByLevel(3).getLevel());
        // 设置响应头，返回文件给前端下载
        if (contentType == null) {
            // 如果无法获取内容类型，可以根据扩展名设置默认类型
            contentType = "application/octet-stream";
        }
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("UTF-8");
        // 设置Content-Length
        response.setContentLengthLong(compressedFile.length());
        response.setHeader("Access-Control-Expose-Headers", HttpHeaders.CONTENT_DISPOSITION);

        String encodedFileName = URLEncoder.encode(originalFileName, StandardCharsets.UTF_8);
        encodedFileName = encodedFileName.replaceAll("\\+", "%20");
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + encodedFileName);
        // 返回压缩后的文件流
        try (InputStream in = Files.newInputStream(compressedFile.toPath());
             OutputStream out = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
                response.flushBuffer();
            }
        } catch (Exception e) {
            log.error("文件下载失败: {}", e.getMessage(), e);
        } finally {
            compressedFile.delete();
            originalFile.delete();
        }
    }

    @Override
    public Map<String, String> getVerifyField(String objectType, String verifyDocType) {
        VerifyDocument verifyDocument = VerifyDocument.getVerifyDocument(verifyDocType);
        List<String> predefinedFields = verifyDocument.getFieldNames();
        QueryWrapper<VerifyFieldContent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_key", objectType)
                .eq("verify_doc_type", verifyDocType)
                .eq("status", 0);

        List<VerifyFieldContent> fieldContents = verifyFieldContentMapper.selectList(queryWrapper);
        if (fieldContents.isEmpty()) {
            return Collections.emptyMap();
        }
        // 将查询结果转换为 Map<fieldName, content>
        Map<String, String> existingFieldsMap = fieldContents.stream()
                .collect(Collectors.toMap(
                        VerifyFieldContent::getFieldName,
                        VerifyFieldContent::getContent,
                        (existing, replacement) -> existing // 处理重复键，保留现有值
                ));

        // 构建结果Map，缺失的字段设置为空字符串
        Map<String, String> resultMap = new LinkedHashMap<>();
        for (String fieldName : predefinedFields) {
            resultMap.put(fieldName, existingFieldsMap.getOrDefault(fieldName, ""));
        }

        return resultMap;
    }
}
