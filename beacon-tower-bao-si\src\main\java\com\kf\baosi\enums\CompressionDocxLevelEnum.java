package com.kf.baosi.enums;

import lombok.Getter;

import java.util.zip.Deflater;

@Getter
public enum CompressionDocxLevelEnum {
    /**
     * 最快的压缩速度，压缩率较低。
     */
    BEST_SPEED(1,Deflater.BEST_SPEED,"速度优先","最快的压缩速度，压缩率较低"),

    /**
     * 平衡压缩速度和压缩率，默认的压缩级别。
     */
    DEFAULT_COMPRESSION(2,Deflater.DEFAULT_COMPRESSION,"平衡","平衡压缩速度和压缩率"),

    /**
     * 最好的压缩率，压缩速度较慢。
     */
    BEST_COMPRESSION(3,Deflater.BEST_COMPRESSION,"压缩率优先","最高压缩率，压缩速度较慢");

    private final int level;
    private final int DefLaterLevel;
    private final String desc;
    private final String tip;

    // 构造函数
    CompressionDocxLevelEnum(int level,int DefLaterLevel, String desc, String tip) {
        this.level = level;
        this.DefLaterLevel = DefLaterLevel;
        this.desc = desc;
        this.tip = tip;
    }

    // 通过level获取枚举
    public static CompressionDocxLevelEnum getEnumByLevel(int level) {
        for (CompressionDocxLevelEnum value : CompressionDocxLevelEnum.values()) {
            if (value.getLevel() == level) {
                return value;
            }
        }
        throw new IllegalArgumentException("未知的压缩级别");
    }

    // 返回列表
    public static CompressionDocxLevelEnum[] getAll() {
        return CompressionDocxLevelEnum.values();
    }

}
