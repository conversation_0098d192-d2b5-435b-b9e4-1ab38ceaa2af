#commons
error_lang_invalid=语言参数错误
cannot_be_null=不能为空
number=第
row=行
error=出错
delete_fail=删除失败
start_engine_fail=启动失败
upload_fail=文件上传失败
invalid_parameter=非法的参数
name_already_exists=该名称已经存在
resource_not_exist=资源不存在或已删除
read_permission_file_fail=读取权限文件失败!
#user related
user_email_already_exists=用户邮箱已存在
user_id_is_null=用户ID不能为空
user_name_is_null=用户名不能为空
user_email_is_null=用户邮箱不能为空
password_is_null=密码不能为空
user_id_already_exists=用户id已存在
password_modification_failed=旧密码输入错误，请重新输入
cannot_delete_current_user=无法删除当前登录用户
connection_failed=连接失败
connection_timeout=连接超时
user_already_exists=该用户已存在于当前成员列表中
cannot_remove_current=无法移除当前登录用户
login_fail=登录失败
password_is_incorrect=用户名或密码不正确
user_not_exist=用户不存在：
user_has_been_disabled=用户已被禁用
excessive_attempts=操作频繁
user_locked=用户被锁定
user_expires=用户过期
not_authorized=未经授权
user_apikey_limit=您最多可创建5个API Key
please_logout_current_user=请先登出当前用户
#attachment upload
test_case_attachment_upload_not_found=无法上传附件，未找到相关联用例：
issues_attachment_upload_not_found=无法上传附件，未找到相关联缺陷：
# issue template copy
target_issue_template_not_checked=无法复制，未选中目标项目
source_issue_template_is_empty=复制错误，源项目为空
#load test
edit_load_test_not_found=无法编辑测试，未找到测试：
run_load_test_not_found=无法运行测试，未找到测试：
run_load_test_file_not_found=无法运行测试，无法获取测试文件元信息，测试ID：
run_load_test_file_content_not_found=无法运行测试，无法获取测试文件内容，测试ID：
run_load_test_file_init_error=无法运行测试，请前往 [系统设置-系统-系统参数设置] 检查当前站点配置，详情见 https://metersphere.io/docs/v2.x/faq/load_test/#url
load_test_is_running=测试正在运行, 请等待
load_test_kafka_invalid=Kafka 不可用，请检查配置
cannot_edit_load_test_running=不能修改正在运行的测试
test_not_found=测试不存在:
test_not_running=测试未运行
load_test_already_exists=测试名称不能重复
load_test_name_length=测试名称长度超过限制
no_nodes_message=没有节点信息
duplicate_node_ip=节点 IP 重复
duplicate_node_port=节点 Port 重复
duplicate_node_ip_port=节点 IP、Port 重复
max_thread_insufficient=并发用户数超额
related_case_del_fail_prefix=已关联到
related_case_del_fail_suffix=测试用例，请先解除关联
jmx_content_valid=JMX 内容无效，请检查
container_delete_fail=容器由于网络原因停止失败，请重试
load_test_report_file_not_exist=当前报告没有JTL文件，请等待或重新执行以便获取
#workspace
workspace_name_is_null=工作空间名不能为空
workspace_name_already_exists=工作空间名已存在
workspace_does_not_belong_to_user=当前工作空间不属于当前用户
workspace_not_exists=工作空间不存在
#test resource pool
test_resource_pool_id_is_null=资源池ID不能为空
test_resource_pool_name_is_null=资源池名称不能为空
test_resource_pool_name_already_exists=资源池名称已存在
load_test=性能测试
test_resource_pool_is_use=正在使用此资源池，无法删除
only_one_k8s=只能添加一个 K8S
test_resource_pool_not_exists=测试资源池不存在
test_resource_pool_invalid=当前测试使用的资源池处于禁用状态
#project
project_name_is_null=项目名称不能为空
project_name_already_exists=项目名称已存在
project_file_already_exists=项目下该文件已经存在
project_file_in_use=占用文件，无法删除。
#organization
organization_name_is_null=组织名不能为空
organization_name_already_exists=组织名已存在
organization_does_not_belong_to_user=当前组织不属于当前用户
organization_id_is_null=组织 ID 不能为空
#api
api_load_script_error=读取脚本失败
illegal_xml_format=不合法的 XML 格式
api_report_is_null="测试报告是未生成，无法更新"
api_test_environment_already_exists="已存在该名称的环境配置"
api_test=接口测试
api_versions_update_http=该接口存在多版本，不允许修改请求类型或路径，请新建接口
api_versions_update=该接口存在多版本，不允许修改名称，请新建接口
api_versions_create=该接口已存在，请在版本处创建新版本
#test case
test_case_node_level=层
test_case_node_level_tip=模块树最大深度为
test_case_module_not_null=所属模块不能为空
test_case_create_module_fail=创建模块失败
test_case_import_template_name=测试用例模版
test_case_import_template_sheet=模版
module_not_null=所属模块不能为空格
user_not_exists=该项目下无该用户
test_case_already_exists=该项目下已存在该测试用例
parse_data_error=解析数据出错
missing_header_information=缺少头部信息
test_case_exist=该项目下已存在用例：
node_deep_limit=节点深度不超过8层！
before_delete_plan=该计划下存在关联测试用例，请先取消关联！
incorrect_format=格式错误
test_case_name=用例名称
test_case_type=用例类型
test_case_maintainer=维护人
test_case_priority=用例等级
test_case_prerequisite=前置条件
test_case_remark=备注
test_case_step_desc=步骤描述
test_case_step_result=预期结果
test_case_module=所属模块
test_case=功能用例
user=用户
user_import_template_name=用户导入模板
user_import_template_sheet=模版
user_import_format_wrong=格式错误
user_import_id_is_repeat=ID重复
user_import_email_is_repeat=E-mail重复
user_import_organization_not_fond=组织未找到
user_import_workspace_not_fond=工作空间未找到
org_admin=组织管理员
org_member=组织成员
test_manager=测试经理
tester=测试成员
read_only_user=只读用户
module=模块
tag_tip_pattern=标签之间以分号或者逗号隔开
preconditions_optional=前置条件选填
remark_optional=备注选填
do_not_modify_header_order=请勿修改表头顺序
module_created_automatically=若无该模块将自动创建
options=选项
options_yes=是
options_no=否
required=必填
password_format_is_incorrect=有效密码：8-30位，英文大小写字母+数字+特殊字符（可选）
please_input_project_member=请填写该项目下的相关人员ID
test_case_report_template_repeat=同一工作空间下不能存在同名模版
plan_name_already_exists=测试计划名称已存在
test_case_already_exists_excel=文件中存在多条相同用例
test_case_module_already_exists=同层级下已存在该模块名称
functional_method_tip=功能测试不支持自动方式
custom_num_is_exist=用例自定义ID已存在
custom_num_is_not_exist=用例自定义ID不存在
id_required=ID必填
id_repeat_in_table=表格内ID重复
step_model_tip=步骤描述填写 STEP,文本描述请填写 TEXT (非必填)
case_status_not_exist=用例状态必须为未开始(Prepare)、进行中(Underway)、已完成(Completed)
tapd_project_not_exist=关联的TAPD项目ID不存在
zentao_get_project_builds_fail=获取影响版本错误
#ldap
ldap_url_is_null=LDAP地址为空
ldap_dn_is_null=LDAP绑定DN为空
ldap_ou_is_null=LDAP参数OU为空
ldap_password_is_null=LDAP密码为空
ldap_connect_fail=连接LDAP失败
ldap_connect_fail_user=连接LDAP失败，绑定的DN或密码错误
ldap_user_filter_is_null=LDAP用户过滤器为空
ldap_user_mapping_is_null=LDAP用户属性映射为空
authentication_failed=用户认证失败,用户名或密码错误
user_not_found_or_not_unique=用户不存在或者不唯一
ldap_authentication_not_enabled=LDAP认证未启用
login_fail_ou_error=登录失败，请检查用户OU
login_fail_filter_error=登录失败，请检查用户过滤器
check_ldap_mapping=检查LDAP属性映射
ldap_mapping_value_null=LDAP用户属性映射字段为空值
#quota
quota_project_excess_ws_resource_pool=项目的资源池不能超过工作空间的资源池范围
quota_performance_excess_project=性能测试数量超过项目限额
quota_max_threads_excess_project=最大并发数超过项目限额
quota_duration_excess_project=压测时长超过项目限额
quota_member_excess_project=成员数超过项目配额
quota_project_excess_project=项目数超过工作空间配额
import_xmind_count_error=思维导图导入用例数量不能超过 800 条
license_valid_license_error=授权认证失败
import_xmind_not_found=未找到测试用例
test_review_task_notice=测试评审任务通知
swagger_url_scheduled_import_notification=swagger_url定时导入通知
swagger_parse_error=Swagger 解析失败，请确认文件格式是否正确！
swagger_parse_error_with_auth=Swagger 解析失败，请确认认证信息是否正确或文件格式是否正确！
test_track.length_less_than=标题过长，字数必须小于
# check owner
check_owner_project=当前用户没有操作此项目的权限
check_owner_test=当前用户没有操作此测试的权限
check_owner_case=当前用户没有操作此用例的权限
check_owner_plan=当前用户没有操作此计划的权限
check_owner_review=当前用户没有操作此评审的权限
check_owner_comment=当前用户没有操作此评论的权限
upload_content_is_null=导入内容为空
test_plan_notification=测试计划通知
task_notification_=定时任务结果通知
api_definition_url_not_repeating=接口请求地址已经存在
api_definition_name_not_repeating=同一模块下相同的名称-url组合已存在
api_definition_name_already_exists=同一模块下接口名称不能重复
api_definition_module=模块路径为
api_definition_name=接口
task_notification_jenkins=jenkins任务通知
task_notification=任务通知
message_task_already_exists=任务接收人已经存在
#automation
automation_name_already_exists=同一个项目的同一模块下，场景名称不能重复
automation_name=场景
automation_exec_info=没有测试步骤，无法执行
delete_check_reference_by=被场景引用
not_execute=未执行
execute_not_pass=未通过
execute_pass=通过
import_fail_custom_num_exists=导入失败，自定义ID已存在
automation_versions_update=该场景存在多版本，不允许修改名称，请新建接口
automation_versions_create=该场景已存在，请在版本处创建新版本
#authsource
authsource_name_already_exists=认证源名称已经存在
custom_field_already=工作空间下已存在该字段：
expect_name_exists=预期名称已存在
ssl_password_error=认证密码错误，请重新输入密码
ssl_file_error=认证文件加载失败，请检查认证文件
#log
api_definition=接口定义
api_definition_case=接口定义用例
api_automation=接口自动化
api_automation_schedule=接口自动化-定时任务
api_automation_report=测试报告
track_test_case=测试用例
track_test_case_review=用例评审
track_test_plan=测试计划
track_test_plan_schedule=测试计划-定时任务
track_bug=缺陷管理
track_report=报告
performance_test=性能测试
performance_test_report=性能测试报告
system_user=系统-用户
system_organization=系统-组织
system_workspace=工作空间
system_test_resource=系统-测试资源池
system_parameter_setting=系统-系统参数设置
system_quota_management=系统-配额管理
system_authorization_management=系统-授权管理
organization_member=组织-成员
organization_workspace=组织-工作空间
workspace_service_integration=工作空间-服务集成
workspace_message_settings=工作空间-消息设置
workspace_member=工作空间-成员
workspace_template_settings_field=工作空间-模版设置-自定义字段
workspace_template_settings_case=工作空间-模版设置-用例模版
workspace_template_settings_issue=工作空间-模版设置-缺陷模版
project_project_manager=项目-项目管理
project_project_member=项目-成员
project_project_jar=項目-JAR包管理
project_environment_setting=项目-环境设置
project_file_management=项目-文件管理
personal_information_personal_settings=个人信息-个人设置
personal_information_apikeys=个人信息-API Keys
auth_title=系统认证
group_permission=用户组与权限
test_case_status_prepare=未开始
test_case_status_running=进行中
test_case_status_finished=已完成
connection_expired=连接已失效，请重新获取
# track home
api_case=接口用例
performance_case=性能用例
scenario_case=场景用例
test_case_status_error=失败
test_case_status_success=成功
test_case_status_trash=废弃
test_case_status_saved=已保存
create_user=创建人
test_case_status=用例状态
id_not_rightful=ID 不合法
project_reference_multiple_plateform=项目指向多个第三方平台
# mock
mock_warning=未找到匹配的Mock期望
#项目报告
enterprise_test_report=项目报告
count=统计
cannot_find_project=未找到测试项目
project_repeatable_is_false=项目未配置URL可重复
#环境组
null_environment_group_name=环境组名称不存在
environment_group_name=环境组名称
environment_group_exist=已存在
environment_group_has_duplicate_project=每个项目只能选择一个环境！
environment_group=环境组
#误报库
error_report_library=误报库
issue_jira_info_error=请检查服务集成信息或Jira项目ID
error_code_is_unique=错误代码不可重复
no_version_exists=不存在版本！请先创建项目的版本
jira_auth_error=账号名或密码(Token)错误
jira_auth_url_error=测试连接失败，请检查Jira地址是否正确
#ui 指令校验
is_null=不能为空
url_is_null=URL 参数不能为空
locator_is_null=元素定位参数不能有空
coord=坐标
input_content=输入内容
subitem_type=子选项类型
subitem=子选项值
varname=变量名
attributeName=属性名
expression=表达式
times=循环次数
command=步骤
extract_type=提取信息类型
cmdValidation=断言
cmdValidateValue=断言值
cmdValidateText=弹窗文本
cmdValidateDropdown=下拉框
cmdValidateElement=元素断言
cmdValidateTitle=网页标题
cmdOpen=打开网页
cmdSelectWindow=切换窗口
cmdSetWindowSize=设置窗口大小
cmdSelectFrame=选择内嵌网页
cmdDialog=弹窗操作
cmdDropdownBox=下拉框操作
submit=提交表单
cmdSetItem=设置选项
cmdWaitElement=等待元素
cmdInput=输入操作
cmdMouseClick=鼠标点击
cmdMouseMove=鼠标移动
cmdMouseDrag=鼠标拖拽
cmdTimes=次数循环
cmdForEach=ForEach 循环
cmdWhile=While 循环
cmdIf=If
cmdElse=Else
cmdElseIf=ElseIf
close=关闭网页
cmdExtraction=数据提取
cmdExtractWindow=提取窗口信息
cmdExtractElement=提取元素信息
tcp_mock_not_unique=该TCP端口号已被使用
report_warning=报告类型和报告ID不能为空
report_type_error=报告类型错误
serial=串行
parallel=并行
csv_no_exist=CSV文件不存在
update_scenario=更新了场景
scenario_update_notice=接口自动化通知
create_scenario=新建了场景
scenario_create_notice=接口自动化通知
update_api=更新了接口定义
api_update_notice=接口更新通知
create_api=新建了接口定义
api_create_notice=接口新建通知
create_api_case=新建了接口用例
api_case_create_notice=接口用例新建通知
update_api_case=更新了接口用例
api_case_update_notice=接口用例更新通知
error_xml_struct=错误的xml数据
case_name_is_already_exist=用例名称不能重复
file_format_does_not_meet_requirements=文件格式不符合要求
url_is_not_valid=URL 格式不正确！
scenario_step_parsing_error_check=场景步骤解析错误，请检查是否包含插件步骤！
pre_processor_env=全局前置脚本
post_processor_env=全局后置脚本
scenario_warning=场景包含插件步骤，插件已删除不能导出
