<template>
    <div class="test-step-wrapper">
        <div class="operation-buttons">
            <el-button>录制用例</el-button>
            <el-button>运行调试</el-button>
            <el-button :disabled="!hasChanges" type="primary" @click="handleSave">保存</el-button>
            <el-button 
                v-if="internalSteps.length > 0 || selectedCaseId" 
                type="primary" 
                @click="addNewStep"
            >
                <el-icon><Plus /></el-icon>新增步骤
            </el-button>
        </div>

        <el-table ref="tableRef" :data="internalSteps" stripe style="width: 100%" row-key="id" max-height="calc(100vh - 200px)">
            <!-- 拖拽 handle 列 -->
            <el-table-column label="" min-width="2%">
                <template #default>
                    <div class="drag-handle drag-button" @mousedown.stop>
                        <el-icon><Sort /></el-icon>
                    </div>
                </template>
            </el-table-column>

            <!-- 动作类型（下拉组件） -->
            <el-table-column prop="actionType" label="动作类型" min-width="14%">
                <template #default="{ row }">
                    <ActionTypeSelect 
                        :action-type-options="actionTypeOptions"
                        v-model="row.actionType"
                        @change="handleActionTypeChange(row)"
                    />
                </template>
            </el-table-column>

            <!-- 选择器列 -->
            <el-table-column prop="selector" label="元素选择器" min-width="27%">
                <template #default="{ row }">
                    <el-input 
                        v-if="needSelector(row.actionType)"
                        v-model="row.selector" 
                        :placeholder="getSelectorPlaceholder(row.actionType, row.selectorType)"
                        class="selector-input"
                    >
                        <template #prepend>
                            <el-select 
                                v-model="row.selectorType" 
                                placeholder="选择器类型"
                                style="width: 90px"
                                @change="handleSelectorTypeChange(row)"
                            >
                                <el-option
                                    v-for="item in selectorTypes"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </template>
                        <template #prefix v-if="row.selectorType">
                            {{ getSelectorPrefix(row.selectorType) }}
                        </template>
                    </el-input>
                </template>
            </el-table-column>

            <!-- 输入数据(仅示例 fill / assert) -->
            <el-table-column prop="inputData" label="输入" min-width="27%">
                <template #default="{ row }">
                    <el-input 
                        v-if="needInputData(row.actionType)"
                        v-model="row.inputData" 
                        :placeholder="getInputDataPlaceholder(row.actionType)"
                    />
                </template>
            </el-table-column>

            <el-table-column prop="useEnvVariable" label="使用环境变量" min-width="10%">
                <template #default="{ row }">
                    <el-switch
                        v-model="row.useEnvVariable"
                        :active-value="1"
                        :inactive-value="0"
                    />
                </template>
            </el-table-column>

            <el-table-column label='前置操作' min-width="10%">
                <template #default="{ row }">
                    <el-button type="text" @click="handleEdit(row)">前置操作</el-button>
                </template>
            </el-table-column>
            <el-table-column label='后置操作' min-width="10%">
                <template #default="{ row }">
                    <el-button type="text" @click="handleEdit(row)">后置操作</el-button>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label='操作' min-width="10%">
                <template #default="{ row }">
                    <el-button type="text" @click="handleEdit(row)">编辑</el-button>
                    <el-button type="text" @click="handleDelete(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script lang="ts" setup>

import { ref, watch, onMounted, nextTick } from 'vue'
import { ElTable } from 'element-plus'
import Sortable from 'sortablejs'
import { Sort, Plus } from '@element-plus/icons-vue'
import { cloneDeep } from 'lodash-es'

import ActionTypeSelect from './ActionTypeSelect.vue'
import { getTestSteps } from '@/api/layout'

interface TestStepDTO {
    id: string
    testCaseId: string
    stepOrder: number
    stepName: string
    actionType: string
    selector: string
    inputData: string
    waitTime: number
    metadata: string
    remark: string
    useEnvVariable: number
    createTime: string
    updateTime: string
    selectorType?: string  // 添加选择器类型字段
}

// 父组件传入的属性
const props = defineProps<{
    selectedCaseId?: string  // 改为 string 类型以匹配后端
    actionTypeOptions: Array<{label: string, value: string}>
}>()

// 新增 emit
const emit = defineEmits(['save'])

// 内部维护一份数据
const internalSteps = ref<TestStepDTO[]>([])

// table 引用，用于获取 DOM
const tableRef = ref<InstanceType<typeof ElTable> | null>(null)

// Sortable 实例
let sortableInstance: Sortable | null = null

// 记录原始数据，用于比较是否有变化
const originalSteps = ref<TestStepDTO[]>([])
// 是否有未保存的变化
const hasChanges = ref(false)

// 选择器类型定义
const selectorTypes = [
    { label: 'CSS', value: 'css' },
    { label: 'ID', value: 'id' },
    { label: 'XPath', value: 'xpath' },
    { label: '文本', value: 'text' },
    { label: '角色', value: 'role' },
    { label: '测试ID', value: 'testId' },
    { label: '占位符', value: 'placeholder' },
    { label: '标题', value: 'title' },
    { label: '名称', value: 'name' },
    { label: '标签', value: 'label' }
]

// 初始化 Sortable
const initSortable = () => {
    // 先销毁之前的实例
    if (sortableInstance) {
        sortableInstance.destroy()
        sortableInstance = null
    }

    // 获取 tbody 的真实 DOM
    const tbody = tableRef.value?.$el.querySelector('.el-table__body-wrapper tbody')
    if (!tbody) return

    // 创建新的 Sortable 实例
    sortableInstance = Sortable.create(tbody, {
        animation: 150,
        handle: '.drag-handle',
        onEnd: (evt) => {
            const { oldIndex, newIndex } = evt
            if (oldIndex == null || newIndex == null || oldIndex === newIndex) return

            const [movedItem] = internalSteps.value.splice(oldIndex, 1)
            internalSteps.value.splice(newIndex, 0, movedItem)

            // 重新计算 stepOrder
            internalSteps.value.forEach((step, index) => {
                step.stepOrder = index + 1
            })
        }
    })
}

// 获取测试步骤数据
const fetchTestSteps = async (caseId: string) => {
    try {
        const response = await getTestSteps(caseId)
        if (response.data) {
            internalSteps.value = response.data.data
            originalSteps.value = cloneDeep(response.data.data)
            hasChanges.value = false
            nextTick(() => initSortable())
        }
    } catch (error) {
        console.error('获取测试步骤失败:', error)
    }
}

// 监听 selectedCaseId 变化
watch(() => props.selectedCaseId, (newVal) => {
    if (newVal) {
        fetchTestSteps(newVal)
    } else {
        internalSteps.value = []
        originalSteps.value = []
    }
}, { immediate: true })

// 检查数据变化
const checkChanges = () => {
    const original = JSON.stringify(originalSteps.value)
    const current = JSON.stringify(internalSteps.value)
    hasChanges.value = original !== current
}

watch(internalSteps, () => {
    checkChanges()
}, { deep: true })

// 生成新的唯一ID
const generateNewId = () => {
    const maxId = Math.max(0, ...internalSteps.value.map(step => step.id))
    return maxId + 1
}

// 添加动作类型变化处理函数
const handleActionTypeChange = (row: TestStepDTO) => {
    // 当动作类型改变时，清空相关字段
    if (!needSelector(row.actionType)) {
        row.selector = ''
    }
    if (!needInputData(row.actionType)) {
        row.inputData = ''
    }
    
    // 根据动作类型设置默认值
    if (row.actionType === 'FORCE_WAIT_ELEMENT') {
        row.inputData = '1000' // 默认等待1秒
    } else if (row.actionType === 'VISIT_URL') {
        row.inputData = 'https://' // 默认URL前缀
    }
}

// 获取选择器前缀
const getSelectorPrefix = (type: string) => {
    const prefixMap: Record<string, string> = {
        id: '#',
        css: '',
        xpath: '//',
        text: 'text=',
        role: 'role=',
        testId: 'data-testid=',
        placeholder: '[placeholder=',
        title: '[title=',
        name: '[name=',
        label: '[label='
    }
    return prefixMap[type] || ''
}

// 获取选择器输入框的占位符
const getSelectorPlaceholder = (actionType: string, selectorType: string) => {
    const selectorTypePlaceholders: Record<string, string> = {
        id: '输入元素ID',
        css: '输入CSS选择器',
        xpath: '输入XPath表达式',
        text: '输入元素文本内容',
        role: '输入元素角色',
        testId: '输入测试ID',
        placeholder: '输入占位符文本',
        title: '输入标题文本',
        name: '输入name属性值',
        label: '输入标签文本'
    }
    
    return selectorTypePlaceholders[selectorType] || getSelectorPlaceholder(actionType)
}

// 处理选择器类型变化
const handleSelectorTypeChange = (row: TestStepDTO) => {
    // 当选择器类型改变时，清空选择器值
    row.selector = ''
}

// 修改添加新步骤的方法
const addNewStep = () => {
    if (!props.selectedCaseId) return
    
    const lastStep = internalSteps.value[internalSteps.value.length - 1]
    const newStep: TestStepDTO = {
        id: generateNewId().toString(),
        testCaseId: props.selectedCaseId,
        stepOrder: (lastStep?.stepOrder || 0) + 1,
        stepName: `步骤${(lastStep?.stepOrder || 0) + 1}`,
        actionType: props.actionTypeOptions[0]?.value || 'CLICK_ELEMENT', // 默认选中第一个选项
        selectorType: 'css',  // 默认使用 CSS 选择器
        selector: '',
        inputData: '',
        useEnvVariable: 0,
        waitTime: 0,
        metadata: JSON.stringify({ validation: {} }),
        remark: '',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
    }
    
    // 根据默认动作类型设置相应的默认值
    handleActionTypeChange(newStep)
    
    // 在最后添加新步骤
    internalSteps.value.push(newStep)
    
    // 滚动到新添加的步骤
    nextTick(() => {
        const bodyWrapper = tableRef.value?.$el.querySelector('.el-table__body-wrapper')
        if (bodyWrapper) {
            bodyWrapper.scrollTop = bodyWrapper.scrollHeight
        }
    })
}

// 保存变更
const handleSave = async () => {
    if (!hasChanges.value) return
    
    try {
        // 这里调用保存接口
        // await saveSteps(internalSteps.value)
        
        // 保存成功后更新原始数据
        originalSteps.value = cloneDeep(internalSteps.value)
        hasChanges.value = false
        
        // 通知父组件保存成功
        emit('save', internalSteps.value)
    } catch (error) {
        // 处理错误
        console.error('保存失败:', error)
    }
}

// 组件挂载后初始化
onMounted(() => {
    if (internalSteps.value.length > 0) {
        nextTick(() => initSortable())
    }
})

/**
 * 以下两个方法演示：根据不同的 actionType 显示/隐藏某些列
 * 你可以按实际业务需求进行调整
 */
const needSelector = (actionType: string) => {
    // 需要元素选择器的动作类型
    const selectorTypes = [
        'CLICK_ELEMENT',         // 点击
        'INPUT_ELEMENT',         // 输入
        'UPLOAD_ELEMENT',        // 上传
        'DRAG_ELEMENT',          // 拖拽
        'WAIT_ELEMENT_VALUE',    // 等待元素
        'DOUBLE_CLICK_ELEMENT',  // 双击
        'HOVER_ELEMENT',         // 悬停
        'RIGHT_CLICK_ELEMENT',   // 右键
        'SELECT_DROPDOWN',       // 下拉选择
        'SCROLL_ELEMENT',        // 滚动元素
        'CHECK_CHECKBOX',        // 勾选复选框
        'UNCHECK_CHECKBOX',      // 取消勾选
        'SELECT_RADIO',          // 单选框
        'SWITCH_IFRAME',         // 切换 IFrame
        'ASSERT_ELEMENT',        // 断言元素
        'CLEAR_FIELD'           // 清空输入框
    ]
    return selectorTypes.includes(actionType)
}

const needInputData = (actionType: string) => {
    // 需要输入数据的动作类型
    const inputDataTypes = [
        'INPUT_ELEMENT',         // 输入文本
        'UPLOAD_ELEMENT',        // 上传文件路径
        'REQUEST_API',           // API 请求数据
        'VISIT_URL',            // URL 地址
        'SELECT_DROPDOWN',       // 下拉选项值
        'EXECUTE_SCRIPT',        // JavaScript 代码
        'SEND_KEYSTROKE',       // 按键值
        'WAIT_FOR_RESPONSE',    // API 响应匹配
        'EXECUTE_ASSERT',       // 断言表达式
        'SET_COOKIE',           // Cookie 值
        'MANAGE_STORAGE',       // Storage 操作
        'SCROLL_TO_POSITION',   // 滚动到指定位置
        'FORCE_WAIT_ELEMENT'    // 强制等待时间
    ]
    return inputDataTypes.includes(actionType)
}

// 获取输入数据的占位符
const getInputDataPlaceholder = (actionType: string) => {
    const placeholders: Record<string, string> = {
        'INPUT_ELEMENT': '请输入要填写的文本',
        'UPLOAD_ELEMENT': '请输入文件路径',
        'REQUEST_API': '请输入API请求数据',
        'VISIT_URL': '请输入要访问的URL',
        'SELECT_DROPDOWN': '请输入要选择的选项值',
        'EXECUTE_SCRIPT': '请输入要执行的JavaScript代码',
        'SEND_KEYSTROKE': '请输入按键值',
        'WAIT_FOR_RESPONSE': '请输入要等待的API响应匹配条件',
        'EXECUTE_ASSERT': '请输入断言表达式',
        'SET_COOKIE': '请输入Cookie值',
        'MANAGE_STORAGE': '请输入Storage操作数据',
        'SCROLL_TO_POSITION': '请输入滚动位置(x,y)',
        'FORCE_WAIT_ELEMENT': '请输入等待时间(ms)'
    }
    return placeholders[actionType] || '请输入数据'
}

// 删除步骤
const handleDelete = (row: TestStepDTO) => {
    const index = internalSteps.value.findIndex(item => item.id === row.id)
    if (index > -1) {
        internalSteps.value.splice(index, 1)
        // 重新计算 stepOrder
        internalSteps.value.forEach((step, idx) => {
            step.stepOrder = idx + 1
        })
    }
}
</script>

<style scoped>
.test-step-wrapper {
    width: 100%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.operation-buttons {
    margin-bottom: 16px;
    display: flex;
    gap: 8px;
    align-items: center;
}

/* 设置表格容器的样式 */
.el-table {
    flex: 1;
    overflow: auto;
}

/* 拖拽按钮样式 */
.drag-button {
    display: none;
    cursor: move;
}

.el-table__row:hover .drag-button {
    display: inline-block;
}

/* 确保表格在容器内可滚动 */
:deep(.el-table__body-wrapper) {
    overflow-y: auto !important;
}

/* 调整选择器输入框样式 */
.selector-input {
    :deep(.el-input-group__prepend) {
        padding: 0;
        background-color: var(--el-fill-color-blank);
        border-right: 0;
        
        .el-select {
            margin: 0;
        }
    }
    
    :deep(.el-select .el-input) {
        border: none;
        
        .el-input__wrapper {
            border: none;
            box-shadow: none !important;
            padding: 0 8px;
            height: 32px;
            line-height: 32px;
        }
    }
    
    :deep(.el-input__wrapper) {
        padding-left: 8px;
        height: 32px;
        line-height: 32px;
    }
    
    :deep(.el-input__prefix) {
        color: var(--el-text-color-secondary);
        margin-right: 4px;
    }

    /* 确保边框样式一致 */
    :deep(.el-input-group__prepend),
    :deep(.el-input__wrapper) {
        box-shadow: var(--el-input-border-color) 0px 0px 0px 1px inset;
    }

    /* hover 状态下的边框样式 */
    &:hover {
        :deep(.el-input-group__prepend),
        :deep(.el-input__wrapper) {
            box-shadow: var(--el-input-hover-border-color) 0px 0px 0px 1px inset;
        }
    }

    /* focus 状态下的边框样式 */
    :deep(.el-select.is-focus .el-input__wrapper),
    :deep(.el-input__wrapper.is-focus) {
        box-shadow: var(--el-input-focus-border-color) 0px 0px 0px 1px inset !important;
    }
}

/* 确保下拉框在表格中正确显示 */
:deep(.el-select__popper) {
    z-index: 3000;
}

/* 调整下拉选项的样式 */
:deep(.el-select-dropdown__item) {
    padding: 0 8px;
    height: 32px;
    line-height: 32px;
}
</style>
