server:
  port: 8003
  tomcat:
    max-swallow-size: -1
kf:
  git:
    local:
      directory: D:\work\branch1
  maven:
    settings: ./beacon-tower-accurate-test/src/main/resources/static/settings.xml
  pa:
#    interface: http://localhost:8080/interface/getAnnotation?taskId=
    interface: http://localhost:3002/seigneur/InterfaceDetailsList/
  compile:
    # 本地调试置为false，使用运行时的环境变量
    setJavaHome: false
spring:
  application:
    name: beacon-tower-accurate-test
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
  datasource:
    #   数据源基本配置
    username: root
    password: zc1234
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://************:3306/k_base?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false&allowPublicKeyRetrieval=true
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      validation-query: SELECT 1
  rabbitmq:
    queue: kf_analyze_queue
    listener:
      simple:
        retry:
          max-attempts: 1
    username: fs
    password: FS_adminuser@123
    host: test-mq-001.taimei.com
    port: 5672
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml


