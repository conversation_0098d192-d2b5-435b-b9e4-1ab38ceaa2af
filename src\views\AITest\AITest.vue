<template>
  <div>
    <!-- 查询区域 - 完全参考验证文档管理的样式 -->
    <div ref="searchEl" class="table-search-form">
      <el-row :gutter="15" class="clear-both">
        <el-col :span="24">
          <card-list :show-header="true" title="搜索" type="keyvalue">
            <template #btn>
              <el-button-group>
                <el-button v-prevent-default icon="el-icon-search" size="small" @click="selectData">
                  搜索
                </el-button>
              </el-button-group>
            </template>
            <template #keyvalue>
              <el-form ref="refForm" :model="form" class="card-list-form" size="small">
                <el-row :gutter="15">
                  <card-list-item prop="status" width="100px">
                    <template #key>任务状态</template>
                    <template #value>
                      <el-select
                          v-model="form.status"
                          clearable
                          filterable
                          placeholder="选择任务状态"
                      >
                        <el-option label="待执行" value="PENDING"/>
                        <el-option label="执行中" value="RUNNING"/>
                        <el-option label="已完成" value="SUCCESS"/>
                        <el-option label="执行失败" value="FAILED"/>
                        <el-option label="部分成功" value="PARTIAL_SUCCESS"/>
                      </el-select>
                    </template>
                  </card-list-item>
                  <card-list-item prop="dateRange" width="100px">
                    <template #key>创建时间</template>
                    <template #value>
                      <el-date-picker
                          v-model="form.dateRange"
                          type="daterange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          format="YYYY-MM-DD"
                          value-format="YYYY-MM-DD"
                      />
                    </template>
                  </card-list-item>
                </el-row>
              </el-form>
            </template>
          </card-list>
        </el-col>
      </el-row>
    </div>

    <!-- 操作区域 - 完全参考验证文档管理的样式 -->
    <div class="flex justify-between items-center mb-2">
      <div>
        <el-button-group>
          <el-button
              v-prevent-default
              type="primary"
              @click="showCreateDialog"
              :loading="loading"
          >
            创建任务
          </el-button>
          <el-button
              v-prevent-default
              @click="handleRefresh"
              :loading="refreshing"
          >
            刷新列表
          </el-button>
        </el-button-group>
      </div>
      <el-button v-prevent-default type="text" @click="toggleSearch">
        搜索
        <el-icon>
          <el-icon-arrow-up v-if="isShow"/>
          <el-icon-arrow-down v-else/>
        </el-icon>
      </el-button>
    </div>

    <!-- 列表区域 - 完全参考验证文档管理的样式 -->
    <el-table
        v-loading="tableLoading"
        :data="tableData.data"
        stripe
        style="width:100%"
    >
      <el-table-column label="文件MD5" prop="fileMd5" width="25%" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="file-md5">{{ row.fileMd5 || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="任务阶段" prop="currentStage" width="15%">
        <template #default="{ row }">
          <el-tag size="small" type="info">
            {{ getStageText(row.successStageCount, row.totalStageCount) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="overallStatus" width="15%">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.overallStatus)">
            {{ getStatusText(row.overallStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="评分" prop="overallScore" width="10%">
        <template #default="{ row }">
          <span v-if="row.overallScore !== null && row.overallScore !== undefined"
                :class="getScoreClass(row.overallScore)">
            {{ row.overallScore }}分
          </span>
          <span v-else class="no-score">-</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="20%">
        <template #default="{ row }">
          {{ formatDateTime(row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="15%" fixed="right">
        <template #default="{ row }">
          <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
          >
            查看
          </el-button>
          <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              :disabled="row.overallStatus === 'RUNNING'"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 - 参考验证文档管理的样式 -->
    <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :small="small"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    />

    <!-- 创建/编辑任务模态框 -->
    <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="80%"
        :close-on-click-modal="false"
        destroy-on-close
    >
      <el-tabs
          v-model="activeTestTab"
          type="border-card"
          class="test-tabs"
          @tab-change="handleTestTabChange"
      >
        <!-- API测试标签页 -->
        <el-tab-pane label="API接口测试" name="api">
          <template #label>
            <span class="tab-label">
              <el-icon><Connection/></el-icon>
              API接口测试
            </span>
          </template>
          <div class="tab-content">
            <APITest ref="apiTestRef"/>
          </div>
        </el-tab-pane>

        <!-- Markdown测试标签页 -->
        <el-tab-pane label="Markdown测试" name="markdown">
          <template #label>
            <span class="tab-label">
              <el-icon><Document/></el-icon>
              Markdown测试
            </span>
          </template>
          <div class="tab-content">
            <MarkdownTest ref="markdownTestRef"/>
          </div>
        </el-tab-pane>

        <!-- 性能测试标签页 -->
        <el-tab-pane label="性能测试" name="performance">
          <template #label>
            <span class="tab-label">
              <el-icon><TrendCharts/></el-icon>
              性能测试
            </span>
          </template>
          <div class="tab-content">
            <MarkdownPerformanceTest ref="performanceTestRef"/>
          </div>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveTask">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情模态框 -->
    <el-dialog
        v-model="detailDialogVisible"
        title="测试详情"
        width="90%"
        :close-on-click-modal="false"
        destroy-on-close
    >
      <div v-if="currentTaskDetail" class="detail-content">
        <!-- 基本信息 -->
        <el-card class="detail-info-card" shadow="never">
          <template #header>
            <span class="detail-card-title">基本信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">文件MD5:</span>
                <span class="detail-value">{{ currentTaskDetail.fileMd5 || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">任务状态:</span>
                <el-tag :type="getStatusTagType(currentTaskDetail.overallStatus)">
                  {{ getStatusText(currentTaskDetail.overallStatus) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">评分:</span>
                <span v-if="currentTaskDetail.overallScore !== null"
                      :class="getScoreClass(currentTaskDetail.overallScore)">
                  {{ currentTaskDetail.overallScore }}分
                </span>
                <span v-else>-</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 15px;">
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">任务阶段:</span>
                <span>{{ getStageText(currentTaskDetail.successStageCount, currentTaskDetail.totalStageCount) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">创建时间:</span>
                <span>{{ formatDateTime(currentTaskDetail.createTime) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">完成时间:</span>
                <span>{{ formatDateTime(currentTaskDetail.endTime) }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 数据对比区域 -->
        <el-card class="detail-diff-card" shadow="never" style="margin-top: 20px;">
          <template #header>
            <span class="detail-card-title">数据对比</span>
          </template>
          <div class="diff-container">
            <CodeDiff
                v-if="currentTaskDetail.stageResults && currentTaskDetail.stageResults.length > 0"
                :left-data="(currentTaskDetail.stageResults[0] && currentTaskDetail.stageResults[0].uatData) || ''"
                :right-data="(currentTaskDetail.stageResults[0] && currentTaskDetail.stageResults[0].testData) || ''"
                left-label="UAT环境"
                right-label="TEST环境"
            />
            <div v-else class="no-data">
              <el-empty description="暂无对比数据"/>
            </div>
          </div>
        </el-card>

        <!-- AI评估结果 -->
        <el-card v-if="currentTaskDetail.overallAiEvaluation" class="detail-ai-card" shadow="never"
                 style="margin-top: 20px;">
          <template #header>
            <span class="detail-card-title">AI评估结果</span>
          </template>
          <div class="ai-evaluation">
            <pre>{{ currentTaskDetail.overallAiEvaluation }}</pre>
          </div>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, computed, watch, reactive } from 'vue'
import {
    ElCard,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElButton,
    ElIcon,
    ElMessage,
    ElMessageBox,
    ElBadge,
    ElTooltip,
    ElTable,
    ElTableColumn,
    ElTag,
    ElProgress,
    ElPagination,
    ElDialog,
    ElTabs,
    ElTabPane
} from 'element-plus'
import {
    Refresh, Connection, Document, TrendCharts, Warning, SuccessFilled, Search, Plus, View, Edit, Delete
} from '@element-plus/icons-vue'
import { slide } from '@/utils/animate'

// 导入组件
import CardList from '@/components/CardList/CardList.vue'
import CardListItem from '@/components/CardList/CardListItem.vue'
import APITest from '@/components/AITest/APITest.vue'
import MarkdownTest from '@/components/AITest/MarkdownTest.vue'
import MarkdownPerformanceTest from '@/components/AITest/MarkdownPerformanceTest.vue'
import CodeDiff from '@/components/CodeDiff/CodeDiff.vue'

// 导入API和类型
import { getAITestList, startAITest, getAITestDetail, deleteAITestRecord } from '@/api/ai-test'
import type { AITestListRequest, AITestTaskStatus } from '@/api/ai-test/types'

// 导入Composables和Store
import { useAITest } from '@/utils/composables/useAITest'
import { useSSEConnection } from '@/utils/composables/useSSEConnection'
import { useAITestStore } from '@/stores/modules/aiTest'

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('创建任务')
const activeTestTab = ref('api')

// 搜索相关 - 参考验证文档管理
const isShow = ref(false)
const searchEl = ref(null)
const refForm = ref(null)
const tableLoading = ref(false)

// 分页相关 - 参考验证文档管理
let currentPage = ref(1)
let pageSize = ref(10)
const total = ref(0)
const small = ref(false)

// 表格数据 - 完全参考验证文档管理
const tableData = reactive({
    data: [] as any[]
})

// 搜索表单 - 完全参考验证文档管理
const form = reactive({
    status: '',
    dateRange: null as [string, string] | null
})

// 组件引用
const apiTestRef = ref<InstanceType<typeof APITest>>()
const markdownTestRef = ref<InstanceType<typeof MarkdownTest>>()
const performanceTestRef = ref<InstanceType<typeof MarkdownPerformanceTest>>()

// 详情模态框相关
const detailDialogVisible = ref(false)
const currentTaskDetail = ref<any>(null)

// 使用Composables和Store
const aiTest = useAITest()
const sseConnection = useSSEConnection()
const aiTestStore = useAITestStore()

// 搜索动画函数 - 参考验证文档管理
const toggleSearch = () => {
    isShow.value = !isShow.value
    slide(searchEl, isShow.value)
}

// 分页处理函数 - 参考验证文档管理
const handleSizeChange = (val: number) => {
    pageSize.value = val
    selectData()
}

const handleCurrentChange = (val: number) => {
    currentPage.value = val
    selectData()
}

// 数据查询函数 - 完全参考验证文档管理
const selectData = async () => {
    try {
        tableLoading.value = true

        const { status, dateRange } = form
        const params: AITestListRequest = {
            current: currentPage.value,
            size: pageSize.value,
            overallStatus: status as AITestTaskStatus,
            startTime: dateRange && dateRange[0],
            endTime: dateRange && dateRange[1]
        }

        console.log('AI测试列表查询参数:', params)

        const { data } = await getAITestList(params)
        console.log('AI测试列表API响应:', data)

        if (!data.isSuccess) {
            ElMessage.error(data.message || '查询失败')
            return
        }

        total.value = data.data.total || 0
        tableData.data = data.data.records || []

    } catch (error) {
        console.error('查询AI测试任务列表失败:', error)
        ElMessage.error('查询失败，请稍后重试')
    } finally {
        tableLoading.value = false
    }
}

// 计算属性
const hasActiveTasks = computed(() => aiTestStore.hasActiveTasks)
const activeTasksCount = computed(() => aiTestStore.activeTasks.length)
const hasErrors = computed(() => aiTest.hasError || sseConnection.state.status === 'error')

// 获取状态标签类型
const getStatusTagType = (status: AITestTaskStatus) => {
    switch (status) {
        case 'PENDING':
            return 'info'
        case 'RUNNING':
            return 'warning'
        case 'SUCCESS':
            return 'success'
        case 'FAILED':
            return 'danger'
        case 'PARTIAL_SUCCESS':
            return 'warning'
        default:
            return 'info'
    }
}

// 获取状态文本
const getStatusText = (status: AITestTaskStatus) => {
    switch (status) {
        case 'PENDING':
            return '待执行'
        case 'RUNNING':
            return '执行中'
        case 'SUCCESS':
            return '已完成'
        case 'FAILED':
            return '执行失败'
        case 'PARTIAL_SUCCESS':
            return '部分成功'
        default:
            return '未知'
    }
}

// 获取测试类型文本
const getTestTypeText = (testType: string) => {
    switch (testType) {
        case 'api':
            return 'API测试'
        case 'markdown':
            return 'Markdown测试'
        case 'performance':
            return '性能测试'
        default:
            return '未知'
    }
}

// 格式化日期时间
const formatDateTime = (dateTime: string | Date | null) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取任务阶段文本
const getStageText = (successCount: number, totalCount: number) => {
    if (!totalCount) return '-'
    return `${successCount}/${totalCount}`
}

// 获取评分样式类
const getScoreClass = (score: number) => {
    if (score >= 90) return 'score-excellent'
    if (score >= 80) return 'score-good'
    if (score >= 60) return 'score-normal'
    return 'score-poor'
}

// 重置搜索 - 完全参考验证文档管理
const resetForm = () => {
    form.status = ''
    form.dateRange = null
    currentPage.value = 1
    selectData()
}

// 刷新列表
const handleRefresh = () => {
    refreshing.value = true
    selectData().finally(() => {
        refreshing.value = false
    })
}

// 显示创建对话框
const showCreateDialog = () => {
    dialogTitle.value = '创建任务'
    dialogVisible.value = true
    activeTestTab.value = 'api'
}

// SSE连接处理
const handleConnectSSE = async (task: any) => {
    try {
        ElMessage.info(`开始监控任务 ${task.taskId} 的进度`)

        // 使用SSE连接监控任务进度
        await sseConnection.connect(task.taskId, {
            onProgress: (data) => {
                console.log('任务进度更新:', data)
                // 更新任务进度
                const taskIndex = tableData.data.findIndex(t => t.taskId === task.taskId)
                if (taskIndex !== -1) {
                    tableData.data[taskIndex].progress = data.progress || 0
                }
            },
            onAiEvaluation: (data) => {
                console.log('AI评估结果:', data)
            },
            onAiResult: (data) => {
                console.log('AI结果:', data)
            },
            onStageComplete: (data) => {
                console.log('阶段完成:', data)
                ElMessage.success(`任务 ${task.taskId} 阶段完成: ${data.stage}`)
            },
            onError: (error) => {
                console.error('SSE连接错误:', error)
                ElMessage.error(`任务监控出错: ${error}`)
            },
            onClose: () => {
                console.log('SSE连接关闭')
                ElMessage.info(`任务 ${task.taskId} 监控连接已关闭`)
                // 刷新任务列表获取最新状态
                selectData()
            }
        })

    } catch (error) {
        console.error('连接SSE失败:', error)
        ElMessage.error('连接任务监控失败')
    }
}

// 处理测试标签页切换
const handleTestTabChange = (tabName: string) => {
    console.log('切换到测试标签页:', tabName)
    // 保存用户偏好
    localStorage.setItem('aitest_active_test_tab', tabName)
}

// 查看任务详情
const handleViewDetail = async (task: any) => {
    try {
    // 打开详情模态框
        detailDialogVisible.value = true
        currentTaskDetail.value = task

        // 获取详细数据
        const { data } = await getAITestDetail(task.id)
        if (data.isSuccess) {
            currentTaskDetail.value = { ...task, ...data.data }

            // 根据任务状态判断是否建立SSE连接
            if (task.overallStatus === 'RUNNING' || task.overallStatus === 'PENDING') {
                await connectSSEForDetail(task.taskId)
            }
        } else {
            ElMessage.error(data.message || '获取任务详情失败')
        }
    } catch (error) {
        console.error('获取任务详情失败:', error)
        ElMessage.error('获取任务详情失败')
    }
}

// 删除任务
const handleDelete = async (task: any) => {
    try {
        await ElMessageBox.confirm(
            '确定要删除这条测试记录吗？删除后将无法恢复。',
            '确认删除',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        )

        // 调用删除API
        await deleteAITestRecord(task.id)
        ElMessage.success('删除成功')
        selectData() // 刷新列表

    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除任务失败:', error)
            ElMessage.error('删除失败')
        }
    }
}

// 为详情模态框建立SSE连接
const connectSSEForDetail = async (taskId: string) => {
    try {
        await sseConnection.connect(taskId, {
            onProgress: (data) => {
                console.log('详情页面收到进度更新:', data)
                // 更新当前任务详情
                if (currentTaskDetail.value) {
                    // 根据SSE数据更新任务状态
                    currentTaskDetail.value = { ...currentTaskDetail.value, ...data.data }
                }
            },
            onAiEvaluation: (data) => {
                console.log('详情页面收到AI评估:', data)
                if (currentTaskDetail.value) {
                    currentTaskDetail.value.overallAiEvaluation = data.data.evaluation
                    currentTaskDetail.value.overallScore = data.data.score
                }
            },
            onAiResult: (data) => {
                console.log('详情页面收到AI结果:', data)
                if (currentTaskDetail.value) {
                    currentTaskDetail.value = { ...currentTaskDetail.value, ...data.data }
                }
            },
            onStageComplete: (data) => {
                console.log('详情页面收到阶段完成:', data)
                if (currentTaskDetail.value && currentTaskDetail.value.stageResults) {
                    // 更新阶段结果
                    const stageIndex = currentTaskDetail.value.stageResults.findIndex(
                        (stage: any) => stage.stageName === data.data.stageName
                    )
                    if (stageIndex >= 0) {
                        currentTaskDetail.value.stageResults[stageIndex] = {
                            ...currentTaskDetail.value.stageResults[stageIndex],
                            ...data.data
                        }
                    }
                }
            },
            onError: (error) => {
                console.error('详情页面SSE连接错误:', error)
                ElMessage.error('实时连接出现错误')
            },
            onClose: () => {
                console.log('详情页面SSE连接已关闭')
            }
        })
    } catch (error) {
        console.error('建立详情页面SSE连接失败:', error)
        ElMessage.error('建立实时连接失败')
    }
}



// 生命周期钩子
onMounted(() => {
    // 初始化加载数据
    selectData()

    // 恢复用户偏好的测试标签页
    const savedTab = localStorage.getItem('aitest_active_test_tab')
    if (savedTab) {
        activeTestTab.value = savedTab
    }

    // 添加键盘快捷键
    const handleKeydown = (event: KeyboardEvent) => {
        if (event.ctrlKey) {
            switch (event.key) {
                case 'r':
                case 'R':
                    event.preventDefault()
                    handleRefresh()
                    break
                case 'n':
                case 'N':
                    event.preventDefault()
                    showCreateDialog()
                    break
            }
        }
    }

    document.addEventListener('keydown', handleKeydown)

    // 清理函数
    onUnmounted(() => {
        document.removeEventListener('keydown', handleKeydown)
        // 关闭所有SSE连接
        sseConnection.disconnect()
    })
})

// 保存任务
const handleSaveTask = () => {
    // 这里应该收集表单数据并保存
    ElMessage.success('保存成功')
    dialogVisible.value = false
    selectData()
}
</script>

<style lang="postcss" scoped>
/* 完全参考验证文档管理的样式 */
.table-search-form {
  overflow: hidden;
  height: 0;
}

::v-deep(.el-card__header) {
  padding: 7px 15px;
}

::v-deep(.el-button) {
  padding: 4px 6px;
  border-radius: 3px;
}

/* 文件MD5样式 */
.file-md5 {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #666;
}

/* 评分样式 */
.score-excellent {
  color: #67c23a;
  font-weight: bold;
}

.score-good {
  color: #409eff;
  font-weight: bold;
}

.score-normal {
  color: #e6a23c;
  font-weight: bold;
}

.score-poor {
  color: #f56c6c;
  font-weight: bold;
}

.no-score {
  color: #c0c4cc;
}

/* 详情模态框样式 */
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-info-card,
.detail-diff-card,
.detail-ai-card {
  margin-bottom: 0;
}

.detail-card-title {
  font-weight: bold;
  color: #303133;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.detail-label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.detail-value {
  color: #303133;
}

.diff-container {
  min-height: 300px;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.ai-evaluation {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
}

.ai-evaluation pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #303133;
}
</style>
