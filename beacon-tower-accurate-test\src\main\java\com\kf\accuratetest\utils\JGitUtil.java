package com.kf.accuratetest.utils;

import com.kf.accuratetest.entity.CommitMessage;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.ResetCommand;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.lib.Constants;
import org.eclipse.jgit.lib.Ref;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.revwalk.RevCommit;
import org.eclipse.jgit.revwalk.RevWalk;
import org.eclipse.jgit.revwalk.filter.RevFilter;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Slf4j
public class JGitUtil {
    //JGit下载代码
    public static void downloadCode(String url, String branch, String directory, String username, String password) throws Exception {
        //创建git对象
        Git git = Git.cloneRepository()
                .setURI(url)
                .setBranch(branch)
                .setDirectory(new File(directory))
                .setCredentialsProvider(new UsernamePasswordCredentialsProvider(username, password))
                .call();
        //关闭资源
        git.close();
    }

    //获得分支最新地提交记录号
    public static String lastCommitRecord(String url, String branch, String username, String password) {
        Collection<Ref> refList;
        try {
            refList = Git.lsRemoteRepository().setRemote(url).setCredentialsProvider(new UsernamePasswordCredentialsProvider(username, password)).call();
        } catch (Exception e) {
            return null;
        }
        for (Ref ref : refList) {
            //获得提交记录号
            if (ref.getName().equals("refs/heads/" + branch)) {
                return ref.getObjectId().getName();
            }
        }
        log.error("{}的提交记录号获取失败", branch);
        return null;
    }

    /**
     * 获得默认分支名称
     */
    public static String getHEADToBranch(String url, String username, String password) throws Exception {
        return Git.lsRemoteRepository().setRemote(url).setCredentialsProvider(new UsernamePasswordCredentialsProvider(username, password)).callAsMap()
                .get("HEAD").getTarget().getName().substring("refs/heads/".length());
    }

    /**
     * 获得本地仓库的分支的所有提交记录
     */
    public static List<CommitMessage> getCommitMessages(String directory, String branch, String userName, String PassWord, int endNumber) throws IOException, GitAPIException {
        List<CommitMessage> commitMessages;
        CommitMessage commitMessage;
        Iterable<RevCommit> commits;
        File file = new File(directory);
        try (Git git = Git.open(file);
             Repository repository = git.getRepository();
             RevWalk walk = new RevWalk(repository)) {
            // 更新代码
            git.fetch().setCredentialsProvider(new UsernamePasswordCredentialsProvider(userName, PassWord)).call();
            commitMessages = new ArrayList<>();
            commits = git.log().all().call();

            for (RevCommit commit : commits) {
                commitMessage = new CommitMessage();
                boolean foundInThisBranch = false;
                RevCommit targetCommit = walk.parseCommit(commit.getId());
                for (Map.Entry<String, Ref> e : repository.getAllRefs().entrySet()) {
                    if (e.getKey().startsWith("refs/remotes/origin")) {
                        if (walk.isMergedInto(targetCommit, walk.parseCommit(e.getValue().getObjectId()))) {
                            String foundInBranch = e.getValue().getTarget().getName();
                            if (foundInBranch.contains(branch)) {
                                foundInThisBranch = true;
                                break;
                            }
                        }
                    }
                }
                if (foundInThisBranch) {
                    commitMessage.setCommitId(commit.getName());
                    commitMessage.setCommitIdent(commit.getAuthorIdent().getName());
                    commitMessage.setCommitMessage(commit.getFullMessage());
                    commitMessages.add(commitMessage);
                }
                if (commitMessages.size() == endNumber) {
                    break;
                }
            }
        }
        return commitMessages;
    }

    /**
     * 获得本地仓库的分支上一次记录
     */
    public static CommitMessage getPreviousCommitMessage(String directory) throws IOException, GitAPIException {
        CommitMessage commitMessage = null;
        File file = new File(directory);
        try (Git git = Git.open(file);
             Repository repository = git.getRepository()) {
            RevWalk walk = new RevWalk(repository);
            walk.markStart(walk.parseCommit(repository.resolve(Constants.HEAD)));
//            walk.setRevFilter(RevFilter.NO_MERGES);
            RevCommit commit = walk.next();
            if (commit != null) {
                commit = walk.next();
                if (commit != null) {
                    commitMessage = new CommitMessage();
                    commitMessage.setCommitId(commit.getName());
                    commitMessage.setCommitIdent(commit.getAuthorIdent().getName());
                    commitMessage.setCommitMessage(commit.getFullMessage());
                }
            }
//            //获取到第一个非合并提交后结束循环
//            RevCommit commit;
//            while ((commit = walk.next()) != null) {
//                if (commit.getParentCount() == 1) { // 只选择没有父提交的提交，即非合并提交
//                    commitMessage = new CommitMessage();
//                    commitMessage.setCommitId(commit.getName());
//                    commitMessage.setCommitIdent(commit.getAuthorIdent().getName());
//                    commitMessage.setCommitMessage(commit.getFullMessage());
//                    break; // 获取到第一个非合并提交后结束循环
//                }
//            }
            walk.close();
        }
        return commitMessage;
    }

    public static List<CommitMessage> getCommitMessages333(String directory, String branch, String userName, String PassWord, int endNumber) throws IOException, GitAPIException {
        Repository repository;
        List<CommitMessage> commitMessages;
        CommitMessage commitMessage;
        Iterable<RevCommit> commits;
        try (Git git = Git.open(new File(directory))) {
            repository = git.getRepository();
            //更新代码
            git.fetch().setCredentialsProvider(new UsernamePasswordCredentialsProvider(userName, PassWord)).call();
            commitMessages = new ArrayList<>();
            commits = git.log().all().call();
        }
        RevWalk walk = new RevWalk(repository);
        for (RevCommit commit : commits) {
            commitMessage = new CommitMessage();
            boolean foundInThisBranch = false;
            RevCommit targetCommit = walk.parseCommit(commit.getId());
            for (Map.Entry<String, Ref> e : repository.getAllRefs().entrySet()) {
                if (e.getKey().startsWith("refs/remotes/origin")) {
                    if (walk.isMergedInto(targetCommit, walk.parseCommit(e.getValue().getObjectId()))) {
                        String foundInBranch = e.getValue().getTarget().getName();
//                        foundInBranch = foundInBranch.replace("refs/heads","");
                        if (foundInBranch.contains(branch)) {
                            // 如果只想获取commit merge的记录，则在此处添加一个条件即可
                            // if(targetCommit.getParents().length==2) {
                            foundInThisBranch = true;
                            break;
                        }
                    }
                }
            }
            if (foundInThisBranch) {
                commitMessage.setCommitId(commit.getName());
                commitMessage.setCommitIdent(commit.getAuthorIdent().getName());
                commitMessage.setCommitMessage(commit.getFullMessage());
                commitMessages.add(commitMessage);
            }
            if (commitMessages.size() == endNumber) {
                break;
            }
        }
        //关闭资源
        repository.close();
        return commitMessages;
    }

    /**
     * 获获最近的非合并提交的父提交的提交消息
     */
    public static CommitMessage getPreviousCommitMessage2(String directory) throws IOException, GitAPIException {
        CommitMessage commitMessage = null;
        File file = new File(directory);
        try (Git git = Git.open(file);
             Repository repository = git.getRepository()) {
            RevWalk walk = new RevWalk(repository);
            walk.markStart(walk.parseCommit(repository.resolve(Constants.HEAD)));
            walk.setRevFilter(RevFilter.NO_MERGES);
            RevCommit commit = walk.next();
            if (commit != null) {
                RevCommit parent = walk.parseCommit(commit.getParent(0));
                commitMessage = new CommitMessage();
                commitMessage.setCommitId(parent.getName());
                commitMessage.setCommitIdent(parent.getAuthorIdent().getName());
                commitMessage.setCommitMessage(parent.getFullMessage());
            }
            walk.close();
        }
        return commitMessage;
    }

    /**
     * 切换分支到指定提交记录
     */
    public static void checkout(String directory, String commitId) throws IOException, GitAPIException {
        try (Git git = Git.open(new File(directory))) {
//        git.checkout().setName(branch).setStartPoint(commitId).call();
            //迁出修订版本
//            git.checkout().setName(branch).setStartPoint(commitId).setForceRefUpdate(true).call();
            git.reset().setMode(ResetCommand.ResetType.HARD).setRef(commitId).call();
        }
    }

    /**
     * 使用账号密码更新代码
     */
    public static void fetch(String directory, String username, String password) throws IOException, GitAPIException {
        try (Git git = Git.open(new File(directory))) {
            git.fetch().setCredentialsProvider(new UsernamePasswordCredentialsProvider(username, password)).call();
        }
    }
}