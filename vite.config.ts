import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'node:path'

const setAlias = (alias: any[][]) => {
    return alias.map((v: any[]) => {
        return { find: v[0], replacement: path.resolve(__dirname, v[1]) }
    })
}
const proxy = (list: any[]) => {
    const obj: any = {}
    list.forEach(v => {
        obj[v[0]] = {
            target: v[1],
            changeOrigin: true,
            rewrite: (path: string) => path.replace(new RegExp(`^${v[0]}`), ''),
            ...(/^https:\/\//.test(v[1]) ? { secure: false } : {})
        }
    })
    return obj
}
// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, process.cwd(), '')

    return {
        base: '/seigneur',
        plugins: [
            vue()
        ],
        resolve: {
            alias: {
                '@': fileURLToPath(new URL('./src', import.meta.url))
            }
        },
        server: {
            proxy: env.VITE_PROXY ? proxy(JSON.parse(env.VITE_PROXY)) : {},
            port: env.VITE_PORT ? Number(env.VITE_PORT) : 3002,
            hmr: { overlay: false }
        }
    }


})
