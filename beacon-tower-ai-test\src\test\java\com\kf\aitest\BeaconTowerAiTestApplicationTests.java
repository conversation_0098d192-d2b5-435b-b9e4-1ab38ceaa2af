package com.kf.aitest;

import com.langfuse.client.LangfuseClient;
import com.langfuse.client.core.LangfuseClientApiException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
class BeaconTowerAiTestApplicationTests {

	@Autowired
	private LangfuseClient langfuseClient;

	@Test
	void contextLoads() {
	}


	@Test
	void testGetSessionInfoFromLangfuse() {

		System.out.printf(langfuseClient.prompts().list().toString());
	}

}
