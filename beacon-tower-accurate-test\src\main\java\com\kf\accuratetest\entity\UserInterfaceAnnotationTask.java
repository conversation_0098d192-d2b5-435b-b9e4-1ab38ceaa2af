package com.kf.accuratetest.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("t_user_interface_annotation_task")
public class UserInterfaceAnnotationTask implements Serializable {
    private static final long serialVersionUID = 715514383086582057L;
    /**
     * 用户id
     */
    private String userId;
    /**
     * task_id
     */
    private String taskId;
    /**
     * 仓库地址
     */
    private String repositoryUrl;
    /**
     * 分支名称
     */
    private String branch;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 是否删除  0未删除 1删除
     */
    private Integer isDeleted;

}

