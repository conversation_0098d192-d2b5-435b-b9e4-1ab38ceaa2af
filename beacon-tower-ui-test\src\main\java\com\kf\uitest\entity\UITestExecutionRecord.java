package com.kf.uitest.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("test_execution_record")
public class UITestExecutionRecord extends Model<UITestExecutionRecord> {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "execution_id", type = IdType.INPUT)
    private String executionId;

    @TableField(value = "scene_ids")
    private String sceneIdsJson;

    @TableField("status")
    private String status;

    @TableField(value = "result")
    private String resultJson;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("end_time")
    private LocalDateTime endTime;
}