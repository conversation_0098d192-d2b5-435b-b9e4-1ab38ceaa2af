package com.kf.baosi.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class CreateBugRequest {

    @NotNull(message = "项目key不能为空")
    private String projectKey;

    // 缺陷标题
    @NotNull(message = "缺陷标题不能为空")
    private String summary;

    // 严重程度
    @NotNull(message = "严重程度不能为空")
    private Integer severity;

    // 优先级
    @NotNull(message = "优先级不能为空")
    private Integer priority;

    // 复现情况
    private Integer bugReproduction;

    // 原因分类
    private Integer bugReason;

    // 重现步骤
    private String reproductionSteps;

    // 期望结果
    private String expectedResult;

    // 实际结果
    private String actualResult;

    // 缺陷实际负责人
    @NotNull(message = "缺陷实际负责人不能为空")
    private List<String> defectAssignee;

    // 研发负责人
    private String developerInCharge;

    // 模块
    private List<String> components;

    // 修复版本
    @NotNull(message = "修复版本不能为空")
    private List<String> fixVersions;

    // 描述
    private String description;

    // 附件文件ID
    private List<String> attachmentFileIds;

    // 运行ID
    private String runId;

    // 步骤ID
    private String stepId;
}
