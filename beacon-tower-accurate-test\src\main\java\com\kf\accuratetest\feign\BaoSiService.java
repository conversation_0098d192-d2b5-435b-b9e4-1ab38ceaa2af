package com.kf.accuratetest.feign;

import com.kf.accuratetest.common.ResponseDoMain;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.multipart.MultipartFile;

@FeignClient(value = "beacon-tower-bao-si")
public interface BaoSiService {

    @PostMapping(value = "/file/xmlUpload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ResponseDoMain fileUpload(@RequestHeader("userId") String userId, MultipartFile file);

    @GetMapping("/file/getXmlFileList")
    ResponseDoMain queryXmlByUserId(@RequestHeader("userId") String userId);

    @PostMapping("/file/deleteFile")
    ResponseDoMain deleteFile(@RequestHeader("userId") String userId, String fileId);

    @GetMapping("/file/getInterfacePageTemplateFile")
    Response getInterfacePageTemplateFile();


}
