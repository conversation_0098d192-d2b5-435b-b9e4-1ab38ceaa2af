package com.kf.accuratetest.utils;

import cn.hutool.core.collection.CollUtil;
import com.kf.accuratetest.entity.MethodInstruction;
import org.objectweb.asm.ClassReader;
import org.objectweb.asm.Opcodes;
import org.objectweb.asm.tree.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class AsmUtil {
    public static String getAnnotationValue(AnnotationNode classAnnotation, String annotationKey) {
        String annotationValue = "";
        List<Object> values = classAnnotation.values;
        if (CollUtil.isEmpty(values)) return annotationValue;
        Object mappingValue = values.get(values.indexOf(annotationKey) + 1);
        if (mappingValue instanceof List) {
            return ((List<Object>) mappingValue).get(0) + "";
        } else {
            return mappingValue + "";
        }
    }

    public static String methodAllName(ClassNode classNode, MethodNode methodNode) {
        return classNode.name +
                "." +
                methodNode.name +
                "[" + methodNode.desc.substring(1, methodNode.desc.lastIndexOf(")")) + "]";
    }

    public static List<MethodInstruction> methodInvokeInstructions(MethodNode methodNode) {
        List<MethodInstruction> list = new ArrayList<>();
        InsnList instructions = methodNode.instructions;

        for (AbstractInsnNode i : instructions) {
            int opcode = i.getOpcode();
            if (opcode == 182 || opcode == 183 || opcode == 184 || opcode == 185 || opcode == 186) {
                ASMMethodVisitor visitor = new ASMMethodVisitor(Opcodes.ASM9);
                i.accept(visitor);
                MethodInstruction instruction = visitor.getInstructions();
                if (instruction != null && instruction.getOwner() != null) {
                    //将方法调用指令装入list
                    list.add(instruction);
                }
            }
        }
        return list.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 判断类中是否包含某个方法
     */
    public static Boolean classContainsMethod(ClassNode cn, String methodName, String methodParam) {
        List<MethodNode> methodNodes = cn.methods;
        for (MethodNode mn : methodNodes) {
            if (mn.name.equals(methodName) && mn.desc.equals(methodParam)) {
                return true;
            }
        }
        return false;
    }

    public static MethodNode classMatchingMethod(ClassNode cn, String methodName, String methodParam) {
        List<MethodNode> methodNodes = cn.methods;
        for (MethodNode mn : methodNodes) {
            if (mn.name.equals(methodName) && mn.desc.equals(methodParam)) {
                return mn;
            }
        }
        return null;
    }

    public static List<String> getInterfaceName(List<List<String>> links) {
        if (CollUtil.isEmpty(links)) return null;
        return links.stream().map(item -> item.get(0)).distinct().collect(Collectors.toList());
    }

    /**
     * 遍历所有class文件，将所有ClassNode装入list，将所有接口与它的实现类装入map
     */
    public static void buildProjectClassNode(File file, List<ClassNode> classNodes, Map<String, List<String>> interfaceMap) {
        if (!file.exists()) {
            return;
        }
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files != null) {
                for (File f : files) {
                    buildProjectClassNode(f, classNodes, interfaceMap);
                }
            }
        } else if (file.getName().endsWith(".class")) {
            try (FileInputStream fileInputStream = new FileInputStream(file.getAbsolutePath())) {
                ClassReader cr = new ClassReader(fileInputStream);
                ClassNode cn = new ClassNode();
//                cr.accept(cn, ClassReader.SKIP_FRAMES);
                cr.accept(cn, 0);
                classNodes.add(cn);

                //获取接口对应的实现类
                List<String> interfaces = cn.interfaces;
                if (!interfaces.isEmpty()) {
                    for (String anInterface : interfaces) {
                        //如果map中已经存在该接口，就覆盖它
                        List<String> list;
                        if (interfaceMap.containsKey(anInterface)) {
                            list = interfaceMap.get(anInterface);
                        } else {
                            list = new ArrayList<>();
                        }
                        list.add(cn.name);
                        interfaceMap.put(anInterface, list);
                    }
                }
            } catch (IOException e) {
                //忽略任何异常
                e.printStackTrace();
            }
        }
    }
}
