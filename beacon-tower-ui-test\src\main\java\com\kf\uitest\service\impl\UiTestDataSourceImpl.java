package com.kf.uitest.service.impl;

import com.kf.uitest.dao.UiTestDataSourceMapper;
import com.kf.uitest.exception.TestExecutionException;
import com.kf.uitest.service.UiTestDataSource;
import com.kf.uitest.utils.DataSourceValidator;
import com.kf.uitest.utils.PasswordUtils;
import com.kf.uitest.utils.SecurityUtils;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.sql.DataSource;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class UiTestDataSourceImpl implements UiTestDataSource {

    @Resource
    private UiTestDataSourceMapper dataSourceMapper;

    @Resource
    private DataSourceValidator validator;

    @Resource
    private PasswordUtils passwordUtils;

    // 数据源缓存
    private final Map<String, DataSource> dataSourceCache = new ConcurrentHashMap<>();

    @Override
    public List<Map<String, Object>> executeQuery(String dbSourceName, String sql, Map<String, Object> params) {
        try {
            DataSource dataSource = getDataSource(dbSourceName);
            try (Connection conn = dataSource.getConnection()) {
                try (PreparedStatement stmt = prepareStatement(conn, sql, params)) {
                    try (ResultSet rs = stmt.executeQuery()) {
                        return resultSetToList(rs);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Failed to execute query: {} [source={}]", sql, dbSourceName, e);
            throw new TestExecutionException("Database query failed: " + e.getMessage());
        }
    }

    @Override
    public int executeUpdate(String dbSourceName, String sql, Map<String, Object> params) {
        try {
            DataSource dataSource = getDataSource(dbSourceName);
            try (Connection conn = dataSource.getConnection()) {
                try (PreparedStatement stmt = prepareStatement(conn, sql, params)) {
                    return stmt.executeUpdate();
                }
            }
        } catch (Exception e) {
            log.error("Failed to execute update: {} [source={}]", sql, dbSourceName, e);
            throw new TestExecutionException("Database update failed: " + e.getMessage());
        }
    }

    /**
     * 获取数据源
     */
    private DataSource getDataSource(String name) {
        return dataSourceCache.computeIfAbsent(getCacheKey(name), key -> createDataSource(name));
    }

    /**
     * 创建数据源
     */
    private DataSource createDataSource(String name) {
        try {
            com.kf.uitest.entity.UiTestDataSource config = getDataSourceConfig(name);
            if (config == null) {
                throw new TestExecutionException("Database source not found: " + name);
            }

            validator.validate(config);

            // 解密密码
            String password = config.getPassword();
            if (password.startsWith("ENC(")) {
                password = passwordUtils.decryptDbPassword(
                        password.substring(4, password.length() - 1));
            }

            HikariConfig hikariConfig = new HikariConfig();
            hikariConfig.setJdbcUrl(config.getUrl());
            hikariConfig.setUsername(config.getUsername());
            hikariConfig.setPassword(password);
            hikariConfig.setDriverClassName(config.getDriverClass());

            // 连接池配置
            hikariConfig.setMaximumPoolSize(5);
            hikariConfig.setMinimumIdle(1);
            hikariConfig.setIdleTimeout(300000);
            hikariConfig.setConnectionTimeout(20000);
            hikariConfig.setPoolName("UiTest-" + name);

            return new HikariDataSource(hikariConfig);

        } catch (Exception e) {
            throw new TestExecutionException("Failed to create database source: " + name, e);
        }
    }

    /**
     * 获取数据源配置
     */
    private com.kf.uitest.entity.UiTestDataSource getDataSourceConfig(String name) {
        Assert.hasText(name, "Data source name must not be empty");
        return dataSourceMapper.findByNameAndProjectIdAndUserId(
                name,
                SecurityUtils.getCurrentProjectId(),
                SecurityUtils.getCurrentUserId()
        );
    }

    /**
     * 获取缓存键
     */
    private String getCacheKey(String name) {
        return String.format("%s_%s_%s",
                name,
                SecurityUtils.getCurrentProjectId(),
                SecurityUtils.getCurrentUserId()
        );
    }

    /**
     * 创建预处理语句并设置参数
     */
    private PreparedStatement prepareStatement(Connection conn, String sql, Map<String, Object> params) throws SQLException {
        PreparedStatement stmt = conn.prepareStatement(sql);
        if (params != null && !params.isEmpty()) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                sql = sql.replace(":" + entry.getKey(), "?");
            }
            stmt = conn.prepareStatement(sql);

            int paramIndex = 1;
            for (Object value : params.values()) {
                setParameter(stmt, paramIndex++, value);
            }
        }
        return stmt;
    }

    /**
     * 设置预处理语句参数
     */
    private void setParameter(PreparedStatement stmt, int index, Object value) throws SQLException {
        if (value == null) {
            stmt.setNull(index, Types.NULL);
        } else if (value instanceof String) {
            stmt.setString(index, (String) value);
        } else if (value instanceof Integer) {
            stmt.setInt(index, (Integer) value);
        } else if (value instanceof Long) {
            stmt.setLong(index, (Long) value);
        } else if (value instanceof Double) {
            stmt.setDouble(index, (Double) value);
        } else if (value instanceof Boolean) {
            stmt.setBoolean(index, (Boolean) value);
        } else if (value instanceof Date) {
            stmt.setDate(index, (Date) value);
        } else if (value instanceof Timestamp) {
            stmt.setTimestamp(index, (Timestamp) value);
        } else {
            stmt.setString(index, value.toString());
        }
    }

    /**
     * 将ResultSet转换为List<Map>
     */
    private List<Map<String, Object>> resultSetToList(ResultSet rs) throws SQLException {
        List<Map<String, Object>> results = new ArrayList<>();
        ResultSetMetaData md = rs.getMetaData();
        int columns = md.getColumnCount();

        while (rs.next()) {
            Map<String, Object> row = new HashMap<>();
            for (int i = 1; i <= columns; i++) {
                row.put(md.getColumnLabel(i), rs.getObject(i));
            }
            results.add(row);
        }
        return results;
    }
}