/**
 * UI测试服务 API
 */

import request from '@/utils/request'
import type { AxiosResponse } from 'axios'
import type {
    Suite,
    TestCase,
    CreateEnvironmentDTO,
    UpdateEnvironmentDTO,
    EnvironmentWithVariablesDTO
} from './types'

const serviceName = '/api/beacon-tower'
const uiTestName = 'ui-test'

// UI测试服务API端点
const uiTestApi = {
    // 创建测试场景
    createTestSuite: `${serviceName}/${uiTestName}/test-tree/createTestSuite`,
    // 创建测试用例
    createUITestCase: `${serviceName}/${uiTestName}/test-tree/createTestCase`,
    // 获取测试场景树
    getTestTree: `${serviceName}/${uiTestName}/test-tree/getTestTree`,
    // 获取测试用例的步骤
    getTestSteps: `${serviceName}/${uiTestName}/test-step/getTestSteps`,
    // 获取步骤的操作类型
    getActionTypes: `${serviceName}/${uiTestName}/test-step/getActionTypes`,
    // 获取环境和环境变量
    getAllEnvironmentsWithVariables: `${serviceName}/${uiTestName}/environment/getAllEnvironmentsWithVariables`,
    // 获取浏览器列表
    getBrowsersOptions: `${serviceName}/${uiTestName}/browsers/options`,
    // 创建环境配置
    createEnvironment: `${serviceName}/${uiTestName}/environment/createEnvironment`,
    // 更新环境配置
    updateEnvironment: `${serviceName}/${uiTestName}/environment/updateEnvironment`,
    // 删除环境配置
    deleteEnvironment: `${serviceName}/${uiTestName}/environment/deleteEnvironment`
}

/**
 * 创建测试套件
 */
export function createTestSuite(suiteRequest: Suite): Promise<AxiosResponse<IResponse>> {
    return request({
        url: uiTestApi.createTestSuite,
        method: 'post',
        data: suiteRequest
    })
}

/**
 * 获取测试树
 */
export function getTestTree(projectId: number): Promise<AxiosResponse<IResponse>> {
    return request({
        url: uiTestApi.getTestTree,
        method: 'get',
        params: { projectId: projectId }
    })
}

/**
 * 创建UI测试用例
 */
export function createUITestCase(testCaseRequest: TestCase): Promise<AxiosResponse<IResponse>> {
    return request({
        url: uiTestApi.createUITestCase,
        method: 'post',
        data: testCaseRequest
    })
}

/**
 * 获取测试步骤
 */
export function getTestSteps(testCaseId: string): Promise<AxiosResponse<IResponse>> {
    return request({
        url: uiTestApi.getTestSteps,
        method: 'get',
        params: { testCaseId: testCaseId }
    })
}

/**
 * 获取操作类型
 */
export function getActionTypes(): Promise<AxiosResponse<IResponse>> {
    return request({
        url: uiTestApi.getActionTypes,
        method: 'get'
    })
}

/**
 * 获取所有环境和变量
 */
export function getAllEnvironmentsWithVariables(projectId: number): Promise<AxiosResponse<IResponse>> {
    return request({
        url: uiTestApi.getAllEnvironmentsWithVariables,
        method: 'get',
        params: { projectId: projectId }
    })
}

/**
 * 获取浏览器选项
 */
export function getBrowsersOptions(): Promise<AxiosResponse<IResponse>> {
    return request({
        url: uiTestApi.getBrowsersOptions,
        method: 'get'
    })
}

/**
 * 创建环境配置
 */
export function createEnvironment(data: CreateEnvironmentDTO): Promise<AxiosResponse<IResponse<EnvironmentWithVariablesDTO>>> {
    return request({
        url: uiTestApi.createEnvironment,
        method: 'post',
        data: data
    })
}

/**
 * 更新环境配置
 */
export function updateEnvironment(data: UpdateEnvironmentDTO): Promise<AxiosResponse<IResponse<EnvironmentWithVariablesDTO>>> {
    return request({
        url: uiTestApi.updateEnvironment,
        method: 'post',
        data: data
    })
}

/**
 * 删除环境配置
 */
export function deleteEnvironment(environmentId: number): Promise<AxiosResponse<IResponse<void>>> {
    return request({
        url: uiTestApi.deleteEnvironment,
        method: 'delete',
        params: { environmentId }
    })
}
