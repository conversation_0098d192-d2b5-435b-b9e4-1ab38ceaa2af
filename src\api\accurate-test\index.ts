/**
 * 精准测试服务 API
 */

import request from '@/utils/request'
import type { AxiosResponse } from 'axios'
import type {
    AnalysisParam,
    InterfacePage
} from './types'

const serviceName = '/api/beacon-tower'
const accurateTestApiName = 'accurate-test'

// 精准测试服务API端点
const accurateTestApi = {
    // 代码分析
    codeAnalysis: `${serviceName}/${accurateTestApiName}/code/analysis`,
    // 保存钉钉推送信息
    saveInFrom: `${serviceName}/${accurateTestApiName}/config/saveInFrom`,
    // 获取钉钉推送信息列表
    inFromData: `${serviceName}/${accurateTestApiName}/config/inFromData`,
    // 获取用户设置的编译工程需要的setting.xml文件
    getProfileFileList: `${serviceName}/${accurateTestApiName}/file/getXmlFileList`,
    // 删除用户设置的编译工程需要的setting.xml文件
    deleteProfileFile: `${serviceName}/${accurateTestApiName}/file/deleteFile`,
    // 上传用户设置的编译工程需要的setting.xml文件
    uploadXmlFile: `${serviceName}/${accurateTestApiName}/file/xmlUpload`,
    // 获取首页静态分析简报列表
    getTaskList: `${serviceName}/${accurateTestApiName}/getTaskList`,
    // 获得受影响的接口列表（免登录）
    getAnnotation: `${serviceName}/${accurateTestApiName}/interface/getAnnotation`,
    // 获取已有的仓库列表
    getRepositoryList: `${serviceName}/${accurateTestApiName}/getRepositoryList`,
    // 获取页面与接口关联列表
    getInterfacePageList: `${serviceName}/${accurateTestApiName}/interfacePage/getPageList`,
    // 通过id删除页面与接口关联数据
    deleteInterfacePage: `${serviceName}/${accurateTestApiName}/interfacePage/deleteInterfacePage`,
    // 通过id编辑页面与接口关联数据
    editInterfacePage: `${serviceName}/${accurateTestApiName}/interfacePage/editInterfacePage`,
    // 新增一条页面与接口关联数据
    addInterfacePage: `${serviceName}/${accurateTestApiName}/interfacePage/addInterfacePage`,
    // 上传页面与接口关联数据（未使用）
    pageUpload: `${serviceName}/${accurateTestApiName}/interfacePage/pageUpload`,
    // 上传页面与接口关联数据进行校验
    checkFileData: `${serviceName}/${accurateTestApiName}/interfacePage/checkFileData`,
    // 批量增加页面与接口关联数据
    incrementalAdd: `${serviceName}/${accurateTestApiName}/interfacePage/incrementalAdd`,
    // 获取页面与接口关联数据的模板文件
    getInterfacePageTemplateFile: `${serviceName}/${accurateTestApiName}/file/getInterfacePageTemplateFile`
}

/**
 * 代码分析
 */
export function codeAnalysis(param: AnalysisParam): Promise<AxiosResponse<IResponse>> {
    return request({
        url: accurateTestApi.codeAnalysis,
        method: 'post',
        data: param
    })
}

/**
 * 保存钉钉推送信息
 */
export function saveInFrom(param: any): Promise<AxiosResponse<IResponse>> {
    return request({
        url: accurateTestApi.saveInFrom,
        method: 'post',
        data: param
    })
}

/**
 * 获取钉钉推送信息列表
 */
export function inFromData(): Promise<AxiosResponse<IResponse>> {
    return request({
        url: accurateTestApi.inFromData,
        method: 'get'
    })
}

/**
 * 获取配置文件列表
 */
export function getProfileFileList(): Promise<AxiosResponse<IResponse>> {
    return request({
        url: accurateTestApi.getProfileFileList,
        method: 'get'
    })
}

/**
 * 删除配置文件
 */
export function deleteProfileFile(data: any): Promise<AxiosResponse<IResponse>> {
    return request({
        url: accurateTestApi.deleteProfileFile,
        method: 'post',
        data: data
    })
}

/**
 * 获取任务列表
 */
export function getTaskList(): Promise<AxiosResponse<IResponse>> {
    return request({
        url: accurateTestApi.getTaskList,
        method: 'get'
    })
}

/**
 * 获取注解信息
 */
export function getAnnotation(param: any): Promise<AxiosResponse<IResponse>> {
    return request({
        url: accurateTestApi.getAnnotation,
        method: 'get',
        params: param
    })
}

/**
 * 获取仓库列表
 */
export function getRepositoryList(): Promise<AxiosResponse<IResponse>> {
    return request({
        url: accurateTestApi.getRepositoryList,
        method: 'get'
    })
}

/**
 * 获取上传文件URL
 */
export function getUploadFile(): string {
    return accurateTestApi.uploadXmlFile
}

/**
 * 获取接口页面关联列表
 */
export function getInterfacePageList(params: InterfacePage): Promise<AxiosResponse<IResponse>> {
    return request({
        url: accurateTestApi.getInterfacePageList,
        method: 'get',
        params: params
    })
}

/**
 * 删除接口页面关联
 */
export function deleteInterfacePage(params: number): Promise<AxiosResponse<IResponse>> {
    return request({
        url: accurateTestApi.deleteInterfacePage,
        method: 'delete',
        params: { id: params }
    })
}

/**
 * 编辑接口页面关联
 */
export function editInterfacePage(data: any): Promise<AxiosResponse<IResponse>> {
    return request({
        url: accurateTestApi.editInterfacePage,
        method: 'post',
        data: data
    })
}

/**
 * 添加接口页面关联
 */
export function addInterfacePage(data: any): Promise<AxiosResponse<IResponse>> {
    return request({
        url: accurateTestApi.addInterfacePage,
        method: 'post',
        data: data
    })
}

/**
 * 上传页面数据
 */
export function pageUpload(data: FormData): Promise<AxiosResponse<IResponse>> {
    return request({
        url: accurateTestApi.pageUpload,
        method: 'post',
        data: data
    })
}

/**
 * 校验文件数据
 */
export function checkFileData(data: FormData): Promise<AxiosResponse<IResponse>> {
    return request({
        url: accurateTestApi.checkFileData,
        method: 'post',
        data: data
    })
}

/**
 * 增量添加数据
 */
export function incrementalAdd(data: any): Promise<AxiosResponse<IResponse>> {
    return request({
        url: accurateTestApi.incrementalAdd,
        method: 'post',
        data: data
    })
}

/**
 * 获取接口页面模板文件
 */
export function getInterfacePageTemplateFile(): Promise<AxiosResponse<IResponse>> {
    return request({
        url: accurateTestApi.getInterfacePageTemplateFile,
        method: 'get',
        responseType: 'blob'
    })
}
