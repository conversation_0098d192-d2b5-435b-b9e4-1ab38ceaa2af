package com.kf.baosi.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kf.baosi.common.PaginatedResponse;
import com.kf.baosi.common.RequireHeader;
import com.kf.baosi.common.ResponseDoMain;
import com.kf.baosi.dao.TXMindMapper;
import com.kf.baosi.dto.CSVFileToJiraDTO;
import com.kf.baosi.dto.XMindToExcelListDTO;
import com.kf.baosi.entity.TXMind;
import com.kf.baosi.enums.TXMindToJiraEnum;
import com.kf.baosi.service.FileService;
import com.kf.baosi.service.JiraService;
import com.kf.baosi.service.XMindService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
@RestController
@RequestMapping("/xmind")
public class XMindController {

    @Resource
    XMindService xMindService;

    @Resource
    FileService fileService;

    @Resource
    JiraService jiraService;

    @Resource
    TXMindMapper xMindDAO;

    /**
     * 查询验证文档列表
     */
    @RequireHeader("userId")
    @GetMapping("/getFileList")
    public ResponseDoMain getVerifyDocumentList(HttpServletRequest request,
                                                @RequestParam(required = false) String fileName,
                                                @RequestParam(required = false) String isComplete,
                                                @RequestParam int current,
                                                @RequestParam int size) {
        String userId = request.getHeader("userId");
        IPage<XMindToExcelListDTO> verifyDocumentList = xMindService.getXMindList(userId, fileName, isComplete, current, size);
        // 获取枚举映射表
        Map<String, String> enumMap = TXMindToJiraEnum.getEnumMap();
        // 映射枚举
        verifyDocumentList.getRecords().forEach(dto -> {
            dto.setIsComplete(enumMap.getOrDefault(dto.getIsComplete(), dto.getIsComplete()));
            // 判断StringUtils.isEmpty(dto.getTaskId())是否为空
            dto.setAllowDelete(ObjectUtil.isNotEmpty(dto.getTestCaseId()));
        });
        PaginatedResponse<XMindToExcelListDTO> response = new PaginatedResponse<>(verifyDocumentList);
        return ResponseDoMain.custom("", true, response, 200);
    }

    /**
     * 查询导入状态
     */
    @GetMapping("/getImportStatus")
    public ResponseDoMain getImportStatus() {
        Map<String, String> importStatus = TXMindToJiraEnum.getEnumMap();
        return ResponseDoMain.custom("", true, importStatus, 200);
    }

    /**
     * xmind转excel文件上传
     */
    @RequireHeader("userId")
    @PostMapping("/xMindToExcelFileUpload")
    public ResponseDoMain xMindToExcelFileUpload(HttpServletRequest request, @RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            throw new RuntimeException("文件是空的!");
        }
        String fileName = file.getOriginalFilename();
        String prefix = fileName.substring(fileName.lastIndexOf("."));
        //判断文件后缀名是否为.xmind，如果不是则不允许上传
        if (!prefix.endsWith(".xmind")) {
            throw new RuntimeException("文件类型错误!");
        }
        xMindService.xMindToExcelFileUpload(file, request.getHeader("userId"));
        return ResponseDoMain.custom("转换成功", true, "", 200);

    }

    /**
     * 下载CSV文件
     */
    @GetMapping("/downloadCSVFile")
    public void downloadFile(@RequestParam("fileId") String fileId, HttpServletResponse response) {
        fileService.downloadFile(fileId, response);
    }

    /**
     * 通过ID删除单条数据（物理删除）
     */
    @RequireHeader("userId")
    @PostMapping("/deleteFile")
    public ResponseDoMain deleteFileById(HttpServletRequest request, @RequestParam("fileId") String fileId) {
        log.info("用户{}，删除文件ID：{}", request.getHeader("userId"), fileId);
        xMindService.deleteFileByFileId(fileId);
        boolean done = fileService.deleteFileById(fileId);
        return ResponseDoMain.custom(done ? "删除成功" : "删除失败", done, "", 200);
    }

    /**
     * 通过ID删除单条数据（物理删除）
     */
    @RequireHeader("userId")
    @DeleteMapping("/deleteTestCaseByFileId")
    public ResponseDoMain deleteTestCaseByFileId(HttpServletRequest request, @RequestParam("fileId") String fileId) {
        int num = jiraService.deleteTestCaseByFileId(request.getHeader("jiraToken"), fileId);
        return ResponseDoMain.custom("成功删除" + num + "个测试用例", true, "", 200);
    }

    /**
     * 获得xMind模板文件
     */
    @GetMapping("/getXMindTemplateFile")
    public void getXMindTemplateFile(HttpServletRequest req, HttpServletResponse response) {
        fileService.downloadXMindTemplateFile(req, response);
    }

    /**
     * csv文件导入jira中
     */
    @PostMapping("/csvFileToJira")
    public ResponseDoMain csvToJira(@RequestBody @Valid CSVFileToJiraDTO csvFileToJiraDTO, HttpServletRequest request) {
        String taskId = IdUtil.simpleUUID();
        UpdateWrapper<TXMind> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("file_id", csvFileToJiraDTO.getFileId()).set("task_id", taskId);
        xMindDAO.update(null, updateWrapper);
        CompletableFuture.runAsync(() -> jiraService.createTestCaseTask(request.getHeader("jiraToken"), taskId, csvFileToJiraDTO));
        jiraService.setEmitter(taskId);
        jiraService.startProgressUpdate(taskId);
        Map<String, Object> map = new HashMap<>();
        map.put("taskId", taskId);
        return ResponseDoMain.custom("开始导入", true, map, 200);
    }

}
