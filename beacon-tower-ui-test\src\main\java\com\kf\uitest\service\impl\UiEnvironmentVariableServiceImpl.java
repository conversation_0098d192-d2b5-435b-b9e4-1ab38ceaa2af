package com.kf.uitest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kf.uitest.dao.UiTestEnvironmentVariableMapper;
import com.kf.uitest.entity.UiTestEnvironmentVariable;
import com.kf.uitest.service.UiEnvironmentVariableService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class UiEnvironmentVariableServiceImpl implements UiEnvironmentVariableService {

    @Resource
    private UiTestEnvironmentVariableMapper variableMapper;
    
    @Override
    public UiTestEnvironmentVariable getVariable(Long environmentId, String name) {
        return variableMapper.selectOne(new LambdaQueryWrapper<UiTestEnvironmentVariable>()
                .eq(UiTestEnvironmentVariable::getEnvironmentId, environmentId)
                .eq(UiTestEnvironmentVariable::getVariableName, name));
    }
    
    @Override
    public List<UiTestEnvironmentVariable> listVariables(Long environmentId) {
        return variableMapper.selectList(new LambdaQueryWrapper<UiTestEnvironmentVariable>()
                .eq(UiTestEnvironmentVariable::getEnvironmentId, environmentId));
    }
    
    @Override
    public Map<String, Object> getVariablesMap(Long environmentId) {
        return listVariables(environmentId).stream()
                .collect(Collectors.toMap(
                    var -> "env." + var.getVariableName(),
                    UiTestEnvironmentVariable::getVariableValue
                ));
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVariable(Long environmentId, String name, String value) {
        UiTestEnvironmentVariable variable = getVariable(environmentId, name);
        
        if (variable == null) {
            // 创建新变量
            variable = new UiTestEnvironmentVariable();
            variable.setEnvironmentId(environmentId);
            variable.setVariableName(name);
            variable.setVariableValue(value);
            variable.setCreateTime(LocalDateTime.now());
            variable.setUpdateTime(LocalDateTime.now());
            variableMapper.insert(variable);
        } else {
            // 更新现有变量
            variable.setVariableValue(value);
            variable.setUpdateTime(LocalDateTime.now());
            variableMapper.updateById(variable);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVariable(Long environmentId, String name) {
        variableMapper.delete(new LambdaQueryWrapper<UiTestEnvironmentVariable>()
                .eq(UiTestEnvironmentVariable::getEnvironmentId, environmentId)
                .eq(UiTestEnvironmentVariable::getVariableName, name));
    }
}