package com.kf.accuratetest.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kf.accuratetest.dto.InterfacePageBatchImportDTO;
import com.kf.accuratetest.entity.InterfacePage;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface InterfacePageService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    InterfacePage queryById(Long id);

    /**
     * 分页查询接口页面
     *
     * @param current      当前页数
     * @param size         每页记录数
     * @return 分页结果
     */
    IPage<InterfacePage> listInterfacePages(InterfacePage InterfacePage, int current, int size);

    /**
     * 数据批量新增
     *
     * @param TInterfacePageList list
     * @param userId 用户ID
     * @return 插入数量
     */
    int incrementalAdd(List<InterfacePageBatchImportDTO> TInterfacePageList, String userId);
    /**
     * 上传文件，新增数据
     *
     * @param tFile 文件
     * @return 受影响行数
     */
    int pageUpload(MultipartFile tFile, String userId);

    /**
     * 校验数据
     *
     * @param tFile 实例对象
     * @return 受影响行数
     */
    List<InterfacePageBatchImportDTO> checkFileData(MultipartFile tFile, String userId);

    /**
     * 新增单条数据
     *
     * @param InterfacePage 实例对象
     * @return 实例对象
     */
    InterfacePage insert(InterfacePage InterfacePage);

    /**
     * 修改数据
     *
     * @param InterfacePage 实例对象
     * @return 实例对象
     */
    InterfacePage update(InterfacePage InterfacePage);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(int id);

}
