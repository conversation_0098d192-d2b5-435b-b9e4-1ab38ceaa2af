<template>
    <el-dialog
        v-model="localVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="470px"
        title="请先登录JIRA"
        @close="handleClose"
    >
        <div class="body">
            您还没有登录JIRA或登录状态已失效，是否前往个人中心进行登录？
        </div>
        <template #footer>
            <el-button v-prevent-default type="text" @click="handleCancel">取消</el-button>
            <el-button v-prevent-default :loading="loading" type="primary" @click="handleButtonClick">
                前往登录
            </el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

// 定义传入的 props
const props = defineProps<{
    visible: boolean
}>()
const localVisible = ref(props.visible)
// 定义触发的事件
const emit = defineEmits<{
    (e: 'close'): void
}>()
watch(() => props.visible, (newVal) => {
    localVisible.value = newVal
})
const loading = ref(false)
const router = useRouter()

// 取消按钮处理
const handleCancel = () => {
    emit('close')
}

// 关闭对话框处理
const handleClose = () => {
    emit('close')
}

// 按钮点击处理
const handleButtonClick = async () => {
    try {
        emit('close')
        await router.push({ path: '/seigneur/UserProfile', query: { from: 'jiraLogin' } })
    } catch (error) {
        ElMessage.error('跳转失败，请重试')
    }
}
</script>

<style scoped>
.body {
    margin-bottom: 20px;
}
</style>
