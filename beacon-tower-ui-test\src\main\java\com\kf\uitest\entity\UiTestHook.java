package com.kf.uitest.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ui_test_hook")
public class UiTestHook extends Model<UiTestHook> {

    @TableId(type = IdType.AUTO)
    private String id;

    @TableField("owner_id")
    private String ownerId;

    @TableField("owner_type")
    private String ownerType;

    @TableField("hook_timing")
    private String hookTiming;
    
    @TableField("action_type")
    private String actionType;
    
    @TableField("hook_order")
    private Integer hookOrder;
    
    @TableField("hook_name")
    private String hookName;
    
    @TableField("selector")
    private String selector;
    
    @TableField("input_value")
    private String inputValue;

    @TableField("parameters")
    private String parameters;
    
    @TableField("timeout")
    private Integer timeout;
    
    @TableField("retry_count")
    private Integer retryCount;
    
    @TableField("retry_interval")
    private Integer retryInterval;
    
    @TableField("error_continue")
    private Integer errorContinue;
    
    @TableField("description")
    private String description;
    
    @TableField("create_time")
    private LocalDateTime createTime;
    
    @TableField("update_time")
    private LocalDateTime updateTime;
}