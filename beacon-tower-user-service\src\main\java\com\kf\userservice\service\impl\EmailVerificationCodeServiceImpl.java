package com.kf.userservice.service.impl;

import com.kf.userservice.dao.TEmailVerificationCodeDao;
import com.kf.userservice.entity.TEmailVerificationCode;
import com.kf.userservice.service.EmailVerificationCodeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;


@Service("emailVerificationCodeService")
public class EmailVerificationCodeServiceImpl implements EmailVerificationCodeService {
    @Resource
    private TEmailVerificationCodeDao tEmailVerificationCodeDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public TEmailVerificationCode queryById(Long id) {
        return this.tEmailVerificationCodeDao.queryById(id);
    }

    @Override
    public TEmailVerificationCode queryByEmail(String email) {
        return this.tEmailVerificationCodeDao.queryByEmail(email);
    }


    /**
     * 新增数据
     *
     * @param tEmailVerificationCode 实例对象
     * @return 实例对象
     */
    @Override
    public TEmailVerificationCode insert(TEmailVerificationCode tEmailVerificationCode) {
        this.tEmailVerificationCodeDao.insert(tEmailVerificationCode);
        return tEmailVerificationCode;
    }

    /**
     * 修改数据
     *
     * @param tEmailVerificationCode 实例对象
     * @return 实例对象
     */
    @Override
    public TEmailVerificationCode update(TEmailVerificationCode tEmailVerificationCode) {
        this.tEmailVerificationCodeDao.update(tEmailVerificationCode);
        return this.queryById(tEmailVerificationCode.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return this.tEmailVerificationCodeDao.deleteById(id) > 0;
    }
}
