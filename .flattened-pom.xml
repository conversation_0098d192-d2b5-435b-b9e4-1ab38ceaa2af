<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.1.1</version>
    <relativePath></relativePath>
  </parent>
  <groupId>com.kf</groupId>
  <artifactId>beacon-tower</artifactId>
  <version>1.0</version>
  <packaging>pom</packaging>
  <name>beacon-tower</name>
  <description>beacon-tower</description>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <modules>
    <module>beacon-tower-accurate-test</module>
    <module>beacon-tower-ai-test</module>
    <module>beacon-tower-api-test</module>
    <module>beacon-tower-gateway</module>
    <module>beacon-tower-user-service</module>
    <module>beacon-tower-sdk</module>
    <module>beacon-tower-bao-si</module>
    <module>beacon-tower-flow-playback</module>
    <module>beacon-tower-ui-test</module>
    <module>beacon-tower-mcp-clint</module>
  </modules>
  <properties>
    <jmeter-plugins-webdriver.version>4.8.3</jmeter-plugins-webdriver.version>
    <swagger-parser.version>2.1.12</swagger-parser.version>
    <java-websocket.version>1.5.3</java-websocket.version>
    <easyexcel.version>3.1.1</easyexcel.version>
    <reflections.version>0.10.2</reflections.version>
    <commons-dbcp2-version>2.9.0</commons-dbcp2-version>
    <zookeeper.version>3.8.0</zookeeper.version>
    <xmlbeans.version>3.1.0</xmlbeans.version>
    <commons-beanutils.version>1.9.4</commons-beanutils.version>
    <opentelemetry.version>1.24.0</opentelemetry.version>
    <spring-cloud.version>2022.0.3</spring-cloud.version>
    <common-random.version>1.0.14</common-random.version>
    <xz.version>1.9</xz.version>
    <avro.version>1.11.1</avro.version>
    <platform-plugin-sdk.version>1.6.0</platform-plugin-sdk.version>
    <xmlgraphics-commons.version>2.7</xmlgraphics-commons.version>
    <commons-fileupload.version>1.5</commons-fileupload.version>
    <generex.version>1.0.2</generex.version>
    <hikaricp.version>5.0.1</hikaricp.version>
    <npm.version>8.12.1</npm.version>
    <dom4j.version>2.1.3</dom4j.version>
    <metersphere-plugin-core.version>2.0</metersphere-plugin-core.version>
    <plexus.version>3.0.24</plexus.version>
    <docker-java.version>3.2.14</docker-java.version>
    <rhino.version>1.7.14</rhino.version>
    <htmlcleaner.version>2.26</htmlcleaner.version>
    <frontend-maven-plugin.version>1.12.1</frontend-maven-plugin.version>
    <commons-compress.version>1.21</commons-compress.version>
    <revision>1.0</revision>
    <org-json.version>20220924</org-json.version>
    <commonmark.version>0.19.0</commonmark.version>
    <jmeter-plugins-dubbo.version>2.7.17</jmeter-plugins-dubbo.version>
    <graalvmjs.version>22.3.1</graalvmjs.version>
    <guice.version>5.1.0</guice.version>
    <hessian-lite.version>3.2.13</hessian-lite.version>
    <minio.version>8.5.3</minio.version>
    <bcprov-jdk15on.version>1.70</bcprov-jdk15on.version>
    <dubbo.version>2.7.22</dubbo.version>
    <guava.version>32.0.1-jre</guava.version>
    <redisson-starter.version>3.21.3</redisson-starter.version>
    <httpclient.version>4.5.14</httpclient.version>
    <nacos.version>2.2.3</nacos.version>
    <springdoc-openapi-ui.version>2.1.0</springdoc-openapi-ui.version>
    <jython.version>2.7.0</jython.version>
    <commons-io.version>2.11.0</commons-io.version>
    <dec.version>0.1.2</dec.version>
    <dingtalk-sdk.version>2.0.0</dingtalk-sdk.version>
    <flatten.version>1.2.7</flatten.version>
    <quartz-starter.version>1.0.8</quartz-starter.version>
    <java.version>17</java.version>
    <jsoup.version>1.15.3</jsoup.version>
    <json-lib.version>2.4</json-lib.version>
    <skipAntRunForJenkins>false</skipAntRunForJenkins>
    <oracle-database.version>********</oracle-database.version>
    <xmindjbehaveplugin.version>0.8</xmindjbehaveplugin.version>
    <metersphere-jmeter-functions.version>1.5</metersphere-jmeter-functions.version>
    <xstream.version>1.4.20</xstream.version>
    <json-schema-validator.version>2.2.14</json-schema-validator.version>
    <node.version>v16.10.0</node.version>
    <commons-text.version>1.10.0</commons-text.version>
    <shiro.version>1.11.0</shiro.version>
    <jmeter.version>5.5</jmeter.version>
    <pagehelper.version>5.3.2</pagehelper.version>
    <codehaus-groovy.version>3.0.11</codehaus-groovy.version>
    <mybatis-starter.version>3.0.1</mybatis-starter.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>${spring-cloud.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        <version>2022.0.0.0-RC2</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>3.5.5</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid-spring-boot-starter</artifactId>
        <version>1.2.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
        <version>3.1.0</version>
      </dependency>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>5.8.19</version>
      </dependency>
      <dependency>
        <groupId>com.mysql</groupId>
        <artifactId>mysql-connector-j</artifactId>
        <version>8.3.0</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor</groupId>
        <artifactId>reactor-test</artifactId>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.kafka</groupId>
        <artifactId>spring-kafka-test</artifactId>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.github.xiaoymin</groupId>
        <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        <version>4.5.0</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <id>aliyun</id>
      <url>http://maven.aliyun.com/nexus/content/groups/public</url>
    </repository>
  </repositories>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <release>${java.version}</release>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>${flatten.version}</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <updatePomFile>true</updatePomFile>
          <flattenMode>resolveCiFriendliesOnly</flattenMode>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
