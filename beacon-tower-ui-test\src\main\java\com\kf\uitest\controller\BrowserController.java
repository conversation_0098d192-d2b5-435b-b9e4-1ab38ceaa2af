package com.kf.uitest.controller;

import com.kf.uitest.common.ResponseDoMain;
import com.kf.uitest.dto.BrowserOptionDTO;
import com.kf.uitest.service.BrowserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/browsers")
public class BrowserController {
    
    @Autowired
    private BrowserService browserService;
    
    @GetMapping("/options")
    public ResponseDoMain getBrowserOptions() {
        return ResponseDoMain.custom("", true, browserService.getBrowserOptions(), 200);
    }
} 