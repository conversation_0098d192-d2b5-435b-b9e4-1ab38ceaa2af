<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kf.baosi.dao.TXMindMapper">
    <resultMap id="TXMindToCsvDTOResultMap" type="com.kf.baosi.dto.XMindToExcelListDTO">
        <result property="fileId" column="id"/>
        <result property="fileName" column="file_name"/>
        <result property="taskId" column="task_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isComplete" column="is_complete"/>
        <result property="errorMsg" column="error_message"/>
        <result property="testCaseId" column="test_case_id"/>
    </resultMap>

    <select id="getFileList" resultMap="TXMindToCsvDTOResultMap">
        SELECT f.id,
        f.file_name,
        v.task_id,
        v.create_time,
        v.update_time,
        v.is_complete,
        v.error_message,
        v.test_case_id
        FROM t_xmind v
        LEFT JOIN t_file f ON v.file_id = f.id
        WHERE v.user_id = #{userId}
        AND v.status = 0
        AND f.is_deleted = 0
        <if test="fileName != null and !fileName.isEmpty()">
            AND f.file_name LIKE CONCAT('%', #{fileName}, '%')
        </if>
        <if test="isComplete != null and !isComplete.isEmpty()">
            AND v.is_complete = #{isComplete}
        </if>
        ORDER BY v.create_time DESC
    </select>
</mapper>