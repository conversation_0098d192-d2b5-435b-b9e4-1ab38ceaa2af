package com.kf.aitest.service;

/**
 * 提示词模板服务接口
 */
public interface PromptTemplateService {
    
    /**
     * 根据阶段名称获取提示词模板
     *
     * @param stageName 阶段名称 (recognize, extraction, structured, transformer, deduplication-guide)
     * @return 提示词模板内容
     */
    String getPromptTemplate(String stageName);
    
    /**
     * 构建完整的评估提示词
     * 
     * @param stageName 阶段名称
     * @param uatData UAT环境数据
     * @param testData TEST环境数据
     * @return 完整的提示词
     */
    String buildEvaluationPrompt(String stageName, Object uatData, Object testData);
    
    /**
     * 检查提示词模板是否存在
     * 
     * @param stageName 阶段名称
     * @return 是否存在对应的模板文件
     */
    boolean hasTemplate(String stageName);
    
    /**
     * 重新加载所有提示词模板（用于热更新）
     */
    void reloadTemplates();
}
