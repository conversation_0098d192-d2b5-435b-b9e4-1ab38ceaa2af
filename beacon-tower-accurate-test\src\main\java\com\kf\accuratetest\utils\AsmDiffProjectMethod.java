package com.kf.accuratetest.utils;

import cn.hutool.crypto.SecureUtil;
import org.objectweb.asm.ClassReader;
import org.objectweb.asm.tree.AbstractInsnNode;
import org.objectweb.asm.tree.ClassNode;
import org.objectweb.asm.tree.InsnList;
import org.objectweb.asm.tree.MethodNode;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AsmDiffProjectMethod {
    private final String masterProjectPath;
    private final String devProjectPath;

    public AsmDiffProjectMethod(String masterProjectPath, String devProjectPath) {
        this.masterProjectPath = masterProjectPath;
        this.devProjectPath = devProjectPath;
    }

    /**
     * 返回新版本中，新增和修改的方法
     */
    public List<String> diff() {
        //存放所有dev分支中新增、修改的方法
        List<String> methods = new ArrayList<>();
        //先通过文件MD5值过滤掉没有任何变化的class文件，加快对比效率
        Map<String, String> masterClassMap = classFileAddToMap(this.masterProjectPath);
        Map<String, String> devClassMap = classFileAddToMap(this.devProjectPath);
        List<String> masterClasses = new ArrayList<>();
        List<String> devClasses = new ArrayList<>();

        devClassMap.forEach((key, value) -> {
            if (masterClassMap.get(key) == null) {
                devClasses.add(value);
            }
        });
        masterClassMap.forEach((key, value) -> {
            if (devClassMap.get(key) == null) {
                masterClasses.add(value);
            }
        });

        //获得<方法名，指令集>格式的map
        Map<String, List<Integer>> masterMethodMap = getMethodOpcodeMap(masterClasses, new HashMap<>());
        Map<String, List<Integer>> devMethodMap = getMethodOpcodeMap(devClasses, new HashMap<>());

        //将类路径分别循环对比方法指令，找出新增和修改的方法
        devMethodMap.forEach((key, value) -> {
            //如果当前方法不在map中，就是新增的方法
            if (masterMethodMap.get(key) == null) {
                methods.add(key);
            }
            //如果方法的ASM指令集不相等，就是修改的方法
            else if (!devMethodMap.get(key).equals(masterMethodMap.get(key))) {
                methods.add(key);
            }
        });
        return methods;
    }

    //将所有的class文件以<MD5,绝对路径>的格式存入map
    private Map<String, String> classFileAddToMap(String path) {
        return classFileAddToMap(new File(path), new HashMap<>());
    }

    private Map<String, String> classFileAddToMap(File file, Map<String, String> classParamMap) {
        if (!file.exists()) {
            return classParamMap;
        }
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files != null) {
                for (File f : files) {
                    classFileAddToMap(f, classParamMap);
                }
            }
        } else if (file.getName().endsWith(".class")) {
            classParamMap.put(SecureUtil.md5(file), file.getAbsolutePath());
        }
        return classParamMap;
    }

    private Map<String, List<Integer>> getMethodOpcodeMap(List<String> classList, Map<String, List<Integer>> map) {
        for (String l : classList) {
            try (FileInputStream fileInputStream = new FileInputStream(l)) {
                ClassReader cr = new ClassReader(fileInputStream);
                ClassNode cn = new ClassNode();
                cr.accept(cn, 0);
                List<MethodNode> methods = cn.methods;
                for (MethodNode mn : methods) {
                    //过滤掉构造方法
                    if (!mn.name.equals("<init>") && !mn.name.equals("<clinit>")) {
                        InsnList instructions = mn.instructions;
                        List<Integer> opcodeList = new ArrayList<>();
                        for (AbstractInsnNode a : instructions) {
                            opcodeList.add(a.getOpcode());
                        }
                        map.put(AsmUtil.methodAllName(cn, mn), opcodeList);
                    }
                }
            } catch (IOException e) {
                //忽略任何异常
//                e.printStackTrace();
            }
        }
        return map;
    }


}
