package com.kf.accuratetest.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("t_repository_info")
public class RepositoryInfo {
    /**
     * 主键
     */
    private Long id;

    /**
     * 仓库地址
     */
    private String repositoryUrl;

    /**
     * 分支名称
     */
    private String branch;

    /**
     * 项目版本号
     */
    private String objectId;

    /**
     * 项目本地存放路径
     */
    private String directoryPath;

    /**
     * 是否下载完成
     */
    private int isDone;

    /**
     * 是否编译完成
     */
    private int isCompile;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 是否删除  0未删除 1删除
     */
    private int isDeleted;
}