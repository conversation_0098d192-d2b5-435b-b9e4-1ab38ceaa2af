package com.kf.uitest.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("ui_test_block")
public class UiTestBlock {
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    
    @TableField("case_id")
    private String caseId;
    
    @TableField("block_type")
    private String blockType;
    
    @TableField("block_name")
    private String blockName;
    
    @TableField("block_order")
    private Integer blockOrder;
    
    @TableField("parent_block_id")
    private String parentBlockId;
    
    @TableField("description")
    private String description;
    
    @TableField("create_time")
    private LocalDateTime createTime;
    
    @TableField("update_time")
    private LocalDateTime updateTime;
} 