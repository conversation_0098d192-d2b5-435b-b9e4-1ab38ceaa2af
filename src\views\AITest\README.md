# AI测试管理平台使用说明

## 概述

AI测试管理平台是一个集成的前端应用，提供AI测试、Markdown渲染和性能测试功能。采用Vue 3 + TypeScript + Element Plus技术栈，具有现代化的用户界面和丰富的交互功能。

## 🔄 架构重构说明

### 重构内容
1. **路由系统调整**: 移除了静态路由配置，完全依赖动态路由系统
2. **Composables重构**: 将composables移动到 `src/utils/composables/` 目录，提高复用性
3. **组件结构优化**: 将子组件移动到 `src/components/AITest/` 目录，遵循项目规范
4. **页面布局重构**: 采用文档管理页面的设计模式，包含查询区域、列表区域和模态框

### 新架构特点
- **查询区域**: 支持按任务名称、状态、创建时间筛选
- **列表区域**: 表格形式展示任务列表，支持分页和批量操作
- **模态框**: 点击"创建任务"打开模态框，包含原有的Tab页面功能
- **快捷键**: 支持 Ctrl+R 刷新、Ctrl+N 创建新任务

## 功能特性

### 🚀 核心功能
- **API接口测试**: 启动AI测试任务，实时监控测试进度
- **Markdown测试**: 测试和预览Markdown渲染效果
- **性能测试**: 评估Markdown渲染性能和系统负载

### 🎯 用户体验
- **统一界面**: Tab页面布局，集成所有测试功能
- **实时状态**: 显示活动任务数量和系统状态
- **快捷操作**: 支持键盘快捷键和操作提示
- **数据持久化**: 自动保存用户偏好设置

## 快捷键

| 快捷键 | 功能 |
|--------|------|
| `Ctrl + R` | 刷新任务列表 |
| `Ctrl + N` | 创建新任务 |

## 页面说明

### API接口测试页面

**功能**:
- 启动AI测试任务
- 配置测试参数（AI评估、数据分片等）
- 建立SSE连接监控测试进度
- 查看测试结果和日志

**使用步骤**:
1. 在"启动测试"标签页中输入测试ID列表
2. 配置测试选项（启用AI评估、禁用数据分片）
3. 点击"启动测试"按钮
4. 切换到"SSE连接测试"标签页监控进度
5. 查看实时事件和测试结果

### Markdown测试页面

**功能**:
- 测试Markdown内容渲染
- 预览渲染效果
- 验证语法支持

**使用步骤**:
1. 输入或粘贴Markdown内容
2. 实时预览渲染效果
3. 检查语法高亮和格式化

### 性能测试页面

**功能**:
- 评估Markdown渲染性能
- 测试大文档处理能力
- 监控内存使用情况

**使用步骤**:
1. 选择测试文档大小
2. 启动性能测试
3. 查看性能指标和统计数据

## 状态指示器

### 活动任务指示器
- **绿色徽章**: 显示当前活动任务数量
- **位置**: 页面标题右侧
- **交互**: 点击可查看任务详情

### 系统状态指示器
- **红色警告图标**: 系统异常或错误状态
- **位置**: 页面标题右侧
- **交互**: 悬停显示错误详情

## 技术架构

### 组件结构
```
src/views/AITest/
└── AITest.vue (主页面视图)

src/components/AITest/
├── APITest.vue (API测试组件)
├── MarkdownTest.vue (Markdown测试组件)
└── MarkdownPerformanceTest.vue (性能测试组件)
```

### Composables (位于 src/utils/composables/)
- `useAITest`: AI测试逻辑和状态管理
- `useSSEConnection`: SSE连接管理和事件处理
- `useMarkdownRenderer`: Markdown渲染和配置管理

### 状态管理
- `aiTestStore`: Pinia store管理全局AI测试状态
- 支持任务管理、会话管理、数据导出等功能

## 配置选项

### AI测试配置
```typescript
{
  enableAiEvaluation: boolean,    // 启用AI评估
  disableChunking: boolean,       // 禁用数据分片
  maxTokens: number,              // 最大Token数量
  timeout: number                 // 请求超时时间
}
```

### SSE连接配置
```typescript
{
  taskId: string,                 // 任务ID
  autoReconnect: boolean,         // 自动重连
  maxReconnectAttempts: number,   // 最大重连次数
  reconnectInterval: number       // 重连间隔
}
```

### Markdown渲染配置
```typescript
{
  html: boolean,                  // 允许HTML标签
  linkify: boolean,               // 自动链接化
  typographer: boolean,           // 排版优化
  breaks: boolean,                // 换行转换
  highlight: boolean,             // 代码高亮
  theme: string                   // 高亮主题
}
```

## 错误处理

### 常见错误及解决方案

1. **SSE连接失败**
   - 检查后端服务是否正常运行
   - 验证任务ID是否正确
   - 查看网络连接状态

2. **AI测试启动失败**
   - 确认测试ID格式正确
   - 检查后端API服务状态
   - 验证请求参数配置

3. **Markdown渲染异常**
   - 检查Markdown语法是否正确
   - 验证特殊字符是否需要转义
   - 确认渲染配置是否合适

## 性能优化

### 内存管理
- 事件列表自动限制数量（最大1000条）
- 组件卸载时自动清理资源
- 定期清理已完成的任务数据

### 渲染优化
- 使用虚拟滚动处理大量数据
- 按需加载highlight.js语言包
- 实现智能缓存策略

## 开发指南

### 添加新功能
1. 在对应的Composable中添加逻辑
2. 更新相关的TypeScript类型定义
3. 在组件中集成新功能
4. 添加相应的测试用例

### 自定义配置
1. 修改默认配置对象
2. 更新配置接口定义
3. 在UI中添加配置选项
4. 实现配置持久化

## 故障排除

### 调试技巧
1. 打开浏览器开发者工具
2. 查看Console面板的错误信息
3. 检查Network面板的请求状态
4. 使用Vue DevTools查看组件状态

### 日志记录
- 所有重要操作都有console日志输出
- SSE事件会记录详细的时间戳和数据
- 错误信息包含堆栈跟踪信息

## 更新日志

### v1.0.0 (2025-07-29)
- ✅ 完成基础架构重构
- ✅ 集成Composables和Pinia Store
- ✅ 添加快捷键支持
- ✅ 优化用户体验和视觉效果
- ✅ 完善错误处理和状态管理

---

如有问题或建议，请联系开发团队。
