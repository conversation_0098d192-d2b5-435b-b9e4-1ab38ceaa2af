package com.kf.accuratetest.service;

import com.kf.accuratetest.dto.TInterfaceAnnotationDTO;

import java.util.List;
import java.util.Map;

public interface InterfaceAnnotationService {

    /**
     * 通过ID查询受影响的接口
     *
     * @param taskId 任务ID
     * @return 实例对象
     */
    List<TInterfaceAnnotationDTO> queryById(String taskId);

    /**
     * 新增数据
     *
     * @param map map
     * @param taskId 任务ID
     * @return 实例对象
     */
    int insert(Map<String, List<String>> map,String taskId);

    /**
     * 根据taskId查询数据总数
     */
    int queryCount(String taskId);

}
