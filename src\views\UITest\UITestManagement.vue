<template>
    <div class="ui-test-container">
        <div ref="searchEl" class="table-search-form">
            <el-row :gutter="15" class="clear-both">
                <el-col :span="24">
                    <card-list :show-header="true" title="搜索" type="keyvalue">
                        <template #btn>
                            <el-button-group>
                                <el-button v-prevent-default icon="el-icon-search" size="small" @click="selectData">
                                    搜索
                                </el-button>
                            </el-button-group>
                        </template>
                        <template #keyvalue>
                            <el-form ref="refForm" :model="form" class="card-list-form" size="small">
                                <el-row :gutter="15">
                                    <card-list-item width="100px">
                                        <template #key>项目名称</template>
                                        <template #value>
                                            <el-select
                                                v-model="form.projectKey"
                                                clearable
                                                filterable
                                                placeholder="选择项目"
                                                :filter-method="filterProjects"
                                            >
                                                <el-option
                                                    v-for="item in filteredProjects"
                                                    :key="item.value"
                                                    :label="item.name"
                                                    :value="item.value"
                                                />
                                            </el-select>
                                        </template>
                                    </card-list-item>
                                </el-row>
                            </el-form>
                        </template>
                    </card-list>
                </el-col>
            </el-row>
        </div>
        <div class="flex justify-between items-center mb-2 ">
            <div style="display: flex; align-items: center;">
                <el-button-group>
                    <el-button v-prevent-default type="primary" @click="openEnvironmentConfig">环境配置</el-button>
                </el-button-group>
            </div>
            <el-button v-prevent-default type="text" @click="toggleSearch">
                搜索
                <el-icon>
                    <el-icon-arrow-up v-if="isShow" />
                    <el-icon-arrow-down v-else />
                </el-icon>
            </el-button>
        </div>
        <el-row :gutter="3" class="content-container">
            <el-col :span="5" class="h-full">
                <div class="tree-container">
                    <div class="tree-header">
                        <el-input
                            v-model="searchQuery"
                            placeholder="搜索场景或用例"
                            @input="handleSearch"
                            class="tree-search"
                            clearable
                            :suffix-icon="Search"
                        />
                        <el-tooltip content="创建场景" placement="top" :show-after="500">
                            <el-button
                                v-prevent-default
                                type="text"
                                @click="createSuite"
                                :icon="Plus"
                                class="create-button"
                            ></el-button>
                        </el-tooltip>
                    </div>
                    <el-tree
                        :data="treeData"
                        node-key="id"
                        ref="treeRef"
                        :props="defaultProps"
                        :filter-node-method="filterNode"
                        accordion
                        @node-click="handleNodeClick"
                        class="custom-tree"
                    >
                        <!-- 自定义树节点内容 -->
                        <template #default="{ node, data }">
                            <!-- 标签文字内容带省略 -->
                            <span class="node-label">{{ data.label }}</span>
                            <!-- 操作按钮组，仅在当前行悬浮时显示 -->
                            <span v-if="node.level === 1" class="button-group">
                                <el-button v-prevent-default @click.stop="onSceneSetting(data)" type="text" :icon="Setting"></el-button>
                                <el-button v-prevent-default @click.stop="onSceneAdd(data)" type="text" :icon="Plus"></el-button>
                                <el-popconfirm
                                    v-if="data.children && data.children.length > 0"
                                    title="该测试场景下存在测试用例，确定要删除吗？"
                                    @confirm="onSceneDelete(data)"
                                >
                                    <template #reference>
                                        <el-button v-prevent-default @click.stop type="text" :icon="Delete"></el-button>
                                    </template>
                                </el-popconfirm>
                                <el-button v-else v-prevent-default @click.stop="onSceneDelete(data)" type="text" :icon="Delete"></el-button>
                            </span>

                            <span v-else-if="node.level === 2" class="button-group">
                                <el-button v-prevent-default @click.stop="onCaseSetting(data)" type="text" :icon="Setting"></el-button>
                                <el-popconfirm title="该用例下存在测试步骤，确定要删除吗？" @confirm="onCaseDelete(data)">
                                    <template #reference>
                                        <el-button v-prevent-default @click.stop type="text" :icon="Delete"></el-button>
                                    </template>
                                </el-popconfirm>
                            </span>
                        </template>
                    </el-tree>
                </div>
            </el-col>
            <el-col :span="19" class="h-full">
                <div class="steps-container">
                    <test-steps 
                        :steps="selectedCaseSteps"
                        :selected-case-id="selectedCaseId"
                        :action-type-options="actionTypeOptions"
                        :environments="environments"
                    ></test-steps>
                </div>
            </el-col>
        </el-row>
        <CreateTestSuite
            v-if="createSuiteVisible"
            :visible="createSuiteVisible"
            project-id="1"
            @close="createSuiteVisible = false"
            @save="createSuiteVisibleSave"
        ></CreateTestSuite>
        <CreateTestCase
            v-if="createTestCaseVisible"
            :visible="createTestCaseVisible"
            :suite-id="suiteId"
            @close="createTestCaseVisible = false"
            @save="createTestCaseVisibleSave"
        ></CreateTestCase>
        <EnvironmentConfig
            v-model:visible="environmentConfigVisible"
            v-model:environments="environments"
            :project-id="1"
        />
    </div>
</template>

<script setup lang="ts">
import CardList from '@/components/CardList/CardList.vue'
import CardListItem from '@/components/CardList/CardListItem.vue'
import { computed, nextTick, reactive, ref, watch, onMounted } from 'vue'
import { slide } from '@/utils/animate'
import { ElMessage } from 'element-plus'
import type { EnvironmentWithVariablesDTO } from '@/api/layout'
import { getTestTree, getTestSteps, getActionTypes, getAllEnvironmentsWithVariables } from '@/api/layout'
import CreateTestSuite from '@/components/UiTest/CreateTestSuite.vue'
import { Setting, Plus, Delete, Search } from '@element-plus/icons-vue'
import CreateTestCase from '@/components/UiTest/CreateUITestCase.vue'
import TestSteps from '@/components/UiTest/TestSteps.vue'
import EnvironmentConfig from '@/components/UiTest/EnvironmentConfig.vue'

const searchEl = ref(null)
const isShow = ref(false)
// 点击搜索按钮
const toggleSearch = () => {
    isShow.value = !isShow.value
    slide(searchEl, isShow.value)
}

interface ISearchForm {
    projectKey: string
}
// 搜索表单
const form: ISearchForm = reactive({
    projectKey: ''
})
// 定义查询，用于过滤项目列表
const query = ref('')
// 自定义的过滤方法，更新查询条件
const filterProjects = (val: string) => {
    query.value = val
}

interface select {
    name: string
    value: string
}
// 项目列表
const projects = ref<select[]>([])
// 过滤后的项目列表
const filteredProjects = computed(() => {
    if (!query.value) {
        return projects.value
    } else {
        const lowerCaseQuery = query.value.toLowerCase()
        return projects.value.filter((project) => {
            return (
                project.name.toLowerCase().includes(lowerCaseQuery) ||
                project.value.toLowerCase().includes(lowerCaseQuery)
            )
        })
    }
})


// 环境配置相关状态
const environmentConfigVisible = ref(false)
const createSuite = async () => {
    environmentConfigVisible.value = true
}

interface TreeNodeDTO {
    id: number
    label: string
    children?: TreeNodeDTO[]
}

const projectId = 1
const treeData = ref<TreeNodeDTO[]>([])
const searchQuery = ref('')
const treeRef = ref()
const defaultProps = {
    children: 'children',
    label: 'label'
}

const selectData = async () => {
    try {
        const res = await getTestTree(projectId)
        if (res.data.isSuccess){
            treeData.value = res.data.data
        }
        const environmentsRes = await getAllEnvironmentsWithVariables(projectId)
        if (environmentsRes.data.isSuccess) {
            environments.value = environmentsRes.data.data
        }
    } catch (error) {
        console.error('获取数据失败', error)
    }
}

/**
 * 根据搜索关键字过滤节点
 */
const filterNode = (value: string, data: TreeNodeDTO) => {
    if (!value) return true
    return data.label.indexOf(value) !== -1
}

/**
 * 处理搜索输入，调用 el-tree 的 filter 方法过滤节点
 */
const handleSearch = () => {
    nextTick(() => {
        treeRef.value.filter(searchQuery.value)
    })
}

// 创建测试场景的控制变量
const createSuiteVisible = ref(false)
const createSuiteVisibleSave = (payload: any) => {
    createSuiteVisible.value = false
    treeData.value.push({
        id: payload.id,
        label: payload.label,
        children: payload.children || []
    })
}
// 处理保存事件
const createTestCaseVisibleSave = ({ newNode, suiteId }: { newNode: TreeNodeDTO, suiteId: number }) => {
    if (!suiteId) {
        ElMessage.error('无效的场景ID')
        return
    }
    // 使用 Map 追加新节点
    appendNodeUsingMap(suiteId, newNode)
    createTestCaseVisible.value = false
}
// 初始化 Map 来缓存节点
const nodeMap = new Map<string, TreeNodeDTO>()

// 构建节点映射的函数
const buildNodeMap = (nodes: TreeNodeDTO[], level = 1) => {
    for (const node of nodes) {
        // level=1 表示场景，其他表示用例
        if (level === 1) {
            nodeMap.set(`S_${node.id}`, node)
        } else {
            nodeMap.set(`C_${node.id}`, node)
        }
        // 递归处理 children
        if (node.children && node.children.length > 0) {
            buildNodeMap(node.children, level + 1)
        }
    }
}

// 使用 Map 追加节点
const appendNodeUsingMap = (targetId: number, newNode: TreeNodeDTO) => {
    // 目标一定是场景 => S_ 前缀
    const targetNode = nodeMap.get(`S_${targetId}`)
    if (targetNode) {
        if (!targetNode.children) {
            targetNode.children = []
        }
        targetNode.children.push(newNode)
        nodeMap.set(`S_${targetId}`, targetNode)
        nodeMap.set(`C_${newNode.id}`, newNode)
    } else {
        ElMessage.error('未找到对应的场景节点')
    }
}
// 监听 treeData 的变化并重新构建 Map
watch(treeData, (newData) => {
    nodeMap.clear()
    buildNodeMap(newData)
}, { immediate: true, deep: true })
/** 操作事件处理 **/
const onSceneSetting = (data: TreeNodeDTO) => {
    console.log('设置测试场景：', data)
}
const suiteId = ref()
const createTestCaseVisible = ref(false)
const onSceneAdd = (data: TreeNodeDTO) => {
    suiteId.value = data.id
    createTestCaseVisible.value = true
}

const onSceneDelete = (data: TreeNodeDTO) => {
    console.log('删除测试场景：', data)
}

const onCaseSetting = (data: TreeNodeDTO) => {
    console.log('设置测试用例：', data)
}

const onCaseDelete = (data: TreeNodeDTO) => {
    console.log('删除测试用例：', data)
}
// 当还未选择用例时，steps 为空数组 => 右侧表格显示空
const selectedCaseSteps = ref<any[]>([])
// 添加 selectedCaseId ref
const selectedCaseId = ref<string>()
// 点击节点回调：当点击的是二级节点(用例)时，请求接口获取用例步骤
const handleNodeClick = async (data: any, node: any) => {
    console.log('点击节点', data, node)
    // node.level=1 表示场景，level=2 表示用例
    if (node.level === 2) {
        selectedCaseId.value = data.id  // 设置选中的测试用例 ID
        const res = await getTestSteps(data.id)
        if (!res.data.isSuccess) {
            ElMessage.error('获取用例步骤失败')
            return
        }
        selectedCaseSteps.value = res.data.data
    } else {
        // 如果点击的不是测试用例，清空选中状态
        selectedCaseId.value = undefined
        selectedCaseSteps.value = []
    }
}

// actionTypeOptions 状态
const actionTypeOptions = ref<Array<{label: string, value: string}>>([])

// 添加获取动作类型的方法
const fetchActionTypes = async () => {
    try {
        const response = await getActionTypes()
        if (response.data.isSuccess) {
            actionTypeOptions.value = response.data.data
        }
    } catch (error) {
        console.error('获取动作类型选项失败:', error)
    }
}

onMounted(() => {
    fetchActionTypes()
})
const environments = ref<EnvironmentWithVariablesDTO[]>([])

// 打开环境配置弹窗
const openEnvironmentConfig = () => {
    environmentConfigVisible.value = true
}

// 处理环境配置保存
// const handleEnvironmentSave = async (updatedEnvironments: EnvironmentWithVariablesDTO[]) => {
//     try {
//         // 直接更新本地环境数据
//         environments.value = updatedEnvironments
//     } catch (error) {
//         console.error('更新环境配置失败:', error)
//         ElMessage.error('更新环境配置失败')
//     }
// }
</script>

<style lang="postcss" scoped>
.ui-test-container {
    position: absolute;  /* 新增：使容器相对于最近的定位父元素定位 */
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;    /* 防止出现滚动条 */
}

.table-search-form {
    overflow: hidden;
    height: 0;
    flex-shrink: 0;     /* 防止搜索框被压缩 */
}

.content-container {
    flex: 1;
    display: flex;      /* 让行内容也使用flex布局 */
    min-height: 0;      /* 重要：允许内容在flex容器中正确滚动 */
    margin: 0 !important; /* 覆盖 el-row 的默认外边距 */
}

/* 确保列容器占满高度 */
:deep(.el-col) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.tree-container {
    flex: 1;            /* 让树容器占满列高度 */
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    padding: 10px;
    box-sizing: border-box;
}

.tree-header {
    display: flex;
    align-items: center;  /* 添加垂直居中对齐 */
    gap: 5px;            /* 减小间距 */
    margin-bottom: 10px;
    flex-shrink: 0;
}

.tree-search {
    flex: 1;
}

.custom-tree {
    flex: 1;
    overflow-y: auto;
    min-height: 0;      /* 允许树在flex容器中正确滚动 */
}

.steps-container {
    flex: 1;            /* 让步骤容器占满列高度 */
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    padding: 10px;
    box-sizing: border-box;
    overflow: auto;
}

/* 确保内容区域不会出现横向滚动条 */
.el-row {
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 100%;
}

/* 移除列的默认内边距 */
:deep(.el-col:first-child) {
    padding-left: 0 !important;
}

:deep(.el-col:last-child) {
    padding-right: 0 !important;
}

::v-deep(.el-tree-node__content) {
    display: flex !important;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
    padding: 8px 5px;
}

/* 标签样式，防止超出部分显示省略 */
.node-label {
    flex: 1;
    min-width: 0;            /* 不加会导致文字可能不收缩 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 8px;       /* 给按钮留一点间距 */
}

/* 默认隐藏按钮组 */
.button-group {
    flex-shrink: 0;     /* 防止被文字挤压 */
    display: none;      /* 默认隐藏 */
}

/* 可选择让整行悬浮时显示按钮组 */
::v-deep(.el-tree-node__content:hover) .button-group {
    display: inline-flex;
}

/* 调整按钮间距和尺寸 */
.button-group > .el-button {
    padding: 2px;
    margin-left: 4px;
    font-size: 16px;
}

/* 美化树的样式 */
:deep(.el-tree) {
    --el-tree-node-hover-bg-color: #f5f7fa;
    background: transparent;
}

:deep(.el-tree-node__content) {
    border-radius: 4px;
    margin: 2px 0;
    transition: all 0.3s;
}

:deep(.el-tree-node__content:hover) {
    background-color: var(--el-tree-node-hover-bg-color);
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
}

.create-button {
    padding: 8px;        /* 调整内边距使按钮更紧凑 */
    font-size: 16px;     /* 调整图标大小 */
}

.create-button:hover {
    background-color: var(--el-color-primary-light-9);
}
</style>
