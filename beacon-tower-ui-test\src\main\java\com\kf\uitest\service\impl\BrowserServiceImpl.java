package com.kf.uitest.service.impl;

import com.kf.uitest.dto.BrowserOptionDTO;
import com.kf.uitest.enums.BrowserEngine;
import com.kf.uitest.service.BrowserService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class BrowserServiceImpl implements BrowserService {
    
    @Override
    public List<BrowserOptionDTO> getBrowserOptions() {
        return Arrays.stream(BrowserEngine.values())
                .map(browser -> {
                    BrowserOptionDTO dto = new BrowserOptionDTO();
                    dto.setLabel(browser.getDisplayName());
                    dto.setValue(browser.getValue());
                    return dto;
                })
                .collect(Collectors.toList());
    }
} 