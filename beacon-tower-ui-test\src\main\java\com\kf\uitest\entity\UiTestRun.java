package com.kf.uitest.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ui_test_run")
public class UiTestRun extends Model<UiTestRun> {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("environment_id")
    private Long environmentId;

    @TableField("run_name")
    private String runName;

    @TableField("status")
    private Integer status = 0;

    @TableField("start_time")
    private LocalDateTime startTime;

    @TableField("end_time")
    private LocalDateTime endTime;

    @TableField("total_cases")
    private Integer totalCases = 0;

    @TableField("pass_cases")
    private Integer passCases = 0;

    @TableField("fail_cases")
    private Integer failCases = 0;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;
}
