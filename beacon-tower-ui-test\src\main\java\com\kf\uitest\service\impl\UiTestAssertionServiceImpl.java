package com.kf.uitest.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.JsonPath;
import com.kf.uitest.dao.UiTestAssertionMapper;
import com.kf.uitest.entity.UiTestAssertion;
import com.kf.uitest.entity.UiTestHook;
import com.kf.uitest.enums.AssertionOperator;
import com.kf.uitest.enums.AssertionType;
import com.kf.uitest.model.HookExecutionResult;
import com.kf.uitest.model.TestExecutionContext;
import com.kf.uitest.service.UiTestAssertionService;
import com.kf.uitest.service.UiTestHookService;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class UiTestAssertionServiceImpl implements UiTestAssertionService {

    private final UiTestAssertionMapper assertionMapper;
    private final UiTestHookService hookService;
    
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public List<UiTestAssertion> findByHookId(String hookId) {
        return assertionMapper.findByHookId(hookId);
    }

    @Override
    public UiTestAssertion create(UiTestAssertion assertion) {
        // 验证断言配置
        if (!validateAssertion(assertion)) {
            throw new IllegalArgumentException("断言配置无效");
        }
        
        // 设置创建时间和更新时间
        initializeAssertion(assertion);
        
        // 使用BaseMapper的insert方法
        assertionMapper.insert(assertion);
        return assertion;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UiTestAssertion> batchCreate(List<UiTestAssertion> assertions) {
        if (CollectionUtils.isEmpty(assertions)) {
            return Collections.emptyList();
        }
        
        // 验证所有断言
        for (UiTestAssertion assertion : assertions) {
            if (!validateAssertion(assertion)) {
                throw new IllegalArgumentException("断言配置无效: " + assertion.getAssertionType());
            }
            initializeAssertion(assertion);
        }
        
        // 使用Db工具类进行批量插入
        Db.saveBatch(assertions);
        return assertions;
    }

    @Override
    public boolean update(UiTestAssertion assertion) {
        if (!validateAssertion(assertion)) {
            throw new IllegalArgumentException("断言配置无效");
        }
        assertion.setUpdateTime(LocalDateTime.now());
        return assertionMapper.updateById(assertion) > 0;
    }

    @Override
    public boolean delete(String id) {
        return assertionMapper.deleteById(id) > 0;
    }

    @Override
    public boolean deleteByHookId(String hookId) {
        // 使用Db工具类进行条件删除
        return Db.lambdaUpdate(UiTestAssertion.class)
                .eq(UiTestAssertion::getHookId, hookId)
                .remove();
    }

    @Override
    public List<UiTestAssertion> findByStepId(String stepId) {
        // 1. 获取步骤相关的所有hook
        List<UiTestHook> hooks = hookService.findByStepId(stepId);
        if (hooks.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 2. 获取这些hook相关的所有断言
        List<String> hookIds = hooks.stream()
            .map(UiTestHook::getId)
            .collect(Collectors.toList());
            
        return assertionMapper.selectList(
            new LambdaQueryWrapper<UiTestAssertion>()
                .in(UiTestAssertion::getHookId, hookIds)
        );
    }

    private void initializeAssertion(UiTestAssertion assertion) {
        LocalDateTime now = LocalDateTime.now();
        assertion.setId(UUID.randomUUID().toString());
        assertion.setCreateTime(now);
        assertion.setUpdateTime(now);
        
        // 设置默认值
        if (assertion.getTimeout() == null) {
            assertion.setTimeout(5000); // 默认5秒超时
        }
        if (assertion.getPollingInterval() == null) {
            assertion.setPollingInterval(500); // 默认500ms轮询间隔
        }
        if (assertion.getSoftAssert() == null) {
            assertion.setSoftAssert(false); // 默认为硬断言
        }
    }

    @Override
    public boolean validateAssertion(UiTestAssertion assertion) {
        if (assertion == null || StringUtils.isBlank(assertion.getAssertionType())
            || StringUtils.isBlank(assertion.getAssertionOperator())) {
            return false;
        }
        
        try {
            AssertionType type = AssertionType.valueOf(assertion.getAssertionType());
            AssertionOperator operator = AssertionOperator.valueOf(assertion.getAssertionOperator());
            
            // 验证基本配置
            if (StringUtils.isBlank(assertion.getAssertionTarget())) {
                return false;
            }
            
            // 根据断言类型验证配置
            return switch (type) {
                // 元素存在性断言不需要期望值
                case ELEMENT_PRESENT, ELEMENT_NOT_PRESENT, 
                     ELEMENT_VISIBLE, ELEMENT_NOT_VISIBLE,
                     ELEMENT_ENABLED, ELEMENT_DISABLED,
                     ELEMENT_SELECTED, ELEMENT_NOT_SELECTED -> true;
                
                // 需要期望值的断言
                case ELEMENT_TEXT, ELEMENT_VALUE, 
                     ELEMENT_ATTRIBUTE, ELEMENT_CSS -> 
                    !StringUtils.isBlank(assertion.getExpectedValue());
                
                // 数值类断言
                case ELEMENT_COUNT -> {
                    try {
                        Integer.parseInt(assertion.getExpectedValue());
                        yield true;
                    } catch (NumberFormatException e) {
                        yield false;
                    }
                }
                
                // JSON路径断言
                case JSON_PATH -> {
                    try {
                        // 验证JSON路径语法
                        JsonPath.compile(assertion.getAssertionTarget());
                        // 验证期望值是否为有效的JSON（如果不为空）
                        if (StringUtils.isNotBlank(assertion.getExpectedValue())) {
                            objectMapper.readTree(assertion.getExpectedValue());
                        }
                        yield true;
                    } catch (Exception e) {
                        log.warn("JSON断言验证失败", e);
                        yield false;
                    }
                }
                
                // 数据库记录断言
                case DB_RECORD_EXISTS, DB_RECORD_NOT_EXISTS -> {
                    try {
                        // 验证SQL语法
                        String sql = assertion.getAssertionTarget();
                        if (!sql.trim().toLowerCase().startsWith("select")) {
                            yield false;
                        }
                        yield true;
                    } catch (Exception e) {
                        log.warn("数据库断言验证失败", e);
                        yield false;
                    }
                }
                
                // 其他类型断言的验证逻辑
                default -> true;
            };
            
        } catch (IllegalArgumentException e) {
            log.warn("断言类型或操作符无效", e);
            return false;
        }
    }

} 