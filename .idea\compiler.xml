<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="beacon-tower-user-service" />
        <module name="beacon-tower-ui-test" />
        <module name="beacon-tower-bao-si" />
        <module name="beacon-tower-accurate-test" />
        <module name="beacon-tower-ai-test" />
        <module name="beacon-tower-flow-playback" />
        <module name="beacon-tower-gateway" />
        <module name="beacon-tower-api-test" />
        <module name="beacon-tower-mcp-clint" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="beacon-tower-mcp" target="17" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="beacon-tower" options="-parameters" />
      <module name="beacon-tower-accurate-test" options="-parameters -parameters" />
      <module name="beacon-tower-ai-test" options="-parameters" />
      <module name="beacon-tower-api-test" options="-parameters" />
      <module name="beacon-tower-bao-si" options="-parameters" />
      <module name="beacon-tower-flow-playback" options="-parameters" />
      <module name="beacon-tower-gateway" options="-parameters" />
      <module name="beacon-tower-mcp" options="-parameters" />
      <module name="beacon-tower-mcp-clint" options="-parameters" />
      <module name="beacon-tower-sdk" options="-parameters" />
      <module name="beacon-tower-ui-test" options="-parameters" />
      <module name="beacon-tower-user-service" options="-parameters" />
    </option>
  </component>
</project>