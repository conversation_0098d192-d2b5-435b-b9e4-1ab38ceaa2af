package com.kf.uitest.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kf.uitest.service.impl.ConditionConfig;
import lombok.Data;

import java.util.Map;

@Data
public class ControlLogic {
    private String type;  // WHILE, DO_WHILE, IF, ELSE_IF, ELSE
    private Integer maxIterations;
    private ConditionConfig condition;
    private Map<String, Object> parameters;
    
    @JsonIgnore
    public boolean isLoop() {
        return "WHILE".equals(type) || "DO_WHILE".equals(type);
    }
    
    @JsonIgnore
    public boolean isCondition() {
        return "IF".equals(type) || "ELSE_IF".equals(type) || "ELSE".equals(type);
    }
    
    @JsonIgnore
    public boolean requiresCondition() {
        return !"ELSE".equals(type);
    }
} 