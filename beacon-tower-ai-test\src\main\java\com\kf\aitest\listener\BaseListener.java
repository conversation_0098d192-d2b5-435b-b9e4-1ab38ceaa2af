package com.kf.aitest.listener;

import lombok.extern.slf4j.Slf4j;

/**
 * 基础监听器类
 */
@Slf4j
public abstract class BaseListener {
    
    /**
     * 处理消息前的通用逻辑
     */
    protected void beforeProcess(String message) {
        log.info("开始处理消息: {}", message);
    }
    
    /**
     * 处理消息后的通用逻辑
     */
    protected void afterProcess(String message) {
        log.info("消息处理完成: {}", message);
    }
    
    /**
     * 处理异常的通用逻辑
     */
    protected void handleException(String message, Exception e) {
        log.error("处理消息异常: {}", message, e);
    }
}
