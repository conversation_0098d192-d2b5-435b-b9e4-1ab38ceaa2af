package com.kf.aitest.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kf.aitest.dto.DataComparisonRequestDTO;
import com.kf.aitest.service.DataComparisonService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 数据对比控制器测试
 */
@WebMvcTest(DataComparisonController.class)
public class DataComparisonControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private DataComparisonService dataComparisonService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    public void testGetConfig() throws Exception {
        when(dataComparisonService.getDefaultUatUrl()).thenReturn("https://uat.example.com");
        when(dataComparisonService.getDefaultTestUrl()).thenReturn("https://test.example.com");
        
        mockMvc.perform(get("/data-comparison/config"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.defaultUatUrl").value("https://uat.example.com"))
                .andExpect(jsonPath("$.data.defaultTestUrl").value("https://test.example.com"));
    }
    
    @Test
    public void testStartComparison() throws Exception {
        DataComparisonRequestDTO request = new DataComparisonRequestDTO();
        request.setIds(Arrays.asList("test-id-1", "test-id-2"));
        request.setEnableAiEvaluation(true);
        
        mockMvc.perform(post("/data-comparison/start")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.taskId").exists())
                .andExpect(jsonPath("$.data.progressUrl").exists());
    }
    
    @Test
    public void testGetProgress() throws Exception {
        when(dataComparisonService.createSseConnection(anyString())).thenReturn(new SseEmitter());
        
        mockMvc.perform(get("/data-comparison/progress/test-task-id"))
                .andExpect(status().isOk());
    }
    
    @Test
    public void testHealth() throws Exception {
        mockMvc.perform(get("/data-comparison/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
}
