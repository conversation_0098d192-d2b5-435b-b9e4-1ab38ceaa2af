package com.kf.uitest.model;

import com.kf.uitest.enums.NodeType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;
import java.util.HashSet;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DependencyNode {
    /**
     * 节点类型
     */
    private NodeType type;

    /**
     * 节点ID（用例ID）
     */
    private Long id;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 依赖的节点集合
     */
    private Set<Long> dependencies = new HashSet<>();

    public DependencyNode(NodeType type, Long id, String name) {
        this.type = type;
        this.id = id;
        this.name = name;
    }

    /**
     * 获取节点的唯一标识
     */
    public String getKey() {
        return type.name() + ":" + id;
    }
}