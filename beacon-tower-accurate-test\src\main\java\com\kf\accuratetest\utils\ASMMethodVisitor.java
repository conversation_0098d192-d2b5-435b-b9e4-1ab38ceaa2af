package com.kf.accuratetest.utils;

import com.kf.accuratetest.entity.MethodInstruction;
import lombok.Getter;
import org.objectweb.asm.Handle;
import org.objectweb.asm.MethodVisitor;
import org.objectweb.asm.Opcodes;

@Getter
public class ASMMethodVisitor extends MethodVisitor {

    MethodInstruction instructions = new MethodInstruction();

    public ASMMethodVisitor(int api) {
        super(api);
    }

    public ASMMethodVisitor(MethodVisitor mv) {
        super(Opcodes.ASM9, mv);
    }

    @Override
    public void visitMethodInsn(int opcode, String owner, String name, String desc, boolean itf) {
        super.visitMethodInsn(opcode, owner, name, desc, itf);
        if (opcode != 0) {
            instructions.setOpcode(opcode);
            instructions.setOwner(owner);
            instructions.setName(name);
            instructions.setDesc(desc);
            instructions.setItf(itf);
        }
    }

    @Override
    public void visitInvokeDynamicInsn(String name, String descriptor, Handle bootstrapMethodHandle, Object... bootstrapMethodArguments) {
        super.visitInvokeDynamicInsn(name, descriptor, bootstrapMethodHandle, bootstrapMethodArguments);
        int opcode = bootstrapMethodHandle.getTag();
        if (opcode == 5 || opcode == 6 || opcode == 7 || opcode == 8 || opcode == 9 && bootstrapMethodArguments.length > 2) {
            try {
                String bootstrapMethodArgument = bootstrapMethodArguments[1] + "";
                int i2 = bootstrapMethodArgument.indexOf(".");
                int i = bootstrapMethodArgument.indexOf("(");
                int i1 = bootstrapMethodArgument.indexOf(" ");
                instructions.setOpcode(opcode);
                instructions.setOwner(bootstrapMethodArgument.substring(0, i2));
                instructions.setName(bootstrapMethodArgument.substring(i2 + 1, i));
                instructions.setDesc(bootstrapMethodArgument.substring(i, i1));
                instructions.setItf(false);
            } catch (Exception ignored) {

            }
        }
    }
}

