package com.kf.accuratetest.controller;

import com.kf.accuratetest.common.RequireHeader;
import com.kf.accuratetest.common.ResponseDoMain;
import com.kf.accuratetest.feign.BaoSiService;
import feign.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.InputStream;
import java.io.OutputStream;

@Slf4j
@RestController
@RequestMapping("/file")
@Tag(name = "文件相关接口", description = "文件相关接口")
public class FileController {

    @Resource
    private BaoSiService baoSiService;

    /**
     * 单文件上传,上传文件类型为xml
     */
    @RequireHeader("userId")
    @PostMapping("/xmlUpload")
    @Operation(summary = "上传settings.xml文件", description = "上传maven编译使用的settings文件，不上传则使用默认settings.xml")
    public ResponseDoMain fileUpload(HttpServletRequest request, @RequestParam("file") @Parameter(description="maven的settings.xml文件") MultipartFile file) {
        return baoSiService.fileUpload(request.getHeader("userId"), file);
    }

    /**
     * 查询setting.xml文件列表
     */
    @RequireHeader("userId")
    @GetMapping("/getXmlFileList")
    @Operation(summary = "查询maven-settings.xml文件列表", description = "查询maven编译使用的settings.xml文件列表")
    public ResponseDoMain queryXmlByUserId(HttpServletRequest request) {
        return baoSiService.queryXmlByUserId(request.getHeader("userId"));
    }


    /**
     * 通过ID删除单条数据（逻辑删除）
     */
    @RequireHeader("userId")
    @PostMapping("/deleteFile")
    @Operation(summary = "删除已经配置的settings.xml文件", description = "删除已经配置的maven-settings.xml文件")
    @Parameters({
            @Parameter(name = "fileId", description = "文件ID")
    })
    public ResponseDoMain deleteFile(HttpServletRequest request, @RequestBody @Parameter(description="文件ID") String fileId) {
        return baoSiService.deleteFile(request.getHeader("userId"), fileId);
    }

    /**
     * 获得页面配置的模板文件
     */
    @GetMapping("/getInterfacePageTemplateFile")
    @Operation(summary = "获得页面配置的模板文件", description = "获得页面配置的模板文件")
    public void getInterfacePageTemplateFile(HttpServletRequest req, HttpServletResponse response) {
        Response feignResponse = baoSiService.getInterfacePageTemplateFile();
        response.setContentType("application/octet-stream;charset=utf-8");

        // 获取并设置Content-Disposition头
        String contentDisposition = feignResponse.headers().get("Content-Disposition").stream()
                .findFirst().orElse("attachment");
        response.setHeader("Content-Disposition", contentDisposition);

        try (
                InputStream inputStream = feignResponse.body().asInputStream();
                BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
                OutputStream outputStream = response.getOutputStream()
        ) {
            byte[] buffer = new byte[1024 * 64]; //使用64KB的缓冲区
            int len;
            while ((len = bufferedInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }
        } catch (Exception e) {
            log.error("下载文件异常：{}", e.getMessage());
        }
    }

}

