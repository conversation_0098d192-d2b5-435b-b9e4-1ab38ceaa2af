package com.kf.aitest.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kf.aitest.dto.DataComparisonResultDTO;
import com.kf.aitest.dto.StageDataDTO;
import com.kf.aitest.entity.TDataComparison;
import com.kf.aitest.entity.TDataComparisonStage;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据对比存储服务接口
 */
public interface DataComparisonStorageService {

    /**
     * 保存对比结果
     *
     * @param resultDTO 对比结果DTO
     * @param userId 用户ID
     * @return 保存的主表记录
     */
    TDataComparison saveComparisonResult(DataComparisonResultDTO resultDTO, String userId);

    /**
     * 保存阶段结果
     *
     * @param comparisonId 对比主表ID
     * @param dataId 数据ID
     * @param stageData 阶段数据
     * @return 保存的阶段记录
     */
    TDataComparisonStage saveStageResult(Long comparisonId, String dataId, StageDataDTO stageData);

    /**
     * 批量保存阶段结果
     *
     * @param comparisonId 对比主表ID
     * @param stageDataList 阶段数据列表
     * @return 保存的阶段记录列表
     */
    List<TDataComparisonStage> batchSaveStageResults(Long comparisonId, List<StageDataDTO> stageDataList);

    /**
     * 更新对比结果
     *
     * @param comparisonId 对比主表ID
     * @param resultDTO 对比结果DTO
     * @return 更新后的记录
     */
    TDataComparison updateComparisonResult(Long comparisonId, DataComparisonResultDTO resultDTO);

    /**
     * 根据任务ID查询对比结果
     *
     * @param taskId 任务ID
     * @return 对比结果
     */
    DataComparisonResultDTO getComparisonResultByTaskId(String taskId);

    /**
     * 根据ID查询对比结果
     *
     * @param comparisonId 对比主表ID
     * @return 对比结果
     */
    DataComparisonResultDTO getComparisonResultById(Long comparisonId);

    /**
     * 分页查询对比记录
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param overallStatus 整体状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    IPage<TDataComparison> getComparisonPage(
            Page<TDataComparison> page,
            String userId,
            String taskId,
            String overallStatus,
            LocalDateTime startTime,
            LocalDateTime endTime
    );

    /**
     * 查询用户的最近对比记录
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 对比记录列表
     */
    List<TDataComparison> getRecentComparisons(String userId, Integer limit);

    /**
     * 统计用户的对比记录数量
     *
     * @param userId 用户ID
     * @return 记录数量
     */
    Long countUserComparisons(String userId);

    /**
     * 删除对比记录（软删除）
     *
     * @param comparisonId 对比主表ID
     * @return 是否删除成功
     */
    boolean deleteComparison(Long comparisonId);

    /**
     * 根据阶段名称查询阶段结果
     *
     * @param comparisonId 对比主表ID
     * @param stageName 阶段名称
     * @return 阶段结果
     */
    TDataComparisonStage getStageResult(Long comparisonId, String stageName);

    /**
     * 查询对比的所有阶段结果
     *
     * @param comparisonId 对比主表ID
     * @return 阶段结果列表
     */
    List<TDataComparisonStage> getAllStageResults(Long comparisonId);

    /**
     * 导出对比结果
     *
     * @param comparisonId 对比主表ID
     * @return 导出数据
     */
    String exportComparisonResult(Long comparisonId);
}
