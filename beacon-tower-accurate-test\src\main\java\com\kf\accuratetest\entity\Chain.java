package com.kf.accuratetest.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
@Data
@TableName("t_chain")
public class Chain {
    /**
     * 主键
     */
    private Long id;
    /**
     * message_id，目标服务器每次重启，message_id会变
     */
    private String messageId;
    /**
     * 包名
     */
    private String projectId;
    /**
     * 接口名称
     */
    private String interfaceName;
    /**
     * 调用链
     */
    private String chain;
    /**
     * static：静态分析，active：动态分析
     */
    private String source;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 是否删除  0未删除 1删除
     */
    private int isDeleted;
}

