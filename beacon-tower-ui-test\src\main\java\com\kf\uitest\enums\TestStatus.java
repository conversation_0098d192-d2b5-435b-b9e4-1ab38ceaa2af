package com.kf.uitest.enums;

import lombok.Getter;

/**
 * 测试状态枚举
 */
@Getter
public enum TestStatus {
    /**
     * 未开始
     */
    NOT_STARTED("未开始", "测试尚未开始执行"),

    /**
     * 执行中
     */
    RUNNING("执行中", "测试正在执行"),

    /**
     * 成功
     */
    SUCCESS("成功", "测试执行完全成功"),

    /**
     * 通过
     */
    PASSED("通过", "测试执行通过"),

    /**
     * 失败
     */
    FAILED("失败", "测试执行失败"),

    /**
     * 跳过
     */
    SKIPPED("跳过", "测试被跳过执行"),

    /**
     * 阻塞
     */
    BLOCKED("阻塞", "测试执行被阻塞"),

    /**
     * 部分成功
     */
    PARTIAL_SUCCESS("部分成功", "部分测试执行成功"),

    /**
     * 已取消
     */
    CANCELLED("已取消", "测试执行被取消"),

    /**
     * 超时
     */
    TIMEOUT("超时", "测试执行超时");

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 详细说明
     */
    private final String detail;

    TestStatus(String description, String detail) {
        this.description = description;
        this.detail = detail;
    }

    /**
     * 检查是否为最终状态
     */
    public boolean isFinal() {
        return this != NOT_STARTED && this != RUNNING;
    }

    /**
     * 检查是否为成功状态
     */
    public boolean isSuccess() {
        return this == SUCCESS || this == PASSED || this == PARTIAL_SUCCESS;
    }

    /**
     * 检查是否为完全成功状态
     */
    public boolean isCompleteSuccess() {
        return this == SUCCESS;
    }

    /**
     * 检查是否为失败状态
     */
    public boolean isFailure() {
        return this == FAILED || this == TIMEOUT;
    }

    /**
     * 检查是否需要人工干预
     */
    public boolean needsIntervention() {
        return this == BLOCKED || this == FAILED;
    }

    /**
     * 检查是否可以重试
     */
    public boolean canRetry() {
        return this == FAILED || this == TIMEOUT || this == BLOCKED;
    }

    /**
     * 获取状态显示文本
     */
    public String getDisplayText() {
        return String.format("%s (%s)", this.description, this.detail);
    }

    /**
     * 根据名称查找状态
     */
    public static TestStatus fromName(String name) {
        for (TestStatus status : values()) {
            if (status.name().equalsIgnoreCase(name)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown test status: " + name);
    }
}