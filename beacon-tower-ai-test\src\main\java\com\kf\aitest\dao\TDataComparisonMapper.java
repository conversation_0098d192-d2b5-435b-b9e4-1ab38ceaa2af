package com.kf.aitest.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kf.aitest.entity.TDataComparison;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据对比主表Mapper接口
 */
@Mapper
public interface TDataComparisonMapper extends BaseMapper<TDataComparison> {

    /**
     * 分页查询数据对比记录
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param overallStatus 整体状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    IPage<TDataComparison> selectComparisonPage(
            Page<TDataComparison> page,
            @Param("userId") String userId,
            @Param("taskId") String taskId,
            @Param("overallStatus") String overallStatus,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 根据任务ID查询对比记录
     *
     * @param taskId 任务ID
     * @return 对比记录列表
     */
    List<TDataComparison> selectByTaskId(@Param("taskId") String taskId);

    /**
     * 根据用户ID统计对比记录数量
     *
     * @param userId 用户ID
     * @return 记录数量
     */
    Long countByUserId(@Param("userId") String userId);

    /**
     * 查询用户的最近对比记录
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 对比记录列表
     */
    List<TDataComparison> selectRecentByUserId(@Param("userId") String userId, @Param("limit") Integer limit);

    /**
     * 统计各状态的对比记录数量
     *
     * @param userId 用户ID
     * @return 统计结果
     */
    List<TDataComparison> selectStatusStatistics(@Param("userId") String userId);
}
