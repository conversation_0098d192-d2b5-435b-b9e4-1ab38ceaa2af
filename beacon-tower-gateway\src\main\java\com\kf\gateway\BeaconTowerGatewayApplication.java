package com.kf.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.session.FlushMode;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;
import org.springframework.session.data.redis.config.annotation.web.server.EnableRedisWebSession;
import org.springframework.web.client.RestTemplate;

@EnableRedisWebSession
@EnableDiscoveryClient
@SpringBootApplication
public class BeaconTowerGatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(BeaconTowerGatewayApplication.class, args);
    }

}
