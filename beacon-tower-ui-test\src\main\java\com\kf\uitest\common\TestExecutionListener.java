package com.kf.uitest.common;

import com.kf.uitest.dto.TestExecutionRecordDTO;
import com.kf.uitest.enums.TestStatus;
import com.kf.uitest.event.TestStepCompletedEvent;
import com.kf.uitest.event.TestStepFailedEvent;
import com.kf.uitest.event.TestCaseCompletedEvent;
import com.kf.uitest.event.TestCaseFailedEvent;
import com.kf.uitest.model.TestExecutionStep;
import com.kf.uitest.model.TestResult;
import com.kf.uitest.service.UiTestExecutionRecordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@Slf4j
public class TestExecutionListener {

    @Resource
    private UiTestExecutionRecordService executionRecordService;

    /**
     * 处理步骤完成事件
     */
    @EventListener
    public void onTestStepCompleted(TestStepCompletedEvent event) {
        String executionId = event.getExecutionId();
        TestExecutionStep step = event.getStep();

        log.info("Test step completed: stepId={}, caseId={}, status={}",
                step.getId(), step.getNodeId(), step.getStatus());

        updateExecutionRecord(executionId);
    }

    /**
     * 处理步骤失败事件
     */
    @EventListener
    public void onTestStepFailed(TestStepFailedEvent event) {
        String executionId = event.getExecutionId();
        TestExecutionStep step = event.getStep();
        String errorMessage = event.getErrorMessage();

        log.error("Test step failed: stepId={}, caseId={}, error={}",
                step.getId(), step.getNodeId(), errorMessage);

        updateExecutionRecord(executionId);
    }

    /**
     * 处理用例完成事件
     */
    @EventListener
    public void onTestCaseCompleted(TestCaseCompletedEvent event) {
        String executionId = event.getExecutionId();
        Long caseId = event.getCaseId();
        TestResult.StepExecutionResult result = event.getResult();

        log.info("Test case completed: caseId={}, status={}, retries={}",
                caseId, result.getStatus(), result.getRetryCount());

        updateExecutionRecord(executionId);
    }

    /**
     * 处理用例失败事件
     */
    @EventListener
    public void onTestCaseFailed(TestCaseFailedEvent event) {
        String executionId = event.getExecutionId();
        Long caseId = event.getCaseId();
        TestResult.StepExecutionResult result = event.getResult();

        log.error("Test case failed: caseId={}, error={}, retries={}",
                caseId, result.getErrorMessage(), result.getRetryCount());

        updateExecutionRecord(executionId);
    }

    /**
     * 更新执行记录
     */
    private void updateExecutionRecord(String executionId) {
        try {
            TestExecutionRecordDTO dto = executionRecordService.getRecord(executionId);
            if (dto != null) {
                dto.setUpdateTime(LocalDateTime.now());
                TestStatus newStatus = calculateStatus(dto.getResult());
                dto.setStatus(newStatus.name());
                executionRecordService.updateById(dto.toEntity());
            }
        } catch (Exception e) {
            log.error("Failed to update execution record: {}", executionId, e);
        }
    }

    /**
     * 计算执行状态
     */
    private TestStatus calculateStatus(TestResult result) {
        if (result == null) {
            return TestStatus.RUNNING;
        }

        // 如果还没有结束时间，说明还在运行中
        if (result.getEndTime() == null) {
            return TestStatus.RUNNING;
        }

        // 获取用例执行结果
        int totalCases = result.getCaseResults().size();
        int failedCases = result.getFailedCases();
        int passedCases = result.getPassedCases();

        if (failedCases > 0) {
            return TestStatus.FAILED;
        } else if (totalCases == 0) {
            return TestStatus.SKIPPED;
        } else if (passedCases == totalCases) {
            return TestStatus.SUCCESS;
        } else {
            return TestStatus.RUNNING;
        }
    }
}