package com.kf.baosi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

// 主 DTO 类
@Data
public class JiraUserDTO {
    private String self;
    private String key;
    // xuewen.wang
    private String name;
    // 邮箱
    private String emailAddress;
    // 头像
    private AvatarUrls avatarUrls;
    // 姓名
    private String displayName;
    // 姓名的拼音，后端预处理给前端使用
    private String displayNamePinyin;
    private boolean active;
    private boolean deleted;
    private String timeZone;
    private String locale;
    private Groups groups;
    private ApplicationRoles applicationRoles;
    private String expand;

    @Data
    public static class AvatarUrls {
        @JsonProperty("48x48")
        private String x48;

        @JsonProperty("24x24")
        private String x24;

        @JsonProperty("16x16")
        private String x16;

        @JsonProperty("32x32")
        private String x32;
    }

    @Data
    public static class Groups {
        private int size;
        private List<Object> items;
    }

    @Data
    public static class ApplicationRoles {
        private int size;
        private List<Object> items;
    }
}
