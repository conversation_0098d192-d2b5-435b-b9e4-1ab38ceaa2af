package com.kf.uitest.model;

import lombok.Data;
import lombok.Builder;

import java.util.HashMap;
import java.util.Map;

@Data
@Builder
public class HookExecutionResult {
    private String executionId;
    private String hookId;
    private boolean success;
    private String message;
    private Map<String, Object> variables;
    private Map<String, Object> testData;

    public static HookExecutionResult success() {
        return HookExecutionResult.builder()
                .success(true)
                .variables(new HashMap<>())
                .build();
    }

    public static HookExecutionResult failure(String message) {
        return HookExecutionResult.builder()
                .success(false)
                .message(message)
                .variables(new HashMap<>())
                .build();
    }

    public HookExecutionResult addVariable(String name, Object value) {
        if (variables == null) {
            variables = new HashMap<>();
        }
        variables.put(name, value);
        return this;
    }

    /**
     * 添加测试数据
     */
    public HookExecutionResult addTestData(String key, Object value) {
        if (testData == null) {
            testData = new HashMap<>();
        }
        testData.put(key, value);
        return this;
    }

    /**
     * 批量添加变量
     */
    public HookExecutionResult addVariables(Map<String, Object> vars) {
        if (variables == null) {
            variables = new HashMap<>();
        }
        if (vars != null) {
            variables.putAll(vars);
        }
        return this;
    }

    /**
     * 批量添加测试数据
     */
    public HookExecutionResult addTestData(Map<String, Object> data) {
        if (testData == null) {
            testData = new HashMap<>();
        }
        if (data != null) {
            testData.putAll(data);
        }
        return this;
    }
}