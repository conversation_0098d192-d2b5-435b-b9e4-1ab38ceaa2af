package com.kf.uitest.service.impl;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Map;

@Data
public class ConditionConfig {
    private String type;  // ASSERTION, EXPRESSION
    private String target;
    private String operator;
    private String value;
    private Map<String, Object> parameters;

    @JsonIgnore
    public boolean isAssertion() {
        return "ASSERTION".equals(type);
    }

    @JsonIgnore
    public boolean isExpression() {
        return "EXPRESSION".equals(type);
    }
}
