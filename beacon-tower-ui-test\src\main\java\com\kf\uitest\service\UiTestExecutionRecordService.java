package com.kf.uitest.service;

import com.kf.uitest.entity.UITestExecutionRecord;
import com.kf.uitest.dto.TestExecutionRecordDTO;
import com.kf.uitest.enums.TestStatus;
import com.kf.uitest.model.TestResult;
import com.kf.uitest.model.StepExecutionResult;

import java.util.List;

public interface UiTestExecutionRecordService {
    /**
     * 创建执行记录
     */
    TestExecutionRecordDTO create(String executionId, List<String> sceneIds);

    /**
     * 更新执行状态
     */
    void updateStatus(String executionId, TestStatus status);

    /**
     * 保存执行结果
     */
    void saveResult(String executionId, TestResult result);

    /**
     * 获取执行记录
     */
    TestExecutionRecordDTO getRecord(String executionId);

    /**
     * 获取执行结果
     */
    TestResult getResult(String executionId);

    /**
     * 更新记录
     */
    void updateById(UITestExecutionRecord record);

    void saveStepResult(String executionId, StepExecutionResult stepResult);
}