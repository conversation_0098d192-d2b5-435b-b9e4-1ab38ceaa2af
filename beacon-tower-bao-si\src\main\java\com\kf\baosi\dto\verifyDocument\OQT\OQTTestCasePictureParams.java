package com.kf.baosi.dto.verifyDocument.OQT;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class OQTTestCasePictureParams {

    // wordTemplatePlus规定的图片类型的type为1
    @JsonProperty("wordTemplateType")
    private String wordTemplateType = "1";

    // FS服务存储的文件id
    @JsonProperty("value")
    private String value;

    // 图片宽度默认为500
    @JsonProperty("width")
    private Integer width = 500;

    // 图片高度默认为350
    @JsonProperty("height")
    private Integer height = 350;

}
