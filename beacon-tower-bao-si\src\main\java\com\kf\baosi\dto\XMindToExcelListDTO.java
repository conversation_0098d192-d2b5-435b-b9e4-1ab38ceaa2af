package com.kf.baosi.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class XMindToExcelListDTO {

    @Schema(description = "文件id")
    String fileId;

    @Schema(description = "文件名称")
    String fileName;

    @Schema(description = "任务ID")
    String taskId;

    // 是否允许删除测试用例
    @Schema(description = "是否允许删除测试用例.有测试用例时，返回true,否则返回false")
    boolean allowDelete;

    @Schema(description = "创建时间")
    String createTime;

    @Schema(description = "更新时间")
    String updateTime;

    @Schema(description = "验证文档的状态 是否完成 0未导入 1.未完成 2.导入成功 3.导入失败 4部分失败")
    String isComplete;

    @Schema(description = "当状态为失败时的错误信息")
    String errorMsg;

    //测试用例ID
    @JsonIgnore
    @Schema(description = "测试用例ID")
    String testCaseId;

}
