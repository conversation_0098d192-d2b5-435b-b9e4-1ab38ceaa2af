package com.kf.uitest.model;

import com.kf.uitest.entity.UiTestHook;
import com.kf.uitest.enums.StepStatus;
import com.kf.uitest.enums.TestExecutionStepType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestExecutionStep {
    /**
     * 步骤ID
     */
    private String id;

    /**
     * 步骤类型
     */
    private TestExecutionStepType type;

    /**
     * 节点ID（步骤ID/钩子ID）
     */
    private String nodeId;

    /**
     * 所属对象ID（用例ID/步骤ID）
     */
    private String ownerId;

    /**
     * 步骤名称
     */
    private String name;

    /**
     * 步骤内容（UiTestHook/UiTestStep）
     */
    private Object content;

    /**
     * 依赖步骤ID列表
     */
    private List<String> dependencyIds;

    /**
     * 执行顺序
     */
    private Integer order;

    /**
     * 步骤状态
     */
    @Builder.Default
    private StepStatus status = StepStatus.PENDING;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行时长（毫秒）
     */
    private Long duration;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 超时时间（秒）
     */
    @Builder.Default
    private Integer timeout = 3600;

    /**
     * 执行上下文
     */
    private Map<String, Object> context;

    /**
     * 标记步骤开始执行
     */
    public void markAsStarted() {
        this.status = StepStatus.RUNNING;
        this.startTime = LocalDateTime.now();
        this.errorMessage = null;
    }

    /**
     * 标记步骤执行完成
     */
    public void markAsCompleted(StepStatus status) {
        this.status = status;
        this.endTime = LocalDateTime.now();
        this.duration = calculateDuration();
    }

    /**
     * 标记步骤执行失败
     */
    public void markAsFailed(String errorMessage) {
        this.status = StepStatus.FAILED;
        this.errorMessage = errorMessage;
        this.endTime = LocalDateTime.now();
        this.duration = calculateDuration();
    }

    /**
     * 标记步骤被阻塞
     */
    public void markAsBlocked(String reason) {
        this.status = StepStatus.BLOCKED;
        this.errorMessage = reason;
    }

    /**
     * 标记步骤超时
     */
    public void markAsTimeout() {
        this.status = StepStatus.TIMEOUT;
        this.endTime = LocalDateTime.now();
        this.duration = calculateDuration();
        this.errorMessage = "Step execution timed out after " + timeout + " seconds";
    }

    /**
     * 重置步骤状态
     */
    public void reset() {
        this.status = StepStatus.PENDING;
        this.startTime = null;
        this.endTime = null;
        this.duration = null;
        this.errorMessage = null;
    }

    /**
     * 检查步骤是否已完成
     */
    public boolean isCompleted() {
        return this.status == StepStatus.SUCCESS ||
                this.status == StepStatus.FAILED ||
                this.status == StepStatus.SKIPPED ||
                this.status == StepStatus.CANCELLED ||
                this.status == StepStatus.TIMEOUT;
    }

    /**
     * 检查步骤是否成功完成
     */
    public boolean isSuccessful() {
        return this.status == StepStatus.SUCCESS;
    }

    /**
     * 检查步骤是否正在执行
     */
    public boolean isRunning() {
        return this.status == StepStatus.RUNNING;
    }

    /**
     * 检查步骤是否被阻塞
     */
    public boolean isBlocked() {
        return this.status == StepStatus.BLOCKED;
    }

    /**
     * 计算执行时长
     */
    private Long calculateDuration() {
        if (startTime != null && endTime != null) {
            return ChronoUnit.MILLIS.between(startTime, endTime);
        }
        return null;
    }

    /**
     * 获取步骤的简要信息
     */
    public String getStepSummary() {
        return String.format("%s[%s]-%s", type, nodeId, name);
    }

    /**
     * 获取步骤的详细信息
     */
    public String getStepDetail() {
        StringBuilder detail = new StringBuilder(getStepSummary());

        if (startTime != null) {
            detail.append(String.format(", startTime=%s", startTime));
        }
        if (endTime != null) {
            detail.append(String.format(", endTime=%s", endTime));
        }
        if (duration != null) {
            detail.append(String.format(", duration=%dms", duration));
        }
        if (errorMessage != null) {
            detail.append(String.format(", error=%s", errorMessage));
        }

        // 添加钩子特定信息
        if (content instanceof UiTestHook hook) {
            detail.append(String.format(", hookId=%s, inputValue=%s",
                    hook.getId(),
                    hook.getInputValue()));
        }

        return detail.toString();
    }

    @JsonIgnore
    public boolean isHook() {
        return type == TestExecutionStepType.HOOK;
    }

    @JsonIgnore
    public boolean isStep() {
        return type == TestExecutionStepType.STEP;
    }

    @JsonIgnore
    public boolean isBlock() {
        return type.isBlock();
    }

    @JsonIgnore
    public boolean isBlockStart() {
        return type.isBlockStart();
    }

    @JsonIgnore
    public boolean isBlockEnd() {
        return type.isBlockEnd();
    }
}