<template>
    <div style="width: 40%;" class="user-activity">
        <el-form ref="updatePasswordForm" :model="updatePasswordInfo" :rules="updatePasswordRules" label-position="left"
                 label-width="auto">
            <el-form-item label="旧密码" prop="oldPassword" required>
                <el-input type="password" v-model="updatePasswordInfo.oldPassword"
                          placeholder="请输入旧密码"></el-input>
            </el-form-item>
            <el-form-item label="新密码" prop="newPassword" required>
                <el-input type="password" v-model="updatePasswordInfo.newPassword"
                          placeholder="请输入新密码"></el-input>
            </el-form-item>
            <el-form-item label="确认密码" prop="confirmPassword" required>
                <el-input type="password" v-model="updatePasswordInfo.confirmPassword" placeholder="请再次输入新密码"
                          @keydown.enter="resetUserPasswordSubmit"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button v-prevent-default type="primary" @click="resetUserPasswordSubmit">确定</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang='ts'>
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { validate } from '@/utils/formExtend'
import { resetUserPassword } from '@/api/layout'

let updatePasswordInfo = reactive({
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
})

const updatePasswordValidatePass = (rule: any, value: string, callback: any) => {
    if (value === '') {
        callback(new Error('请再次输入密码！'))
    } else if (value !== updatePasswordInfo.newPassword) {
        callback(new Error('两次输入密码不一致！'))
    } else {
        callback()
    }
}

const updatePasswordRules = reactive({
    oldPassword: [{ required: true, message: '请输入密码', trigger: 'blur' }],
    newPassword: [{ required: true, message: '请输入密码', trigger: 'blur' }, {
        min: 6,
        max: 20,
        message: '长度在 6 到 20 个字符',
        trigger: 'blur'
    }],
    confirmPassword: [{
        required: true,
        message: '请再次输入密码',
        trigger: 'blur'
    }, { validator: updatePasswordValidatePass, trigger: 'blur' }]
})

const updatePasswordForm = ref(null)

const resetUserPasswordSubmit = async () => {
    if (!await validate(updatePasswordForm)) return
    let { oldPassword, newPassword } = updatePasswordInfo
    const res = await resetUserPassword({ oldPassWord: oldPassword, newPassWord: newPassword })
    if (res.data.isSuccess) {
        ElMessage.success(res.data.message)
    } else {
        ElMessage.error(res.data.message)
    }
}
</script>

<style scoped>

</style>
