package com.kf.accuratetest.utils;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.druid.util.StringUtils;
import com.kf.accuratetest.entity.MethodInstruction;
import lombok.extern.slf4j.Slf4j;
import org.objectweb.asm.tree.AnnotationNode;
import org.objectweb.asm.tree.ClassNode;
import org.objectweb.asm.tree.MethodNode;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class AsmControllerAnalyse {

    //存放变化的方法
    private final List<String> changeMethod;

    //存放受影响的调用链
    private final List<List<String>> controllerLinks = new ArrayList<>();
    private final List<ClassNode> classNodes = new ArrayList<>();
    private Map<String, ClassNode> classNodeMap;//key为classname

    //存放接口对应实现类的map
    private final Map<String, List<String>> interfaceMap = new HashMap<>();

    public AsmControllerAnalyse(String projectPath, List<String> changeMethod) {
        this.changeMethod = changeMethod;
        AsmUtil.buildProjectClassNode(new File(projectPath), classNodes, interfaceMap);
    }

    public List<List<String>> analyse() {
        //classNodes与changeMethod都不为空才进行调用链分析，否则没有意义
        if (CollUtil.isEmpty(this.classNodes) || CollUtil.isEmpty(this.changeMethod)) {
            log.info("classNodes或changeMethod为空，不进行调用链分析");
            return controllerLinks;
        }

        this.classNodeMap = classNodes.stream().collect(Collectors.toMap(t -> t.name, c -> c, (k1, k2) -> k1));
        classNodes.forEach(this::analyseController);
        return controllerLinks;
    }

    private void analyseController(ClassNode classNode) {
        boolean isController = false;
        //获取类的所有注解
        List<AnnotationNode> classAnnotations = classNode.visibleAnnotations;
        String classMappingValue = "";//这里类上面的接口
        if (CollUtil.isEmpty(classAnnotations)) return;
        //先遍历注解，得到接口名称
        for (AnnotationNode classAnnotation : classAnnotations) {
            String a = classAnnotation.desc;
            if (a.contains("Controller") || a.contains("Mapping")) {
                isController = true;
                //获取类的接口名称
                if (classAnnotation.values != null) {
                    classMappingValue = AsmUtil.getAnnotationValue(classAnnotation, "value");
                    break;
                }
            }
        }

        if (!isController) return;
        List<MethodNode> methodNodes = classNode.methods;
        for (MethodNode methodNode : methodNodes) {
            //获取方法的所有注解
            List<AnnotationNode> methodAnnotations = methodNode.visibleAnnotations;
            if (CollUtil.isEmpty(methodAnnotations)) continue;
            //遍历方法注解,获得接口名称
            boolean isInterface = false;
            String methodMappingValue = "";
            String methodApiOperation = "";
            String methodApiOperationNotes = "";
            for (AnnotationNode methodAnnotation : methodAnnotations) {
                List<Object> mav = methodAnnotation.values;
                if (CollUtil.isEmpty(mav)) continue;
                if (methodAnnotation.desc.contains("ApiOperation")) {
                    for (int i = 0; i < methodAnnotation.values.size(); i++) {
                        if (methodAnnotation.values.get(i).equals("value")) {
                            methodApiOperation = methodAnnotation.values.get(i + 1).toString();
                        }
                        if (methodAnnotation.values.get(i).equals("notes")) {
                            methodApiOperationNotes = methodAnnotation.values.get(i + 1).toString();
                        }
                    }
                } else if (methodAnnotation.desc.contains("Operation")) {
                    //todo swagger3注解，后续优化
                    //swagger3注解，后续优化
                }
                //如果该注解包含Mapping，则获取他的value
                if (!methodAnnotation.desc.contains("Mapping")) continue;
                isInterface = true;
                methodMappingValue = AsmUtil.getAnnotationValue(methodAnnotation, "value");
            }
            if (!isInterface) continue;
            List<MethodInstruction> methodInstructions = AsmUtil.methodInvokeInstructions(methodNode);
            String methodAllName = AsmUtil.methodAllName(classNode, methodNode);
            //调用链集合，第一个元素是接口名称
            List<String> invokeLink = new ArrayList<>();
            //如果方法注解前没有“/”，就拼上去一个
            if (!classMappingValue.endsWith("/") && !methodMappingValue.startsWith("/"))
                methodMappingValue = "/" + methodMappingValue;
            invokeLink.add(classMappingValue + methodMappingValue);
            invokeLink.add(methodApiOperation);
            invokeLink.add(methodApiOperationNotes);
            invokeLink.add(methodAllName);
            //如果调用指令是空的那就是最终链
            if (CollUtil.isEmpty(methodInstructions)) {
                addToLink(invokeLink);
                continue;
            }
            for (MethodInstruction instruction : methodInstructions) {
                List<String> invokeLinkc = new ArrayList<>(invokeLink);
                analyseInvokeInstruction(instruction, invokeLinkc, classNode.superName);
            }
        }
    }

    private void analyseInvokeInstruction(MethodInstruction instruction, List<String> invokeLink, String classSuperName) {

        //当前方法的类名
        String ownerClassName = instruction.getOwner();
        //如果是接口就判断是否有该接口的唯一实现类，如果有就进行桥接
        if (instruction.getItf()) {
            //如果是接口，就获取他的所有实现类
            List<String> interfaces = interfaceMap.get(ownerClassName);
            //如果有多个实现类就跳过，因为静态分析不知道应该找哪个实现类
            if (CollUtil.isEmpty(interfaceMap) || CollUtil.isEmpty(interfaces) || interfaces.size() != 1) {
                addToLink(invokeLink);
                return;
            }
            //只有一个实现类，就把这个实现类与调用方法进行桥接
            ownerClassName = interfaces.get(0);

        }

        ClassNode ownerClassNode = classNodeMap.get(ownerClassName);
        if (ownerClassNode == null) {
            addToLink(invokeLink);
            return;
        }

        //判断类中是否包含某个方法,解决this关键字调用父类方法导致ASM解析异常
        Boolean classContainsMethod = AsmUtil.classContainsMethod(ownerClassNode, instruction.getName(), instruction.getDesc());
        if (!classContainsMethod && !StringUtils.equals("java/lang/Object", classSuperName)) {
            ownerClassNode = classNodeMap.get(classSuperName);
        }

        if (ownerClassNode == null) {
            addToLink(invokeLink);
            return;
        }

        MethodNode methodNode = AsmUtil.classMatchingMethod(ownerClassNode, instruction.getName(), instruction.getDesc());
        if (methodNode == null) {
            //如果没有匹配到方法
            addToLink(invokeLink);
            return;
        }

        String methodAllName = AsmUtil.methodAllName(ownerClassNode, methodNode);
        //过滤递归导致的死循环
        if (invokeLink.contains(methodAllName)) {
            addToLink(invokeLink);
            return;
        }
        invokeLink.add(methodAllName);

        List<MethodInstruction> methodInstructions = AsmUtil.methodInvokeInstructions(methodNode);
        //这里可能出现没有方法调用指令
        if (CollUtil.isEmpty(methodInstructions)) {
            addToLink(invokeLink);
            return;
        }

        for (MethodInstruction ins : methodInstructions) {
            List<String> invokeLinkc = new ArrayList<>(invokeLink);
            analyseInvokeInstruction(ins, invokeLinkc, ownerClassNode.superName);
        }
    }

    //这里需要判断满足变化的方法是否在调用链中
    private void addToLink(List<String> invokeLink) {
        if (CollUtil.isEmpty(invokeLink)) return;
        //从后往前截取有效调用链
        for (int i = invokeLink.size() - 1; i >= 0; i--) {
            if (this.changeMethod.contains(invokeLink.get(i))) {
                if (invokeLink.size() - 1 != i) {
                    invokeLink = invokeLink.subList(0, i + 1);
                }
                if (!this.controllerLinks.contains(invokeLink)) {
                    this.controllerLinks.add(invokeLink);
                    //向队列中添加新地调用链
//                    resultHandler.add(invokeLink.toString());
                }
                return;
            }
        }
//        for (String method : this.changeMethod) {
//            if (invokeLink.contains(method)) {
//                if (!this.controllerLinks.contains(invokeLink)) {
//                    this.controllerLinks.add(invokeLink);
//                }
//                return;
//            }
//        }
    }

}
