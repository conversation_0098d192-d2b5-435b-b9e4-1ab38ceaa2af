package com.kf.aitest.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kf.aitest.service.PromptTemplateService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 提示词模板服务实现类
 */
@Slf4j
@Service
public class PromptTemplateServiceImpl implements PromptTemplateService {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 提示词模板缓存
     */
    private final Map<String, String> templateCache = new ConcurrentHashMap<>();
    
    /**
     * 支持的阶段名称
     */
    private static final String[] SUPPORTED_STAGES = {
        "recognize", "extraction", "structured", "transformer"
    };

    /**
     * 支持的指导文档
     */
    private static final String[] SUPPORTED_GUIDES = {
        "deduplication-guide"
    };
    
    @PostConstruct
    public void init() {
        loadAllTemplates();
    }
    
    @Override
    public String getPromptTemplate(String stageName) {
        if (stageName == null || stageName.trim().isEmpty()) {
            throw new IllegalArgumentException("阶段名称不能为空");
        }
        
        String template = templateCache.get(stageName.toLowerCase());
        if (template == null) {
            log.warn("未找到阶段 {} 的提示词模板，尝试重新加载", stageName);
            loadTemplate(stageName);
            template = templateCache.get(stageName.toLowerCase());
        }
        
        if (template == null) {
            throw new IllegalArgumentException("不支持的阶段名称: " + stageName);
        }
        
        return template;
    }
    
    @Override
    public String buildEvaluationPrompt(String stageName, Object uatData, Object testData) {
        try {
            // 获取提示词模板
            String template = getPromptTemplate(stageName);
            
            // 构建完整提示词
            StringBuilder prompt = new StringBuilder();
            prompt.append(template).append("\n\n");
            
            prompt.append("## 数据对比\n\n");
            prompt.append("### UAT环境数据（基准数据）：\n");
            prompt.append("```json\n");
            prompt.append(objectMapper.writeValueAsString(uatData));
            prompt.append("\n```\n\n");
            
            prompt.append("### TEST环境数据（待评估数据）：\n");
            prompt.append("```json\n");
            prompt.append(objectMapper.writeValueAsString(testData));
            prompt.append("\n```\n\n");
            
            prompt.append("请根据上述提示词要求，对比两份数据并给出详细的评估结果。");
            
            return prompt.toString();
            
        } catch (Exception e) {
            log.error("构建评估提示词失败: stage={}, error={}", stageName, e.getMessage(), e);
            throw new RuntimeException("构建评估提示词失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean hasTemplate(String stageName) {
        if (stageName == null) {
            return false;
        }
        return templateCache.containsKey(stageName.toLowerCase());
    }
    
    @Override
    public void reloadTemplates() {
        log.info("开始重新加载所有提示词模板");
        templateCache.clear();
        loadAllTemplates();
        log.info("提示词模板重新加载完成，共加载 {} 个模板", templateCache.size());
    }
    
    /**
     * 加载所有提示词模板
     */
    private void loadAllTemplates() {
        for (String stageName : SUPPORTED_STAGES) {
            loadTemplate(stageName);
        }
        // 加载指导文档
        for (String guideName : SUPPORTED_GUIDES) {
            loadTemplate(guideName);
        }
    }
    
    /**
     * 加载单个提示词模板
     */
    private void loadTemplate(String stageName) {
        try {
            String resourcePath = "prompts/" + stageName.toLowerCase() + ".md";
            ClassPathResource resource = new ClassPathResource(resourcePath);
            
            if (!resource.exists()) {
                log.warn("提示词模板文件不存在: {}", resourcePath);
                return;
            }
            
            byte[] bytes = resource.getInputStream().readAllBytes();
            String template = new String(bytes, StandardCharsets.UTF_8);
            
            templateCache.put(stageName.toLowerCase(), template);
            log.debug("成功加载提示词模板: {}", stageName);
            
        } catch (IOException e) {
            log.error("加载提示词模板失败: stage={}, error={}", stageName, e.getMessage(), e);
        }
    }
}
