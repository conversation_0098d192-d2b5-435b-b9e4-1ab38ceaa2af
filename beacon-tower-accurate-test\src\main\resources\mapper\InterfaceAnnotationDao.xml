<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kf.accuratetest.dao.InterfaceAnnotationDao">

    <resultMap id="BaseResultMap" type="com.kf.accuratetest.entity.InterfaceAnnotation">
        <!--@Table t_interface_annotation-->
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="interfaceName" column="interface_name" jdbcType="VARCHAR"/>
        <result property="interfaceDesc" column="interface_desc" jdbcType="VARCHAR"/>
        <result property="interfaceNotes" column="interface_notes" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select interface_name,
               interface_desc,
               interface_notes
        from t_interface_annotation
        where task_id = #{taskId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
        task_id, interface_name, interface_desc, interface_notes, create_time, update_time, is_deleted
        from t_interface_annotation
        <where>
            <if test="taskId != null and taskId != ''">
                and task_id = #{taskId}
            </if>
            <if test="interfaceName != null and interfaceName != ''">
                and interface_name = #{interfaceName}
            </if>
            <if test="interfaceDesc != null and interfaceDesc != ''">
                and interface_desc = #{interfaceDesc}
            </if>
            <if test="interfaceNotes != null and interfaceNotes != ''">
                and interface_notes = #{interfaceNotes}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null and isDeleted != ''">
                and is_deleted = #{isDeleted}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
    select
    task_id, interface_name, interface_desc, interface_notes, create_time, update_time, is_deleted
    from t_interface_annotation
    <where>
        <if test="taskId != null and taskId != ''">
            and task_id = #{taskId}
        </if>
        <if test="interfaceName != null and interfaceName != ''">
            and interface_name = #{interfaceName}
        </if>
        <if test="interfaceDesc != null and interfaceDesc != ''">
            and interface_desc = #{interfaceDesc}
        </if>
        <if test="interfaceNotes != null and interfaceNotes != ''">
            and interface_notes = #{interfaceNotes}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="isDeleted != null and isDeleted != ''">
            and is_deleted = #{isDeleted}
        </if>
    </where>
    </select>
    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from t_interface_annotation
        <where>
            <if test="taskId != null and taskId != ''">
                and task_id = #{taskId}
            </if>
            <if test="interfaceName != null and interfaceName != ''">
                and interface_name = #{interfaceName}
            </if>
            <if test="interfaceDesc != null and interfaceDesc != ''">
                and interface_desc = #{interfaceDesc}
            </if>
            <if test="interfaceNotes != null and interfaceNotes != ''">
                and interface_notes = #{interfaceNotes}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null and isDeleted != ''">
                and is_deleted = #{isDeleted}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="" useGeneratedKeys="true">
        insert into t_interface_annotation(task_id, interface_name, interface_desc, interface_notes, create_time, update_time, is_deleted)
        values (#{taskId}, #{interfaceName}, #{interfaceDesc}, #{interfaceNotes}, #{createTime}, #{updateTime}, #{isDeleted})
    </insert>

    <insert id="insertBatch" keyProperty="" useGeneratedKeys="true">
        insert into t_interface_annotation(task_id, interface_name, interface_desc, interface_notes,
                                           create_time, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.taskId}, #{entity.interfaceName}, #{entity.interfaceDesc}, #{entity.interfaceNotes},
              #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="" useGeneratedKeys="true">
        insert into t_interface_annotation(task_id,interface_name, interface_desc, interface_notes, create_time, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.taskId},#{entity.interfaceName}, #{entity.interfaceDesc}, #{entity.interfaceNotes}, #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
        on duplicate key update
        task_id = values(task_id),
        interface_name = values(interface_name),
        interface_desc = values(interface_desc),
        interface_notes = values(interface_notes),
        create_time = values(create_time),
        update_time = values(update_time),
        is_deleted = values(is_deleted)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update t_interface_annotation
        <set>
            <if test="taskId != null and taskId != ''">
                task_id = #{taskId},
            </if>
            <if test="interfaceName != null and interfaceName != ''">
                interface_name = #{interfaceName},
            </if>
            <if test="interfaceDesc != null and interfaceDesc != ''">
                interface_desc = #{interfaceDesc},
            </if>
            <if test="interfaceNotes != null and interfaceNotes != ''">
                interface_notes = #{interfaceNotes},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isDeleted != null and isDeleted != ''">
                is_deleted = #{isDeleted},
            </if>
        </set>
        where task_id = #{taskId}
    </update>


</mapper>

