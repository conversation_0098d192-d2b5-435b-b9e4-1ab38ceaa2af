package com.kf.uitest.utils;

import com.kf.uitest.exception.TestExecutionException;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 表达式计算器 - 使用Spring Expression Language (SpEL)
 */
@Component
public class ExpressionEvaluator {

    private final ExpressionParser parser = new SpelExpressionParser();

    /**
     * 计算表达式（带变量Map）
     */
    public Object evaluate(String expression, Map<String, Object> variables) {
        try {
            Expression exp = parser.parseExpression(expression);
            EvaluationContext context = new StandardEvaluationContext();

            // 将变量添加到上下文
            if (variables != null) {
                variables.forEach(context::setVariable);
            }

            return exp.getValue(context);

        } catch (Exception e) {
            throw new TestExecutionException(
                    String.format("Failed to evaluate expression: %s, error: %s",
                            expression, e.getMessage()), e);
        }
    }

    /**
     * 计算表达式（带单个值）
     */
    public Object evaluate(String expression, Object value) {
        try {
            Expression exp = parser.parseExpression(expression);
            StandardEvaluationContext context = new StandardEvaluationContext();
            context.setRootObject(value);

            return exp.getValue(context);

        } catch (Exception e) {
            throw new TestExecutionException(
                    String.format("Failed to evaluate expression: %s, error: %s",
                            expression, e.getMessage()), e);
        }
    }

    /**
     * 计算布尔表达式
     *
     * @param expression 表达式
     * @param variables  变量
     * @return 布尔结果
     */
    public boolean evaluateBoolean(String expression, Map<String, Object> variables) {
        Object result = evaluate(expression, variables);
        if (result instanceof Boolean) {
            return (Boolean) result;
        }
        throw new TestExecutionException(
                String.format("Expression result is not boolean: %s, actual type: %s",
                        expression, result != null ? result.getClass().getName() : "null"));
    }

    /**
     * 计算字符串表达式
     *
     * @param expression 表达式
     * @param variables  变量
     * @return 字符串结果
     */
    public String evaluateString(String expression, Map<String, Object> variables) {
        Object result = evaluate(expression, variables);
        return result != null ? result.toString() : null;
    }
}