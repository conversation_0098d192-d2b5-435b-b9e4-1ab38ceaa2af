package com.kf.uitest.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kf.uitest.entity.UiTestAssertion;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface UiTestAssertionMapper extends BaseMapper<UiTestAssertion> {
    // 只保留特殊的查询方法，基础的CRUD操作直接使用BaseMapper提供的方法
    @Select("SELECT * FROM ui_test_assertion WHERE hook_id = #{hookId} ORDER BY id ASC")
    List<UiTestAssertion> findByHookId(@Param("hookId") String hookId);
}