<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kf.userservice.dao.TUserDao">

    <resultMap type="com.kf.userservice.entity.TUser" id="TUserMap">
        <result property="userId" column="USER_ID" jdbcType="INTEGER"/>
        <result property="username" column="USERNAME" jdbcType="VARCHAR"/>
        <result property="password" column="PASSWORD" jdbcType="VARCHAR"/>
        <result property="email" column="EMAIL" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="modifyTime" column="MODIFY_TIME" jdbcType="TIMESTAMP"/>
        <result property="avatar" column="AVATAR" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="TUserMap">
        select
          t_user.USER_ID, USERNAME, PASSWORD, EMAIL, STATUS, t_user.CREATE_TIME, t_user.MODIFY_TIME, AVATAR
        from t_user
        where t_user.USER_ID = #{userId}
    </select>

    <!--查询单个-->
    <select id="queryByUserName" resultMap="TUserMap">
        select
          USER_ID, USERNAME, PASSWORD, EMAIL,STATUS, CREATE_TIME, MODIFY_TIME, AVATAR
        from t_user
        where USERNAME = #{username}
    </select>

    <!--查询单个-->
    <select id="queryByEmail" resultMap="TUserMap">
        select
        USER_ID, USERNAME, PASSWORD, EMAIL,STATUS, CREATE_TIME, MODIFY_TIME, AVATAR
        from t_user
        where EMAIL = #{email}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="TUserMap">
        select
          USER_ID, USERNAME, PASSWORD, EMAIL, STATUS, CREATE_TIME, MODIFY_TIME, AVATAR
        from t_user
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultType="com.kf.userservice.entity.TUser" parameterType="com.kf.userservice.entity.TUser">
        select
        t_user.USER_ID, USERNAME, PASSWORD, EMAIL, STATUS, CREATE_TIME, MODIFY_TIME, AVATAR, GROUP_CONCAT(t_user_role.role_id) roleId
        from t_user left join t_user_role on t_user.USER_ID = t_user_role.USER_ID
        <where>
            <if test="userId != null">
                and t_user.USER_ID = #{userId}
            </if>
            <if test="username != null and username != ''">
                and USERNAME = #{username}
            </if>
            <if test="password != null and password != ''">
                and PASSWORD = #{password}
            </if>
            <if test="email != null and email != ''">
                and EMAIL = #{email}
            </if>
            <if test="status != null and status != ''">
                and STATUS = #{status}
            </if>
            <if test="createTime != null and createTime != ''">
                <![CDATA[
                     and CREATE_TIME > #{createTime}
                ]]>
            </if>
            <if test="modifyTime != null and modifyTime != ''">
                <![CDATA[
                  and MODIFY_TIME < #{modifyTime}
                ]]>
            </if>
            <if test="avatar != null and avatar != ''">
                and AVATAR = #{avatar}
            </if>
        </where>
        group by
        t_user.USER_ID, USERNAME, PASSWORD, EMAIL, STATUS, CREATE_TIME, MODIFY_TIME, AVATAR
    </select>

    <select id="getRoleById" resultType="java.lang.String">
        SELECT
        t_role.ROLE_NAME
        FROM
        t_user_role  LEFT JOIN
        t_role on t_user_role.ROLE_ID = t_role.ROLE_ID
        WHERE t_user_role.USER_ID = #{userId};
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="userId" useGeneratedKeys="true">
        insert into t_user(USERNAME, PASSWORD, EMAIL, STATUS, CREATE_TIME, MODIFY_TIME, AVATAR)
        values (#{username}, #{password}, #{email}, #{status}, #{createTime}, #{modifyTime}, #{avatar})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update t_user
        <set>
            <if test="username != null and username != ''">
                USERNAME = #{username},
            </if>
            <if test="password != null and password != ''">
                PASSWORD = #{password},
            </if>
            <if test="email != null and email != ''">
                EMAIL = #{email},
            </if>
            <if test="status != null and status != ''">
                STATUS = #{status},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime},
            </if>
            <if test="modifyTime != null">
                MODIFY_TIME = #{modifyTime},
            </if>
            <if test="avatar != null and avatar != ''">
                AVATAR = #{avatar},
            </if>
        </set>
        where USER_ID = #{userId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from t_user where USER_ID = #{userId}
    </delete>
    <delete id="deleteByIds">
        delete from t_user where USER_ID in (#{ids})
    </delete>

</mapper>