package com.kf.aitest.dto;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据对比结果DTO
 */
@Data
public class DataComparisonResultDTO {
    
    /**
     * 对比ID
     */
    private String id;
    
    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 请求文件的MD5哈希值
     */
    private String fileMd5;

    /**
     * 四个阶段的数据对比结果
     */
    private List<StageDataDTO> stageResults;
    
    /**
     * 整体对比状态：SUCCESS、FAILED、PARTIAL_SUCCESS
     */
    private String overallStatus;
    
    /**
     * 整体AI评估结果
     */
    private String overallAiEvaluation;
    
    /**
     * 整体评估得分（0-100）
     */
    private Integer overallScore;
    
    /**
     * 总耗时（毫秒）
     */
    private Long totalDuration;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 错误信息汇总
     */
    private List<String> errors;
    
    /**
     * 成功的阶段数量
     */
    private Integer successStageCount;
    
    /**
     * 总阶段数量
     */
    private Integer totalStageCount = 4;
}
