package com.kf.uitest.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("ui_test_block_step")
public class UiTestBlockStep {
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    
    @TableField("block_id")
    private String blockId;
    
    @TableField("step_id")
    private String stepId;
    
    @TableField("step_order")
    private Integer stepOrder;
    
    @TableField("create_time")
    private LocalDateTime createTime;
    
    @TableField("update_time")
    private LocalDateTime updateTime;
} 