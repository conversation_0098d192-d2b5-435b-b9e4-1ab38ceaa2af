package com.kf.uitest.utils;

import com.kf.uitest.exception.TestExecutionException;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * 安全工具类 - 从请求上下文中获取用户和项目信息
 */
public class SecurityUtils {

    private static final String USER_ID_ATTRIBUTE = "X-User-Id";
    private static final String PROJECT_ID_ATTRIBUTE = "X-Project-Id";

    private SecurityUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 获取当前用户ID
     */
    public static Long getCurrentUserId() {
        Object userId = getRequestAttribute(USER_ID_ATTRIBUTE);
        if (userId == null) {
            throw new TestExecutionException("User ID not found in request context");
        }
        return (Long) userId;
    }

    /**
     * 获取当前项目ID
     */
    public static Long getCurrentProjectId() {
        Object projectId = getRequestAttribute(PROJECT_ID_ATTRIBUTE);
        if (projectId == null) {
            throw new TestExecutionException("Project ID not found in request context");
        }
        return (Long) projectId;
    }

    /**
     * 从请求上下文获取属性
     */
    private static Object getRequestAttribute(String name) {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            throw new TestExecutionException("Request context not found");
        }
        return attributes.getAttribute(name, RequestAttributes.SCOPE_REQUEST);
    }
}