package com.kf.aitest.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kf.aitest.dto.StageDataDTO;
import com.kf.aitest.service.DataFetchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.web.util.UriUtils;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据获取服务实现
 */
@Slf4j
@Service
public class DataFetchServiceImpl implements DataFetchService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${api.fs.path:/pv-manus-front/api/fs/preview}")
    private String fsApiPath;
    
    // 阶段名称到文件名的映射
    private static final Map<String, String> STAGE_FILE_MAP = new HashMap<>();
    
    static {
        STAGE_FILE_MAP.put("recognize", "content/content.md");
        STAGE_FILE_MAP.put("extraction", "extract_result.json");
        STAGE_FILE_MAP.put("structured", "structured.json");
        STAGE_FILE_MAP.put("transformer", "transformer.json");
    }
    
    @Override
    public StageDataDTO fetchStageData(String id, String stageName, String uatBaseUrl, String testBaseUrl, Integer timeoutSeconds) {
        StageDataDTO stageData = new StageDataDTO();
        stageData.setStageName(stageName);
        stageData.setDataType(getDataType(stageName));
        stageData.setFetchTime(LocalDateTime.now());

        long startTime = System.currentTimeMillis();

        try {
            // 构建UAT环境URL
            String uatUrl = buildUrl(uatBaseUrl, id, stageName);
            Object uatData = fetchDataFromUrl(uatUrl, timeoutSeconds);
            stageData.setUatData(uatData);
            // 构建TEST环境URL
            String testUrl = buildUrl(testBaseUrl, id, stageName);
            Object testData = fetchDataFromUrl(testUrl, timeoutSeconds);
            stageData.setTestData(testData);

            stageData.setStatus("SUCCESS");
            log.info("成功获取阶段数据: id={}, stage={}", id, stageName);

        } catch (Exception e) {
            stageData.setStatus("FAILED");
            stageData.setErrorMessage(e.getMessage());
            log.error("获取阶段数据失败: id={}, stage={}, error={}", id, stageName, e.getMessage(), e);
        } finally {
            stageData.setDuration(System.currentTimeMillis() - startTime);
        }

        return stageData;
    }
    
    @Override
    public String getStageFileName(String stageName) {
        return STAGE_FILE_MAP.getOrDefault(stageName, "unknown.json");
    }

    /**
     * 构建请求URL
     * @param baseUrl 基础URL
     * @param id 数据ID
     * @param stageName 阶段名称
     * @return 构建的URL
     */
    public String buildUrl(String baseUrl, String id, String stageName) {
        String fileName = getStageFileName(stageName);

        // 构建file_path参数：id/stage/filename
        String filePathParam = String.format("%s/%s/%s", id, stageName, fileName);

        // 如果fsApiPath为null（如在测试中），使用默认值
        String apiPath = (fsApiPath != null) ? fsApiPath : "/pv-manus-front/api/fs/preview";

        // 使用UriComponentsBuilder构建URL
        String url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                .path(apiPath)
                .queryParam("type", "document")
                .queryParam("file_path", filePathParam)
                .build()
                .toUriString();

        log.debug("构建的URL: {}", url);
        return url;
    }
    
    /**
     * 从URL获取数据
     */
    private Object fetchDataFromUrl(String url, Integer timeoutSeconds) {
        try {
            log.debug("正在获取数据: {}", url);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, null, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                
                // 尝试解析为JSON，如果失败则返回原始字符串
                try {
                    return objectMapper.readValue(responseBody, Object.class);
                } catch (Exception e) {
                    // 如果不是JSON格式（如markdown），直接返回字符串
                    return responseBody;
                }
            } else {
                throw new RuntimeException("HTTP请求失败，状态码: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("从URL获取数据失败: {}, error: {}", url, e.getMessage());
            throw new RuntimeException("获取数据失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 根据阶段名称获取数据类型
     */
    private String getDataType(String stageName) {
        if ("recognize".equals(stageName)) {
            return "md";
        }
        return "json";
    }
}
