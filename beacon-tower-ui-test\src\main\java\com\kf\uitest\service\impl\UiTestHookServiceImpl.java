package com.kf.uitest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kf.uitest.dao.UiTestHookMapper;
import com.kf.uitest.entity.UiTestHook;
import com.kf.uitest.enums.HookOwnerType;
import com.kf.uitest.enums.HookTiming;
import com.kf.uitest.service.UiTestHookService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
public class UiTestHookServiceImpl implements UiTestHookService {

    private final UiTestHookMapper hookMapper;

    @Override
    public List<UiTestHook> findByOwnerId(HookOwnerType ownerType, String ownerId) {
        if (ownerType == null || ownerId == null) {
            return Collections.emptyList();
        }
        return hookMapper.selectList(new LambdaQueryWrapper<UiTestHook>()
                .eq(UiTestHook::getOwnerId, ownerId)
                .orderByAsc(UiTestHook::getHookOrder));
    }

    @Override
    public List<UiTestHook> findHooks(HookOwnerType ownerType, String ownerId, HookTiming timing) {
        if (ownerType == null || ownerId == null || timing == null) {
            return Collections.emptyList();
        }
        return hookMapper.selectList(new LambdaQueryWrapper<UiTestHook>()
                .eq(UiTestHook::getOwnerId, ownerId)
                .eq(UiTestHook::getHookTiming, timing)
                .orderByAsc(UiTestHook::getHookOrder));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UiTestHook save(UiTestHook hook) {
        if (hook == null) {
            throw new IllegalArgumentException("Hook cannot be null");
        }

        if (hook.getId() == null) {
            hookMapper.insert(hook);
        } else {
            hookMapper.updateById(hook);
        }
        return hook;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UiTestHook> saveBatch(List<UiTestHook> hooks) {
        if (CollectionUtils.isEmpty(hooks)) {
            return Collections.emptyList();
        }

//        if (hooks.stream().anyMatch(hook ->
//            !HookValidationUtils.isValidAction(hook.getOwnerType(), hook.getActionType()))) {
//            throw new IllegalArgumentException("Invalid action type found in hooks");
//        }

        for (UiTestHook hook : hooks) {
            save(hook);
        }
        return hooks;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (id == null) {
            return;
        }
        hookMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        hookMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByOwner(HookOwnerType ownerType, Long ownerId) {
        if (ownerType == null || ownerId == null) {
            return;
        }
        hookMapper.delete(new LambdaQueryWrapper<UiTestHook>()
                .eq(UiTestHook::getOwnerId, ownerId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrder(List<UiTestHook> hooks) {
        if (CollectionUtils.isEmpty(hooks)) {
            return;
        }

        for (int i = 0; i < hooks.size(); i++) {
            UiTestHook hook = hooks.get(i);
            hook.setHookOrder(i + 1);
            hookMapper.updateById(hook);
        }
    }

    @Override
    public List<UiTestHook> findByStepId(String stepId) {
        return hookMapper.selectList(
            new LambdaQueryWrapper<UiTestHook>()
                .eq(UiTestHook::getOwnerType, HookOwnerType.STEP)
                .eq(UiTestHook::getOwnerId, stepId)
                .orderByAsc(UiTestHook::getHookOrder)
        );
    }
}