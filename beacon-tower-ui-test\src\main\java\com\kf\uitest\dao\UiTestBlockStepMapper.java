package com.kf.uitest.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kf.uitest.entity.UiTestBlockStep;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface UiTestBlockStepMapper extends BaseMapper<UiTestBlockStep> {
    @Select("SELECT * FROM ui_test_block_step WHERE block_id = #{blockId} ORDER BY step_order ASC")
    List<UiTestBlockStep> findByBlockId(@Param("blockId") String blockId);
}