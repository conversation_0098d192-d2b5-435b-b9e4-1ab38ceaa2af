package com.kf.uitest.enums;

import lombok.Getter;

@Getter
public enum AssertionOperator {
    // 相等性比较
    EQUALS("等于"),
    NOT_EQUALS("不等于"),
    
    // 包含关系
    CONTAINS("包含"),
    NOT_CONTAINS("不包含"),
    STARTS_WITH("以...开始"),
    ENDS_WITH("以...结束"),
    
    // 正则表达式
    MATCHES("匹配正则"),
    NOT_MATCHES("不匹配正则"),
    
    // 数值比较
    GREATER_THAN("大于"),
    LESS_THAN("小于"),
    GREATER_THAN_OR_EQUALS("大于等于"),
    LESS_THAN_OR_EQUALS("小于等于"),
    
    // 范围比较
    IN_RANGE("在范围内"),
    NOT_IN_RANGE("不在范围内"),
    
    // 集合操作
    IN("在集合中"),
    NOT_IN("不在集合中"),
    
    // 空值判断
    IS_NULL("为空"),
    IS_NOT_NULL("不为空"),
    IS_EMPTY("为空字符串"),
    IS_NOT_EMPTY("不为空字符串"),
    
    // 类型判断
    IS_TYPE("类型为"),
    IS_NOT_TYPE("类型不为"),
    
    // 布尔判断
    IS_TRUE("为真"),
    IS_FALSE("为假"),
    
    // 时间比较
    BEFORE("早于"),
    AFTER("晚于"),
    BETWEEN("在时间范围内");

    private final String description;
    
    AssertionOperator(String description) {
        this.description = description;
    }

} 