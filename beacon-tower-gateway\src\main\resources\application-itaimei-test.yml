server:
  port: 8080
  tomcat:
    max-swallow-size: -1
spring:
  application:
    name: beacon-tower-gateway
  cloud:
    gateway:
      routes:
        - id: beacon-tower-accurate-test
          uri: http://beacon-tower-accurate-test
          predicates:
            - Path=/accurate-test/**
          filters:
            - StripPrefix=1
        - id: beacon-tower-api-test
          uri: http://beacon-tower-api-test
          predicates:
            - Path=/api-test/**
          filters:
            - StripPrefix=1
        - id: beacon-tower-user-service
          uri: http://beacon-tower-user-service
          predicates:
            - Path=/user-service/**
          filters:
            - StripPrefix=1
        - id: beacon-tower-bao-si
          uri: http://beacon-tower-bao-si
          predicates:
            - Path=/bao-si/**
          filters:
            - StripPrefix=1
  data:
    redis:
      host: test-redis-global.taimei.com
      port: 6379
      database: 6
      password: Taimei@2022
      timeout: 10000
      jedis:
        pool:
          max-active: 1000
          max-wait: 1000
          max-idle: 50
          min-idle: 10
management:
  endpoints:
    web:
      exposure:
        include: "*"
  metrics:
    # 下面选项建议打开，以监控 http 请求的 P99/P95 等，具体的时间分布可以根据实际情况设置
    distribution:
      percentiles:
        http:
          server:
            requests: 0.5, 0.75, 0.9, 0.95, 0.99
    # 在 Prometheus 中添加特别的 Labels
    tags:
      # 必须加上对应的应用名，因为需要以应用的维度来查看对应的监控
      application:  beacon-tower-gateway
