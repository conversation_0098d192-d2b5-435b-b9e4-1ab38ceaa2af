package com.kf.aitest.service;

import com.kf.aitest.service.impl.PromptTemplateServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 提示词模板服务测试
 */
@SpringBootTest
@SpringJUnitConfig
public class PromptTemplateServiceTest {
    
    @Test
    public void testGetPromptTemplate() {
        PromptTemplateServiceImpl service = new PromptTemplateServiceImpl();
        service.init();
        
        // 测试获取recognize阶段的提示词
        String recognizeTemplate = service.getPromptTemplate("recognize");
        assertNotNull(recognizeTemplate);
        assertTrue(recognizeTemplate.contains("临床试验报告识别阶段"));
        
        // 测试获取extraction阶段的提示词
        String extractionTemplate = service.getPromptTemplate("extraction");
        assertNotNull(extractionTemplate);
        assertTrue(extractionTemplate.contains("临床试验报告信息提取阶段"));
        
        // 测试获取structured阶段的提示词
        String structuredTemplate = service.getPromptTemplate("structured");
        assertNotNull(structuredTemplate);
        assertTrue(structuredTemplate.contains("临床试验报告结构化阶段"));
        
        // 测试获取transformer阶段的提示词
        String transformerTemplate = service.getPromptTemplate("transformer");
        assertNotNull(transformerTemplate);
        assertTrue(transformerTemplate.contains("临床试验报告转换阶段"));
    }
    
    @Test
    public void testHasTemplate() {
        PromptTemplateServiceImpl service = new PromptTemplateServiceImpl();
        service.init();
        
        assertTrue(service.hasTemplate("recognize"));
        assertTrue(service.hasTemplate("extraction"));
        assertTrue(service.hasTemplate("structured"));
        assertTrue(service.hasTemplate("transformer"));
        
        assertFalse(service.hasTemplate("nonexistent"));
        assertFalse(service.hasTemplate(null));
    }
    
    @Test
    public void testInvalidStageName() {
        PromptTemplateServiceImpl service = new PromptTemplateServiceImpl();
        service.init();
        
        assertThrows(IllegalArgumentException.class, () -> {
            service.getPromptTemplate("invalid_stage");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            service.getPromptTemplate(null);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            service.getPromptTemplate("");
        });
    }
}
