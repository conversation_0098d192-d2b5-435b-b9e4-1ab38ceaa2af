package com.kf.uitest.controller;

import com.kf.uitest.common.RequireHeader;
import com.kf.uitest.common.ResponseDoMain;
import com.kf.uitest.dto.UiTestEnvironmentDTO;
import com.kf.uitest.dto.UiTestEnvironmentCreateRequest;
import com.kf.uitest.dto.UiTestEnvironmentUpdateRequest;
import com.kf.uitest.service.UiTestEnvironmentService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/environment")
public class UiTestEnvironmentController {

    @Resource
    private UiTestEnvironmentService environmentService;

    /**
     * 创建环境
     */
    @PostMapping("/createEnvironment")
    public ResponseDoMain createEnvironment(HttpServletRequest request, @Valid @RequestBody UiTestEnvironmentCreateRequest environmentCreateRequest) {
        Long userId = Long.parseLong(request.getHeader("userId"));
        return ResponseDoMain.custom("", true, environmentService.WithVariables(userId, environmentCreateRequest), 200);
    }

    /**
     * 更新环境
     */
    @PostMapping("/updateEnvironment")
    public void updateEnvironment(@Valid @RequestBody UiTestEnvironmentUpdateRequest request) {
        environmentService.updateWithVariables(request);
    }

    /**
     * 获取用户所有环境及其环境变量
     */
    @RequireHeader("userId")
    @GetMapping("/getAllEnvironmentsWithVariables")
    public ResponseDoMain getAllEnvironmentsWithVariables(HttpServletRequest request, @RequestParam("projectId") Long projectId) {
        Long userId = Long.parseLong(request.getHeader("userId"));
        return ResponseDoMain.custom("", true, environmentService.getAllEnvironmentsWithVariables(userId,projectId), 200);
    }

} 