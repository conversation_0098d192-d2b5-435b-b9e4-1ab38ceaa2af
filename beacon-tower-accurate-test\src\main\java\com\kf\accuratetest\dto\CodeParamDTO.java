package com.kf.accuratetest.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(description = "代码分析入参")
public class CodeParamDTO {

    @NotBlank(message = "git地址不能为空")
    @Schema(description = "git地址", example = "http://xxx/xxx.git")
    private String gitPath;

    /**
     * 账号
     */
    @Schema(description = "账号，如果有token则可不填此项")
    private String userName = "";

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Schema(description = "密码或者token")
    private String passWord;

    /**
     * 基准分支
     */
    @Schema(description = "基准分支,不填则默认HEAD指向的分支")
    private String masterBranch;

    /**
     * 基准分支的提交记录
     */
    @Schema(description = "基准分支的提交记录，默认最后一次提交记录")
    private String masterBranchCommitId;

    /**
     * 开发分支
     */
    @Schema(description = "开发分支,不填则默认HEAD指向的分支")
    private String devBranch;

    /**
     * 开发分支的提交记录
     */
    @Schema(description = "开发分支的提交记录,默认最后一次提交记录；如果基准与开发的分支和提交记录都没填写，此项会默认为分支的倒数第二次提交记录")
    private String devBranchCommitId;

    /**
     * jdk版本
     */
    @Schema(description = "jdk版本，不填则默认8 可填：8、11、17")
    private String jdkVersion;

//    /**
//     * 跟踪id
//     */
//    @ApiModelProperty(value = "跟踪id",notes = "回调时会原样返回")
//    private String traceId;
//
    /**
     * 结果回调地址
     */
    @Schema(description = "结果回调地址")
    private String callbackUrl;
//
//    /**
//     * 是否使用钉钉机器人推送结果,默认使用
//     */
//    @ApiModelProperty(value = "是否使用钉钉机器人推送结果,默认true")
//    private Boolean useDingTalk = true;

}
