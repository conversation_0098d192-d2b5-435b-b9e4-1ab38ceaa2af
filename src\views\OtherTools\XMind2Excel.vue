<template>
    <div>
        <div ref='searchEl' class='table-search-form'>
            <el-row :gutter='15' class='clear-both'>
                <el-col :span='24'>
                    <card-list :show-header='true' title='搜索' type='keyvalue'>
                        <template #btn>
                            <el-button-group>
                                <el-button v-prevent-default icon='el-icon-search' size='small' @click='selectData'>
                                    搜索
                                </el-button>
                            </el-button-group>
                        </template>
                        <template #keyvalue>
                            <el-form ref='refForm' :model='form' class='card-list-form' size='small'>
                                <el-row :gutter='15'>
                                    <card-list-item width='100px'>
                                        <template #key>文档名称</template>
                                        <template #value>
                                            <el-input v-model='form.fileName' placeholder='请输入文档名称'/>
                                        </template>
                                    </card-list-item>
                                    <card-list-item width='100px'>
                                        <template #key>导入状态</template>
                                        <template #value>
                                            <el-select v-model='form.isComplete' clearable filterable
                                                       placeholder='选择导入状态'>
                                                <el-option v-for='item in importStatus' :key='item.name'
                                                           :label='item.value'
                                                           :value='item.name'/>
                                            </el-select>
                                        </template>
                                    </card-list-item>
                                </el-row>
                            </el-form>
                        </template>
                    </card-list>
                </el-col>
            </el-row>
        </div>

        <div class='flex justify-between items-center mb-2 '>
            <div style="display: flex; align-items: center;">
                <el-button-group>
                    <el-button v-prevent-default type="primary" @click="xMindTemplateFile">下载模板</el-button>
                    <input ref="fileInput" accept=".xmind" style="visibility: hidden" type="file" @change="uploadFile"
                           @dragenter.prevent @dragover.prevent @drop.prevent/>
                    <el-button v-prevent-default type="primary" @click="chooseFile">上传文件</el-button>

                </el-button-group>
            </div>
            <el-button v-prevent-default type='text' @click='toggleSearch'>
                搜索
                <el-icon>
                    <el-icon-arrow-up v-if='isShow'/>
                    <el-icon-arrow-down v-else/>
                </el-icon>
            </el-button>
        </div>
        <el-table v-loading="tableLoading" :data='tableData.data' stripe style="width:100%">
            <el-table-column label='文档名称' prop='fileName'/>
            <el-table-column label='创建时间' prop='createTime'/>
            <el-table-column label="导入进度" prop="isComplete">
                <template #default="{ row }">
                    <div class="status-container">
                        <div v-if="row.isComplete === '未导入'|| row.isComplete === '导入成功'">
                            {{ row.isComplete }}
                        </div>
                        <div v-if="(row.isComplete === '失败' || row.isComplete === '部分失败') && row.errorMsg" class="status-container">
                            {{ row.isComplete }}
                            <el-tooltip
                                :content="row.errorMsg"
                                :popper-style="{ width: '250px !important' }"
                                effect="dark" hide-after="0" placement="top-start">
                                <el-icon-warning-filled class="el-icon-warning-filled"/>
                            </el-tooltip>
                        </div>
                        <el-progress v-if="row.isComplete ==='导入中'" :percentage="row.progress" :stroke-width="13" :text-inside="true" style="width: 80%;">
                        </el-progress>
                    </div>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label='操作' prop='operation' width=" 300">
                <template #default="{ row }">
                    <div class="button-container">
                        <el-button v-prevent-default size="small"
                                   type="primary" @click="() => handleClick(row.id)">下载
                        </el-button>
                        <el-button v-prevent-default size="small"
                                   type="primary" @click="() => importJiraClick(row.id, row.fileName)">导入Jira
                        </el-button>

                        <el-popconfirm cancel-button-text="取消" confirm-button-text="确定" title="从JIRA中删除已导入的测试用例，确定删除吗？"
                                       width="220"
                                       @confirm="() => deleteTestCase(row.id)">
                            <template #reference>
                                <el-button v-prevent-default size="small" v-if="row.isComplete === '导入成功'" :disabled="!row.allowDelete" type="primary">删除用例</el-button>
                            </template>
                        </el-popconfirm>
                        <el-popconfirm cancel-button-text="取消" confirm-button-text="确定" title="确定删除吗？"
                                       width="220"
                                       @confirm="() => deleteFile(row.id)">
                            <template #reference>
                                <el-button v-prevent-default plain size="small" type="danger">删除</el-button>
                            </template>
                        </el-popconfirm>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-dialog v-model="importJiraDataVisible" :close-on-click-modal="false" :close-on-press-escape="false"
                   :width="'500px'" class="dialogClass" title="测试用例导入">
            <template #>
                <div>文件名：{{ importJiraDataForm.fileName }}</div>
                <el-form ref="importJiraDataRef" :model="importJiraDataForm" :rules="loginJiraRules">
                    <el-form-item prop="project">
                        <div class="flex-container">
                            <el-checkbox v-model="checkbox.project" @change="clearInputOnCheckboxChange('project')"></el-checkbox>
                            <span class="no-wrap">项目名称
                                <el-tooltip placement="top">
                                    <template #content>
                                        优先以此处填写的项目为准<br/>
                                        如果为空则使用CSV中的项目代号<br/>
                                        如果都为空则不允许导入
                                    </template>
                                    <el-icon>
                                        <el-icon-InfoFilled class="aligned-icon"></el-icon-InfoFilled>
                                    </el-icon>
                                </el-tooltip>
                            </span>
                            <transition name="fade" style="height: 32px;">
                                <div v-show="checkbox.project" style="width: 100%;">
                                    <el-autocomplete v-model="importJiraDataForm.project"
                                                     :fetch-suggestions="projectKeyQuerySearch"
                                                     placeholder="填写项目代号"
                                                     teleported
                                                     value-key="name"
                                                     @select="projectKeyHandleSelect">
                                    </el-autocomplete>
                                </div>
                            </transition>
                        </div>
                    </el-form-item>

                    <el-form-item prop="fixVersion">
                        <div class="flex-container">
                            <el-checkbox v-model="checkbox.fixVersion" @change="clearInputOnCheckboxChange('fixVersion')"></el-checkbox>
                            <span class="no-wrap">修复版本
                                <el-tooltip placement="top">
                                    <template #content>
                                        填写修复版本，多个版本用,分割<br/>
                                        优先以此处填写为准<br/>
                                        如果为空则使用CSV中的版本<br/>
                                        如果都为空则没有修复版本
                                    </template>
                                    <el-icon>
                                        <el-icon-InfoFilled class="aligned-icon"></el-icon-InfoFilled>
                                    </el-icon>
                                </el-tooltip>
                            </span>
                            <transition name="fade">
                                <div v-show="checkbox.fixVersion" style="width: 100%;">
                                    <el-select
                                        v-model="importJiraDataForm.fixVersion"
                                        multiple
                                        placeholder="请选择版本"
                                        filterable
                                        clearable
                                        :reserve-keyword="false"
                                        @change = "changeFixVersion"
                                    >
                                        <el-option
                                            v-for="version in availableVersions"
                                            :key="version"
                                            :label="version"
                                            :value="version"
                                        ></el-option>
                                    </el-select>
                                </div>
                            </transition>
                        </div>
                    </el-form-item>

                    <el-form-item prop="testPlan">
                        <div class="flex-container">
                            <el-checkbox v-model="checkbox.testPlan" @change="clearInputOnCheckboxChange('testPlan')"></el-checkbox>
                            <span class="no-wrap">关联计划
                                <el-tooltip placement="top">
                                    <template #content>
                                        需要将测试用例关联到计划时勾选此选项<br/>
                                        如果计划不存在则会创建一个新计划<br/>
                                        并把测试用例关联到该计划中<br/>
                                    </template>
                                    <el-icon>
                                        <el-icon-InfoFilled class="aligned-icon"></el-icon-InfoFilled>
                                    </el-icon>
                                </el-tooltip>
                            </span>
                            <transition name="fade" style="height: 32px;">
                                <div v-show="checkbox.testPlan" style="width: 100%;">
                                    <el-autocomplete v-model="importJiraDataForm.testPlan"
                                                     :fetch-suggestions="testPlanQuerySearch"
                                                     placeholder="选择/填写计划名称"
                                                     teleported
                                                     value-key="name"
                                                     @select="testPlanHandleSelect">
                                    </el-autocomplete>
                                </div>
                            </transition>
                        </div>
                    </el-form-item>

                    <el-form-item prop="testCycle">
                        <div class="flex-container">
                            <el-checkbox v-model="checkbox.testCycle" @change="clearInputOnCheckboxChange('testCycle')"></el-checkbox>
                            <span class="no-wrap">关联周期
                                <el-tooltip placement="top">
                                    <template #content>
                                        需要将测试用例关联到周期时勾选此选项<br/>
                                        勾选此选项时必须填写关联计划<br/>
                                        如果周期不存在则会在计划下创建一个周期<br/>
                                        并把测试用例关联到该周期中
                                    </template>
                                    <el-icon>
                                        <el-icon-InfoFilled class="aligned-icon"></el-icon-InfoFilled>
                                    </el-icon>
                                </el-tooltip>
                            </span>
                            <transition name="fade" style="height: 32px;">
                                <div v-show="checkbox.testCycle" style="width: 100%;">
                                    <el-autocomplete v-model="importJiraDataForm.testCycle"
                                                     :fetch-suggestions="testCycleQuerySearch"
                                                     placeholder="选择/填写周期名称"
                                                     teleported
                                                     value-key="name"
                                                     @select="testCycleHandleSelect">
                                    </el-autocomplete>
                                </div>
                            </transition>
                        </div>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer style="text-align:right;">
                <el-button @click="cancelImportJiraDataVisible">取消</el-button>
                <el-button v-prevent-default type="primary" @click="ImportJiraSubmit">确定
                </el-button>
            </template>
        </el-dialog>

        <jiraLoginDialog
            v-if="jiraLoginDialogModalVisible"
            :visible="jiraLoginDialogModalVisible"
            @close="jiraLoginDialogModalVisible = false"
        >
        </jiraLoginDialog>
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
                       :small="small" :total="total" layout="total, sizes, prev, pager, next, jumper"
                       @size-change="handleSizeChange"
                       @current-change="handleCurrentChange"/>
    </div>
</template>

<script lang='ts' setup>
import CardList from '@/components/CardList/CardList.vue'
import CardListItem from '@/components/CardList/CardListItem.vue'
import { reactive, ref } from 'vue'
import {
    csvToJira,
    deleteFileById, deleteTestCaseByFileId,
    downloadCSVFile,
    getAllCycleForPlan,
    getAllPlanForProject,
    getAllProject,
    getAllVersionForProject, getImportStatus,
    getJiraProgress,
    getXMindFileInfo,
    getXMindTemplateFile,
    getXMindToExcelFileList,
    loginCheck,
    reloadTestRuns,
    type XMindToExcelFileList, xMindToExcelFileUpload
} from '@/api/layout'
import { slide } from '@/utils/animate'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus/lib/components'
import JiraLoginDialog from '@/components/TestRun/JiraLoginDialog.vue'
import router from '@/router'
// 动画绑定
const searchEl = ref(null)
// 表格loading初始化
const tableLoading = ref(false)
// 分页初始化参数
let currentPage = ref(1)
let pageSize = ref(10)
const total = ref(0)
const small = ref(false)
const isShow = ref(false)
const projectInitName = ref('')
const importJiraDataVisible = ref(false)
const importJiraDataRef = ref<FormInstance | null>(null)



// 默认不打开登录提示框
const jiraLoginDialogModalVisible = ref(false)

const loginJiraRules = reactive({
    userName: [{ required: true, message: '请输入JIRA账号', trigger: 'blur' }],
    passWord: [{ required: true, message: '请输入JIRA密码', trigger: 'blur' }]
})

// 点击搜索按钮
const toggleSearch = () => {
    isShow.value = !isShow.value
    slide(searchEl, isShow.value)
}
// 设置分页
const handleSizeChange = (val: number) => {
    pageSize.value = val
    console.log('pageSize', pageSize.value)
    selectData()
}
const handleCurrentChange = (val: number) => {
    currentPage.value = val
    console.log('currentPage', currentPage.value)
    selectData()
}

interface ISearchForm {
    fileName: string
    isComplete: string
}

// 搜索表单
const form: ISearchForm = reactive({
    fileName: '',
    isComplete: ''

})
// 查询数据
const selectData = async () => {
    try {
        tableLoading.value = true

        const { fileName, isComplete } = form
        const info: XMindToExcelFileList = {
            fileName,
            isComplete,
            current: currentPage.value,
            size: pageSize.value
        }

        const { data } = await getXMindToExcelFileList(info)

        if (!data.isSuccess) {
            ElMessage.error('错误，请稍后再试')
            return
        }

        total.value = data.data.total

        tableData.data = data.data.data.map((item: any) => {
            const row = {
                id: item.fileId,
                fileName: item.fileName,
                taskId: item.taskId,
                createTime: item.createTime,
                isComplete: item.isComplete,
                errorMsg: item.errorMsg,
                progress: 0,
                allowDelete: item.allowDelete
            }

            // 如果 isComplete 为 "导入中"，调用 startProgressTracking 方法
            if (item.isComplete === '导入中') {
                startProgressTracking(item.taskId, item.fileId)
            }
            return row
        })
    } catch (error) {
        ElMessage.error('错误，请稍后再试')
    } finally {
        tableLoading.value = false
    }
}
selectData()

interface complete {
    name: string
    value: string
}

const importStatus = ref<complete[]>([])
const getFileImportStatus = async () => {
    const res = await getImportStatus()
    if (res.data.isSuccess) {
        importStatus.value = Object.entries(res.data.data).map(([name, value]) => ({
            name,
            value: String(value)
        }))
    }
}
getFileImportStatus()

// 表单数据接口
interface data {
    id: string
    fileName: string
    createTime: string
    isComplete: string
    errorMsg: string
    allowDelete: boolean
    progress?: number
}

// 表格数据
let tableData: ITable<data> = reactive({
    data: [],
    total: 0,
    page: 1,
    size: 10
})
const importJiraDataForm = reactive({
    project: '',
    fixVersion: [] as string[],
    testPlan: '',
    testCycle: '',
    id: '',
    fileName: '',
    jiraToken: '',

    // 实际请求使用的值
    projectForRequest: '',
    fixVersionForRequest: [] as string[],
    testPlanForRequest: '',
    testCycleForRequest: '',
    mergeStepAndResult: false
})
interface ProjectSelect {
    name: string
    value: string
}
const projects = ref<ProjectSelect[]>([])
// 测试项目
const getProjects = async () => {
    let { jiraToken } = importJiraDataForm
    const res = await getAllProject(jiraToken)
    if (res.data.isSuccess) {
        projects.value = res.data.data.map((project: any) => ({
            name: project.name,
            value: project.key
        }))
    }
    // 查找是否有与importJiraDataForm.project匹配的项目
    const foundProject = projects.value.find(item => item.value === importJiraDataForm.projectForRequest)
    // 如果找到，更新importJiraDataForm.project的值
    if (foundProject) {
        importJiraDataForm.project = foundProject.name
    }
}

const projectKeyQuerySearch = (queryString: string, cb: (results: ProjectSelect[]) => void) => {
    const results = queryString
        ? projects.value.filter(item =>
            item.name.toLowerCase().includes(queryString.toLowerCase()) ||
            item.value.toLowerCase().includes(queryString.toLowerCase())
        )
        : projects.value
    cb(results)
}

// 测试计划
let testPlanMap = [] as any[]
const getAllPlan = async () => {
    let { jiraToken, projectForRequest } = importJiraDataForm
    const params = { projectKey: projectForRequest }
    const res = await getAllPlanForProject(params, jiraToken)
    if (res.data.isSuccess) {
        testPlanMap = Object.entries(res.data.data).map(([name, value]) => ({ name, value }))
    }
}
const testPlanQuerySearch = (queryString: string, cb: (string: any[]) => void) => {
    const results = queryString
        ? testPlanMap.filter(item =>
            item.name.toLowerCase().includes(queryString.toLowerCase()) ||
            item.value.toLowerCase().includes(queryString.toLowerCase())
        )
        : testPlanMap
    cb(results)
}
const testPlanHandleSelect = (item: any) => {
    importJiraDataForm.testPlan = item.name
    importJiraDataForm.testPlanForRequest = item.value
    // 变更之后，清空testCycle
    importJiraDataForm.testCycle = ''
    importJiraDataForm.testCycleForRequest = ''
    // 如果周期的checkbox是勾选状态，则重新获取周期
    if (checkbox.testCycle) {
        getAllCycle()
    }
}
// 测试周期
let testCycleMap = [] as any[]
const getAllCycle = async () => {
    let { testPlanForRequest, jiraToken } = importJiraDataForm
    const res = await getAllCycleForPlan(testPlanForRequest, jiraToken)
    if (res.data.isSuccess) {
        testCycleMap = Object.entries(res.data.data).map(([name, value]) => ({ name, value }))
    }
}
const testCycleQuerySearch = (queryString: string, cb: (string: any[]) => void) => {
    const results = queryString
        ? testCycleMap.filter(item =>
            item.name.toLowerCase().includes(queryString.toLowerCase()) ||
            item.value.toLowerCase().includes(queryString.toLowerCase())
        )
        : testCycleMap
    cb(results)
}
const testCycleHandleSelect = (item: any) => {
    importJiraDataForm.testCycle = item.name
    importJiraDataForm.testCycleForRequest = item.value
}
// 测试版本
const availableVersions = ref<string[]>([])
const getAllVersion = async () => {
    let { jiraToken, projectForRequest } = importJiraDataForm
    const res = await getAllVersionForProject(projectForRequest, jiraToken)
    if (res.data.isSuccess) {
        availableVersions.value = res.data.data as string[]
    }
}

interface xMindFileInfo {
    data: {
        message: string
        isSuccess: boolean
        data: {
            projectKey: string
            fixVersions: {
                name: string
            }[]
        }
    }
}


const XMindFileInfo = async (fileId: string) => {
    const res = await getXMindFileInfo({ fileId: fileId }) as xMindFileInfo
    if (res.data.isSuccess) {
        importJiraDataForm.projectForRequest = res.data.data.projectKey
        projectInitName.value = res.data.data.projectKey
        if (res.data.data.fixVersions) {
            importJiraDataForm.fixVersion = res.data.data.fixVersions.map(version => version.name)
        }
        getProjects()
        // 提前获取计划列表
        if (importJiraDataForm.projectForRequest) {
            getAllPlan()
        }
    }
}
const projectKeyHandleSelect = (item: any) => {
    console.log('item', item)
    importJiraDataForm.project = item.name
    importJiraDataForm.projectForRequest = item.value

    // 变更之后，清空testPlan和testCycle
    importJiraDataForm.testPlan = ''
    importJiraDataForm.testPlanForRequest = ''
    importJiraDataForm.testCycle = ''
    importJiraDataForm.testCycleForRequest = ''
    // 如果计划和周期的checkbox是勾选状态，则重新获取计划和周期
    // if (checkbox.testPlan) {
    //     getAllPlan();
    // }
    if (item.name != projectInitName.value) {
        getAllPlan()
    }
}

// 点击导入jira按钮
const importJiraClick = async (id: string, fileName: string) => {
    const res = await jiraLoginClick()
    if (!res) {
        jiraLoginDialogModalVisible.value = true
        return
    }
    XMindFileInfo(id)
    // 重置表单
    importJiraDataRef.value?.resetFields()

    // 打开弹窗
    importJiraDataVisible.value = true

    importJiraDataForm.id = id
    importJiraDataForm.fileName = fileName

    // 重置checkbox
    checkbox.project = false
    checkbox.fixVersion = false
    checkbox.testPlan = false
    checkbox.testCycle = false
}

interface CheckboxInterface {
    project: boolean
    fixVersion: boolean
    testPlan: boolean
    testCycle: boolean

    [key: string]: boolean
}

const checkbox = reactive<CheckboxInterface>({
    project: false,
    fixVersion: false,
    testPlan: false,
    testCycle: false
})




const cancelImportJiraDataVisible = () => {
    importJiraDataVisible.value = false
}
const ImportJiraSubmit = async () => {
    let {
        id,
        project,
        projectForRequest,
        fixVersion,
        fixVersionForRequest,
        testPlan,
        testPlanForRequest,
        testCycle,
        testCycleForRequest,
        jiraToken,
        mergeStepAndResult
    } = importJiraDataForm
    if (!projectForRequest) {
        projectForRequest = project
    }
    if (!fixVersionForRequest) {
        fixVersionForRequest = fixVersion
    }
    if (!testPlanForRequest) {
        testPlanForRequest = testPlan
    }
    if (!testCycleForRequest) {
        testCycleForRequest = testCycle
    }
    const csvToJiraRes = await csvToJira({
        jiraToken: jiraToken,
        fileId: id,
        projectKey: projectForRequest,
        fixVersion: fixVersionForRequest,
        testPlan: testPlanForRequest,
        testCycle: testCycleForRequest,
        mergeStepAndResult: mergeStepAndResult
    }, jiraToken)
    if (!csvToJiraRes.data.isSuccess) {
        ElMessage.error(csvToJiraRes.data.message)
        return
    }
    // 关闭弹窗
    importJiraDataVisible.value = false
    startProgressTracking(csvToJiraRes.data.data.taskId, id)
}

const clearInputOnCheckboxChange = (propName: string) => {
    if (!checkbox[propName]) {
        (importJiraDataForm as any)[propName] = ''
        switch (propName) {
            case 'project':
                // 取消勾选时，恢复默认值
                importJiraDataForm.projectForRequest = projectInitName.value
                break
            case 'fixVersion':
                importJiraDataForm.fixVersionForRequest = []
                break
            case 'testPlan':
                importJiraDataForm.testPlanForRequest = ''
                break
            case 'testCycle':
                importJiraDataForm.testCycleForRequest = ''
                break
        }
    } else if (propName === 'fixVersion') {
        if (importJiraDataForm.projectForRequest) {
            getAllVersion()
        }

    } else if (propName === 'testCycle') {
        const isTestPlanInList = testPlanMap.some(item => item.name === importJiraDataForm.testPlan)

        if (isTestPlanInList && importJiraDataForm.testPlanForRequest) {
            getAllCycle()
        }
    }
}

const startProgressTracking = (taskId: string, rowId: string) => {
    const eventSource = new EventSource(`${getJiraProgress()}/${taskId}`)
    const row = tableData.data.find(r => r.id === rowId)
    if (row) {
        row.isComplete = '导入中'
        row.progress = 0
    }
    eventSource.addEventListener('progress', function (event) {
        const progressValue = JSON.parse(event.data)
        const progress = Math.floor(progressValue)
        if (progress !== undefined) {
            const row = tableData.data.find(r => r.id === rowId)
            if (row) {
                row.progress = progress
                if (progress === 100) {
                    // 为了显示效果，延迟500ms再显示已完成
                    setTimeout(() => {
                        row.isComplete = '导入成功'
                        row.allowDelete = true
                    }, 500)
                }
            }
        }
    })
    eventSource.onerror = function () {
        eventSource.close()
    }
}

const uploadFile = (event: Event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (file) {
        upload(file)
    }
}
const chooseFile = () => {
    const fileInput = document.querySelector('input[type=file]') as HTMLInputElement
    fileInput.click()
}
const upload = async (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    const res = await xMindToExcelFileUpload(formData)
    if (res.data.isSuccess) {
        ElMessage.success('上传成功')
        await selectData()
    } else {
        ElMessage.error(res.data.message)
    }
    // 清空input的value值
    const fileInput = document.querySelector('input[type=file]') as HTMLInputElement
    fileInput.value = ''

}

// 下载文件
const handleClick = async (id: string) => {
    try {
        const res: any = await downloadCSVFile({ fileId: id })
        const contentDisposition = res.headers['content-disposition']
        const matches = contentDisposition.match('filename=([^;\n\r]+)')
        let fileName = matches ? matches[1] : 'unknown'
        fileName = decodeURIComponent(fileName)
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
    } catch (err) {
        // if (err instanceof Error) {
        //     console.log('出错啦', err.message)
        // }
    }
}
// 删除文件
const deleteFile = async (id: string) => {
    const res = await deleteFileById(id)
    if (res.data.isSuccess) {
        ElMessage.success('删除成功')

        await selectData()
    } else {
        ElMessage.error(res.data.message)
    }
}
// 删除已导入的测试用例
const deleteTestCase = async (id: string) => {
    const loginRes = await jiraLoginClick()
    if (!loginRes) {
        jiraLoginDialogModalVisible.value = true
        return
    }
    const res = await deleteTestCaseByFileId(id, jiraToken.value)
    if (res.data.isSuccess) {
        ElMessage.success(res.data.message)
        await selectData()
    } else {
        ElMessage.error(res.data.message)
    }
}
// 下载模板
const xMindTemplateFile = async () => {
    try {
        const res: any = await getXMindTemplateFile()
        const contentDisposition = res.headers['content-disposition']
        const matches = contentDisposition.match('filename=([^;\n\r]+)')
        let fileName = matches ? matches[1] : 'unknown'
        fileName = decodeURIComponent(fileName)
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
    } catch (err) {
        // if (err instanceof Error) {
        //     console.log('出错啦', err.message)
        // }
    }
}
const jiraToken = ref('')
const jiraLoginClick = async (): Promise<boolean> => {
    if (jiraToken.value) {
        return true
    }
    const res = await loginCheck()
    if (!res.data.isSuccess) {
        // 如果登录失败，就打开登录框提示
        jiraLoginDialogModalVisible.value = true
        return false
    }
    // 如果登录成功，就设置jiraToken
    importJiraDataForm.jiraToken = res.headers['jiratoken']
    jiraToken.value = res.headers['jiratoken']
    return true
}
jiraLoginClick()
const changeFixVersion = (value: string[]) => {
    importJiraDataForm.fixVersion = value
    importJiraDataForm.fixVersionForRequest = value
}


</script>
<style lang='postcss' scoped>
.table-search-form {
    overflow: hidden;
    height: 0;
}

::v-deep(.el-card__header) {
    padding: 7px 15px;
}

::v-deep(.el-button) {
    padding: 4px 6px;
    border-radius: 3px;
}

.input-tag ::v-deep(.el-input__wrapper) {
    box-shadow: none !important;
}

.status-container {
    display: flex;
    align-items: center;
}

.el-icon-warning-filled {
    color: red;
    margin-left: 5px;
    vertical-align: middle;
    width: 15px;
    height: 15px;
}

.button-container {
    display: flex;
    flex-wrap: wrap; /* 允许按钮换行 */
}

.button-container .el-button {
    margin: 2px 3px;
}

::v-deep(.el-autocomplete) {
    width: 100%;
}

.aligned-icon {
    vertical-align: middle;
}


.no-wrap {
    white-space: nowrap;
    margin-right: 10px;
    /* 添加间隔 */
}

.no-wrap,
el-tooltip,
el-icon {
    display: flex;
    align-items: center;
}

.flex-container {
    display: flex;
    align-items: center;
    width: 100%;
}
.el-checkbox {
    margin-right: 10px;
}
.dialogClass .el-form-item {
    margin-bottom: 20px;
}

.dialogClass .el-form-item:first-child {
    margin-top: 10px;
}

.dialogClass .el-form-item__label {
    font-size: 16px;
    font-weight: bold;
}

.dialogClass .el-input__inner {
    font-size: 14px;
}

.upload-container span {
    margin-bottom: 10px;
}

.upload-container p {
    margin-top: 5px;
    font-size: 14px;
    color: #999;
}

.upload-container i {
    font-size: 48px;
    margin-bottom: 10px;
}




.upload-container .el-button {
    margin-top: 10px;
    font-size: 14px;
}

p {
    padding: 12px;
    font-size: 15px;
    font-family: 'Roboto', sans-serif;
}

.button-container {
    display: flex;
    flex-wrap: wrap; /* 允许按钮换行 */
}

.button-container .el-button {
    margin: 2px 3px;
}


</style>

