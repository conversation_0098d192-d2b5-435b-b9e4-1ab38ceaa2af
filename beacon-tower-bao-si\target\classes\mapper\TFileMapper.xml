<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kf.baosi.dao.TFileMapper">
    <resultMap type="com.kf.baosi.entity.TFile" id="TFileMap">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="file_path" jdbcType="VARCHAR" property="filePath"/>
        <result column="file_size" jdbcType="BIGINT" property="fileSize"/>
        <result column="file_md5" jdbcType="VARCHAR" property="fileMd5"/>
        <result column="file_suffix" jdbcType="VARCHAR" property="fileSuffix"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
    </resultMap>

    <resultMap id="DatatoJiraMap" type="com.kf.baosi.entity.DataToJiraParam">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="file_path" jdbcType="VARCHAR" property="filePath"/>
        <result column="file_size" jdbcType="BIGINT" property="fileSize"/>
        <result column="file_md5" jdbcType="VARCHAR" property="fileMd5"/>
        <result column="file_suffix" jdbcType="VARCHAR" property="fileSuffix"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
        <!-- TDataJiraTask 的字段 -->
        <result column="task_Id" property="taskId"/>
        <result column="is_finished" property="isFinished"/>
    </resultMap>
    <select id="queryJoinedByBySuffixUserId" resultMap="DatatoJiraMap">
        SELECT tf.id,tf.user_id,tf.file_name,tf.file_path,tf.file_size,tf.file_md5,tf.file_suffix,tf.create_time,tf.update_time,tf.is_deleted,tdj.task_id,tdj.is_finished
        FROM t_file tf
        LEFT JOIN t_data_jira_task tdj ON tf.id = tdj.file_id AND tdj.is_deleted = 0
        WHERE tf.user_id = #{userId} AND tf.file_suffix = #{fileSuffix} AND tf.is_deleted = 0 order by tf.create_time desc
    </select>

    <!--根据UserId和fileSuffix查询xml数据-->
    <select id="queryBySuffixUserId" resultMap="TFileMap">
        SELECT *
        FROM t_file
        WHERE user_id = #{userId} AND is_deleted = 0 AND file_suffix = #{fileSuffix} order by create_time desc
    </select>

    <!--根据UId查询数据-->
    <select id="queryById" resultMap="TFileMap">
        SELECT *
        FROM t_file
        WHERE id = #{id} AND is_deleted = 0
    </select>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into t_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="fileName != null">
                file_name,
            </if>
            <if test="filePath != null">
                file_path,
            </if>
            <if test="fileSize != null">
                file_size,
            </if>
            <if test="fileMd5 != null">
                file_md5,
            </if>
            <if test="fileSuffix != null">
                file_suffix,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="filePath != null">
                #{filePath,jdbcType=VARCHAR},
            </if>
            <if test="fileSize != null">
                #{fileSize,jdbcType=BIGINT},
            </if>
            <if test="fileMd5 != null">
                #{fileMd5,jdbcType=VARCHAR},
            </if>
            <if test="fileSuffix != null">
                #{fileSuffix,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=BOOLEAN},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.kf.baosi.entity.TFile">
        update t_file
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                file_name = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="filePath != null">
                file_path = #{filePath,jdbcType=VARCHAR},
            </if>
            <if test="fileSize != null">
                file_size = #{fileSize,jdbcType=BIGINT},
            </if>
            <if test="fileMd5 != null">
                file_md5 = #{fileMd5,jdbcType=VARCHAR},
            </if>
            <if test="fileSuffix != null">
                file_suffix = #{fileSuffix,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=BOOLEAN},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <!--    <delete id="deleteByUserId">-->
    <!--        delete from t_file-->
    <!--        where user_id = #{userId}-->
    <!--    </delete>-->
</mapper>