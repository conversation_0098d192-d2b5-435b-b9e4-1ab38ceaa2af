<template>
  <div 
    :class="[
      'markdown-renderer',
      `markdown-renderer--${currentTheme}`,
      props.class
    ]"
    :style="containerStyle"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="markdown-renderer__loading">
      <el-skeleton :rows="3" animated />
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="markdown-renderer__error">
      <el-alert
        title="渲染失败"
        :description="error.message"
        type="error"
        show-icon
        :closable="false"
      />
    </div>
    
    <!-- 渲染内容 -->
    <div
      v-else
      ref="contentRef"
      class="markdown-renderer__content"
      v-html="renderedHtml"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick, defineExpose } from 'vue'
import { ElSkeleton, ElAlert } from 'element-plus'
import * as MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import type { 
    MarkdownRendererProps, 
    MarkdownRendererEmits,
    MarkdownRendererInstance 
} from './types'

// Props定义
const props = withDefaults(defineProps<MarkdownRendererProps>(), {
    content: '',
    theme: 'auto',
    loading: false,
    highlight: true,
    class: '',
    maxHeight: 'none'
})

// Emits定义
const emit = defineEmits<MarkdownRendererEmits>()

// 响应式数据
const contentRef = ref<HTMLElement>()
const renderedHtml = ref('')
const error = ref<Error | null>(null)
const md = ref<MarkdownIt>()

// 计算属性
const currentTheme = computed(() => {
    if (props.theme === 'auto') {
    // 这里可以根据Element Plus的主题自动判断
        return 'light' // 默认浅色主题
    }
    return props.theme
})

const containerStyle = computed(() => ({
    maxHeight: typeof props.maxHeight === 'number' 
        ? `${props.maxHeight}px` 
        : props.maxHeight
}))

// 初始化markdown-it实例
const initMarkdownIt = () => {
    try {
        md.value = new MarkdownIt({
            html: true,
            linkify: true,
            typographer: true,
            highlight: props.highlight ? highlightCodeSync : undefined
        })

        // 启用表格插件
        md.value.enable(['table'])

    } catch (err) {
        error.value = err as Error
        console.error('Failed to initialize markdown-it:', err)
    }
}

// 同步代码高亮函数（用于markdown-it）
const highlightCodeSync = (str: string, lang: string) => {
    if (!lang || !hljs.getLanguage(lang)) {
        return `<pre><code>${md.value?.utils.escapeHtml(str) || str}</code></pre>`
    }

    try {
        const result = hljs.highlight(str, { language: lang })
        return `<pre class="hljs"><code class="language-${lang}">${result.value}</code></pre>`
    } catch (err) {
        console.warn('Code highlighting failed:', err)
        return `<pre><code>${md.value?.utils.escapeHtml(str) || str}</code></pre>`
    }
}

// 按需加载语言包（用于后续优化）
const loadLanguage = async (lang: string): Promise<boolean> => {
    // 常见语言已经内置，无需额外加载
    const builtinLanguages = [
        'javascript', 'typescript', 'html', 'css', 'json',
        'xml', 'markdown', 'bash', 'shell', 'sql', 'python', 'java'
    ]

    if (builtinLanguages.includes(lang) || hljs.getLanguage(lang)) {
        return true
    }

    try {
    // 动态导入语言包（这里暂时注释，因为需要配置Vite支持动态导入）
    // const languageModule = await import(`highlight.js/lib/languages/${lang}`)
    // hljs.registerLanguage(lang, languageModule.default)
        console.info(`Language pack for ${lang} not loaded (feature disabled)`)
        return false
    } catch (err) {
        console.warn(`Failed to load language pack for ${lang}:`, err)
        return false
    }
}

// 渲染Markdown内容
const render = async () => {
    if (!md.value || !props.content) {
        renderedHtml.value = ''
        return
    }
  
    try {
        error.value = null
        const html = md.value.render(props.content)
        renderedHtml.value = html
    
        await nextTick()
        emit('rendered', html)
    } catch (err) {
        error.value = err as Error
        emit('error', err as Error)
        console.error('Markdown rendering failed:', err)
    }
}

// 获取渲染后的HTML
const getHtml = () => renderedHtml.value

// 清空内容
const clear = () => {
    renderedHtml.value = ''
    error.value = null
}

// 监听内容变化
watch(() => props.content, render, { immediate: false })
watch(() => props.highlight, () => {
    initMarkdownIt()
    render()
})

// 组件挂载时初始化
onMounted(() => {
    initMarkdownIt()
    if (props.content) {
        render()
    }
})

// 暴露组件实例方法
defineExpose<MarkdownRendererInstance>({
    render,
    getHtml,
    clear
})
</script>

<style scoped>
.markdown-renderer {
  width: 100%;
  overflow-y: auto;
  border-radius: var(--el-border-radius-base);
  border: 1px solid var(--el-border-color-light);
}

.markdown-renderer__loading,
.markdown-renderer__error {
  padding: 16px;
}

.markdown-renderer__content {
  padding: 16px;
  line-height: 1.6;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

/* 浅色主题样式 */
.markdown-renderer--light .markdown-renderer__content {
  background-color: var(--el-bg-color);
}

/* 深色主题样式 */
.markdown-renderer--dark .markdown-renderer__content {
  background-color: var(--el-bg-color-page);
  color: var(--el-text-color-primary);
}

/* Markdown内容样式 */
.markdown-renderer__content :deep(h1),
.markdown-renderer__content :deep(h2),
.markdown-renderer__content :deep(h3),
.markdown-renderer__content :deep(h4),
.markdown-renderer__content :deep(h5),
.markdown-renderer__content :deep(h6) {
  margin: 16px 0 8px 0;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.markdown-renderer__content :deep(h1) { font-size: 24px; }
.markdown-renderer__content :deep(h2) { font-size: 20px; }
.markdown-renderer__content :deep(h3) { font-size: 18px; }
.markdown-renderer__content :deep(h4) { font-size: 16px; }
.markdown-renderer__content :deep(h5) { font-size: 14px; }
.markdown-renderer__content :deep(h6) { font-size: 12px; }

.markdown-renderer__content :deep(p) {
  margin: 8px 0;
  line-height: 1.6;
}

.markdown-renderer__content :deep(ul),
.markdown-renderer__content :deep(ol) {
  margin: 8px 0;
  padding-left: 24px;
}

.markdown-renderer__content :deep(li) {
  margin: 4px 0;
}

.markdown-renderer__content :deep(blockquote) {
  margin: 16px 0;
  padding: 8px 16px;
  border-left: 4px solid var(--el-color-primary);
  background-color: var(--el-fill-color-lighter);
  color: var(--el-text-color-regular);
}

.markdown-renderer__content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  border: 1px solid var(--el-border-color);
}

.markdown-renderer__content :deep(th),
.markdown-renderer__content :deep(td) {
  padding: 8px 12px;
  border: 1px solid var(--el-border-color);
  text-align: left;
}

.markdown-renderer__content :deep(th) {
  background-color: var(--el-fill-color-light);
  font-weight: 600;
}

.markdown-renderer__content :deep(code) {
  padding: 2px 4px;
  background-color: var(--el-fill-color-light);
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  color: var(--el-color-danger);
}

.markdown-renderer__content :deep(pre) {
  margin: 16px 0;
  padding: 16px;
  background-color: var(--el-fill-color-lighter);
  border-radius: var(--el-border-radius-base);
  overflow-x: auto;
  border: 1px solid var(--el-border-color-light);
}

.markdown-renderer__content :deep(pre code) {
  padding: 0;
  background-color: transparent;
  color: inherit;
  border-radius: 0;
}

.markdown-renderer__content :deep(a) {
  color: var(--el-color-primary);
  text-decoration: none;
}

.markdown-renderer__content :deep(a:hover) {
  text-decoration: underline;
}

.markdown-renderer__content :deep(hr) {
  margin: 24px 0;
  border: none;
  border-top: 1px solid var(--el-border-color);
}

/* 代码高亮样式 */
.markdown-renderer__content :deep(.hljs) {
  background-color: var(--el-fill-color-lighter) !important;
  color: var(--el-text-color-primary);
}
</style>
