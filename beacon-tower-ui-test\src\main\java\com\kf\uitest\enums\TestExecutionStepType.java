package com.kf.uitest.enums;

import lombok.Getter;

@Getter
public enum TestExecutionStepType {
    // 基础步骤类型
    STEP("普通步骤"),
    HOOK("钩子步骤"),
    
    // 普通块
    NORMAL_START("普通块开始"),
    NORMAL_END("普通块结束"),
    
    // 循环块
    LOOP_START("循环块开始"),
    LOOP_END("循环块结束"),
    
    // 条件块
    CONDITION_START("条件块开始"),
    CONDITION_END("条件块结束");

    private final String description;
    
    TestExecutionStepType(String description) {
        this.description = description;
    }

    public boolean isBlockStart() {
        return this == NORMAL_START || this == LOOP_START || this == CONDITION_START;
    }
    
    public boolean isBlockEnd() {
        return this == NORMAL_END || this == LOOP_END || this == CONDITION_END;
    }
    
    public boolean isBlock() {
        return isBlockStart() || isBlockEnd();
    }
}