package com.kf.uitest.dto.execution;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecutionGroupDTO {
    /**
     * 组ID，用于标识一组相关联的用例
     * 格式：UUID
     */
    private String id;
    
    /**
     * 组名称，用于描述这组用例的用途
     * 示例：登录-创建订单-支付流程
     */
    private String name;
    
    /**
     * 用例列表，按执行顺序排序
     * 包含：主用例及其依赖用例
     */
    private List<ExecutionCaseDTO> cases;
} 