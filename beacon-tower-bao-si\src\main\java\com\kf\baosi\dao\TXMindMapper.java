package com.kf.baosi.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kf.baosi.dto.XMindToExcelListDTO;
import com.kf.baosi.entity.TXMind;
import org.apache.ibatis.annotations.Param;

public interface TXMindMapper extends BaseMapper<TXMind> {
    IPage<XMindToExcelListDTO> getFileList(@Param("userId") String userId,
                                            @Param("fileName") String fileName,
                                            @Param("isComplete") String isComplete,
                                            @Param("page") Page<?> page);
}
