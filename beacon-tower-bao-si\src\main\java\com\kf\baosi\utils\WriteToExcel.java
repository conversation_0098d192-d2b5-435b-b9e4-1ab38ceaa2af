package com.kf.baosi.utils;

import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class WriteToExcel {

    /**
     * 组装参数
     * 编号 项目 测试用例集 标题 前提 步骤 5期望结果 用例等级 解决版本 需求编号
     *
     * @return HSSFWorkbook
     */
    public static HSSFWorkbook getHSSFWorkbook(List<List<String>> allCaseList) {
        // 第一步：创建Excel工作簿对象
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 第二步：创建工作表
        HSSFSheet sheet = workbook.createSheet("测试用例");

        // 表头数据
        String[] headers = {"NO", "项目", "测试用例集", "标题", "前提", "步骤", "期望结果", "用例等级", "修复版本", "需求编号"};

        // 第三步：在sheet中添加表头第0行
        HSSFRow headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.length; i++) {
            headerRow.createCell(i).setCellValue(headers[i]);
        }
        String project = allCaseList.get(0).get(0);
        String[] parts = project.split("-", 3);
        // 项目代号
        String projectKey = parts[0];
        // 解决版本
        String fixVersions = parts[1];

        // 正则表达式
        String pattern = "【(.*?)】|\\[(.*?)]";
        Pattern regex = Pattern.compile(pattern);

        // 遍历所有case集合
        for (int i = 0; i < allCaseList.size(); i++) {
            // 创建用例内容的行，表头为第0行，因此内容从i+1开始
            HSSFRow row = sheet.createRow(i + 1);
            List<String> caseList = allCaseList.get(i);

            // 第一列是序号
            row.createCell(0).setCellValue(i + 1);

            String reqNum = "";

            // 标志变量，表示是否达到了期望结果列
            boolean reachedExpectedResult = false;
            for (int j = 0; j < caseList.size(); j++) {
                // 如果已经处理到了期望结果列，直接退出
                if (reachedExpectedResult) {
                    break;
                }
                String cellValue = caseList.get(j).trim();
                HSSFCell cell = row.createCell(j + 1);

                switch (j) {
                    case 0:
                        cell.setCellValue(projectKey);
                        break;
                    case 1:
                    case 2:
                        Matcher matcher = regex.matcher(cellValue);
                        if (matcher.find()) {
                            reqNum = matcher.group(1) != null ? matcher.group(1) : matcher.group(2);
                            String beforeBracket = cellValue.substring(0, matcher.start()).trim();
                            String afterBracket = cellValue.substring(matcher.end()).trim();
                            cellValue = beforeBracket + (beforeBracket.isEmpty() || afterBracket.isEmpty() ? "" : " ") + afterBracket;
                        }
                        cell.setCellValue(cellValue);
                        break;
                    case 6:
                        if (!cellValue.startsWith("P")) {
                            cellValue = "P2";
                        }
                        cell.setCellValue(cellValue);
                        break;
                    case 7:
                        // 期望结果之后的列都舍弃
                        reachedExpectedResult = true;
                        break;
                    default:
                        cell.setCellValue(cellValue);
                }
            }

            row.createCell(8).setCellValue(fixVersions);
            row.createCell(9).setCellValue(reqNum);
        }
        return workbook;
    }

    /**
     * 设置表头格式 颜色可参照：<a href="https://blog.csdn.net/w405722907/article/details/76915903">...</a>
     *
     * @param styleHead 表头样式
     * @return 表头样式 HSSFCellStyle
     */
    public static HSSFCellStyle getHeadStyle(HSSFCellStyle styleHead, HSSFFont fontHead) {
        // 水平居中
        styleHead.setAlignment(HorizontalAlignment.CENTER);

        // 垂直居中
        styleHead.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置标题背景色
        styleHead.setFillPattern(FillPatternType.SOLID_FOREGROUND);
//        styleHead.setFillForegroundColor(IndexedColors.LIME.getIndex());// 绿色
        styleHead.setFillForegroundColor(IndexedColors.PALE_BLUE.index);// 蓝色

        // 设置四周边框
        styleHead.setBorderBottom(BorderStyle.THIN);// 下边框
        styleHead.setBorderLeft(BorderStyle.THIN);// 左边框
        styleHead.setBorderTop(BorderStyle.THIN);// 上边框
        styleHead.setBorderRight(BorderStyle.THIN);// 右边框

        // 设置自动换行;
        styleHead.setWrapText(true);

        // 设置字体
        fontHead.setFontName("微软雅黑");
        fontHead.setBold(true);
        styleHead.setFont(fontHead);

        // 自定义一个原谅色
//        HSSFPalette customPalette = workbook.getCustomPalette();
//        HSSFColor yuanLiangColor = customPalette.addColor((byte) 146, (byte) 208, (byte) 80);

        return styleHead;
    }

    /**
     * 设置单元格格式 颜色可参照：<a href="https://blog.csdn.net/w405722907/article/details/76915903">...</a>
     * 对格式的设置进行优化，提升了性能：<a href="https://blog.csdn.net/qq592304796/article/details/52608714/">...</a>
     *
     * @param style 单元格样式
     * @return 单元格样式 HSSFCellStyle
     */
    public static HSSFCellStyle getCellStyle(HSSFCellStyle style, HSSFFont font) {
        // 垂直居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置自动换行;
        style.setWrapText(true);

        font.setFontName("微软雅黑");
        style.setFont(font);
        return style;
    }

    public static boolean containsChinese(String s) {
        for (char c : s.toCharArray()) {
            if (Character.UnicodeScript.of(c) == Character.UnicodeScript.HAN) {
                return true;
            }
        }
        return false;
    }

    public static boolean isAllEnglish(String s) {
        return s.matches("[a-zA-Z]+");
    }

}
