package com.kf.baosi.enums;

import lombok.Getter;
import org.apache.commons.collections4.map.LinkedMap;

/**
 * CSV导入Jira状态枚举
 * 0:未导入 1:导入中 2:导入成功 3:导入失败 4:部分失败
 */
@Getter
public enum TXMindToJiraEnum {

    IMPORT_NOT("0", "未导入"),
    IMPORT_ING("1", "导入中"),
    IMPORT_SUCCESS("2", "导入成功"),
    IMPORT_FAIL("3", "导入失败"),
    IMPORT_PART_FAIL("4", "部分失败");

    private final String value;
    private final String name;

    TXMindToJiraEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    //获取枚举
    public static String getEnum(String name) {
        for (TXMindToJiraEnum xMindEnum : TXMindToJiraEnum.values()) {
            if (xMindEnum.getName().equals(name)) {
                return xMindEnum.value;
            }
        }
        throw new IllegalArgumentException("未找到对应的类型");
    }

    /**
     * 获取枚举列表
     * @return 导入状态枚举列表
     */
    public static LinkedMap<String, String> getEnumMap() {
        LinkedMap<String, String> map = new LinkedMap<>();
        for (TXMindToJiraEnum xMindEnum : TXMindToJiraEnum.values()) {
            map.put(xMindEnum.getValue(), xMindEnum.getName());
        }
        return map;
    }


}
