/**
 * AI测试相关的Composable
 * 提供AI测试的通用逻辑和状态管理
 */

import { ref, reactive, computed, readonly } from 'vue'
import { ElMessage } from 'element-plus'
// import {
//     startAITest,
//     getAITestConfig,
//     checkAITestHealth,
//     type AITestStartRequest,
//     type AITestConfigApiResponse,
//     type AITestHealthApiResponse
// } from '@/api/ai-test'

// 临时类型定义，避免导入错误
interface AITestStartRequest {
  ids: string[]
  enableAiEvaluation: boolean
  disableChunking: boolean
}

interface AITestConfigApiResponse {
  data: {
    isSuccess: boolean
    data: any
  }
}

interface AITestHealthApiResponse {
  data: {
    isSuccess: boolean
  }
}

// 临时函数定义，避免导入错误
const startAITest = async (params: AITestStartRequest): Promise<{ data: { isSuccess: boolean, data: { taskId: string }, message?: string } }> => {
    console.log('Mock startAITest called with:', params)
    return { data: { isSuccess: true, data: { taskId: 'mock-task-id' } } }
}

const getAITestConfig = async (): Promise<AITestConfigApiResponse> => {
    console.log('Mock getAITestConfig called')
    return { data: { isSuccess: true, data: {} } }
}

const checkAITestHealth = async (): Promise<AITestHealthApiResponse> => {
    console.log('Mock checkAITestHealth called')
    return { data: { isSuccess: true } }
}

// AI测试状态枚举
export enum AITestStatus {
  IDLE = 'idle',
  RUNNING = 'running',
  COMPLETED = 'completed',
  ERROR = 'error'
}

// AI测试配置接口
export interface AITestConfig {
  enableAiEvaluation: boolean
  disableChunking: boolean
  maxTokens: number
  timeout: number
}

// AI测试状态接口
export interface AITestState {
  status: AITestStatus
  currentTaskId: string | null
  progress: number
  error: string | null
  startTime: Date | null
  endTime: Date | null
}

/**
 * AI测试Composable
 */
export function useAITest() {
    // 响应式状态
    const state = reactive<AITestState>({
        status: AITestStatus.IDLE,
        currentTaskId: null,
        progress: 0,
        error: null,
        startTime: null,
        endTime: null
    })

    const config = reactive<AITestConfig>({
        enableAiEvaluation: true,
        disableChunking: false,
        maxTokens: 8000,
        timeout: 120000
    })

    const loading = ref(false)

    // 计算属性
    const isRunning = computed(() => state.status === AITestStatus.RUNNING)
    const isCompleted = computed(() => state.status === AITestStatus.COMPLETED)
    const hasError = computed(() => state.status === AITestStatus.ERROR)
    const canStart = computed(() => state.status === AITestStatus.IDLE || state.status === AITestStatus.COMPLETED || state.status === AITestStatus.ERROR)

    const duration = computed(() => {
        if (!state.startTime) return 0
        const endTime = state.endTime || new Date()
        return Math.floor((endTime.getTime() - state.startTime.getTime()) / 1000)
    })

    // 重置状态
    const resetState = () => {
        state.status = AITestStatus.IDLE
        state.currentTaskId = null
        state.progress = 0
        state.error = null
        state.startTime = null
        state.endTime = null
    }

    // 启动AI测试
    const startTest = async (ids: string[]): Promise<string | null> => {
        if (!canStart.value) {
            ElMessage.warning('当前状态不允许启动测试')
            return null
        }

        if (!ids || ids.length === 0) {
            ElMessage.error('请提供测试ID列表')
            return null
        }

        loading.value = true
        state.status = AITestStatus.RUNNING
        state.startTime = new Date()
        state.error = null
        state.progress = 0

        try {
            const params: AITestStartRequest = {
                ids,
                enableAiEvaluation: config.enableAiEvaluation,
                disableChunking: config.disableChunking
            }

            const response = await startAITest(params)
      
            if (response.data.isSuccess && response.data.data.taskId) {
                state.currentTaskId = response.data.data.taskId
                ElMessage.success(`测试启动成功，任务ID: ${response.data.data.taskId}`)
                return response.data.data.taskId
            } else {
                throw new Error(response.data.message || '启动测试失败')
            }
        } catch (error) {
            state.status = AITestStatus.ERROR
            state.error = error instanceof Error ? error.message : '启动测试失败'
            state.endTime = new Date()
            ElMessage.error(state.error)
            return null
        } finally {
            loading.value = false
        }
    }

    // 更新进度
    const updateProgress = (progress: number) => {
        state.progress = Math.max(0, Math.min(100, progress))
    }

    // 标记测试完成
    const markCompleted = () => {
        state.status = AITestStatus.COMPLETED
        state.endTime = new Date()
        state.progress = 100
    }

    // 标记测试错误
    const markError = (error: string) => {
        state.status = AITestStatus.ERROR
        state.error = error
        state.endTime = new Date()
    }

    // 获取AI测试配置
    const fetchConfig = async (): Promise<boolean> => {
        try {
            const response = await getAITestConfig()
            if (response.data.isSuccess && response.data.data) {
                // 更新配置（如果API返回配置信息）
                Object.assign(config, response.data.data)
                return true
            }
            return false
        } catch (error) {
            console.error('获取AI测试配置失败:', error)
            return false
        }
    }

    // 检查AI测试服务健康状态
    const checkHealth = async (): Promise<boolean> => {
        try {
            const response = await checkAITestHealth()
            return response.data.isSuccess
        } catch (error) {
            console.error('检查AI测试服务健康状态失败:', error)
            return false
        }
    }

    // 格式化持续时间
    const formatDuration = (seconds: number): string => {
        const hours = Math.floor(seconds / 3600)
        const minutes = Math.floor((seconds % 3600) / 60)
        const secs = seconds % 60

        if (hours > 0) {
            return `${hours}小时${minutes}分钟${secs}秒`
        } else if (minutes > 0) {
            return `${minutes}分钟${secs}秒`
        } else {
            return `${secs}秒`
        }
    }

    return {
    // 状态
        state: readonly(state),
        config,
        loading: readonly(loading),

        // 计算属性
        isRunning,
        isCompleted,
        hasError,
        canStart,
        duration,

        // 方法
        resetState,
        startTest,
        updateProgress,
        markCompleted,
        markError,
        fetchConfig,
        checkHealth,
        formatDuration
    }
}
