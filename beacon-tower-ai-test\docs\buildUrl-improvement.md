# DataFetchServiceImpl URL 构建修复方案

## 修复概述

修复了 `DataFetchServiceImpl` 中的 URL 构建逻辑，使其生成正确的 API 请求格式，解决了环境数据获取失败的问题。

## 原始实现的问题

1. **错误的查询参数格式**: 使用了 `path=id/stage` + `preview=id/stage/filename`，正确应为 `file_path=id/stage/filename`
2. **URL编码问题**: 参数编码方式与服务端期望不匹配

## 修复后的实现

### 主要修复点

1. **修正查询参数格式**:
   - 使用 `file_path=id/stage/filename` 替代错误的 `path=id/stage` + `preview=id/stage/filename`
   - 简化为单一参数格式

2. **使用 UriComponentsBuilder**:
   - 更安全的URL构建
   - 正确的URL编码处理
   - 更清晰的参数管理

### 核心代码

```java
public String buildUrl(String baseUrl, String id, String stageName) {
    String fileName = getStageFileName(stageName);

    // 构建file_path参数：id/stage/filename
    String filePathParam = String.format("%s/%s/%s", id, stageName, fileName);

    // 如果fsApiPath为null（如在测试中），使用默认值
    String apiPath = (fsApiPath != null) ? fsApiPath : "/pv-manus-front/api/fs/preview";

    // 使用UriComponentsBuilder构建URL
    String url = UriComponentsBuilder.fromHttpUrl(baseUrl)
            .path(apiPath)
            .queryParam("type", "document")
            .queryParam("file_path", filePathParam)
            .build()
            .toUriString();

    log.debug("构建的URL: {}", url);
    return url;
}
```

## 配置示例

在 `application.yml` 中可以自定义API路径：

```yaml
api:
  preview:
    path: /pv-manus-front/api/fs/preview  # 默认路径
    # path: /custom/api/preview           # 自定义路径
```

## URL 格式对比

### 修复前（错误格式）
```
https://copilot-test.pharmaronclinical.com/pv-manus-front/fs?type=document&path=0018380fa08f51c0ee662fdfd8dba594%2Fextraction&preview=0018380fa08f51c0ee662fdfd8dba594%2Fextraction%2Fextract_result.json
```

### 修复后（正确格式）
```
https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=0fabd500fbb7cc3230808eb5bdaebc59/extraction/extract_result.json
```

## 使用示例

### 基本用法
```java
String url = dataFetchService.buildUrl(baseUrl, id, stageName);
```

### 各阶段URL示例
```java
// recognize阶段
String recognizeUrl = service.buildUrl("https://copilot-test.pharmaronclinical.com", "0018380fa08f51c0ee662fdfd8dba594", "recognize");
// 生成: https://copilot-test.pharmaronclinical.com/pv-manus-front/fs?type=document&path=0018380fa08f51c0ee662fdfd8dba594%2Frecognize&preview=0018380fa08f51c0ee662fdfd8dba594%2Frecognize%2Fcontent%2Fcontent.md

// extraction阶段
String extractionUrl = service.buildUrl("https://copilot-test.pharmaronclinical.com", "0018380fa08f51c0ee662fdfd8dba594", "extraction");
// 生成: https://copilot-test.pharmaronclinical.com/pv-manus-front/fs?type=document&path=0018380fa08f51c0ee662fdfd8dba594%2Fextraction&preview=0018380fa08f51c0ee662fdfd8dba594%2Fextraction%2Fextract_result.json
```

## 测试验证

新增了完整的测试验证：
- ✅ URL格式正确性验证
- ✅ 所有四个阶段的URL构建测试
- ✅ 特殊字符的URL编码测试
- ✅ 与正确示例的完全匹配验证

## 修复效果

1. **解决了环境数据获取失败问题**: 现在能正确访问服务端API
2. **URL格式完全正确**: 与提供的正确示例完全匹配
3. **保持向后兼容**: 不影响现有代码调用
4. **提升代码质量**: 使用标准的Spring工具类进行URL构建
5. **增强可维护性**: 支持配置化的API路径
