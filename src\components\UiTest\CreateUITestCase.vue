<template>
    <el-dialog
        v-model="localVisible"
        title="创建测试用例"
        width="30%"
        @close="handleClose"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
        <el-form :model="form" ref="formRef" label-width="60px" class="defect-form" :rules="rules">
            <el-form-item label="名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入名称"></el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="text" @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="submitForm" :loading="loading">确定</el-button>
        </template>
    </el-dialog>
</template>
<script lang="ts" setup>
import { reactive, ref } from 'vue'
import type { FormInstance } from 'element-plus/lib/components'
import { validate } from '@/utils/formExtend'
import { createUITestCase } from '@/api/layout'
import { ElMessage } from 'element-plus'

const props = defineProps<{
    suiteId: number
    visible: boolean
}>()
const localVisible = ref(props.visible)
const emit = defineEmits<{
    (e: 'close'): void
    (e: 'save', payload: {
        suiteId: number
        newNode: any
    }): void
}>()
// 表单引用
const formRef = ref<FormInstance>()
const form = reactive({
    name: ''
})
// 处理对话框关闭
const handleClose = () => {
    emit('close')
}
// 处理取消
const handleCancel = () => {
    // 重置表单
    formRef.value?.resetFields()
    emit('close')
}
const rules = reactive({
    name: [
        { required: true, message: '请输入用例名称', trigger: 'blur' }
    ]
})
const loading = ref(false)
const submitForm = async () => {
    const isValid = await validate(formRef, false)
    if (!isValid) {
        return
    }
    loading.value = true
    try {
        const res = await createUITestCase({
            suiteId: props.suiteId,
            caseName: form.name
        })
        if (!res.data.isSuccess) {
            ElMessage.error('创建用例失败')
            return
        }
        loading.value = false
        emit('save', {
            newNode: res.data.data,
            suiteId: props.suiteId
        })
    } catch (error) {
        ElMessage.error('创建用例失败')
        console.error(error)
    } finally {
        loading.value = false
    }
}
</script>