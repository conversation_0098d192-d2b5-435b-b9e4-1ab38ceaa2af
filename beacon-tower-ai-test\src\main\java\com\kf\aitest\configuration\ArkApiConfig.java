package com.kf.aitest.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 火山引擎ARK API配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "ai.ark")
public class ArkApiConfig {
    
    /**
     * API端点URL
     */
    private String apiUrl;
    
    /**
     * API密钥
     */
    private String apiKey;
    
    /**
     * 模型ID
     */
    private String model;
    
    /**
     * 请求超时时间（毫秒）
     */
    private Integer timeout = 30000;
    
    /**
     * 最大重试次数
     */
    private Integer maxRetries = 3;
    
    /**
     * 默认最大token数
     */
    private Integer defaultMaxTokens = 2000;
    
    /**
     * 默认温度参数
     */
    private Double defaultTemperature = 0.7;
}
