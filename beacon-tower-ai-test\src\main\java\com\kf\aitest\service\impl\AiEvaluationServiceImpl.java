package com.kf.aitest.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kf.aitest.configuration.ArkApiConfig;
import com.kf.aitest.dto.DataComparisonResultDTO;
import com.kf.aitest.dto.StageDataDTO;
import com.kf.aitest.dto.ark.ArkApiRequest;
import com.kf.aitest.dto.ark.ArkApiResponse;
import com.kf.aitest.exception.AiServiceException;
import com.kf.aitest.service.AiEvaluationService;
import com.kf.aitest.service.ComparisonProgressManager;
import com.kf.aitest.service.DataChunkService;
import com.kf.aitest.service.PromptTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * AI评估服务实现
 */
@Slf4j
@Service
public class AiEvaluationServiceImpl implements AiEvaluationService {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private PromptTemplateService promptTemplateService;

    @Autowired
    private DataChunkService dataChunkService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ArkApiConfig arkApiConfig;

    @Autowired
    private ComparisonProgressManager progressManager;

    /**
     * 最大token限制，可通过配置文件调整
     */
    @Value("${ai.evaluation.max-tokens:8000}")
    private int maxTokens;

    /**
     * 是否启用详细的控制台打印
     */
    @Value("${ai.evaluation.enable-console-print:true}")
    private boolean enableConsolePrint;
    
    @Override
    public StageDataDTO evaluateStageData(String taskId, StageDataDTO stageData) {
        return evaluateStageData(taskId, stageData, false);
    }

    /**
     * 评估阶段数据（支持禁用分片）
     */
    public StageDataDTO evaluateStageData(String taskId, StageDataDTO stageData, boolean disableChunking) {
        consolePrint("🤖 开始AI评估阶段: %s, 禁用分片: %s%n", stageData.getStageName(), disableChunking ? "是" : "否");

        // 发送AI评估开始事件
        progressManager.sendAiEvent(taskId, "ai-evaluation-start",
                String.format("开始AI评估阶段: %s", stageData.getStageName()));

        try {
            if (disableChunking) {
                // 禁用分片，直接处理完整数据
                consolePrint("📊 数据分析: 已禁用分片处理，直接发送完整数据%n");
                evaluateDirectly(stageData);
            } else {
                // 检查是否需要分片处理
                boolean needsChunking = dataChunkService.needsChunking(
                        stageData.getUatData(), stageData.getTestData(), maxTokens);

                consolePrint("📊 数据分析: 是否需要分片处理 = %s%n", needsChunking ? "是" : "否");

                if (needsChunking) {
                    // 分片处理
                    evaluateWithChunking(stageData);
                } else {
                    // 直接处理
                    evaluateDirectly(stageData);
                }
            }

            boolean actuallyChunked = !disableChunking && (stageData.getUatData() != null && stageData.getTestData() != null
                    && dataChunkService.needsChunking(stageData.getUatData(), stageData.getTestData(), maxTokens));

            log.info("AI评估完成: stage={}, score={}, chunked={}, disableChunking={}",
                    stageData.getStageName(), stageData.getAiScore(), actuallyChunked, disableChunking);

            consolePrint("✅ AI评估完成: 阶段=%s, 评分=%s, 分片处理=%s%n",
                    stageData.getStageName(),
                    stageData.getAiScore() != null ? stageData.getAiScore() : "N/A",
                    disableChunking ? "已禁用" : (actuallyChunked ? "是" : "否"));

            // 发送AI评估完成事件
            progressManager.sendAiEvent(taskId, "ai-evaluation-complete", stageData);

        } catch (Exception e) {
            log.error("AI评估失败: stage={}, error={}", stageData.getStageName(), e.getMessage(), e);
            consolePrint("❌ AI评估失败: 阶段=%s, 错误=%s%n", stageData.getStageName(), e.getMessage());
            stageData.setAiEvaluation("AI评估失败: " + e.getMessage());
            stageData.setAiScore(0);

            // 发送AI评估失败事件
            progressManager.sendAiEvent(taskId, "ai-evaluation-error",
                    String.format("阶段%s AI评估失败: %s", stageData.getStageName(), e.getMessage()));
        }

        return stageData;
    }
    
    @Override
    public DataComparisonResultDTO evaluateOverallResult(String taskId, DataComparisonResultDTO comparisonResult) {
        consolePrint("🎯 开始整体AI评估: ID=%s%n", comparisonResult.getId());

        // 发送整体AI评估开始事件
        progressManager.sendAiEvent(taskId, "overall-evaluation-start",
                String.format("开始整体AI评估: %s", comparisonResult.getId()));

        try {
            String prompt = buildOverallPrompt(comparisonResult);
            consolePrint("📝 构建整体评估提示词，长度: %d 字符%n", prompt.length());

            // 调用实际的AI服务
            String aiResponse = callAiService(prompt);
            consolePrint("🤖 收到AI响应，长度: %d 字符%n", aiResponse.length());

            // 解析AI响应
            comparisonResult.setOverallAiEvaluation(extractEvaluation(aiResponse));
            comparisonResult.setOverallScore(extractScore(aiResponse));

            log.info("整体AI评估完成: id={}, score={}", comparisonResult.getId(), comparisonResult.getOverallScore());
            consolePrint("✅ 整体AI评估完成: ID=%s, 综合评分=%s%n",
                    comparisonResult.getId(),
                    comparisonResult.getOverallScore() != null ? comparisonResult.getOverallScore() : "N/A");

            // 发送整体AI评估完成事件
            progressManager.sendAiEvent(taskId, "overall-evaluation-complete", comparisonResult);

        } catch (Exception e) {
            log.error("整体AI评估失败: id={}, error={}", comparisonResult.getId(), e.getMessage(), e);
            consolePrint("❌ 整体AI评估失败: ID=%s, 错误=%s%n", comparisonResult.getId(), e.getMessage());
            comparisonResult.setOverallAiEvaluation("整体AI评估失败: " + e.getMessage());
            comparisonResult.setOverallScore(0);

            // 发送整体AI评估失败事件
            progressManager.sendAiEvent(taskId, "overall-evaluation-error",
                    String.format("整体AI评估失败: %s", e.getMessage()));
        }

        return comparisonResult;
    }
    
    @Override
    public String buildStagePrompt(String stageName, Object uatData, Object testData) {
        return promptTemplateService.buildEvaluationPrompt(stageName, uatData, testData);
    }

    /**
     * 调用AI服务（支持图像）
     *
     * @param prompt 文本提示词
     * @param imageUrl 图像URL（可选）
     * @return AI响应内容
     */
    public String callAiServiceWithImage(String prompt, String imageUrl) {
        return callAiService(prompt, imageUrl);
    }

    /**
     * 直接评估（不分片）
     */
    private void evaluateDirectly(StageDataDTO stageData) throws Exception {
        String prompt = buildStagePrompt(stageData.getStageName(),
                stageData.getUatData(), stageData.getTestData());

        String aiResponse = callAiService(prompt);

        stageData.setAiEvaluation(extractEvaluation(aiResponse));
        stageData.setAiScore(extractScore(aiResponse));
    }

    /**
     * 分片评估
     */
    private void evaluateWithChunking(StageDataDTO stageData) throws Exception {
        // 获取数据分片
        DataChunkService.ChunkResult chunkResult = dataChunkService.chunkData(
                stageData.getUatData(), stageData.getTestData(), maxTokens);

        List<DataChunkService.ChunkEvaluationResult> chunkResults = new ArrayList<>();

        // 逐个评估分片
        for (DataChunkService.DataChunk chunk : chunkResult.getChunks()) {
            try {
                String chunkPrompt = buildChunkPrompt(stageData.getStageName(), chunk);
                String aiResponse = callAiService(chunkPrompt);

                String evaluation = extractEvaluation(aiResponse);
                Integer score = extractScore(aiResponse);

                chunkResults.add(new DataChunkService.ChunkEvaluationResult(
                        evaluation, score, chunk.getChunkIndex(), chunk.getChunkDescription()));

            } catch (Exception e) {
                log.error("分片评估失败: chunk={}, error={}", chunk.getChunkIndex(), e.getMessage());
                chunkResults.add(new DataChunkService.ChunkEvaluationResult(
                        "分片评估失败: " + e.getMessage(), 0,
                        chunk.getChunkIndex(), chunk.getChunkDescription()));
            }
        }

        // 合并分片结果
        String mergedEvaluation = dataChunkService.mergeChunkResults(chunkResults, stageData.getStageName());
        Integer overallScore = dataChunkService.calculateOverallScore(chunkResults);

        stageData.setAiEvaluation(mergedEvaluation);
        stageData.setAiScore(overallScore);
    }

    /**
     * 构建分片提示词
     */
    private String buildChunkPrompt(String stageName, DataChunkService.DataChunk chunk) {
        StringBuilder prompt = new StringBuilder();

        // 获取基础提示词模板
        String template = promptTemplateService.getPromptTemplate(stageName);
        prompt.append(template).append("\n\n");

        // 添加分片信息
        prompt.append("## 分片信息\n");
        prompt.append(String.format("当前分片: %d/%d - %s\n\n",
                chunk.getChunkIndex(), chunk.getTotalChunks(), chunk.getChunkDescription()));

        // 添加数据
        try {
            prompt.append("### UAT环境数据（基准数据）：\n");
            prompt.append("```json\n");
            prompt.append(objectMapper.writeValueAsString(chunk.getUatChunk()));
            prompt.append("\n```\n\n");

            prompt.append("### TEST环境数据（待评估数据）：\n");
            prompt.append("```json\n");
            prompt.append(objectMapper.writeValueAsString(chunk.getTestChunk()));
            prompt.append("\n```\n\n");
        } catch (Exception e) {
            prompt.append("数据序列化失败：").append(e.getMessage()).append("\n\n");
        }

        prompt.append("请根据上述提示词要求，对比这个分片的数据并给出评估结果。");

        return prompt.toString();
    }

    private String buildOverallPrompt(DataComparisonResultDTO comparisonResult) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("# 临床试验数据对比整体评估\n\n");
        prompt.append("请基于各阶段的AI评估结果，给出整体的数据对比评估。\n\n");

        prompt.append("**数据ID**: ").append(comparisonResult.getId()).append("\n");
        prompt.append("**处理阶段数**: ").append(comparisonResult.getTotalStageCount()).append("\n");
        prompt.append("**成功阶段数**: ").append(comparisonResult.getSuccessStageCount()).append("\n\n");

        // 添加各阶段的评估结果
        if (comparisonResult.getStageResults() != null) {
            prompt.append("## 各阶段评估结果\n\n");
            for (StageDataDTO stage : comparisonResult.getStageResults()) {
                prompt.append("### ").append(getStageDescription(stage.getStageName())).append("\n");
                prompt.append("**状态**: ").append(stage.getStatus()).append("\n");
                prompt.append("**AI评分**: ").append(stage.getAiScore()).append("/100\n");
                if (stage.getAiEvaluation() != null) {
                    prompt.append("**评估**: ").append(stage.getAiEvaluation()).append("\n\n");
                } else {
                    prompt.append("**评估**: 未进行AI评估\n\n");
                }
            }
        }

        prompt.append("## 评估要求\n");
        prompt.append("请从以下维度进行整体评估：\n");
        prompt.append("1. **数据质量一致性**: 各阶段数据处理的整体质量\n");
        prompt.append("2. **流程完整性**: 从识别到转换的完整流程表现\n");
        prompt.append("3. **临床合规性**: 对临床试验数据合规性的影响\n");
        prompt.append("4. **风险评估**: 数据质量问题可能带来的风险\n\n");

        prompt.append("请按以下格式输出：\n");
        prompt.append("**整体评估**: [综合分析各阶段表现，重点关注临床试验数据质量]\n");
        prompt.append("**综合评分**: [0-100的数字]");

        return prompt.toString();
    }
    
    /**
     * 调用火山引擎ARK AI服务
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    private String callAiService(String prompt) {
        return callAiService(prompt, null);
    }

    /**
     * 调用火山引擎ARK AI服务（支持多模态）
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    private String callAiService(String prompt, String imageUrl) {
        log.debug("调用ARK AI服务，提示词长度: {}, 图像URL: {}", prompt.length(), imageUrl);
        consolePrint("🔗 正在调用ARK AI服务...%n");
        consolePrint("   📝 提示词长度: %d 字符%n", prompt.length());
        if (imageUrl != null) {
            consolePrint("   🖼️ 图像URL: %s%n", imageUrl);
        }

        try {
            // 构建请求
            ArkApiRequest request = buildArkRequest(prompt, imageUrl);
            consolePrint("   📦 请求构建完成，模型: %s%n", request.getModel());

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(arkApiConfig.getApiKey());

            HttpEntity<ArkApiRequest> entity = new HttpEntity<>(request, headers);

            // 发送请求
            consolePrint("   🚀 发送API请求到: %s%n", arkApiConfig.getApiUrl());
            ResponseEntity<ArkApiResponse> response = restTemplate.exchange(
                    arkApiConfig.getApiUrl(),
                    HttpMethod.POST,
                    entity,
                    ArkApiResponse.class
            );

            consolePrint("   ✅ 收到API响应，状态码: %s%n", response.getStatusCode());

            // 处理响应
            return processArkResponse(response.getBody());

        } catch (AiServiceException e) {
            // 重新抛出AI服务异常
            throw e;
        } catch (org.springframework.web.client.HttpClientErrorException e) {
            log.error("ARK API HTTP客户端错误: status={}, body={}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new AiServiceException("ARK API调用失败: HTTP " + e.getStatusCode(),
                    String.valueOf(e.getStatusCode().value()), "HTTP_CLIENT_ERROR", e);
        } catch (org.springframework.web.client.HttpServerErrorException e) {
            log.error("ARK API HTTP服务器错误: status={}, body={}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new AiServiceException("ARK API服务器错误: HTTP " + e.getStatusCode(),
                    String.valueOf(e.getStatusCode().value()), "HTTP_SERVER_ERROR", e);
        } catch (org.springframework.web.client.ResourceAccessException e) {
            log.error("ARK API网络连接错误: {}", e.getMessage());
            throw new AiServiceException("ARK API网络连接失败: " + e.getMessage(),
                    "NETWORK_ERROR", "RESOURCE_ACCESS", e);
        } catch (Exception e) {
            log.error("调用ARK AI服务失败: {}", e.getMessage(), e);
            throw new AiServiceException("AI服务调用失败: " + e.getMessage(),
                    "UNKNOWN_ERROR", "GENERAL", e);
        }
    }

    /**
     * 构建ARK API请求
     */
    private ArkApiRequest buildArkRequest(String prompt, String imageUrl) {
        ArkApiRequest request = new ArkApiRequest();
        request.setModel(arkApiConfig.getModel());
        request.setMaxTokens(arkApiConfig.getDefaultMaxTokens());
        request.setTemperature(arkApiConfig.getDefaultTemperature());

        // 构建消息
        ArkApiRequest.Message message = new ArkApiRequest.Message();
        message.setRole("user");

        if (imageUrl != null && !imageUrl.trim().isEmpty()) {
            // 验证图像URL
            if (!isValidImageUrl(imageUrl)) {
                throw new AiServiceException("无效的图像URL: " + imageUrl,
                        "INVALID_IMAGE_URL", "VALIDATION_ERROR");
            }

            // 多模态消息（文本+图像）
            List<ArkApiRequest.Content> contents = new ArrayList<>();

            // 添加文本内容
            ArkApiRequest.Content textContent = new ArkApiRequest.Content();
            textContent.setType("text");
            textContent.setText(prompt);
            contents.add(textContent);

            // 添加图像内容
            ArkApiRequest.Content imageContent = new ArkApiRequest.Content();
            imageContent.setType("image_url");
            ArkApiRequest.ImageUrl imageUrlObj = new ArkApiRequest.ImageUrl();
            imageUrlObj.setUrl(imageUrl);
            imageUrlObj.setDetail("auto");
            imageContent.setImageUrl(imageUrlObj);
            contents.add(imageContent);

            message.setContent(contents);
            log.debug("构建多模态请求: 文本长度={}, 图像URL={}", prompt.length(), imageUrl);
            consolePrint("   🎨 构建多模态消息: 文本+图像%n");
        } else {
            // 纯文本消息
            message.setContent(prompt);
            log.debug("构建纯文本请求: 文本长度={}", prompt.length());
            consolePrint("   📝 构建纯文本消息%n");
        }

        request.setMessages(List.of(message));
        return request;
    }

    /**
     * 处理ARK API响应
     */
    private String processArkResponse(ArkApiResponse response) {
        if (response == null) {
            throw new AiServiceException("ARK API响应为空", "NULL_RESPONSE", "RESPONSE_ERROR");
        }

        // 检查错误
        if (response.getError() != null) {
            ArkApiResponse.Error error = response.getError();
            log.error("ARK API返回错误: code={}, type={}, message={}",
                    error.getCode(), error.getType(), error.getMessage());
            throw new AiServiceException("ARK API错误: " + error.getMessage(),
                    error.getCode(), error.getType());
        }

        // 提取响应内容
        if (response.getChoices() != null && !response.getChoices().isEmpty()) {
            ArkApiResponse.Choice choice = response.getChoices().get(0);
            if (choice.getMessage() != null && choice.getMessage().getContent() != null) {
                String content = choice.getMessage().getContent().trim();

                // 记录token使用情况
                if (response.getUsage() != null) {
                    log.info("ARK API token使用情况 - 输入: {}, 输出: {}, 总计: {}",
                            response.getUsage().getPromptTokens(),
                            response.getUsage().getCompletionTokens(),
                            response.getUsage().getTotalTokens());

                    consolePrint("   📊 Token使用统计:%n");
                    consolePrint("      📥 输入Token: %d%n", response.getUsage().getPromptTokens());
                    consolePrint("      📤 输出Token: %d%n", response.getUsage().getCompletionTokens());
                    consolePrint("      📈 总计Token: %d%n", response.getUsage().getTotalTokens());
                }

                // 检查内容是否为空
                if (content.isEmpty()) {
                    throw new AiServiceException("ARK API返回空内容", "EMPTY_CONTENT", "RESPONSE_ERROR");
                }

                return content;
            }
        }

        throw new AiServiceException("ARK API响应格式异常，无法提取内容",
                "INVALID_RESPONSE_FORMAT", "RESPONSE_ERROR");
    }

    /**
     * 验证图像URL是否有效
     */
    private boolean isValidImageUrl(String imageUrl) {
        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            return false;
        }

        try {
            // 基本URL格式验证
            java.net.URL url = new java.net.URL(imageUrl);
            String protocol = url.getProtocol();
            if (!"http".equals(protocol) && !"https".equals(protocol)) {
                return false;
            }

            // 检查是否是图像文件扩展名
            String path = url.getPath().toLowerCase();
            return path.endsWith(".jpg") || path.endsWith(".jpeg") ||
                   path.endsWith(".png") || path.endsWith(".gif") ||
                   path.endsWith(".bmp") || path.endsWith(".webp") ||
                   path.contains("image") || imageUrl.contains("image");

        } catch (java.net.MalformedURLException e) {
            log.warn("图像URL格式无效: {}", imageUrl);
            return false;
        }
    }

    /**
     * 从AI响应中提取评估结果
     */
    private String extractEvaluation(String aiResponse) {
        if (aiResponse == null) return "无评估结果";

        // 定义多种可能的评估结果格式模式
        String[] patterns = {
            // Markdown格式：**整体评估**: 内容 (匹配到最终评分之前)
            "\\*\\*(?:评估结果|整体评估|综合评估)\\*\\*[:：]\\s*(.+?)(?=\\n\\*\\*(?:最终评分|评分|综合评分)|$)",
            // 简单格式：整体评估：内容 (匹配到最终评分之前)
            "(?:评估结果|整体评估|综合评估)[:：]\\s*(.+?)(?=\\n(?:最终评分|评分|综合评分)[:：]|$)",
            // 更宽松的匹配：任何以"评估"结尾的标题 (匹配到最终评分之前)
            "[\\*]*[评估结果整体综合]*评估[\\*]*[:：]\\s*(.+?)(?=\\n[\\*]*[最终评分综合]*分[\\*]*[:：]|$)",
            // 匹配整个响应内容，如果没有找到特定格式
            "(.+?)(?=\\n[\\*]*(?:最终评分|评分|综合评分)[\\*]*[:：]|$)"
        };

        for (String patternStr : patterns) {
            Pattern pattern = Pattern.compile(patternStr, Pattern.DOTALL);
            Matcher matcher = pattern.matcher(aiResponse);

            if (matcher.find()) {
                String evaluation = matcher.group(1).trim();
                log.debug("成功提取评估结果，长度: {} (使用模式: {})", evaluation.length(), patternStr);
                return evaluation;
            }
        }

        log.debug("未能匹配评估结果格式，返回原始响应");
        return aiResponse; // 如果没有匹配到格式，返回原始响应
    }
    
    /**
     * 从AI响应中提取评分
     */
    private Integer extractScore(String aiResponse) {
        if (aiResponse == null) return 0;

        // 添加调试日志
        log.debug("尝试从AI响应中提取评分，响应长度: {}", aiResponse.length());

        // 定义多种可能的评分格式模式，按优先级尝试匹配
        String[] patterns = {
            // Markdown格式：**最终评分**: 85 或 **评分**: 85 或 **综合评分**: 85
            "\\*\\*(?:最终评分|评分|综合评分)\\*\\*[:：]\\s*(\\d+)",
            // Markdown格式带括号：**最终评分**: [85] 或 **评分**: [85]
            "\\*\\*(?:最终评分|评分|综合评分)\\*\\*[:：]\\s*[\\[\\(]?(\\d+)[\\]\\)]?",
            // 简单格式：最终评分：85 或 评分：85 或 综合评分：85
            "(?:最终评分|评分|综合评分)[:：]\\s*(\\d+)",
            // 简单格式带括号：最终评分: [85] 或 评分: [85] 或 综合评分: [85]
            "(?:最终评分|评分|综合评分)[:：]\\s*[\\[\\(]?(\\d+)[\\]\\)]?",
            // 更宽松的匹配：任何包含"分"字后跟数字的格式
            "[最终评综合]*分[:：]?\\s*[\\[\\(]?(\\d+)[\\]\\)]?"
        };

        for (String patternStr : patterns) {
            Pattern pattern = Pattern.compile(patternStr);
            Matcher matcher = pattern.matcher(aiResponse);

            if (matcher.find()) {
                try {
                    Integer score = Integer.parseInt(matcher.group(1));
                    log.debug("成功提取评分: {} (使用模式: {})", score, patternStr);
                    return score;
                } catch (NumberFormatException e) {
                    log.warn("无法解析评分数字: {} (模式: {})", matcher.group(1), patternStr);
                }
            }
        }

        // 如果所有模式都匹配失败，记录详细的调试信息
        log.warn("无法从AI响应中提取评分，响应内容: {}",
                aiResponse.length() > 200 ? aiResponse.substring(0, 200) + "..." : aiResponse);

        return 0; // 默认评分
    }
    
    /**
     * 获取阶段描述
     */
    private String getStageDescription(String stageName) {
        switch (stageName) {
            case "recognize":
                return "文档识别阶段";
            case "extraction":
                return "信息提取阶段";
            case "structured":
                return "结构化处理阶段";
            case "transformer":
                return "数据转换阶段";
            default:
                return "未知阶段";
        }
    }

    /**
     * 控制台打印辅助方法
     */
    private void consolePrint(String format, Object... args) {
        if (enableConsolePrint) {
            System.out.printf(format, args);
        }
    }
}
