<template>
    <el-dialog
        v-model="localVisible"
        :title="`${props.testCaseKey} 缺陷列表`"
        width="60%"
        @close="handleClose"
    >
        <el-table :data="bugList" style="width: 100%;" v-loading="loading">
            <el-table-column
                prop="key"
                label="缺陷ID"
                min-width="12%"
            >
                <template #default="{ row }" class="link">
                    <a :href="getLink(row.key)" target="_blank" class="link">{{ row.key }}</a>
                </template>
            </el-table-column>

            <el-table-column
                prop="summary"
                label="缺陷名称"
                min-width="60%"
                show-overflow-tooltip
            ></el-table-column>

            <el-table-column
                prop="status"
                label="状态"
                min-width="12%"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusType(row.status)"
                    >{{ row.status }}</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                prop="assignee"
                label="经办人"
                min-width="10%"
            ></el-table-column>

            <el-table-column
                label="操作"
                min-width="6%"
            >
                <template #default="{ row }">
                    <el-popconfirm cancel-button-text="取消" confirm-button-text="确定" title="从JIRA中删除该缺陷，确定删除吗？"
                                   width="220"
                                   @confirm="() => deleteBugClick(row)">
                        <template #reference>
                            <el-button v-prevent-default type="text" :icon="Delete"></el-button>
                        </template>
                    </el-popconfirm>
                </template>
            </el-table-column>
        </el-table>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { deleteBug, getTestRunBugsInfo } from '@/api/layout'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'

// 定义接收的 props
const props = defineProps<{
    visible: boolean
    jiraToken: string
    runId: string
    requirementKey: string
    testCaseKey: string
}>()

// 定义发出的事件
const emit = defineEmits<{
    (e: 'close', payload: {
        requirementKey: string
        testCaseKey: string
        bugList: BugInfo[]
    }): void
}>()

// 对话框的可见状态
const localVisible = ref(props.visible)

// 接口返回的缺陷信息类型
interface BugInfo {
    key: string
    summary: string
    status: string
    assignee: string
}
// 存储完整的缺陷列表
const bugList = ref<BugInfo[]>([])

const loading = ref(false)

// 获取缺陷列表的方法
const fetchDefectList = async () => {
    loading.value = true
    try {
        const res = await getTestRunBugsInfo(props.jiraToken, props.runId)
        if (!res.data.isSuccess) {
            ElMessage.error('获取缺陷列表失败')
            return
        }
        bugList.value = res.data.data.map((item: BugInfo) => ({
            key: item.key,
            summary: item.summary,
            assignee: item.assignee,
            status: item.status
        }))
    } catch (error) {
        console.error('获取缺陷列表错误:', error)
        ElMessage.error('获取缺陷列表失败')
    } finally {
        loading.value = false
    }
}
fetchDefectList()
// 删除缺陷的方法
const deleteBugClick = async (bug: any) => {
    const res = await deleteBug(props.jiraToken, props.runId, bug.key)
    if (res.data.isSuccess) {
        ElMessage.success('删除成功')
        bugList.value = bugList.value.filter((item) => item.key !== bug.key)
    } else {
        ElMessage.error('删除失败')
    }
}
// 获取状态类型的方法，根据状态值返回对应的类型
const getStatusType = (status: string) => {
    const successStatuses = ['非需求', '非缺陷', 'CLOSED', '作为后续版本需求']
    return successStatuses.includes(status) ? 'success' : 'danger'
}

// 生成链接的方法
const getLink = (key: string) => {
    return `http://jira.taimei.com/browse/${key}`
}

// 处理对话框关闭事件
const handleClose = () => {
    emit('close', {
        requirementKey: props.requirementKey,
        testCaseKey: props.testCaseKey,
        bugList: bugList.value
    })
    bugList.value = []
}

</script>

<style lang="postcss" scoped>

.link {
    color: #3e9dfc;
    text-decoration: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
