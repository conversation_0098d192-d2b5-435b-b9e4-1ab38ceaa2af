{
        "TABLE_CASE_LAB_DATA": {
        "CASE_LAB_DATA": [
        .[] |
        . as $item |

        # 先保存中间变量，避免多次引用可能导致的问题
        $item.labtest as $labtest |

        # 使用系统提供的自定义函数处理
        getMedDraCn($labtest) as $medDraCn |
        getMedDraEn($labtest) as $medDraEn |

        # 获取单位标准代码
        getDictCode("DLIST_CSLD_UNIT_ID"; $item.TXT_labunit_0) as $unitCode |
        getDictCode("DLIST_CSLD_ASSESSMENT"; $item.qualitativeResultType) as $CLD_ASSESS |

        # 处理CLD_DICT_ID逻辑：检查是否有version字段
        (if ($medDraCn.versionName // null) != null then $medDraCn.versionName
        elif ($medDraEn.versionName // null) != null then $medDraEn.versionName
        else "-1" end) as $dict_id |

        # 处理CODE_STATUS逻辑：基于$dict_id是否为-1
        (if $dict_id != "-1" then "1" else "-1" end) as $code_status |

        # 构建基础结构模板
        {
        "CLD_REPTD_ALT": ($medDraEn.ptName // ""),  # 从llt_name改为lltName
        "CLD_REPTD": ($item.labtestreptd // ""),
        "CLD_NAME_ALT": ($medDraEn.ptName // ""),  # 从pt_name改为ptName
        "CLD_NAME": ($medDraCn.ptName // ""),  # 从pt_name改为ptName
        "CLD_UNIT_ID": ($unitCode // "-1"),
        "CLD_LOW": ($item.labtestlow // ""),
        "CLD_HIGH": ($item.labtesthigh // ""),
        "CLD_SOC_CODE": ($medDraCn.socCode // ""),  # 从soc_code改为socCode
        "CLD_SOC": ($medDraCn.socName // ""),  # 从soc_name改为socName
        "CLD_SOC_ALT": ($medDraEn.socName // ""),  # 从soc_name改为socName
        "CLD_HLGT_CODE": ($medDraCn.hlgtCode // ""),  # 从hlgt_code改为hlgtCode
        "CLD_HLGT": ($medDraCn.hlgtName // ""),  # 从hlgt_name改为hlgtName
        "CLD_HLGT_ALT": ($medDraEn.hlgtName // ""),  # 从hlgt_name改为hlgtName
        "CLD_HLT_CODE": ($medDraCn.hltCode // ""),  # 从hlt_code改为hltCode
        "CLD_HLT": ($medDraCn.hltName // ""),  # 从hlt_name改为hltName
        "CLD_HLT_ALT": ($medDraEn.hltName // ""),  # 从hlt_name改为hltName
        "CLD_PT_CODE": ($medDraCn.ptCode // ""),  # 从pt_code改为ptCode
        "CLD_PT": ($medDraCn.ptName // ""),  # 从pt_name改为ptName
        "CLD_PT_ALT": ($medDraEn.ptName // ""),  # 从pt_name改为ptName
        "CLD_LLT": ($medDraCn.lltName // ""),  # 从llt_name改为lltName
        "CLD_LLT_ALT": ($medDraEn.lltName // ""),  # 从llt_name改为lltName
        "CLD_LLT_CODE": ($medDraCn.lltCode // ""),  # 从llt_code改为lltCode
        "CLD_LLT_CODE_ALT": ($medDraEn.lltCode // ""),  # 从llt_code改为lltCode
        "CLD_SYN_CODE": "-1",
        "CLD_SYN_CODE_ALT": "-1",
        "CLD_DICT_ID": $dict_id,
        "CLD_CODE_STATUS": $code_status,
        "CLD_CODE_STATUS_ALT": $code_status,
        "CLD_CODE_LABID": "-1",
        #"CLD_MULTI_EVENT_EXISTS": "0",
        "CLD_DATE": $item.examineDate // "",
        "CLD_SEQNUM": "-1",  # 修改为-1以支持分组后的记录
        "CLD_DELETED": "0",
        "CLD_RESULT_ALT": "",
        "CLD_RESULT": ($item.labresult // ""),
        "CLD_ASSESS": ($CLD_ASSESS // "-1"),
        "CLD_INFO_AVAILABLE": "-1",
        "CLD_COMMENTS": ($item.comments // "")
        } as $baseRecord |

        # 处理CLD_NOTES分组逻辑：将包含多个时间点的notes拆分成多个记录
        ($item.labnotes // "") as $notes |
        if $notes == "" then
        # 如果没有notes，返回基础记录
        $baseRecord + {"CLD_NOTES": ""}
        else
        # 将notes按照&#xA;分割成数组
        ($notes | split("&#xA;")) as $notesList |
        # 为每个note创建一个独立的记录
        $notesList[] | . as $singleNote |
        $baseRecord + {"CLD_NOTES": $singleNote}
        end
        ]
        }
        }