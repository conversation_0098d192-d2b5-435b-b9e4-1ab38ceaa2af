package com.kf.uitest.executor;

import com.kf.uitest.dto.execution.ExecutionGroupDTO;
import com.kf.uitest.entity.UiTestEnvironmentVariable;
import com.kf.uitest.enums.TestStatus;
import com.kf.uitest.model.TestExecutionContext;
import com.kf.uitest.model.TestExecutionStep;
import com.kf.uitest.model.TestResult;
import com.kf.uitest.service.UiTestExecutionRecordService;
import com.kf.uitest.service.UiTestExecutionService;
import com.kf.uitest.service.UiTestPlanService;
import com.kf.uitest.dto.TestExecutionRequest;
import com.kf.uitest.service.UiTestEnvironmentVariableService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class AsyncTestExecutor {

    @Resource
    private UiTestPlanService uiTestPlanService;
    
    @Resource
    private UiTestExecutionService uiTestExecutionService;
    
    @Resource
    private UiTestExecutionRecordService executionRecordService;

    @Resource
    private UiTestEnvironmentVariableService environmentVariableService;

    @Async("testExecutorThreadPool")
    public void execute(String executionId, TestExecutionRequest request) {
        log.info("Starting async test execution: {}", executionId);
        
        try {
            // 1. 更新执行状态为运行中
            executionRecordService.updateStatus(executionId, TestStatus.RUNNING);
            
            // 2. 构建执行计划
            List<ExecutionGroupDTO> executionGroups = uiTestPlanService.buildExecutionPlan(request.getTestCaseIds());
            
            // 3. 创建执行上下文
            TestExecutionContext context = createExecutionContext(executionId, request);
            
            // 4. 执行测试
            TestResult result = uiTestExecutionService.executeTestPlan(executionGroups, context);
            
            // 5. 保存执行结果
            executionRecordService.saveResult(executionId, result);
            
            log.info("Test execution completed: {}, status: {}", executionId, result.getStatus());
            
        } catch (Exception e) {
            log.error("Test execution failed: {}", executionId, e);
            // 更新执行状态为失败
            TestResult failedResult = createFailedResult(executionId, e);
            executionRecordService.saveResult(executionId, failedResult);
        }
    }

    private TestExecutionContext createExecutionContext(String executionId, TestExecutionRequest request) {
        // 获取环境变量
        List<UiTestEnvironmentVariable> envVars = environmentVariableService.findByEnvironmentId(request.getEnvironmentId());
        Map<String, Object> variables = envVars.stream()
                .collect(Collectors.toMap(
                        UiTestEnvironmentVariable::getVariableName,
                        UiTestEnvironmentVariable::getVariableValue,
                        (v1, v2) -> v2,
                        ConcurrentHashMap::new
                ));

        // 初始化循环迭代计数器
        Map<String, AtomicInteger> loopIterations = new ConcurrentHashMap<>();
        if (request.getLoopConfigs() != null) {
            request.getLoopConfigs().forEach((blockId, config) -> 
                loopIterations.put(blockId, new AtomicInteger(0)));
        }

        return TestExecutionContext.builder()
                .executionId(executionId)
                .environmentId(request.getEnvironmentId().toString())
                .browserEngine(request.getBrowserEngine())
                .executionSpeed(request.getExecutionSpeed())
                .concurrent(request.isConcurrent())
                .shareBrowser(request.isShareBrowser())
                // 用例级别控制
                .continueOnCaseFailure(request.isContinueOnCaseFailure())
                .retryOnCaseFailure(request.isRetryOnCaseFailure())
                .maxCaseRetries(request.getMaxCaseRetries())
                // 步骤级别控制
                .continueOnStepFailure(request.isContinueOnStepFailure())
                .retryOnStepFailure(request.isRetryOnStepFailure())
                .maxStepRetries(request.getMaxStepRetries())
                // 环境变量配置
                .variables(variables)
                // 循环迭代计数器
                .loopIterations(loopIterations)
                .build();
    }

    private TestResult createFailedResult(String executionId, Exception e) {
        TestResult result = new TestResult(executionId);
        result.setStatus(TestStatus.FAILED);
        result.setErrorMessage(e.getMessage());
        result.setEndTime(LocalDateTime.now());
        return result;
    }
}