package com.kf.aitest.service;

import com.kf.aitest.dto.DataComparisonRequestDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SSE集成测试
 */
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
class SseIntegrationTest {

    @Autowired
    private DataComparisonService dataComparisonService;
    
    @Autowired
    private ComparisonProgressManager progressManager;

    @Test
    void testSseConnectionCreation() {
        // 测试创建SSE连接
        String taskId = "test-task-" + System.currentTimeMillis();
        
        SseEmitter emitter = dataComparisonService.createSseConnection(taskId);
        assertNotNull(emitter, "应该成功创建SSE连接");
        
        // 验证连接统计
        String stats = progressManager.getConnectionStats();
        assertNotNull(stats);
        assertTrue(stats.contains("连接统计"), "应该包含连接统计信息");
    }

    @Test
    void testMessageCachingFlow() throws InterruptedException {
        String taskId = "cache-test-" + System.currentTimeMillis();
        
        // 准备测试数据
        DataComparisonRequestDTO request = new DataComparisonRequestDTO();
        request.setIds(Arrays.asList("cache1", "cache2"));
        request.setConcurrentLimit(1);
        request.setTimeoutSeconds(5);
        request.setEnableAiEvaluation(false);
        
        // 启动任务（这会初始化连接并开始缓存消息）
        dataComparisonService.startComparison(taskId, request);
        
        // 等待一些消息被缓存
        Thread.sleep(500);
        
        // 验证连接状态
        String statsBeforeConnection = progressManager.getConnectionStats();
        assertTrue(statsBeforeConnection.contains("等待:") || statsBeforeConnection.contains("缓存消息:"), 
                  "应该有等待状态的连接或缓存消息");
        
        // 创建SSE连接（这应该会发送缓存的消息）
        SseEmitter emitter = dataComparisonService.createSseConnection(taskId);
        assertNotNull(emitter, "应该能够创建SSE连接");
        
        // 等待消息发送完成
        Thread.sleep(200);
        
        // 验证连接状态变为已连接
        String statsAfterConnection = progressManager.getConnectionStats();
        assertTrue(statsAfterConnection.contains("已连接:"), "应该有已连接状态的连接");
    }

    @Test
    void testMultipleTasksHandling() {
        String taskId1 = "multi-test-1-" + System.currentTimeMillis();
        String taskId2 = "multi-test-2-" + System.currentTimeMillis();
        
        // 创建多个任务的连接
        SseEmitter emitter1 = dataComparisonService.createSseConnection(taskId1);
        SseEmitter emitter2 = dataComparisonService.createSseConnection(taskId2);
        
        assertNotNull(emitter1, "第一个连接应该成功创建");
        assertNotNull(emitter2, "第二个连接应该成功创建");
        assertNotEquals(emitter1, emitter2, "两个连接应该是不同的对象");
        
        // 验证连接统计
        String stats = progressManager.getConnectionStats();
        assertTrue(stats.contains("已连接:2") || stats.contains("总数:2"), 
                  "应该显示有两个连接");
    }

    @Test
    void testConnectionStatsAccuracy() {
        String initialStats = progressManager.getConnectionStats();
        assertNotNull(initialStats);
        
        // 记录初始连接数
        String taskId = "stats-test-" + System.currentTimeMillis();
        
        // 创建连接
        SseEmitter emitter = dataComparisonService.createSseConnection(taskId);
        assertNotNull(emitter);
        
        // 验证统计信息更新
        String updatedStats = progressManager.getConnectionStats();
        assertTrue(updatedStats.contains("已连接:"), "应该显示已连接的连接");
        assertTrue(updatedStats.contains("总数:"), "应该显示总连接数");
    }

    @Test
    void testErrorHandling() {
        String taskId = "error-test-" + System.currentTimeMillis();
        
        // 发送错误
        progressManager.sendError(taskId, "测试错误消息");
        
        // 验证错误处理不会导致异常
        String stats = progressManager.getConnectionStats();
        assertNotNull(stats, "错误处理后统计信息应该仍然可用");
    }
}
