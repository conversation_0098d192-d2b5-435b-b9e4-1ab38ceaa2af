<template>
  <div class="api-test">
    <el-card header="AI测试API接口测试">
      <el-tabs v-model="activeTab">
        <!-- 启动测试 -->
        <el-tab-pane label="启动测试" name="start">
          <el-form :model="startForm" label-width="120px">
            <el-form-item label="测试ID列表">
              <el-input
                v-model="startForm.idsText"
                type="textarea"
                :rows="3"
                placeholder="输入ID列表，每行一个ID"
              />
            </el-form-item>
            <el-form-item label="启用AI评估">
              <el-switch v-model="startForm.enableAiEvaluation" />
            </el-form-item>
            <el-form-item label="禁用数据分片">
              <el-switch v-model="startForm.disableChunking" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleStartTest">启动测试</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- SSE连接测试 -->
        <el-tab-pane label="SSE连接测试" name="sse">
          <el-form label-width="120px">
            <el-form-item label="任务ID">
              <el-input v-model="sseForm.taskId" placeholder="输入任务ID" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleConnectSSE">连接</el-button>
              <el-button type="danger" @click="handleDisconnectSSE">断开</el-button>
            </el-form-item>
          </el-form>
          
          <div class="connection-status">
            <el-tag :type="sseStatusType">{{ sseStatus }}</el-tag>
          </div>
          
          <div class="events-container">
            <h3>接收到的事件</h3>
            <el-scrollbar height="300px">
              <div v-for="(event, index) in sseEvents" :key="index" class="event-item">
                <div class="event-header">
                  <span class="event-type" :class="'event-type--' + event.type">{{ event.type }}</span>
                  <span class="event-time">{{ formatTime(event.timestamp) }}</span>
                </div>
                <div class="event-data">
                  <pre>{{ JSON.stringify(event.data, null, 2) }}</pre>
                </div>
              </div>
            </el-scrollbar>
          </div>
        </el-tab-pane>

        <!-- 配置测试 -->
        <el-tab-pane label="配置测试" name="config">
          <el-button type="primary" @click="handleGetConfig">获取配置</el-button>
          <el-button type="success" @click="handleCheckHealth">健康检查</el-button>
          
          <div v-if="apiResponse" class="response-container">
            <h3>API响应</h3>
            <pre>{{ JSON.stringify(apiResponse, null, 2) }}</pre>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElCard, ElTabs, ElTabPane, ElForm, ElFormItem, ElInput, ElButton, ElSwitch, ElTag, ElScrollbar, ElMessage } from 'element-plus'
// import {
//     startAITest,
//     getAITestConfig,
//     checkAITestHealth,
//     createAITestSSEConnection,
//     type AITestSSEConnection,
//     type SSEEventData
// } from '@/api/ai-test'

// 临时类型定义，避免导入错误
interface AITestSSEConnection {
  close: () => void
}

interface SSEEventData {
  type: string
  data: any
  timestamp?: Date
}

// 临时函数定义，避免导入错误
const startAITest = async (params: any): Promise<{ data: { isSuccess: boolean, data: { taskId: string }, message?: string } }> => {
    console.log('Mock startAITest called with:', params)
    return { data: { isSuccess: true, data: { taskId: 'mock-task-id' } } }
}

const getAITestConfig = async (): Promise<{ data: { isSuccess: boolean, data: any } }> => {
    console.log('Mock getAITestConfig called')
    return { data: { isSuccess: true, data: {} } }
}

const checkAITestHealth = async (): Promise<{ data: { isSuccess: boolean } }> => {
    console.log('Mock checkAITestHealth called')
    return { data: { isSuccess: true } }
}

const createAITestSSEConnection = async (taskId: string, callbacks: any): Promise<AITestSSEConnection> => {
    console.log('Mock createAITestSSEConnection called with:', taskId)
    return {
        close: () => {
            console.log('Mock SSE connection closed')
            callbacks.onClose()
        }
    }
}

// 当前激活的标签页
const activeTab = ref('start')

// 启动测试表单
const startForm = ref({
    idsText: '',
    enableAiEvaluation: true,
    disableChunking: false
})

// SSE连接表单
const sseForm = ref({
    taskId: ''
})

// SSE连接状态
const sseConnection = ref<AITestSSEConnection | null>(null)
const sseEvents = ref<SSEEventData[]>([])
const sseStatus = ref('未连接')

// API响应
const apiResponse = ref<any>(null)

// SSE状态类型
const sseStatusType = computed(() => {
    switch (sseStatus.value) {
        case '已连接':
            return 'success'
        case '连接中...':
            return 'warning'
        case '连接错误':
            return 'danger'
        default:
            return 'info'
    }
})

// 处理启动测试
const handleStartTest = async () => {
    try {
    // 解析ID列表
        const ids = startForm.value.idsText
            .split('\n')
            .map(id => id.trim())
            .filter(id => id)
    
        if (ids.length === 0) {
            ElMessage.warning('请至少输入一个ID')
            return
        }
    
        // 构建请求参数
        const params = {
            ids,
            enableAiEvaluation: startForm.value.enableAiEvaluation,
            disableChunking: startForm.value.disableChunking
        }
    
        // 发送请求
        const response = await startAITest(params)
        apiResponse.value = response.data
    
        // 如果成功，自动填充SSE连接的任务ID
        if (response.data.isSuccess && response.data.data.taskId) {
            sseForm.value.taskId = response.data.data.taskId
            ElMessage.success(`测试启动成功，任务ID: ${response.data.data.taskId}`)
            activeTab.value = 'sse'
        }
    } catch (error) {
        console.error('启动测试失败:', error)
        ElMessage.error('启动测试失败')
    }
}

// 处理SSE连接
const handleConnectSSE = async () => {
    if (!sseForm.value.taskId) {
        ElMessage.warning('请输入任务ID')
        return
    }

    try {
        sseStatus.value = '连接中...'
        
        const callbacks = {
            onProgress: (data: SSEEventData) => {
                console.log('Progress:', data)
                sseEvents.value.unshift({
                    ...data,
                    timestamp: new Date()
                })
            },
            onAiEvaluation: (data: SSEEventData) => {
                console.log('AI Evaluation:', data)
                sseEvents.value.unshift({
                    ...data,
                    timestamp: new Date()
                })
            },
            onAiResult: (data: SSEEventData) => {
                console.log('AI Result:', data)
                sseEvents.value.unshift({
                    ...data,
                    timestamp: new Date()
                })
            },
            onStageComplete: (data: SSEEventData) => {
                console.log('Stage Complete:', data)
                sseEvents.value.unshift({
                    ...data,
                    timestamp: new Date()
                })
            },
            onError: (error: string) => {
                console.error('SSE Error:', error)
                sseStatus.value = '连接错误'
                ElMessage.error(`SSE连接错误: ${error}`)
            },
            onClose: () => {
                console.log('SSE Connection closed')
                sseStatus.value = '连接已关闭'
                sseConnection.value = null
            }
        }

        const connection = await createAITestSSEConnection(sseForm.value.taskId, callbacks)
        sseConnection.value = connection
        sseStatus.value = '已连接'
        ElMessage.success('SSE连接建立成功')
    } catch (error) {
        console.error('SSE连接失败:', error)
        sseStatus.value = '连接错误'
        ElMessage.error('SSE连接失败')
    }
}

// 处理SSE断开
const handleDisconnectSSE = () => {
    if (sseConnection.value) {
        sseConnection.value.close()
        sseConnection.value = null
        sseStatus.value = '未连接'
        ElMessage.info('SSE连接已断开')
    }
}

// 处理获取配置
const handleGetConfig = async () => {
    try {
        const response = await getAITestConfig()
        apiResponse.value = response.data
        ElMessage.success('获取配置成功')
    } catch (error) {
        console.error('获取配置失败:', error)
        ElMessage.error('获取配置失败')
    }
}

// 处理健康检查
const handleCheckHealth = async () => {
    try {
        const response = await checkAITestHealth()
        apiResponse.value = response.data
        ElMessage.success('健康检查完成')
    } catch (error) {
        console.error('健康检查失败:', error)
        ElMessage.error('健康检查失败')
    }
}

// 格式化时间
const formatTime = (date: Date) => {
    return date.toLocaleTimeString()
}
</script>

<style scoped>
.api-test {
  padding: 20px;
}

.connection-status {
  margin: 20px 0;
}

.events-container {
  margin-top: 20px;
}

.event-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 10px;
  padding: 10px;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.event-type {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.event-type--progress {
  background-color: #e1f3d8;
  color: #67c23a;
}

.event-type--ai-evaluation {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.event-type--ai-result {
  background-color: #f0f9ff;
  color: #409eff;
}

.event-type--stage-complete {
  background-color: #f0f9ff;
  color: #409eff;
}

.event-time {
  font-size: 12px;
  color: #909399;
}

.event-data {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
}

.response-container {
  margin-top: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.response-container pre {
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  font-size: 12px;
  max-height: 400px;
  overflow-y: auto;
}
</style>
