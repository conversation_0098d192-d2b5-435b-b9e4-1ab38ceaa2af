package com.kf.uitest.service;

import com.kf.uitest.entity.UiTestBlock;

import java.util.List;

public interface UiTestBlockService {
    /**
     * 根据用例ID查找所有块
     */
    List<UiTestBlock> findByCaseId(String caseId);
    
    /**
     * 根据ID获取块
     */
    UiTestBlock getById(String blockId);
    
    /**
     * 创建块
     */
    UiTestBlock create(UiTestBlock block);
    
    /**
     * 更新块
     */
    boolean update(UiTestBlock block);
    
    /**
     * 删除块
     */
    boolean delete(String blockId);
    
    /**
     * 获取块的所有子块
     */
    List<UiTestBlock> findChildBlocks(String blockId);
    
    /**
     * 调整块顺序
     */
    boolean updateBlockOrder(String blockId, Integer newOrder);
} 