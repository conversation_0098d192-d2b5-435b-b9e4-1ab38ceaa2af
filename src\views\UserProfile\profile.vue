<template>
    <div>
        <el-row :gutter="20">
            <el-col>
                <el-card>
                    <el-tabs v-model="firstName">
                        <el-tab-pane label="设置" name="activity">
                            <UserActivity />
                        </el-tab-pane>
                        <el-tab-pane label="账号" name="account">
                            <UserAccount />
                        </el-tab-pane>
                    </el-tabs>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script setup lang='ts'>
import { onMounted, ref, watch } from 'vue'
import UserActivity from '@/components/UserConfig/UserActivity.vue'
import UserAccount from '@/components/UserConfig/UserAccount.vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const firstName = ref('activity')

onMounted(() => {
    if (route.query.from === 'jiraLogin') {
        firstName.value = 'account'
    }
})
</script>

<style scoped>

</style>
