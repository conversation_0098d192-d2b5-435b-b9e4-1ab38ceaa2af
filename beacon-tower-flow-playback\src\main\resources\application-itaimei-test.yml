server:
  port: 8001
  tomcat:
    max-swallow-size: -1
kf:
  git:
    local:
      directory: /data/work/branches
  maven:
    settings: /opt/apache-maven-3.8.7/conf/settings.xml
  pa:
    interface: http://itaimei.test.taimei.com/seigneur/InterfaceDetailsList/
spring:
  application:
    name: beacon-tower-accurate-test
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
  datasource:
    #   数据源基本配置
    username: fsuser
    password: TM@fs.456
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      validation-query: SELECT 1
  rabbitmq:
    queue: kf_analyze_queue
    listener:
      simple:
        retry:
          max-attempts: 1
    username: fs
    password: FS_adminuser@123
    host: test-mq-001.taimei.com
    port: 5672
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml


