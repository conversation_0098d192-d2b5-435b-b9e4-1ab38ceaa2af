## 简介

测试平台前端项目
## 前序准备

<p align="center">
    <a href="https://github.com/vuejs/vue-next">
        <img src="https://img.shields.io/badge/vue3-3.2.31-brightgreen.svg" alt="vue">
    </a>
    <a href="https://github.com/element-plus/element-plus">
        <img src="https://img.shields.io/badge/elementPlus-2.0.2-brightgreen.svg" alt="element-plus">
    </a>
    <a href="https://github.com/vitejs/vite">
        <img src="https://img.shields.io/badge/vite-2.8.4-brightgreen.svg" alt="vite">
    </a>
    <a href="https://github.com/microsoft/TypeScript">
        <img src="https://img.shields.io/badge/typescript-4.5.5-brightgreen.svg" alt="typescript">
    </a>
    <a href="https://github.com/postcss/postcss">
        <img src="https://img.shields.io/badge/postcss-8.4.6-brightgreen.svg" alt="postcss">
    </a>
</p>

你需要在本地安装 [node](http://nodejs.org/) 和 [git](https://git-scm.com/)。本项目技术栈基于 [ES2015+](http://es6.ruanyifeng.com/)、[vue-next](https://github.com/vuejs/vue-next)、[typescript](https://github.com/microsoft/TypeScript)、[vite](https://github.com/vitejs/vite)、[postcss](https://github.com/postcss/postcss) 和 [element-plus](https://github.com/element-plus/element-plus)

## 开发

```bash
# 克隆项目

# 进入项目目录

# 安装依赖
npm install

# 启动服务
npm run dev
```

浏览器访问 http://localhost:3002

## 发布

```bash
# 发布
npm run build

# 预览
npm run preview
```

## 其它

```bash
# eslint代码校验
npm run eslint

# stylelint代码校验
npm run stylelint
```
