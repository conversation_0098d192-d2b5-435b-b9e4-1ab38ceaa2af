package com.kf.aitest.config;

import com.langfuse.client.LangfuseClient;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "langfuse")
public class LangfuseConfig {
    
    private String publicKey;
    private String secretKey;
    private String host;
    
    @Bean
    public LangfuseClient langfuseClient() {
        return LangfuseClient.builder()
                .url(host)
                .credentials(publicKey, secretKey)
                .build();
    }
    
    // Getters and Setters
    public String getPublicKey() {
        return publicKey;
    }
    
    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }
    
    public String getSecretKey() {
        return secretKey;
    }
    
    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }
    
    public String getHost() {
        return host;
    }
    
    public void setHost(String host) {
        this.host = host;
    }
} 