package com.kf.baosi.listener;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.kf.baosi.entity.TVerifyFile;
import com.kf.baosi.entity.TVerifyFileAssociates;
import com.kf.baosi.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class RabbitMQListener {

    @RabbitListener(queues = "seigneur_local_word_template_plus_queue", containerFactory = "testMq001Factory")
    public void listenTestQueue(String message) {
        try {
            log.info("收到模板替换服务的队列消息:{}", message);
            // 数据结构：{"message":"处理成功","taskId":"xxx","status":2,"fileId":"xxx"}
            String status = JsonUtil.getFieldFromJson(message, "status");
            String taskId = JsonUtil.getFieldFromJson(message, "taskId");
            String fileId = JsonUtil.getFieldFromJson(message, "fileId");
            String msg = JsonUtil.getFieldFromJson(message, "message");
            //先查询TVerifyFileAssociates表，如果没有找到对应的taskId，就操作TVerifyFile表
            TVerifyFileAssociates tVerifyFileAssociates = new TVerifyFileAssociates().selectOne(new QueryWrapper<TVerifyFileAssociates>().eq("task_id", taskId));
            // 如果没有查询到数据就说明不是我要的数据，直接返回不处理
            if (ObjectUtil.isEmpty(tVerifyFileAssociates)) {
                return;
            }
            // 打印jsonObject
            log.info("需要处理的消息:{}", message);

            // 先入库
            tVerifyFileAssociates.setFileId(fileId);
            tVerifyFileAssociates.setUpdateTime(new Date());
            tVerifyFileAssociates.updateById();
            // 如果是合并数据，合并数据就不管是否是最后一个文档的字段标识，直接将数据入主表
            if (tVerifyFileAssociates.getMergeTag() == 1) {
                QueryWrapper<TVerifyFile> verifyFileInfo = new QueryWrapper<>();
                verifyFileInfo.eq("id", tVerifyFileAssociates.getTVerifyFileId());
                TVerifyFile tVerifyFile = new TVerifyFile().selectOne(verifyFileInfo);
                // 3是失败，记录错误信息
                if (Objects.equals(status, "3")) {
                    tVerifyFile.setErrorMsg(msg);
                }
                tVerifyFile.setFileId(fileId);
                tVerifyFile.setUpdateTime(new Date());
                tVerifyFile.setIsComplete(Objects.equals(status, "2") ? 1 : 2);
                tVerifyFile.updateById();
                return;
            }

            // 查询数据的标签是不是最后一个 0是最后一个 1说明还有数据
            if (tVerifyFileAssociates.getCompleteTag() == 1) {
                return;
            }
            // 如果是最后一个数据就准备合并
            if (tVerifyFileAssociates.getCompleteTag() == 0) {
                // 查询TVerifyFileAssociates表的taskId的所有数据
                QueryWrapper<TVerifyFileAssociates> queryWrapper = new QueryWrapper<>();
                // 按照创建时间
                queryWrapper.orderByAsc("create_time");
                queryWrapper.eq("t_verify_file_id", tVerifyFileAssociates.getTVerifyFileId());
                // 非合并标签
                queryWrapper.eq("merge_tag", 0);
                List<TVerifyFileAssociates> tVerifyFileAssociatesList = new TVerifyFileAssociates().selectList(queryWrapper);
                // 如果只有一个数据，就说明没有合并的必要，直接将数据入主表
                if (tVerifyFileAssociatesList.size() == 1) {
                    QueryWrapper<TVerifyFile> verifyFileInfo = new QueryWrapper<>();
                    verifyFileInfo.eq("id", tVerifyFileAssociatesList.get(0).getTVerifyFileId());
                    TVerifyFile tVerifyFile = new TVerifyFile().selectOne(verifyFileInfo);
                    // 3是失败，记录错误信息
                    if (Objects.equals(status, "3")) {
                        tVerifyFile.setErrorMsg(msg);
                    }
                    tVerifyFile.setFileId(fileId);
                    tVerifyFile.setUpdateTime(new Date());
                    tVerifyFile.setIsComplete(Objects.equals(status, "2") ? 1 : 2);
                    tVerifyFile.updateById();
                    return;
                }
                // 有多个数据的情况：将所有filId存入主表中，标记为完成
                List<String> fileIdList = new LinkedList<>();
                for (TVerifyFileAssociates tVerifyFileAss : tVerifyFileAssociatesList) {
                    fileIdList.add(tVerifyFileAss.getFileId());
                }
                String fileIds = String.join(",", fileIdList);
                QueryWrapper<TVerifyFile> verifyFileInfo = new QueryWrapper<>();
                verifyFileInfo.eq("id", tVerifyFileAssociatesList.get(0).getTVerifyFileId());
                TVerifyFile tVerifyFile = new TVerifyFile().selectOne(verifyFileInfo);
                // 3是失败，记录错误信息
                if (Objects.equals(status, "3")) {
                    tVerifyFile.setErrorMsg(msg);
                }
                tVerifyFile.setFileId(fileIds);
                tVerifyFile.setUpdateTime(new Date());
                tVerifyFile.setIsComplete(Objects.equals(status, "2") ? 1 : 2);
                tVerifyFile.updateById();
            }

        } catch (Exception e) {
            log.error("处理消息异常", e);
        }
    }
}
