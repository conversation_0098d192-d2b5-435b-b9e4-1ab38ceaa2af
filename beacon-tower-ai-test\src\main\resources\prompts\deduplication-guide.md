# 阶段独立评估指导

## 📋 总体原则

### **阶段独立评估的核心原则**
1. **阶段独立评分**：每个阶段的评分独立计算，不受其他阶段影响
2. **阶段内去重**：同一问题在单个阶段内只扣分一次，按最严重类型处理
3. **优先级明确**：当问题符合多个标准时，按预定义优先级处理
4. **明确去重说明**：在扣分汇总中明确说明阶段内去重处理情况
5. **系统汇总**：最终评分由系统汇总用户选择的所有阶段评分

## 🔄 各阶段评估范围

### **Recognize阶段（识别）**
**专属范围**：
- 原始数据识别准确性
- 文本内容识别差异
- 基础数据完整性

**不评估**：
- 数据结构问题（由extraction负责）
- 字段类型问题（由structured负责）
- 转换质量问题（由transformer负责）

### **Extraction阶段（提取）**
**专属范围**：
- 字段提取完整性
- 数据结构层次
- 字段映射准确性

**不评估**：
- 字段内容值的准确性（由structured负责）
- 数据转换质量（由transformer负责）

### **Structured阶段（结构化）**
**专属范围**：
- 字段内容值准确性
- 数据类型一致性
- 格式规范性

**内部优先级**：
1. 数据缺失（最高优先级）
2. 类型错误
3. 内容差异
4. 格式问题（最低优先级）

**不评估**：
- 转换逻辑问题（由transformer负责）

### **Transformer阶段（转换）**
**专属范围**：
- 数据转换逻辑
- 相似度评估
- 转换质量标准

**不评估**：
- 前序阶段已发现的问题
- 基础数据提取问题

## ⚖️ 重复扣分处理规则

### **同一阶段内重复**
**场景1：字段同时有多种问题**
```
示例：字段A既有类型错误又有格式问题
处理：只按类型错误扣分（优先级更高）
说明：在扣分汇总中注明"字段A同时存在格式问题，已合并到类型错误中处理"
```

**场景2：问题分类重叠**
```
示例：字段缺失可能同时触发"缺失字段"和"结构差异"
处理：只在"缺失字段"中扣分
说明：在扣分汇总中注明去重处理
```

### **阶段独立评估原则**
**重要说明：各阶段评分独立计算，不进行跨阶段去重**
```
原则：每个阶段独立评估其专属范围内的问题
处理：各阶段可能对同一数据的不同层面进行评估和扣分
说明：最终评分由系统汇总用户选择的所有阶段评分
```

**示例：同一字段在不同阶段的评估**
```
extraction阶段：评估字段提取完整性，发现缺失扣10分
structured阶段：评估字段内容准确性，发现类型错误扣6分
transformer阶段：评估转换质量，发现相似度低扣5分
最终汇总：10 + 6 + 5 = 21分扣分（如果用户选择了所有三个阶段）
```

## 📊 扣分汇总标准格式

### **必须包含的去重信息**
```markdown
## 扣分汇总
**重复扣分检查：[检查类型说明]**
- 总扣分项数: [数量]
- 主要扣分原因: [列出主要原因]
- 扣分合计: [总扣分数]
- **去重处理**: [具体说明哪些问题被合并处理]
- **优先级应用**: [说明应用的优先级规则]
- **阶段去重**: [说明是否有跨阶段重复问题的处理]
```

## 🎯 具体操作指南

### **AI评估时的检查清单**

#### **1. 同一阶段内检查**
- [ ] 同一字段是否在多个问题类型中出现？
- [ ] 如果是，按优先级只保留最严重的问题
- [ ] 在扣分汇总中说明合并处理情况

#### **2. 跨阶段检查**
- [ ] 当前问题是否在前序阶段已被发现？
- [ ] 如果是，标注"前序阶段已评估"，不重复扣分
- [ ] 如果是新层面的问题，明确说明差异

#### **3. 扣分汇总检查**
- [ ] 总扣分数是否等于各项扣分之和？
- [ ] 是否有重复计算的情况？
- [ ] 去重说明是否清晰明确？

## 🔍 常见重复扣分场景及处理

### **场景1：字段内容格式问题**
```
错误做法：
- 字段内容差异：扣5分
- 格式规范问题：扣3分
- 总计：8分

正确做法：
- 字段内容差异（包含格式问题）：扣5分
- 去重说明：格式问题已合并到内容差异中
- 总计：5分
```

### **场景2：字段缺失问题**
```
错误做法：
- extraction阶段缺失字段：扣10分
- structured阶段数据缺失：扣8分
- 总计：18分

正确做法：
- extraction阶段缺失字段：扣10分
- structured阶段：标注"该字段缺失已在extraction阶段评估"
- 总计：10分
```

### **场景3：类型与格式问题**
```
错误做法：
- 数据类型差异：扣6分
- 格式规范问题：扣4分
- 总计：10分

正确做法：
- 数据类型差异（根本原因）：扣6分
- 去重说明：格式问题由类型错误导致，已合并处理
- 总计：6分
```

## 📝 实施要点

1. **明确责任边界**：每个阶段只负责其专属范围的评估
2. **建立优先级**：预定义问题类型的严重程度排序
3. **强制去重检查**：每个阶段都必须进行去重检查
4. **透明化处理**：所有去重决策都要在报告中明确说明
5. **一致性保证**：所有AI评估都遵循相同的去重标准

---

**更新时间**: 2025-07-25  
**版本**: v1.0 - 去重指导初版
