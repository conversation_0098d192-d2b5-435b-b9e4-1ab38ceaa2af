package com.kf.userservice.service;

import com.kf.userservice.entity.TMenu;
import com.kf.userservice.entity.router.MenuTree;

import java.util.List;

public interface MenuService {
    /**
     * 通过用户ID查询所有菜单
     *
     * @param userId 用户ID
     * @return 实例对象
     */
    List<MenuTree> queryMenuByUserId(String userId);

    /**
     * 通过ID查询单条数据
     *
     * @param menuId 主键
     * @return 实例对象
     */
    TMenu queryById(Long menuId);

    /**
     * 新增数据
     *
     * @param tMenu 实例对象
     * @return 实例对象
     */
    TMenu insert(TMenu tMenu);

    /**
     * 修改数据
     *
     * @param tMenu 实例对象
     * @return 实例对象
     */
    TMenu update(TMenu tMenu);

    /**
     * 通过主键删除数据
     *
     * @param menuId 主键
     * @return 是否成功
     */
    boolean deleteById(Long menuId);

}
