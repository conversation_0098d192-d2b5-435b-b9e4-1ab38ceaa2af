package com.kf.baosi.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kf.baosi.dao.TXMindMapper;
import com.kf.baosi.dto.VerifyDocumentListDTO;
import com.kf.baosi.dto.XMindToExcelListDTO;
import com.kf.baosi.entity.TFile;
import com.kf.baosi.entity.TXMind;
import com.kf.baosi.service.XMindService;
import com.kf.baosi.utils.ReadXml;
import com.kf.baosi.utils.WriteToExcel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class XMindServiceImpl implements XMindService {

    @Value("${file.upload.dir}")
    private String uploadFilePath;

    @Resource
    TXMindMapper xMindToCsvDAO;

    @Override
    public IPage<XMindToExcelListDTO> getXMindList(String userId, String fileName, String isComplete, int current, int size) {
        //设置分页参数，框架会自动处理分页
        Page<VerifyDocumentListDTO> page = new Page<>(current, size);
        return xMindToCsvDAO.getFileList(userId, fileName, isComplete, page);
    }

    @Override
    public void xMindToExcelFileUpload(MultipartFile file, String userId) {
        String simpleUUID = IdUtil.simpleUUID();
        String folderPath = uploadFilePath + File.separator + simpleUUID;
        //创建文件夹
        File fileDir = new File(folderPath);
        if (!fileDir.exists()) {
            fileDir.mkdirs();
        }
        //根据file获得这个文件的文件名
        String fileName = file.getOriginalFilename();
        //获得文件后缀名
        String prefix = fileName.substring(fileName.lastIndexOf("."));
        //将file后缀名改为zip
        String zipFileName = fileName.replace(prefix, ".zip");
        //将xmind文件转存为zip文件
        File srcZipFile = new File(folderPath, zipFileName);
        try {
            FileUtil.writeFromStream(file.getInputStream(), srcZipFile);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        ZipUtil.unzip(folderPath + File.separator + zipFileName, folderPath);
        // 读取Xml文件，获取所有用例集合
        List<List<String>> allCaseList = ReadXml.read(folderPath);
        HSSFWorkbook hssfWorkbook = WriteToExcel.getHSSFWorkbook(allCaseList);
        try {
            String filePrefix = ".csv";
            String fileNameL = fileName.substring(0, fileName.lastIndexOf(".")) + filePrefix;
            //文件全路径
            String originalFilename = folderPath + filePrefix;
            // 生成csv文件
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(Files.newOutputStream(Paths.get(originalFilename)), StandardCharsets.UTF_8));
            writer.write('\uFEFF'); // 写入BOM，用于标识文本的编码格式
            //将HSSFWorkbook中的内容写入CSV文件中
            for (int i = 0; i < hssfWorkbook.getNumberOfSheets(); i++) {
                HSSFSheet sheet = hssfWorkbook.getSheetAt(i);
                for (Row row : sheet) {
                    StringBuilder sb = new StringBuilder();
                    for (Cell cell : row) {
                        CellType cellType = cell.getCellType();
                        switch (cellType) {
                            case STRING -> {
                                String cellValue = cell.getStringCellValue().replaceAll("\"", "\"\"");
                                if (cellValue.contains(",") || cellValue.contains("\"") || cellValue.contains("\n")) {
                                    sb.append("\"").append(cellValue).append("\"");
                                } else {
                                    sb.append(cellValue);
                                }
                            }
                            case NUMERIC -> sb.append(cell.getNumericCellValue());
                            case BOOLEAN -> sb.append(cell.getBooleanCellValue());
                            case FORMULA -> sb.append(cell.getCellFormula());
                            case ERROR -> sb.append(cell.getErrorCellValue());
                        }
                        if (cell.getColumnIndex() != row.getLastCellNum() - 1) {
                            sb.append(",");
                        }
                    }
                    writer.write(sb.toString());
                    if (row.getRowNum() != sheet.getLastRowNum()) {
                        writer.newLine();
                    }
                }
            }

            writer.close();
            TFile tFile = new TFile();
            tFile.setId(simpleUUID);
            tFile.setUserId(userId);
            tFile.setFileName(fileNameL);
            tFile.setFilePath(originalFilename);
            File fileTempObj = new File(originalFilename);
            tFile.setFileSize(FileUtil.size(fileTempObj));
            tFile.setFileMd5(SecureUtil.md5(fileTempObj));
            tFile.setFileSuffix(filePrefix);
            tFile.setCreateTime(new Date());
            tFile.setUpdateTime(new Date());
            tFile.insert();
            TXMind xMindToCSV = new TXMind();
            xMindToCSV.setUserId(userId);
            xMindToCSV.setFileId(tFile.getId());
            xMindToCSV.setCreateTime(new Date());
            xMindToCSV.setUpdateTime(new Date());
            xMindToCSV.setIsComplete(0);
            xMindToCSV.insert();
            log.info("xMindToCSV成功！路径：{}", originalFilename);
        } catch (Exception e) {
            log.error("xMindToCSV失败！原因：{}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }

    }

    /**
     * 通过文件ID删除数据
     *
     * @param fileId 文件ID
     */
    @Override
    public void deleteFileByFileId(String fileId) {
        UpdateWrapper<TXMind> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("file_id", fileId).set("status", 1);
        xMindToCsvDAO.update(null,updateWrapper);
    }
}
