package com.kf.baosi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_verify_file_template")
public class TVerifyFileTemplate extends Model<TVerifyFileTemplate> {

    @Serial
    private static final long serialVersionUID = 1L;

    // 主键
    @TableId(type = IdType.AUTO)
    private Integer id;

    // 文档类型
    private String verifyFileType;

    // 文件id fsId
    private String fileId;

    // 创建时间
    private Date createTime;

    // 修改时间
    private Date updateTime;

    // 状态 0正常 1禁用
    private Integer status;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
