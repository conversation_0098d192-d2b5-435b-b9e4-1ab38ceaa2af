package com.kf.accuratetest.dto;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "接口与页面关系")
public class InterfacePageDTO {
    /**
     * ID
     */
    @Schema(description = "ID")
    int id;
    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    String projectName;
    /**
     * 接口名称
     */
    @Schema(description = "接口名称")
    String interfaceName;
    /**
     * 页面路径
     */
    @Schema(description = "页面路径")
    String pageUrl;
    /**
     * 创建时间
     */
    String CreateTime;
    /**
     * 更新时间
     */
    String UpdateTime;
    /**
     * 状态
     */
    @Schema(description = "状态")
    int status;
    /**
     * 当前页
     */
    @Schema(description = "当前页")
    int current;
    /**
     * 每页显示条数
     */
    @Schema(description = "每页显示条数")
    int size;

}
