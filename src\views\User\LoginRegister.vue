<template>
    <div class="user">
        <div class="user_options-container">
            <div class="user_options-text">
                <div class="user_options-unregistered">
                    <h2 class="user_unregistered-title">没有账号？</h2>
                    <p class="user_unregistered-text">点击按钮注册</p>
                    <button class="user_unregistered-signup" id="signup-button" @click="signupPage">注册</button>
                </div>
                <div class="user_options-registered">
                    <h2 class="user_registered-title">已有账号？</h2>
                    <p class="user_registered-text">点击按钮登录</p>
                    <button class="user_registered-login" id="login-button" @click="loginPage">登录</button>
                </div>
            </div>
            <div class="user_options-forms" id="user_options-forms"
                :class="{ 'bounceLeft': bounceLeft, 'bounceRight': bounceRight }">
                <div v-if="!showForgotPassword" class="user_forms-login">
                    <h2 class="forms_title">登录</h2>
                    <el-form ref="loginForm" :model="loginUserInfo" :rules="loginRules" style="margin-top:105px">
                        <el-form-item prop="userName">
                            <el-input v-model="loginUserInfo.userName" placeholder="账号"></el-input>
                        </el-form-item>
                        <el-form-item prop="passWord">
                            <el-input v-model="loginUserInfo.passWord" type="password" placeholder="密码"
                                @keydown.enter="loginSubmit"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" class="forms_buttons-forgot"
                                @click="showForgotPassword = true">忘记密码?</el-button>
                            <el-button @click="loginSubmit" type="primary" class="forms_buttons-action">登录</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                <div v-if="showForgotPassword" class="user_forms-login">
                    <h2 class="forms_title">重置密码</h2>
                    <div v-if="!showNext">
                        <el-form ref="forgotPasswordForm" :model="forgotPasswordInfo" :rules="forgotPasswordRules"
                            style="margin-top:105px">
                            <el-form-item prop="email">
                                <el-input v-model="forgotPasswordInfo.email" placeholder="邮箱"></el-input>
                            </el-form-item>
                            <el-form-item prop="code">
                                <el-input v-model="forgotPasswordInfo.code" placeholder="验证码" @keydown.enter="next">
                                    <template #append>
                                        <el-button @click="forgotPassWordCodeSubmit" type="primary"
                                            class="forms_input_buttons-action" style="display:inline">获取验证码</el-button>
                                    </template>
                                </el-input>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" class="forms_buttons-forgot"
                                    @click="showForgotPassword = false">←去登录</el-button>
                                <el-button type="primary" class="forms_buttons-action" @click="next">下一步</el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                    <div v-if="showNext">
                        <el-form ref="resetPasswordForm" :model="resetPasswordInfo" :rules="resetPasswordRules"
                            style="margin-top:105px">
                            <div class="simple_div_userName">用户名：{{ resetPasswordInfo.userName }}</div>
                            <el-form-item prop="passWord">
                                <el-input v-model="resetPasswordInfo.passWord" type="password"
                                    placeholder="设置新密码"></el-input>
                            </el-form-item>
                            <el-form-item prop="passWord2">
                                <el-input v-model="resetPasswordInfo.passWord2" type="password" placeholder="确认密码"
                                    @keydown.enter="resetPasswordSubmit">
                                </el-input>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" class="forms_buttons-forgot"
                                    @click="toLoginSubmit">←去登录</el-button>
                                <el-button type="primary" class="forms_buttons-action"
                                    @click="resetPasswordSubmit">确定</el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                </div>
                <div class="user_forms-signup">
                    <h2 class="forms_title">注册</h2>
                    <el-form ref="registerForm" :model="registerUserInfo" :rules="registerRules">
                        <el-form-item prop="userName">
                            <el-input v-model="registerUserInfo.userName" placeholder="账号"></el-input>
                        </el-form-item>
                        <el-form-item prop="passWord">
                            <el-input v-model="registerUserInfo.passWord" type="password" placeholder="密码"></el-input>
                        </el-form-item>
                        <el-form-item prop="passWord2">
                            <el-input v-model="registerUserInfo.passWord2" type="password" placeholder="确认密码">
                            </el-input>
                        </el-form-item>
                        <el-form-item prop="email">
                            <el-input v-model="registerUserInfo.email" placeholder="邮箱"></el-input>
                        </el-form-item>
                        <el-form-item prop="code">
                            <el-input v-model="registerUserInfo.code" placeholder="验证码">
                                <template #append>
                                    <el-button @click="verificationCodeSubmit" type="primary"
                                        class="forms_input_buttons-action" style="display:inline">获取验证码</el-button>
                                </template>
                            </el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button @click="registerSubmit" type="primary" class="forms_buttons-action">注册
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang='ts'>
import { ref, reactive } from 'vue'
import { verificationCode, resetPassWordVerificationCode, checkVerificationCode, resetPassWord } from '@/api/layout'
import { ElMessage } from 'element-plus'
import { validate, validateField } from '@/utils/formExtend'
import { useLayoutStore } from '@/stores/modules/layout'
const showForgotPassword = ref<boolean>(false)
const showNext = ref<boolean>(false)
const bounceLeft = ref<boolean>(false)
const bounceRight = ref<boolean>(false)
const signupPage = () => {
    bounceRight.value = false
    bounceLeft.value = true
}
const loginPage = () => {
    bounceLeft.value = false
    bounceRight.value = true
}

const loginForm = ref(null)
const registerForm = ref(null)
const forgotPasswordForm = ref(null)
const resetPasswordForm = ref(null)
let loginUserInfo = reactive({
    userName: '',
    passWord: ''
})
let registerUserInfo = reactive({
    userName: '',
    passWord: '',
    passWord2: '',
    email: '',
    code: ''
})
let forgotPasswordInfo = reactive({
    email: '',
    code: ''
})
let resetPasswordInfo = reactive({
    userName: '',
    passWord: '',
    passWord2: ''
})
const loginRules = reactive({
    userName: [{ required: true, message: '请输入账号', trigger: 'blur' }],
    passWord: [{ required: true, message: '请输入密码', trigger: 'blur' }]
})
const registerValidatePass = (rule: any, value: string, callback: any) => {
    if (value === '') {
        callback(new Error('请再次输入密码！'))
    } else if (value !== registerUserInfo.passWord) {
        callback(new Error('两次输入密码不一致！'))
    } else {
        callback()
    }
}
const resetPasswordValidatePass = (rule: any, value: string, callback: any) => {
    if (value === '') {
        callback(new Error('请再次输入密码！'))
    } else if (value !== resetPasswordInfo.passWord) {
        callback(new Error('两次输入密码不一致！'))
    } else {
        callback()
    }
}
const registerRules = reactive({
    userName: [{ required: true, message: '请输入账号', trigger: 'blur' }, { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }],
    passWord: [{ required: true, message: '请输入密码', trigger: 'blur' }, { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }],
    passWord2: [{ required: true, message: '请再次输入密码', trigger: 'blur' }, { validator: registerValidatePass, trigger: 'blur' }],
    email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }, { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }],
    code: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
})
const forgotPasswordRules = reactive({
    email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }, { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }],
    code: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
})
const resetPasswordRules = reactive({
    passWord: [{ required: true, message: '请输入密码', trigger: 'blur' }, { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }],
    passWord2: [{ required: true, message: '请再次输入密码', trigger: 'blur' }, { validator: resetPasswordValidatePass, trigger: 'blur' }]
})

const { login } = useLayoutStore()
const loginSubmit = async () => {
    let { userName, passWord } = loginUserInfo
    if (!await validate(loginForm)) return
    await login({ username: userName, password: passWord })
}

const { register } = useLayoutStore()
const registerSubmit = async () => {
    let { userName, passWord, email, code } = registerUserInfo
    if (!await validate(registerForm)) return
    await register({ username: userName, password: passWord, email: email, code: code })
}
const verificationCodeSubmit = async () => {
    let { email } = registerUserInfo
    if (!await validateField(registerForm, ['email', 'userName'])) return
    const res = await verificationCode({ email: email })
    if (res.data.isSuccess) {
        ElMessage.success('验证码发送成功')
    } else {
        ElMessage.error(res.data.message)
    }
}
const forgotPassWordCodeSubmit = async () => {
    if (!await validateField(forgotPasswordForm, 'email')) return
    const res = await resetPassWordVerificationCode(forgotPasswordInfo.email)
    if (res.data.isSuccess) {
        ElMessage.success('验证码发送成功')
    } else {
        ElMessage.error(res.data.message)
    }
}
// 创建一个临时变量，用来存储sign作为下一步的参数
let externalSign = ''
const next = async () => {
    if (!await validate(forgotPasswordForm)) return
    const res = await checkVerificationCode(forgotPasswordInfo)
    // 提供默认值
    const { isSuccess = false } = res.data
    const { data: { sign = '', userName = '' } = {} } = res.data

    if (isSuccess) {
        showForgotPassword.value = true
        showNext.value = true
        resetPasswordInfo.userName = userName
        console.log('sign', externalSign)
        console.log('userName', resetPasswordInfo.userName)
        externalSign = sign
    } else {
        ElMessage.error(res.data.message || '发生错误')
    }
}
const toLoginSubmit = () => {
    showForgotPassword.value = false
    showNext.value = false
}
const resetPasswordSubmit = async () => {
    let { userName, passWord } = resetPasswordInfo
    if (!await validate(resetPasswordForm)) return
    const res = await resetPassWord({ userName: userName, passWord: passWord, sign: externalSign })
    if (res.data.isSuccess) {
        ElMessage.success('密码重置成功')
        showForgotPassword.value = false
        showNext.value = false
    } else {
        ElMessage.error(res.data.message)
    }
}
</script>
<style scoped>
/* * {
    box-sizing: border-box;
}

body {
    font-family: "Montserrat", sans-serif;
    font-size: 12px;
    line-height: 1em;
} */

button {
    background-color: transparent;
    padding: 0;
    border: 0;
    outline: 0;
    cursor: pointer;
}

:deep(.el-input__wrapper) {
    box-shadow: none;
    padding: 0;
    border: none;
}

:deep(.el-form-item.is-error .el-input__wrapper) {
    border: none;
    box-shadow: none;
}

:deep(.el-form-item.is-error .el-input__wrapper.is-focus) {
    box-shadow: none !important;
}

:deep(.el-form-item__content) {
    height: 40px;
}

:deep(.el-form-item.is-error .el-input__inner) {
    border-bottom: 1px solid red;
}

:deep(.el-input__inner:focus) {
    border-bottom: 1px solid gray;
}

:deep(.el-input__inner) {
    height: 40px;
    background-color: transparent;
    outline: 0;
    width: 100%;
    border-bottom: 1px solid #ccc;
    padding: 6px 0px 1px 6px;
    font-family: "Montserrat", sans-serif;
    font-weight: 300;
    color: gray;
    letter-spacing: 0.1rem;
    transition: border-color 0.2s ease-in-out;
    border-radius: 0px;
}

.forms_input_buttons-action {
    height: 40px;
    background-color: #e8716d;
    border-radius: 3px;
    padding: 10px 35px;
    font-size: 1rem;
    font-family: "Montserrat", sans-serif;
    font-weight: 300;
    color: #fff;
    text-transform: uppercase;
    letter-spacing: 0.1rem;
    transition: background-color 0.2s ease-in-out;
}

:deep(.el-input-group__append) {
    background-color: #e8716d;
    border-radius: 3px;
    /* padding: 10px 35px; */
    font-size: 1rem;
    font-family: "Montserrat", sans-serif;
    font-weight: 300;
    color: #fff;
    text-transform: uppercase;
    letter-spacing: 0.1rem;
    transition: background-color 0.2s ease-in-out;

}

:deep(.el-input-group__append:hover) {
    background-color: #e14641;
    color: #fff;

}

/**
 * * Bounce to the left side
 * */
@-webkit-keyframes bounceLeft {
    0% {
        transform: translate3d(100%, -50%, 0);
    }

    50% {
        transform: translate3d(-30px, -50%, 0);
    }

    100% {
        transform: translate3d(0, -50%, 0);
    }
}

@keyframes bounceLeft {
    0% {
        transform: translate3d(100%, -50%, 0);
    }

    50% {
        transform: translate3d(-30px, -50%, 0);
    }

    100% {
        transform: translate3d(0, -50%, 0);
    }
}

/**
 * * Bounce to the left side
 * */
@-webkit-keyframes bounceRight {
    0% {
        transform: translate3d(0, -50%, 0);
    }

    50% {
        transform: translate3d(calc(100% + 30px), -50%, 0);
    }

    100% {
        transform: translate3d(100%, -50%, 0);
    }
}

@keyframes bounceRight {
    0% {
        transform: translate3d(0, -50%, 0);
    }

    50% {
        transform: translate3d(calc(100% + 30px), -50%, 0);
    }

    100% {
        transform: translate3d(100%, -50%, 0);
    }
}

/**
 * * Show Sign Up form
 * */
@-webkit-keyframes showSignUp {
    100% {
        opacity: 1;
        visibility: visible;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes showSignUp {
    100% {
        opacity: 1;
        visibility: visible;
        transform: translate3d(0, 0, 0);
    }
}

/**
 * * Page background
 * */
.user {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100vh;
    background-color: #fff;
    background-size: cover;
}

.user_options-container {
    position: absolute;
    width: 50%;
}

.user_options-text {
    display: flex;
    justify-content: center;
    width: 100%;
    background-color: rgba(34, 34, 34, 0.85);
    border-radius: 3px;
}

/**
 * * Registered and Unregistered user box and text
 * */
.user_options-registered,
.user_options-unregistered {
    width: 50%;
    padding: 75px 45px;
    color: #fff;
    font-weight: 300;
}

.user_registered-title,
.user_unregistered-title {
    margin-bottom: 15px;
    font-size: 1.66rem;
    line-height: 1em;
}

.user_unregistered-text,
.user_registered-text {
    font-size: 0.83rem;
    line-height: 1.4em;
}

.user_registered-login,
.user_unregistered-signup {
    margin-top: 30px;
    border: 1px solid #ccc;
    border-radius: 3px;
    padding: 10px 30px;
    color: #fff;
    text-transform: uppercase;
    line-height: 1em;
    letter-spacing: 0.2rem;
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

.user_registered-login:hover,
.user_unregistered-signup:hover {
    color: rgba(34, 34, 34, 0.85);
    background-color: #ccc;
}

/**
 * * Login and signup forms
 * */
.user_options-forms {
    position: absolute;
    top: 50%;
    left: 30px;
    width: calc(50% - 30px);
    min-height: 490px;
    background-color: #fff;
    border-radius: 3px;
    box-shadow: 2px 0 15px rgba(0, 0, 0, 0.25);
    /* overflow: hidden; */
    transform: translate3d(100%, -50%, 0);
    transition: transform 0.4s ease-in-out;
}

.user_options-forms .user_forms-login {
    transition: opacity 0.4s ease-in-out, visibility 0.4s ease-in-out;
}

.user_options-forms .forms_title {
    margin-bottom: 45px;
    font-size: 1.5rem;
    font-weight: 500;
    line-height: 1em;
    text-transform: uppercase;
    color: #e8716d;
    letter-spacing: 0.1rem;
}

.simple_div_userName {
    margin-bottom: 45px;
    font-size: 1rem;
    font-weight: 500;
    line-height: 1em;
    color: #b1b1b1;
    letter-spacing: 0.1rem;
}

.user_options-forms .forms-input {
    width: 100%;
    border-bottom: 1px solid #ccc;
    padding: 6px 20px 6px 6px;
    font-family: "Montserrat", sans-serif;
    font-size: 1rem;
    font-weight: 300;
    color: gray;
    letter-spacing: 0.1rem;
    transition: border-color 0.2s ease-in-out;
}

.user_options-forms .forms-input:focus {
    border-color: gray;
}

.user_options-forms .forms_buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 35px;
}

.user_options-forms .forms_buttons-forgot {
    letter-spacing: 0.1rem;
    color: #ccc;
    /* text-decoration: underline; */
    transition: color 0.2s ease-in-out;
    background-color: transparent;
}

.user_options-forms .forms_buttons-forgot:hover {
    color: #b3b3b3;
    background-color: transparent;
}

.user_options-forms .forms_buttons-action {
    height: 40px;
    background-color: #e8716d;
    border-radius: 3px;
    padding: 10px 35px;
    font-size: 1rem;
    font-family: "Montserrat", sans-serif;
    font-weight: 300;
    color: #fff;
    text-transform: uppercase;
    letter-spacing: 0.1rem;
    transition: background-color 0.2s ease-in-out;
    position: absolute;
    right: 0;
}

.user_options-forms .forms_buttons-action:hover {
    background-color: #e14641;
    color: #fff;
}

.user_options-forms .user_forms-signup,
.user_options-forms .user_forms-login {
    position: absolute;
    top: 50px;
    left: 35px;
    width: calc(100% - 80px);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.4s ease-in-out, visibility 0.4s ease-in-out,
        transform 0.5s ease-in-out;
}

.user_options-forms .user_forms-signup {
    transform: translate3d(120px, 0, 0);
}

/* .user_options-forms .user_forms-signup .forms_buttons {
    justify-content: flex-end;
} */
.user_options-forms .user_forms-login {
    transform: translate3d(0, 0, 0);
    opacity: 1;
    visibility: visible;
}

/**
 * * Triggers
 * */
.user_options-forms.bounceLeft {
    -webkit-animation: bounceLeft 1s forwards;
    animation: bounceLeft 1s forwards;
}

.user_options-forms.bounceLeft .user_forms-signup {
    -webkit-animation: showSignUp 1s forwards;
    animation: showSignUp 1s forwards;
}

.user_options-forms.bounceLeft .user_forms-login {
    opacity: 0;
    visibility: hidden;
    transform: translate3d(-120px, 0, 0);
}

.user_options-forms.bounceRight {
    -webkit-animation: bounceRight 1s forwards;
    animation: bounceRight 1s forwards;
}

/**
 * * Responsive 990px
 * */
@media screen and (max-width: 990px) {
    .user_options-forms {
        min-height: 350px;
    }

    .user_options-forms .forms_buttons {
        flex-direction: column;
    }

    .user_options-forms .user_forms-login .forms_buttons-action {
        margin-top: 30px;
    }

    .user_options-forms .user_forms-signup,
    .user_options-forms .user_forms-login {
        top: 40px;
    }

    .user_options-registered,
    .user_options-unregistered {
        padding: 50px 45px;
    }
}
</style>
