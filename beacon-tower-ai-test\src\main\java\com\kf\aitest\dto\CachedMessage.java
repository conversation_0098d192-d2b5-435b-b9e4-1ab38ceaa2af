package com.kf.aitest.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 缓存的SSE消息
 */
@Data
public class CachedMessage {
    
    /**
     * 事件名称
     */
    private String eventName;
    
    /**
     * 消息数据
     */
    private Object data;
    
    /**
     * 消息创建时间
     */
    private LocalDateTime timestamp;
    
    /**
     * 消息序号（用于排序）
     */
    private Long sequenceNumber;
    
    public CachedMessage(String eventName, Object data, Long sequenceNumber) {
        this.eventName = eventName;
        this.data = data;
        this.sequenceNumber = sequenceNumber;
        this.timestamp = LocalDateTime.now();
    }
}
