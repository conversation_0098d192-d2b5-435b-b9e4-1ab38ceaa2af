package com.kf.aitest.exception;

/**
 * AI服务调用异常
 */
public class AiServiceException extends RuntimeException {
    
    private String errorCode;
    private String errorType;
    
    public AiServiceException(String message) {
        super(message);
    }
    
    public AiServiceException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public AiServiceException(String message, String errorCode, String errorType) {
        super(message);
        this.errorCode = errorCode;
        this.errorType = errorType;
    }
    
    public AiServiceException(String message, String errorCode, String errorType, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorType = errorType;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public String getErrorType() {
        return errorType;
    }
}
