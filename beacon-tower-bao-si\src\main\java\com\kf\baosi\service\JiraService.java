package com.kf.baosi.service;

import com.kf.baosi.dto.*;
import com.kf.baosi.dto.jiraTestRun.*;
import com.kf.baosi.dto.JiraSynapseRT.TestCase;
import com.kf.baosi.dto.JiraSynapseRT.TestCycle;
import com.kf.baosi.entity.TUserToken;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.File;
import java.util.List;
import java.util.Map;

public interface JiraService {

    /**
     * jira信息获取
     */
    TUserToken getJiraUserToken(String userId);

    /**
     * jiraToken有效性校验
     */
    boolean checkJiraToken(TUserToken userTokenInfo);

    /**
     * 登录jira
     *
     * @param loginJiraDTO 登录信息
     * @param userId       用户id
     * @return JiraToken
     */
    String login(LoginJiraDTO loginJiraDTO, String userId);

    /**
     * 解析xMind文件的项目名称和版本
     */
    Map<String, Object> getXMindFileInfo(String fileId);

    /**
     * 获取所有项目
     */
    List<ProjectDTO> getAllProject(String jiraToken);

    /**
     * 获取项目下所有的测试计划
     */
    List<Map<String, String>> getAllPlanForProject(String jiraToken, String projectKey);

    /**
     * 获取项目下所有的版本
     */
    List<String> getAllVersionForProject(String jiraToken, String projectKey);

    ResponseEntity<String> createTestSuite(String jiraToken, String testSuitPath, String projectKey);

    /**
     * 创建测试用例
     */
    void createTestCaseTask(String JiraToken, String taskId, CSVFileToJiraDTO csvFileToJiraDTO);

    SseEmitter getEmitter(String taskId);

    void setEmitter(String taskId);

    void startProgressUpdate(String taskId);

    /**
     * 解析csv文件
     */
    List<TestCase> csvToTestCaseEntity(File file, String assigneeName,boolean mergeStepAndResult);

    /**
     * 获取测试计划下的测试周期列表
     */
    List<TestCycle> getTestPlanCycles(String JiraToken, String testPlanIssueKey);


    /**
     * 重载测试周期下的测试用例
     */
    boolean reloadTestRuns(ReloadTestRunsDTO reloadTestRunsDTO, String jiraToken);

    int deleteTestCaseByFileId(String jiraToken, String fileId);

    /**
     * 导出验证文档
     */
    void verifyDocument(Long id,String jiraToken, VerifyDocumentDTO verifyDocumentDTO);


    /**
     * 获取测试需求列表
     */
    List<JiraRequirementDTO> getRequirement(String jiraToken, String planKey, String cycleId);

    /**
     * 获取测试需求下的测试用例列表
     */
    List<JiraTestCaseRunStepDTO> getFlattenedTestRunSteps(String jiraToken, String planKey, String cycleId, String requirementKey);

    /**
     * 更新测试运行的结果
     */
    TestRun updateTestRunResult(String jiraToken, UpdateTestRunResultRequest request);

    /**
     * 更新测试步骤的结果
     */
    TestRun updateTestStepResult(String jiraToken, UpdateTestStepResultRequest request);

    /**
     * 删除Issue
     */
    void deleteTestCase(String jiraToken, DeleteTestCaseRequest request);

    /**
     * 删除jira运行的附件
     */
    void deleteTestRunAttachment(String jiraToken, String testRunId, String attachmentId);

    /**
     * 下载jira运行的附件
     */
    void downloadTestRunAttachment(String jiraToken, String jiraUrl, HttpServletResponse response);

    /**
     * 更新测试步骤
     */
    List<JiraTestCaseRunStepDTO> updateTestStep(String jiraToken, List<JiraTestCaseRunStepDTO> steps);

    /**
     * 获取测试运行的附件信息
     */
    List<AttachmentDTO> getTestRunAttachments(String jiraToken, String testRunId);

    /**
     * 创建测试用例
     */
    void createTestCase(String jiraToken, CreateTestCaseRequest request);

    /**
     * 创建缺陷
     */
    BugDTO createBug(String jiraToken, CreateBugRequest request);

    /**
     * 获取项目在下所有测试集
     */
    List<String> getAllTestSuiteForProject(String jiraToken, String projectKey);

    /**
     * 获取测试计划的版本
     */
    List<String> getTestPlanVersions(String jiraToken, String testPlanKey);

    /**
     * 获取jira用户信息
     */
    JiraUserDTO getJiraUserInfo(String jiraToken);

    /**
     * 获取bug信息
     */
    List<BugInfoDTO> getJiraBugsInfo(String jiraToken, List<String> issueKey);

    /**
     * 获取测试运行中的所有bug信息
     */
    List<BugInfoDTO> getTestRunBugsInfo(String jiraToken, String testRunId);
    /**
     * 链接测试运行步骤的缺陷
     */
    void linkTestRunStepBugs(String jiraToken, LinkTestRunStepBugsRequest request);

    /**
     * 获取issue的summary
     */
    String getIssueSummary(String jiraToken, String issueKey);

    /**
     * 获取测试计划的的summary
     */
    String getTestPlanSummary(String jiraToken, String issueKey);

    /**
     * 获取项目下所有的模块
     */
    List<String> getComponentsForProject(String jiraToken, String projectKey);

    /**
     * 查询jira的所有用户
     */
    List<JiraUserDTO> getAllJiraUsers(String jiraToken);

    /**
     * 上传测试运行的附件
     */
    void uploadTestRunAttachment(String jiraToken, String testRunId, MultipartFile file);

    /**
     * 删除缺陷
     */
    List<JiraTestCaseRunStepDTO> deleteBug(String jiraToken, String runId, String bugKey);
}
