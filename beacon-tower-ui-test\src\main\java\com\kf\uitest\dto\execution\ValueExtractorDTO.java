package com.kf.uitest.dto.execution;

import com.kf.uitest.enums.ExtractorType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValueExtractorDTO {
    /**
     * 提取器类型
     * @see com.kf.uitest.enums.ExtractorType
     */
    private ExtractorType type;
    
    /**
     * 目标元素定位配置
     * 当从元素获取值时使用
     */
    private ElementLocatorDTO locator;
    
    /**
     * 属性名
     * 当提取元素属性值时使用
     */
    private String attributeName;
    
    /**
     * 变量名
     * 当提取变量值时使用
     */
    private String variableName;
    
    /**
     * 提取表达式
     * 用于从复杂数据中提取值
     * 支持：JsonPath、XPath、正则表达式等
     */
    private String expression;
} 