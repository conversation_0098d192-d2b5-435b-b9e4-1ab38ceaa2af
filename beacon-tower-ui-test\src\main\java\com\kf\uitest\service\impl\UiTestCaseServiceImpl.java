package com.kf.uitest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kf.uitest.dao.UiTestCaseMapper;
import com.kf.uitest.dao.UiTestHookMapper;
import com.kf.uitest.entity.UiTestCase;
import com.kf.uitest.entity.UiTestHook;
import com.kf.uitest.enums.HookActionType;
import com.kf.uitest.enums.HookOwnerType;
import com.kf.uitest.enums.HookTiming;
import com.kf.uitest.exception.EntityNotFoundException;
import com.kf.uitest.service.UiTestCaseService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UiTestCaseServiceImpl implements UiTestCaseService {

    @Resource
    private UiTestCaseMapper testCaseMapper;
    
    @Resource
    private UiTestHookMapper hookMapper;

    @Override
    public UiTestCase getById(String id) {
        if (id == null) {
            throw new IllegalArgumentException("Test case id cannot be null");
        }
        UiTestCase testCase = testCaseMapper.selectById(id);
        if (testCase == null) {
            throw new EntityNotFoundException("Test case", id);
        }
        return testCase;
    }

    @Override
    public List<UiTestCase> findBySuiteId(Long suiteId) {
        if (suiteId == null) {
            throw new IllegalArgumentException("Suite id cannot be null");
        }

        LambdaQueryWrapper<UiTestCase> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UiTestCase::getSuiteId, suiteId)
                .eq(UiTestCase::getStatus, 1)  // 只查询有效的测试用例
                .orderByAsc(UiTestCase::getId); // 按ID排序

        return testCaseMapper.selectList(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UiTestCase save(UiTestCase testCase) {
        if (testCase == null) {
            throw new IllegalArgumentException("Test case cannot be null");
        }

        try {
            if (testCase.getId() == null) {
                // 新增
                testCase.setCreateTime(LocalDateTime.now());
                testCase.setUpdateTime(LocalDateTime.now());
                testCaseMapper.insert(testCase);
            } else {
                // 更新
                UiTestCase existingCase = getById(testCase.getId());
                testCase.setCreateTime(existingCase.getCreateTime());
                testCase.setUpdateTime(LocalDateTime.now());
                testCaseMapper.updateById(testCase);
            }
            return testCase;
        } catch (Exception e) {
            log.error("Failed to save test case", e);
            throw new RuntimeException("Failed to save test case", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UiTestCase> saveBatch(List<UiTestCase> testCases) {
        if (CollectionUtils.isEmpty(testCases)) {
            return Collections.emptyList();
        }

        try {
            LocalDateTime now = LocalDateTime.now();
            testCases.forEach(testCase -> {
                if (testCase.getId() == null) {
                    testCase.setCreateTime(now);
                    testCase.setUpdateTime(now);
                    testCaseMapper.insert(testCase);
                } else {
                    UiTestCase existingCase = getById(testCase.getId());
                    testCase.setCreateTime(existingCase.getCreateTime());
                    testCase.setUpdateTime(now);
                    testCaseMapper.updateById(testCase);
                }
            });
            return testCases;
        } catch (Exception e) {
            log.error("Failed to batch save test cases", e);
            throw new RuntimeException("Failed to batch save test cases", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("Test case id cannot be null");
        }

        try {
            int result = testCaseMapper.deleteById(id);
            return result > 0;
        } catch (Exception e) {
            log.error("Failed to delete test case: {}", id, e);
            throw new RuntimeException("Failed to delete test case", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(UiTestCase testCase) {
        if (testCase == null || testCase.getId() == null) {
            throw new IllegalArgumentException("Test case or id cannot be null");
        }

        try {
            testCase.setUpdateTime(LocalDateTime.now());
            int result = testCaseMapper.updateById(testCase);
            return result > 0;
        } catch (Exception e) {
            log.error("Failed to update test case: {}", testCase.getId(), e);
            throw new RuntimeException("Failed to update test case", e);
        }
    }

    @Override
    public long count() {
        return testCaseMapper.selectCount(null);
    }

    @Override
    public List<UiTestCase> findByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<UiTestCase> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(UiTestCase::getId, ids)
                .eq(UiTestCase::getStatus, 1)  // 只查询有效的测试用例
                .orderByAsc(UiTestCase::getId); // 按ID排序

        List<UiTestCase> testCases = testCaseMapper.selectList(wrapper);

        if (testCases.size() != ids.size()) {
            log.warn("Some test cases were not found. Requested: {}, Found: {}",
                    ids.size(), testCases.size());
        }

        return testCases;
    }

    @Override
    public List<UiTestCase> listByIds(Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return testCaseMapper.selectBatchIds(ids);
    }

    @Override
    public boolean isCircularDependency(String caseId, Set<String> visited) {
        // 如果当前用例已经被访问过，说明存在循环依赖
        if (visited.contains(caseId)) {
            return true;
        }
        
        // 将当前用例添加到已访问集合
        visited.add(caseId);
        
        try {
            // 获取前置依赖
            List<String> preDependencies = getPreDependencies(caseId);
            // 检查前置依赖的循环依赖
            for (String preId : preDependencies) {
                if (isCircularDependency(preId, new HashSet<>(visited))) {
                    return true;
                }
            }
            
            // 获取后置依赖
            List<String> postDependencies = getPostDependencies(caseId);
            // 检查后置依赖的循环依赖
            for (String postId : postDependencies) {
                if (isCircularDependency(postId, new HashSet<>(visited))) {
                    return true;
                }
            }
            
            return false;
        } finally {
            // 回溯时移除当前用例
            visited.remove(caseId);
        }
    }

    @Override
    public List<String> getPreDependencies(String caseId) {
        List<UiTestHook> hooks = hookMapper.selectList(new LambdaQueryWrapper<UiTestHook>()
            .eq(UiTestHook::getOwnerId, caseId)
            .eq(UiTestHook::getOwnerType, HookOwnerType.CASE)
            .eq(UiTestHook::getHookTiming, HookTiming.PRE)
            .eq(UiTestHook::getActionType, HookActionType.RUN_CASE)
            .orderBy(true, true, UiTestHook::getHookOrder));
            
        return hooks.stream()
            .map(UiTestHook::getInputValue)
            .collect(Collectors.toList());
    }

    @Override
    public List<String> getPostDependencies(String caseId) {
        List<UiTestHook> hooks = hookMapper.selectList(new LambdaQueryWrapper<UiTestHook>()
            .eq(UiTestHook::getOwnerId, caseId)
            .eq(UiTestHook::getOwnerType, HookOwnerType.CASE)
            .eq(UiTestHook::getHookTiming, HookTiming.POST)
            .eq(UiTestHook::getActionType, HookActionType.RUN_CASE)
            .orderBy(true, true, UiTestHook::getHookOrder));
            
        return hooks.stream()
            .map(UiTestHook::getInputValue)
            .collect(Collectors.toList());
    }
}