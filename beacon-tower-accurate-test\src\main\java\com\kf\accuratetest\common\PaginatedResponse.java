package com.kf.accuratetest.common;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class PaginatedResponse<T> {

    @Schema(description = "总数")
    private long total;

    @Schema(description = "当前页码")
    private long current;

    @Schema(description = "数据列表")
    private List<T> data;

    public PaginatedResponse(IPage<T> page) {
        this.total = page.getTotal();
        this.current = page.getCurrent();
        this.data = page.getRecords();
    }
}
