package com.kf.aitest.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kf.aitest.entity.TDataComparisonStage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据对比阶段结果表Mapper接口
 */
@Mapper
public interface TDataComparisonStageMapper extends BaseMapper<TDataComparisonStage> {

    /**
     * 根据对比ID查询所有阶段结果
     *
     * @param comparisonId 对比主表ID
     * @return 阶段结果列表
     */
    List<TDataComparisonStage> selectByComparisonId(@Param("comparisonId") Long comparisonId);

    /**
     * 根据对比ID和阶段名称查询阶段结果
     *
     * @param comparisonId 对比主表ID
     * @param stageName 阶段名称
     * @return 阶段结果
     */
    TDataComparisonStage selectByComparisonIdAndStage(
            @Param("comparisonId") Long comparisonId,
            @Param("stageName") String stageName
    );

    /**
     * 根据数据ID查询所有阶段结果
     *
     * @param dataId 数据ID
     * @return 阶段结果列表
     */
    List<TDataComparisonStage> selectByDataId(@Param("dataId") String dataId);

    /**
     * 批量插入阶段结果
     *
     * @param stageList 阶段结果列表
     * @return 插入数量
     */
    int batchInsert(@Param("stageList") List<TDataComparisonStage> stageList);

    /**
     * 统计各阶段的成功率
     *
     * @param comparisonId 对比主表ID
     * @return 统计结果
     */
    List<TDataComparisonStage> selectStageStatistics(@Param("comparisonId") Long comparisonId);

    /**
     * 查询失败的阶段结果
     *
     * @param comparisonId 对比主表ID
     * @return 失败的阶段结果列表
     */
    List<TDataComparisonStage> selectFailedStages(@Param("comparisonId") Long comparisonId);
}
