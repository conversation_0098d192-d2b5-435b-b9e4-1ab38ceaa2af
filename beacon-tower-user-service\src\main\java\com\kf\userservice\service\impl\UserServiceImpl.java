package com.kf.userservice.service.impl;

import com.kf.userservice.dao.TUserDao;
import com.kf.userservice.entity.TUser;
import com.kf.userservice.service.UserService;
import com.kf.userservice.utils.MD5Util;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service("userService")
public class UserServiceImpl implements UserService {
    @Resource
    private TUserDao tUserDao;

    /**
     * 通过ID查询单条数据
     *
     * @param userId 主键
     * @return 实例对象
     */
    @Override
    public TUser queryById(String userId) {
        return this.tUserDao.queryById(userId);
    }

    @Override
    public TUser queryByUserName(String username) {
        return this.tUserDao.queryByUserName(username);
    }

    @Override
    public TUser queryByEmail(String email) {
        return this.tUserDao.queryByEmail(email);
    }

    @Override
    public List<TUser> queryByUser(TUser user) {
        return this.tUserDao.queryAll(user);
    }

    /**
     * 新增数据
     *
     * @param tUser 实例对象
     * @return 实例对象
     */
    @Override
    public TUser insert(TUser tUser) {
        tUser.setPassword("e10adc3949ba59abbe56e057f20f883e"); // 新添加用户密码默认： 123456
        tUser.setCreateTime(new Date());
        this.tUserDao.insert(tUser);
        return tUser;
    }

    /**
     * 新增数据
     *
     * @param tUser 实例对象
     * @return 实例对象
     */
    @Override
    public TUser insertUser(TUser tUser) {
        this.tUserDao.insert(tUser);
        return tUser;
    }

    /**
     * 修改数据
     *
     * @param tUser 实例对象
     */
    @Override
    public int update(TUser tUser) {
        tUser.setModifyTime(new Date());
        return this.tUserDao.update(tUser);
    }

    /**
     * 通过主键删除数据
     *
     * @param userId 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long userId) {
        return this.tUserDao.deleteById(userId) > 0;
    }

    @Override
    public String getRoleById(Long userId) {
        return this.tUserDao.getRoleById(userId);
    }

    @Override
    public boolean deleteByIds(String ids) {
        return this.tUserDao.deleteByIds(ids) > 0;
    }

    @Override
    public boolean updatePwd(String id, String oldPwd, String newPwd) {
        TUser user = this.tUserDao.queryById(id);
        if (user.getPassword().equals(MD5Util.string2MD5(oldPwd))) {
            user.setPassword(MD5Util.string2MD5(newPwd));
            this.tUserDao.update(user);
            return true;
        }
        return false;
    }
}