package com.kf.baosi.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2021/4/23
 */
public class wordTemplatePlusSignUtil {

    private static final String ACCESS_KEY = "accessKey";
    private static final String SECRET_KEY = "secretKey";
    private static final String TIMESTAMP = "timestamp";

    /**
     * 将所有请求参数放入jsonMap中（包括accessKey、secretKey、timestamp），生成sign
     *
     * @param paramMap paramMap
     * @return sign
     */
    public static String buildSign(Map<String, Object> paramMap) {
        validateSignParam(paramMap, ACCESS_KEY);
        validateSignParam(paramMap, SECRET_KEY);
        validateSignParam(paramMap, TIMESTAMP);

        Map<String, String> requestMap = new HashMap<>();
        paramMap.forEach((k, v) -> {
            if (StringUtils.isNotEmpty(k) && v != null) {
                String value = v.toString();
                if (StringUtils.isNotEmpty(value)) {
                    requestMap.put(k, v.toString());
                }
            }
        });
        List<String> keys = new ArrayList<>(requestMap.keySet());
        Collections.sort(keys);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < keys.size(); i++) {
            if (i < keys.size() - 1) {
                sb.append(keys.get(i)).append("=").append(requestMap.get(keys.get(i))).append("&");
            } else {
                sb.append(keys.get(i)).append("=").append(requestMap.get(keys.get(i)));
            }
        }
        String signStr = sb.toString();
        return DigestUtils.md5DigestAsHex(signStr.getBytes()).toUpperCase();
    }

    private static void validateSignParam(Map<String, Object> paramMap, String signParamKey) {
        Object o = paramMap.get(signParamKey);
        if (o == null || StringUtils.isEmpty(String.valueOf(o))) {
            throw new IllegalArgumentException(signParamKey + " is not null");
        }
    }
}
