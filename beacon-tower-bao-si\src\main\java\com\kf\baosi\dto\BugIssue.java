package com.kf.baosi.dto;

import lombok.Data;

import java.util.List;

@Data
public class BugIssue {
    private Fields fields;
    @Data
    public static class Fields {
        // 项目
        private Project project;
        // 摘要
        private String summary;
        // 类型
        private IssueType issuetype;
        // 修复版本
        private List<FixVersion> fixVersions;
        // 重现步骤
        private String customfield_10103;
        // 期望结果
        private String customfield_10104;
        // 实际结果
        private String customfield_10105;
        // 严重程度
        private Customfield10109 customfield_10109;
        // 优先级
        private IssuePriority priority;
        // 模块
        private List<Components> components;
        // 实际负责人
        private List<Customfield11769> customfield_11769;
        // 研发负责人
        private customfield12958 customfield_12958;
        // 描述
        private String description;

        @Data
        public static class Project {
            private String key;
        }

        @Data
        public static class IssueType {
            private String name = "缺陷";
        }

        @Data
        public static class FixVersion {
            private String name;
        }
        @Data
        public static class Customfield10109 {
            private String id;
        }
        @Data
        public static class IssuePriority {
            private String id;
        }
        @Data
        public static class Components {
            private String name;
        }
        @Data
        public static class Customfield11769 {
            private String name;
        }
        @Data
        public static class customfield12958 {
            private String name;
        }

    }
}
