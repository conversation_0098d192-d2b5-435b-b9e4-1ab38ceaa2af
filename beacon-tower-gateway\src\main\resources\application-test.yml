server:
  port: 8080
  tomcat:
    max-swallow-size: -1
spring:
  application:
    name: beacon-tower-gateway
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
    gateway:
      routes:
        - id: beacon-tower-bao-si
          uri: lb://beacon-tower-bao-si
          predicates:
            - Path=/bao-si/**
          filters:
            - StripPrefix=1
        - id: beacon-tower-accurate-test
          uri: lb://beacon-tower-accurate-test
          predicates:
            - Path=/accurate-test/**
          filters:
            - StripPrefix=1
        - id: beacon-tower-api-test
          uri: lb://beacon-tower-api-test
          predicates:
            - Path=/api-test/**
          filters:
            - StripPrefix=1
        - id: beacon-tower-user-service
          uri: lb://beacon-tower-user-service
          predicates:
            - Path=/user-service/**
          filters:
            - StripPrefix=1
        - id: beacon-tower-ui-test
          uri: lb://beacon-tower-ui-test
          predicates:
            - Path=/ui-test/**
          filters:
            - StripPrefix=1
        - id: beacon-tower-ai-test
          uri: lb://beacon-tower-ai-test
          predicates:
            - Path=/ai-test/**
          filters:
            - StripPrefix=1
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      database: 1
knife4j:
  gateway:
    # 是否开启
    enabled: true
    # 排序规则(tag/operation排序自4.2.0版本新增)
    # 取值：alpha-默认排序规则，官方swagger-ui默认实现,order-Knife4j提供的增强排序规则，开发者可扩展x-order，根据数值来自定义排序
    tags-sorter: order
    operations-sorter: order
    # 指定服务发现的模式聚合微服务文档，并且是默认`default`分组
    strategy: discover
#    # 子服务存在其他分组情况，聚合其他分组，只能手动配置
#    routes:
#      - name: 用户服务-1
#        # 子服务存在其他分组情况，聚合其他分组
#        url: /user-service/v2/api-docs?group=用户服务
#        # 服务名称(Optional)
#        service-name: user-service
#        # 路由前缀
#        context-path: /
#        # 排序
#        order: 2
#      - name: 订单服务-2
#        url: /order-service/v2/api-docs?group=订单服务
#        service-name: order-service
#        # 路由前缀
#        context-path: /
#        order: 3
    # 服务发现模式的配置
    discover:
      # 开启
      enabled: true
      version: openapi3
      # 需要排除的微服务(eg:网关服务)
      excluded-services:
        - gateway-service
        # 如果子服务是OpenAPI3，并且有个性化配置
#      oas3:
#        url: /v3/api-docs?group=default
#        oauth2-redirect-url: ''
#        validator-url: ''
        # 如何子服务是Swagger2，并且个性化配置
#      swagger2:
#        url: /v2/api-docs?group=default
        # 单个服务的个性化配置，key-服务名称，value-配置信息
#      service-config:
#        # 假设order服务(具体真实服务开发者根据自己的情况配置)
#        user-service:
#          # 该服务的排序
#          order: 0
#          # 分组显示名称
#          group-name: 订单服务名称
#          # 兼容OpenAPI3规范在聚合时丢失contextPath属性的异常情况，由开发者自己配置contextPath,Knife4j的前端Ui做兼容处理,与url属性独立不冲突，仅OpenAPI3规范聚合需要，OpenAPI2规范不需要设置此属性,默认为(apiPathPrefix)
#          context-path: /
#          # 该属性自4.2.0添加，支持子服务非`default`分组的其他分组聚合
#          # 参考 https://gitee.com/xiaoym/knife4j/pulls/87
#          group-names:
#            - 分组1
#            - 分组2

