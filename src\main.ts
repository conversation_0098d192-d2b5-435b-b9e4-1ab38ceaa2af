import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import ElementPlus from 'element-plus'
// import direct from '/@/directive/index'
import router from './router'
import './router/permission'
import '@/assets/css/index.css'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/display.css'
import 'nprogress/nprogress.css'
// import 'virtual:svg-icons-register'
import SvgIcon from '@/components/SvnIcon/index.vue'

import * as ElIcons from '@element-plus/icons-vue'
import preventDefault from '@/directive/prevent-default'

const app = createApp(App)

app.use(createPinia())
// direct(app)
app.use(ElementPlus)
app.use(router)
// 自定义指令，解决按钮点击后恢复默认样式
app.directive('prevent-default', preventDefault)
app.component('SvgIcon', SvgIcon)
const ElIconsData = ElIcons as unknown as Array<() => Promise<typeof import('*.vue')>>
for (const iconName in ElIconsData) {
    app.component(`ElIcon${iconName}`, ElIconsData[iconName])
}
app.mount('#app')
