package com.kf.accuratetest.common;

import com.kf.accuratetest.dto.TInterfaceAnnotationDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ExtendedResponseDoMain extends ResponseDoMain {
    private String taskId;
    private List<TInterfaceAnnotationDTO> data;

    public ExtendedResponseDoMain(String message, Boolean isSuccess, List<TInterfaceAnnotationDTO> interfaceAnnotationDTOList, Integer code, String taskId) {
        super(message, isSuccess, interfaceAnnotationDTOList, code);
        this.taskId = taskId;
        this.data = interfaceAnnotationDTOList;
    }

    //失败
    public static ExtendedResponseDoMain failure(String message, String taskId) {
        return new ExtendedResponseDoMain(message, false, null, 400, taskId);
    }

}
