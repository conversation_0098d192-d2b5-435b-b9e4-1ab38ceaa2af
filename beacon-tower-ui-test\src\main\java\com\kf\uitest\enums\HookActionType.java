package com.kf.uitest.enums;

import lombok.Getter;

import java.util.EnumSet;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.HashSet;
import java.util.Set;

@Getter
public enum HookActionType {
    // 用例级别钩子
    RUN_CASE("执行用例", HookOwnerType.CASE),
    WAIT_TIME("等待时间", HookOwnerType.CASE),
    SET_ENVIRONMENT("设置环境变量", HookOwnerType.CASE),
    CLEAN_ENVIRONMENT("清理环境", HookOwnerType.CASE),
    INITIALIZE_DATA("初始化数据", HookOwnerType.CASE),
    CLEAN_DATA("清理数据", HookOwnerType.CASE),

    // 步骤和块级别通用钩子
    WAIT_ELEMENT("等待元素", HookOwnerType.STEP, HookOwnerType.BLOCK),
    WAIT_NETWORK_IDLE("等待网络空闲", HookOwnerType.STEP, HookOwnerType.BLOCK),
    WAIT_LOAD_STATE("等待加载状态", HookOwnerType.STEP, HookOwnerType.BLOCK),
    
    // 断言相关
    ASSERT_ELEMENT("元素断言", HookOwnerType.STEP, HookOwnerType.BLOCK),
    ASSERT_TEXT("文本断言", HookOwnerType.STEP, HookOwnerType.BLOCK),
    ASSERT_URL("URL断言", HookOwnerType.STEP, HookOwnerType.BLOCK),
    ASSERT_TITLE("标题断言", HookOwnerType.STEP, HookOwnerType.BLOCK),
    ASSERT_ATTRIBUTE("属性断言", HookOwnerType.STEP, HookOwnerType.BLOCK),
    ASSERT_VISIBLE("可见性断言", HookOwnerType.STEP, HookOwnerType.BLOCK),
    ASSERT_ENABLED("可用性断言", HookOwnerType.STEP, HookOwnerType.BLOCK),
    ASSERT_SELECTED("选中状态断言", HookOwnerType.STEP, HookOwnerType.BLOCK),
    
    // 数据库相关
    DB_QUERY("数据库查询", HookOwnerType.STEP, HookOwnerType.BLOCK),
    DB_ASSERT("数据库断言", HookOwnerType.STEP, HookOwnerType.BLOCK),
    DB_EXTRACT("数据库数据提取", HookOwnerType.STEP, HookOwnerType.BLOCK),
    
    // 接口相关
    API_LISTEN("接口监听", HookOwnerType.STEP, HookOwnerType.BLOCK),
    API_ASSERT("接口断言", HookOwnerType.STEP, HookOwnerType.BLOCK),
    API_EXTRACT("接口数据提取", HookOwnerType.STEP, HookOwnerType.BLOCK),
    
    // 变量相关
    SET_VARIABLE("设置变量", HookOwnerType.STEP, HookOwnerType.BLOCK),
    GET_VARIABLE("获取变量", HookOwnerType.STEP, HookOwnerType.BLOCK),
    
    // 提取相关
    EXTRACT_ELEMENT("元素提取", HookOwnerType.STEP, HookOwnerType.BLOCK),
    EXTRACT_RESPONSE("响应提取", HookOwnerType.STEP, HookOwnerType.BLOCK),
    EXTRACT_DB("数据库提取", HookOwnerType.STEP, HookOwnerType.BLOCK),
    
    // 脚本相关
    EXECUTE_JS("执行JavaScript", HookOwnerType.STEP, HookOwnerType.BLOCK),
    EXECUTE_PYTHON("执行Python脚本", HookOwnerType.STEP, HookOwnerType.BLOCK);

    private final String description;
    private final Set<HookOwnerType> allowedOwners;

    HookActionType(String description, HookOwnerType... allowedOwners) {
        this.description = description;
        this.allowedOwners = new HashSet<>(Arrays.asList(allowedOwners));
    }

    public String getDescription() {
        return description;
    }

    public boolean isAllowedFor(HookOwnerType ownerType) {
        return allowedOwners.contains(ownerType);
    }

    /**
     * 获取指定所有者类型支持的所有动作
     */
    public static List<HookActionType> getActionsForOwnerType(HookOwnerType ownerType) {
        return Arrays.stream(values())
                .filter(action -> action.isAllowedFor(ownerType))
                .collect(Collectors.toList());
    }
}