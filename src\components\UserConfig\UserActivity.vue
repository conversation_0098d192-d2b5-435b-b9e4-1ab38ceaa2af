<template>
    <el-collapse accordion>
        <el-collapse-item title="配置钉钉推送">
            <Post description="将静态分析的结果推送到钉钉群消息">
                <DingTalkPushForm/>
                <HowToAddDingTalkRobot/>
            </Post>
        </el-collapse-item>
        <el-collapse-item title="设置编译工程的配置文件">
            <Post description="不上传则使用默认的settings.xml进行编译">
                <CurrentConfigFile/>
            </Post>
        </el-collapse-item>
    </el-collapse>
</template>

<script setup lang='ts'>
import DingTalkPushForm from '@/components/UserConfig/DingTalkPushForm.vue'
import HowToAddDingTalkRobot from '@/components/UserConfig/HowToAddDingTalkRobot.vue'
import CurrentConfigFile from '@/components/UserConfig/CurrentConfigFile.vue'
import Post from '@/components/UserConfig/Post.vue'

</script>

<style scoped>
::v-deep(.el-collapse-item__content) {
    padding-bottom: 10px;
}
</style>
