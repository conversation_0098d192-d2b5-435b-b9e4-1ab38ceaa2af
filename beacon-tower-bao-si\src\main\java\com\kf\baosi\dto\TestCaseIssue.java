package com.kf.baosi.dto;

import lombok.Data;
import java.util.List;

@Data
public class TestCaseIssue {
    private Fields fields;
    private List<TestCaseStep> testcasesteps;

    @Data
    public static class Fields {
        // 项目
        private Project project;
        // 摘要
        private String summary;
        // 类型
        private IssueType issuetype;
        // 修复版本
        private List<FixVersion> fixVersions;
        // 指派人
        private Assignee assignee;
        // 用例等级
        private Customfield12851 customfield_12851;
        // 前提
        private String customfield_12859;

        @Data
        public static class Project {
            private String key;
        }

        @Data
        public static class IssueType {
            private String name = "测试用例";
        }

        @Data
        public static class FixVersion {
            private String name;
        }

        @Data
        public static class Assignee {
            private String name;
        }

        @Data
        public static class Customfield12851 {
            private String value;
        }
    }

    @Data
    public static class TestCaseStep {
        private String step;
        private String stepData;
        private String expectedResult;
    }
}
