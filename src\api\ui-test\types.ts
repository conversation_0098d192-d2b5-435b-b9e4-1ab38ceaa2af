/**
 * UI测试服务类型定义
 */

import type { AxiosResponse } from 'axios'

// ==================== UI测试服务类型定义 ====================

export interface Suite {
    projectId: string
    suiteName: string
    description?: string
}

export interface TestCase {
    suiteId: number
    caseName: string
    description?: string
}

export interface EnvironmentDTO {
    id?: number
    userId?: number
    environmentName: string
    browserType: string
    isHeadless: number
    description: string
    createTime?: string
    updateTime?: string
}

export interface EnvironmentVariableDTO {
    id?: number
    environmentId?: number
    variableName: string
    variableValue: string
    description: string
    createTime?: string
    updateTime?: string
}

export interface EnvironmentWithVariablesDTO {
    environment: EnvironmentDTO
    variables: EnvironmentVariableDTO[]
}

export interface CreateEnvironmentDTO {
    environmentName: string
    browserType: string
    isHeadless: number
    variables?: Array<{
        variableName: string
        variableValue: string
        description: string
    }>
}

export interface UpdateEnvironmentDTO {
    id: number
    environmentName?: string
    browserType?: string
    isHeadless?: number
    description?: string
    variables?: {
        create?: Array<{
            variableName: string
            variableValue: string
            description: string
        }>
        update?: Array<{
            id: number
            variableName?: string
            variableValue?: string
            description?: string
        }>
        delete?: number[]
    }
}
