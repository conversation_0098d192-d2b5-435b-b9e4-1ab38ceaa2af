server:
  port: 8001
  tomcat:
    max-swallow-size: -1
file:
  upload:
    fs-url: http://inner.test.com/api/fs-service
    dir: /data/work/file
spring:
  application:
    name: beacon-tower-accurate-test
  servlet:
    multipart:
      max-file-size: 2GB
      max-request-size: 2GB
  datasource:
    #   数据源基本配置
    username: fsuser
    password: TM@fs.456
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      validation-query: SELECT 1
    hikari:
      maximum-pool-size: 2
      connection-timeout: 3000
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
management:
  endpoints:
    web:
      exposure:
        include: "*"
  metrics:
    # 下面选项建议打开，以监控 http 请求的 P99/P95 等，具体的时间分布可以根据实际情况设置
    distribution:
      percentiles:
        http:
          server:
            requests: 0.5, 0.75, 0.9, 0.95, 0.99
    # 在 Prometheus 中添加特别的 Labels
    tags:
      # 必须加上对应的应用名，因为需要以应用的维度来查看对应的监控
      application:  beacon-tower-bao-si
rabbit:
  connections:
    - name: test-mq-001
      host: test-mq-pub-01-sh2b.taimei.com
      port: 5672
      username: MjpyYWJiaXRtcS1zZXJ2ZXJsZXNzLWNuLWxtcjN4YWNlazAxOkxUQUk1dEdRcXh0dVhlVmtLNVNtSm9NNA==
      password: MzMxNERENDFDOTU5RDE4NzFGMzhDQzAxREU3QkJENDUyNjBENkM2OToxNzI3MjQ5MDAxNjA3
      virtual-host: /


