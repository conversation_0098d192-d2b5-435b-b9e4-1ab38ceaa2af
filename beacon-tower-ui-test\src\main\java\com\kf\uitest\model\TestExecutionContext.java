package com.kf.uitest.model;

import com.kf.uitest.enums.BrowserEngine;
import com.kf.uitest.enums.StepStatus;
import com.kf.uitest.exception.TestExecutionException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.Set;
import java.util.HashSet;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestExecutionContext {
    /**
     * 执行ID
     */
    private String executionId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 执行环境ID
     */
    private String environmentId;

    /**
     * 浏览器类型
     */
    private BrowserEngine browserEngine;

    /**
     * 执行速度（毫秒）
     */
    private int executionSpeed;

    /**
     * 是否启用失败重试
     */
    private boolean retryOnFailure;

    /**
     * 最大重试次数
     */
    @Builder.Default
    private int maxRetries = 0;

    /**
     * 当前重试次数
     */
    @Builder.Default
    private AtomicInteger retryCount = new AtomicInteger(0);

    /**
     * 是否启用并发执行
     */
    private boolean concurrent;

    /**
     * 是否共享浏览器实例
     */
    private boolean shareBrowser;

    /**
     * 执行变量（包含环境变量）
     */
    @Builder.Default
    private Map<String, Object> variables = new ConcurrentHashMap<>();

    /**
     * 测试数据
     */
    @Builder.Default
    private Map<String, Object> testData = new ConcurrentHashMap<>();

    /**
     * 步骤状态
     */
    @Builder.Default
    private Map<String, StepStatus> stepStatuses = new ConcurrentHashMap<>();

    /**
     * 用例失败是否继续执行
     */
    private boolean continueOnCaseFailure;

    /**
     * 用例失败是否重试
     */
    private boolean retryOnCaseFailure;

    /**
     * 用例最大重试次数
     */
    private int maxCaseRetries;

    /**
     * 用例当前重试次数
     */
    @Builder.Default
    private Map<String, AtomicInteger> caseRetryCount = new ConcurrentHashMap<>();

    /**
     * 步骤失败是否继续执行
     */
    private boolean continueOnStepFailure;

    /**
     * 步骤失败是否重试
     */
    private boolean retryOnStepFailure;

    /**
     * 步骤最大重试次数
     */
    private int maxStepRetries;

    /**
     * 步骤当前重试次数
     */
    @Builder.Default
    private Map<String, AtomicInteger> stepRetryCount = new ConcurrentHashMap<>();

    /**
     * 循环迭代计数器
     */
    @Builder.Default
    private Map<String, AtomicInteger> loopIterations = new ConcurrentHashMap<>();

    /**
     * 检查是否可以重试
     */
    public boolean canRetry() {
        return retryOnFailure && retryCount.get() < maxRetries;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        retryCount.incrementAndGet();
    }

    /**
     * 重置重试次数
     */
    public void resetRetryCount() {
        retryCount.set(0);
    }

    /**
     * 检查步骤依赖是否满足
     */
    public boolean checkDependencies(TestExecutionStep step) {
        if (step.getDependencyIds() == null || step.getDependencyIds().isEmpty()) {
            return true;
        }

        return step.getDependencyIds().stream()
                .allMatch(depId -> getStepStatus(depId) == StepStatus.SUCCESS);
    }

    /**
     * 设置步骤状态
     */
    public void setStepStatus(String stepId, StepStatus status) {
        this.stepStatuses.put(stepId, status);
    }

    /**
     * 获取步骤状态
     */
    public StepStatus getStepStatus(String stepId) {
        return this.stepStatuses.getOrDefault(stepId, StepStatus.PENDING);
    }

    /**
     * 检查用例是否可以重试
     */
    public boolean canRetryCaseExecution(String caseId) {
        AtomicInteger retryCount = caseRetryCount.computeIfAbsent(caseId, k -> new AtomicInteger(0));
        return retryOnCaseFailure && retryCount.get() < maxCaseRetries;
    }

    /**
     * 增加用例重试次数
     */
    public void incrementCaseRetryCount(String caseId) {
        caseRetryCount.computeIfAbsent(caseId, k -> new AtomicInteger(0)).incrementAndGet();
    }

    /**
     * 检查步骤是否可以重试
     */
    public boolean canRetryStepExecution(String stepId) {
        AtomicInteger retryCount = stepRetryCount.computeIfAbsent(stepId, k -> new AtomicInteger(0));
        return retryOnStepFailure && retryCount.get() < maxStepRetries;
    }

    /**
     * 增加步骤重试次数
     */
    public void incrementStepRetryCount(String stepId) {
        stepRetryCount.computeIfAbsent(stepId, k -> new AtomicInteger(0)).incrementAndGet();
    }

    /**
     * 获取步骤当前重试次数
     */
    public int getStepRetryAttempt(String stepId) {
        return stepRetryCount.computeIfAbsent(stepId, k -> new AtomicInteger(0)).get();
    }

    /**
     * 设置变量值
     *
     * @param name  变量名
     * @param value 变量值
     */
    public void setVariable(String name, Object value) {
        if (name == null) {
            throw new TestExecutionException("Variable name cannot be null");
        }
        variables.put(name, value);
    }

    /**
     * 获取变量值
     */
    public Object getVariable(String name) {
        return variables.get(name);
    }

    /**
     * 获取所有需要持久化的变量
     */
    public Map<String, Object> getPersistentVariables() {
        return variables.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith("env."))
                .collect(Collectors.toMap(
                        entry -> entry.getKey().substring(4), // 移除"env."前缀
                        Map.Entry::getValue
                ));
    }

    /**
     * 获取循环迭代次数
     */
    public int getLoopIteration(String blockId) {
        return loopIterations.computeIfAbsent(blockId, k -> new AtomicInteger(0)).get();
    }

    /**
     * 增加循环迭代次数
     */
    public void incrementLoopIteration(String blockId) {
        loopIterations.computeIfAbsent(blockId, k -> new AtomicInteger(0)).incrementAndGet();
    }

    /**
     * 重置循环迭代次数
     */
    public void resetLoopIteration(String blockId) {
        loopIterations.computeIfAbsent(blockId, k -> new AtomicInteger(0)).set(0);
    }

    /**
     * 获取当前循环的迭代次数
     */
    public int getCurrentLoopIteration(String blockId) {
        return loopIterations.computeIfAbsent(blockId, k -> new AtomicInteger(0)).get();
    }

    @Builder.Default
    private Set<String> completedSteps = new HashSet<>();

    public void markStepComplete(String stepId) {
        completedSteps.add(stepId);
    }

    public boolean isStepComplete(String stepId) {
        return completedSteps.contains(stepId);
    }
}