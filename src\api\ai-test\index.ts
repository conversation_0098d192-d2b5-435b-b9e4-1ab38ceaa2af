/**
 * AI测试API接口封装
 * 基于现有的DataComparisonController接口
 */

import request from '@/utils/request'
import type {
    AITestStartRequest,
    AITestListRequest,
    AITestStartApiResponse,
    AITestListApiResponse,
    AITestDetailApiResponse,
    AITestConfigApiResponse,
    AITestHealthApiResponse,
    SSEConnectionConfig,
    SSEEventCallbacks,
    SSEEventData,
    SSEConnectionStatus
} from './types'

// ==================== API路径常量 ====================

const serviceName = '/api/beacon-tower'
const aiTestName = 'ai-test'

const aiTestApi = {
    /** 启动AI测试任务 */
    start: `${serviceName}/${aiTestName}/data-comparison/start`,
    /** 获取SSE进度连接 */
    progress: `${serviceName}/${aiTestName}/data-comparison/progress`,
    /** 获取配置信息 */
    config: `${serviceName}/${aiTestName}/data-comparison/config`,
    /** 健康检查 */
    health: `${serviceName}/${aiTestName}/data-comparison/health`,
    /** 获取任务列表 */
    list: `${serviceName}/${aiTestName}/list`,
    /** 获取任务详情 */
    detail: `${serviceName}/${aiTestName}/detail`
} as const

// ==================== 基础API接口 ====================

/**
 * 启动AI测试任务
 * @param params 启动参数
 * @returns 任务信息
 */
export const startAITest = (params: AITestStartRequest): Promise<AITestStartApiResponse> => {
    return request({
        url: aiTestApi.start,
        method: 'post',
        data: params
    })
}

/**
 * 获取AI测试任务列表（基于现有分页查询接口）
 * 注意：这个接口需要后端实现，目前复用数据对比的查询逻辑
 * @param params 查询参数
 * @returns 任务列表
 */
export const getAITestList = (params: AITestListRequest): Promise<AITestListApiResponse> => {
    // 这里暂时使用一个占位接口，实际需要后端提供专门的查询接口
    return request({
        url: aiTestApi.list,
        method: 'get',
        params: params
    })
}

/**
 * 根据ID查询AI测试任务详情
 * @param id 记录ID
 * @returns 任务详情
 */
export const getAITestDetail = (id: number): Promise<AITestDetailApiResponse> => {
    return request({
        url: `${aiTestApi.detail}/${id}`,
        method: 'get'
    })
}

/**
 * 删除AI测试记录
 * @param id 记录ID
 * @returns 删除结果
 */
export const deleteAITestRecord = (id: number): Promise<any> => {
    return request({
        url: `${serviceName}/${aiTestName}/${id}`,
        method: 'delete'
    })
}

/**
 * 获取AI测试配置信息
 * @returns 配置信息
 */
export const getAITestConfig = (): Promise<AITestConfigApiResponse> => {
    return request({
        url: aiTestApi.config,
        method: 'get'
    })
}

/**
 * 健康检查
 * @returns 健康状态
 */
export const checkAITestHealth = (): Promise<AITestHealthApiResponse> => {
    return request({
        url: aiTestApi.health,
        method: 'get'
    })
}

// ==================== SSE连接管理 ====================

/**
 * SSE连接管理类
 */
export class AITestSSEConnection {
    private eventSource: EventSource | null = null
    private config: Required<SSEConnectionConfig>
    private callbacks: SSEEventCallbacks
    private status: SSEConnectionStatus = 'DISCONNECTED'
    private reconnectAttempts = 0
    private reconnectTimer: number | null = null

    constructor(config: SSEConnectionConfig, callbacks: SSEEventCallbacks = {}) {
        this.config = {
            taskId: config.taskId,
            reconnectInterval: config.reconnectInterval || 3000,
            maxReconnectAttempts: config.maxReconnectAttempts || 5,
            timeout: config.timeout || 30000
        }
        this.callbacks = callbacks
    }

    /**
   * 创建SSE连接
   */
    connect(): void {
        if (this.status === 'CONNECTED' || this.status === 'CONNECTING') {
            console.warn('SSE connection already exists or connecting')
            return
        }

        this.status = 'CONNECTING'
        const url = `${aiTestApi.progress}/${this.config.taskId}`
    
        try {
            this.eventSource = new EventSource(url)
      
            // 连接成功
            this.eventSource.onopen = () => {
                console.log('SSE connection opened:', this.config.taskId)
                this.status = 'CONNECTED'
                this.reconnectAttempts = 0
                this.callbacks.onConnected?.()
            }

            // 接收消息
            this.eventSource.onmessage = (event) => {
                try {
                    const data: SSEEventData = JSON.parse(event.data)
                    this.handleSSEEvent(data)
                } catch (error) {
                    console.error('Failed to parse SSE message:', error)
                }
            }

            // 连接错误
            this.eventSource.onerror = (error) => {
                console.error('SSE connection error:', error)
                this.status = 'ERROR'
                this.callbacks.onError?.(new Error('SSE connection error'))
        
                // 尝试重连
                this.attemptReconnect()
            }

            // 设置连接超时
            setTimeout(() => {
                if (this.status === 'CONNECTING') {
                    this.disconnect()
                    this.callbacks.onError?.(new Error('SSE connection timeout'))
                }
            }, this.config.timeout)

        } catch (error) {
            console.error('Failed to create SSE connection:', error)
            this.status = 'ERROR'
            this.callbacks.onError?.(error as Error)
        }
    }

    /**
   * 断开SSE连接
   */
    disconnect(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer)
            this.reconnectTimer = null
        }

        if (this.eventSource) {
            this.eventSource.close()
            this.eventSource = null
        }

        this.status = 'DISCONNECTED'
        this.callbacks.onDisconnected?.()
    }

    /**
   * 获取连接状态
   */
    getStatus(): SSEConnectionStatus {
        return this.status
    }

    /**
   * 处理SSE事件
   */
    private handleSSEEvent(data: SSEEventData): void {
        switch (data.type) {
            case 'progress':
                this.callbacks.onProgress?.(data)
                break
            case 'ai-evaluation':
                this.callbacks.onAiEvaluation?.(data)
                break
            case 'ai-result':
                this.callbacks.onAiResult?.(data)
                break
            case 'stage-complete':
                this.callbacks.onStageComplete?.(data)
                break
            case 'task-complete':
                this.callbacks.onTaskComplete?.(data)
                break
            case 'error':
                this.callbacks.onError?.(new Error(data.message || 'Unknown SSE error'))
                break
            default:
                console.warn('Unknown SSE event type:', data.type)
        }
    }

    /**
   * 尝试重连
   */
    private attemptReconnect(): void {
        if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
            console.error('Max reconnect attempts reached')
            this.status = 'ERROR'
            return
        }

        this.reconnectAttempts++
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`)

        this.reconnectTimer = window.setTimeout(() => {
            this.disconnect()
            this.connect()
        }, this.config.reconnectInterval)
    }
}

/**
 * 创建SSE连接的便捷函数
 * @param taskId 任务ID
 * @param callbacks 事件回调
 * @param config 连接配置
 * @returns SSE连接实例
 */
export const createAITestSSEConnection = (
    taskId: string,
    callbacks: SSEEventCallbacks = {},
    config: Partial<SSEConnectionConfig> = {}
): AITestSSEConnection => {
    return new AITestSSEConnection({ taskId, ...config }, callbacks)
}

// ==================== 工具函数 ====================

/**
 * 获取SSE连接URL
 * @param taskId 任务ID
 * @returns SSE连接URL
 */
export const getSSEConnectionUrl = (taskId: string): string => {
    return `${aiTestApi.progress}/${taskId}`
}

/**
 * 检查任务ID格式是否有效
 * @param taskId 任务ID
 * @returns 是否有效
 */
export const isValidTaskId = (taskId: string): boolean => {
    // UUID格式验证
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    return uuidRegex.test(taskId)
}

// ==================== 导出所有类型 ====================

export type * from './types'
