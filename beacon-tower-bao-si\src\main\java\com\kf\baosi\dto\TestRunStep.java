package com.kf.baosi.dto;

import lombok.Data;

import java.util.List;

@Data
public class TestRunStep {
    //关联的bug
    private List<BugDTO> testRunStepBugsWrapper;
    //预期结果
    private String expectedResult;
    //数据
    private String stepData;
    //附件
    private List<AttachmentDTO> testRunStepAttachments;
    //实际结果
    private String actualResult;
    //步骤
    private String step;
    //步骤id
    private String id;
    //步骤结果
    private String status;
}