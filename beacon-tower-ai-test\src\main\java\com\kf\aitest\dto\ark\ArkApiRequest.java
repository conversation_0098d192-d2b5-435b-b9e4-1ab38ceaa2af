package com.kf.aitest.dto.ark;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 火山引擎ARK API请求模型
 */
@Data
public class ArkApiRequest {
    
    /**
     * 模型ID
     */
    private String model;
    
    /**
     * 消息列表
     */
    private List<Message> messages;
    
    /**
     * 最大生成token数
     */
    @JsonProperty("max_tokens")
    private Integer maxTokens;
    
    /**
     * 温度参数，控制随机性
     */
    private Double temperature;
    
    /**
     * 消息对象
     */
    @Data
    public static class Message {
        /**
         * 角色：user、assistant、system
         */
        private String role;
        
        /**
         * 消息内容，可以是字符串或内容数组
         */
        private Object content;
    }
    
    /**
     * 内容对象（支持多模态）
     */
    @Data
    public static class Content {
        /**
         * 内容类型：text、image_url
         */
        private String type;
        
        /**
         * 文本内容
         */
        private String text;
        
        /**
         * 图像URL对象
         */
        @JsonProperty("image_url")
        private ImageUrl imageUrl;
    }
    
    /**
     * 图像URL对象
     */
    @Data
    public static class ImageUrl {
        /**
         * 图像URL
         */
        private String url;
        
        /**
         * 图像详细程度：low、high、auto
         */
        private String detail;
    }
}
