<template>
    <div>
        <div style="font-size: 12px">当前使用的配置文件为：{{ fileName }}</div>
        <el-upload accept=".xml" style="width: 200px;"
                   :action="fileSubmit"
                   :file-list="fileList"
                   :before-remove="beforeRemove"
                   :limit="1" :on-exceed="handleExceed"
                   :on-error="handleError"
                   :headers="headers"
                   :on-success="handleSuccess">
            <el-button type="primary">上传文件</el-button>
        </el-upload>
    </div>
</template>

<script setup lang='ts'>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getProfileFileList, deleteProfileFile, getUploadFile } from '@/api/layout'

let fileName = ref('默认')
interface List {
    name: string
}
let fileList = ref([] as Array<List>)

const removeBox = (file: any, fileList: any): any => {
    return ElMessageBox.confirm(`确定删除 ${file.name}？`, '提示', {
        title: '确定删除 ' + file.name + '？',
        message: '删除之后编译项目会使用默认settings.xml,是否继续？',
        confirmButtonText: '确定',
        cancelButtonText: '取消'

    })
}

const beforeRemove = async (file: any, fileList: any) => {
    const boxRes = await removeBox(file, fileList)
    if (boxRes !== 'confirm') {
        return false
    }
    if (await removeProfileFile()) {
        getProfileFile()
    }
}

const handleExceed = (files: any, fileList: any) => {
    ElMessage.warning('有一个生效的文件，请先删除')
}

const handleSuccess = (response: any, file: any, fileList: any) => {
    if (response.isSuccess) {
        ElMessage.success(response.message)
        getProfileFile()
    } else {
        ElMessage.error(response.message)
        fileList.splice(0, fileList.length)
    }
}

const handleError = (err: any, file: any, fileList: any) => {
    console.log('handleError')
    console.log(err, file, fileList)
}

const fileSubmit = getUploadFile()
const headers = {
    'Authorization': 'Bearer ' + JSON.parse(localStorage.getItem('token') || '{}').ACCESS_TOKEN
}

let fileId = ref('')
const getProfileFile = async () => {
    const res = await getProfileFileList()
    if (res.data.isSuccess) {
        fileList.value = []
        fileName.value = '默认'
        if (res.data.data.length > 0) {
            fileList.value.push({
                name: res.data.data[0].fileName
            })
            fileName.value = res.data.data[0].fileName
            fileId.value = res.data.data[0].id
        }
    }
}
getProfileFile()

let file = reactive({
    fileId: ''
})
const removeProfileFile = async (): Promise<boolean> => {
    file.fileId = fileId.value
    const res = await deleteProfileFile(file)
    if (res.data.isSuccess) {
        ElMessage.success('删除成功')
        return true
    } else {
        ElMessage.error('删除失败')
        return false
    }
}
</script>

<style scoped>

</style>
