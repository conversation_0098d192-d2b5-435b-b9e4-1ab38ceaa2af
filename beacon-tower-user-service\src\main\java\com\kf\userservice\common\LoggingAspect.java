package com.kf.userservice.common;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Slf4j
@Aspect
@Component
public class LoggingAspect {

    // 匹配所有Controller下的方法
    @Before("execution(* com.kf..*Controller.*(..))")
    public void logBefore(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();

        // 方法名
        String methodName = signature.getMethod().getName();

        // 类名
        String className = joinPoint.getTarget().getClass().getName();

        // 参数
        Object[] args = joinPoint.getArgs();
        log.info("调用入参: " + className + "." + methodName + "()" + ", 参数: " + Arrays.toString(args));
    }
}
