package com.kf.uitest.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kf.uitest.entity.UiTestBlock;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface UiTestBlockMapper extends BaseMapper<UiTestBlock> {
    @Select("SELECT * FROM ui_test_block WHERE case_id = #{caseId} ORDER BY block_order ASC")
    List<UiTestBlock> findByCaseId(@Param("caseId") String caseId);

    @Select("SELECT * FROM ui_test_block WHERE parent_block_id = #{blockId} ORDER BY block_order ASC")
    List<UiTestBlock> findChildBlocks(@Param("blockId") String blockId);
}