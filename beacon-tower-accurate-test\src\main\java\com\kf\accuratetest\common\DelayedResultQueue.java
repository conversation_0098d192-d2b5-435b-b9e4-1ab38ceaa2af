package com.kf.accuratetest.common;

import com.kf.accuratetest.entity.Result;

import java.util.concurrent.Delayed;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

class DelayedResultQueue implements Delayed {
    private final String delayedResultId;
    private final long delayTime;
    private final long startTime;

    public DelayedResultQueue(String userId, long delay, TimeUnit unit) {
        this.delayedResultId = userId;
        this.startTime = System.currentTimeMillis();
        this.delayTime = unit.toMillis(delay);
    }

    public String getUserId() {
        return delayedResultId;
    }

    @Override
    public long getDelay(TimeUnit unit) {
        long diff = startTime + delayTime - System.currentTimeMillis();
        return unit.convert(diff, TimeUnit.MILLISECONDS);
    }

    @Override
    public int compareTo(Delayed o) {
        if (this == o) return 0;
        if (o instanceof DelayedResultQueue) {
            long diff = this.startTime + this.delayTime - ((DelayedResultQueue) o).startTime - ((DelayedResultQueue) o).delayTime;
            return (diff == 0) ? 0 : ((diff < 0) ? -1 : 1);
        }
        return 0;
    }

}