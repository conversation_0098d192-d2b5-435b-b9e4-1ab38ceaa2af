<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.kf.aitest.service.StageDataStorageTest" time="11.58" tests="3" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="D:\work\beacon-tower\beacon-tower-ai-test\target\test-classes;D:\work\beacon-tower\beacon-tower-ai-test\target\classes;D:\Program Files\maven_repository\org\springframework\retry\spring-retry\2.0.2\spring-retry-2.0.2.jar;D:\Program Files\maven_repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2022.0.0.0-RC2\spring-cloud-starter-alibaba-nacos-discovery-2022.0.0.0-RC2.jar;D:\Program Files\maven_repository\com\alibaba\cloud\spring-cloud-alibaba-commons\2022.0.0.0-RC2\spring-cloud-alibaba-commons-2022.0.0.0-RC2.jar;D:\Program Files\maven_repository\com\alibaba\nacos\nacos-client\2.2.1\nacos-client-2.2.1.jar;D:\Program Files\maven_repository\com\alibaba\nacos\nacos-auth-plugin\2.2.1\nacos-auth-plugin-2.2.1.jar;D:\Program Files\maven_repository\com\alibaba\nacos\nacos-encryption-plugin\2.2.1\nacos-encryption-plugin-2.2.1.jar;D:\Program Files\maven_repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;D:\Program Files\maven_repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;D:\Program Files\maven_repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\Program Files\maven_repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;D:\Program Files\maven_repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;D:\Program Files\maven_repository\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;D:\Program Files\maven_repository\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;D:\Program Files\maven_repository\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;D:\Program Files\maven_repository\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;D:\Program Files\maven_repository\com\alibaba\spring\spring-context-support\1.0.11\spring-context-support-1.0.11.jar;D:\Program Files\maven_repository\org\springframework\cloud\spring-cloud-commons\4.0.3\spring-cloud-commons-4.0.3.jar;D:\Program Files\maven_repository\org\springframework\security\spring-security-crypto\6.1.1\spring-security-crypto-6.1.1.jar;D:\Program Files\maven_repository\org\springframework\cloud\spring-cloud-context\4.0.3\spring-cloud-context-4.0.3.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter\3.1.1\spring-boot-starter-3.1.1.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot\3.1.1\spring-boot-3.1.1.jar;D:\Program Files\maven_repository\org\springframework\spring-context\6.0.10\spring-context-6.0.10.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-autoconfigure\3.1.1\spring-boot-autoconfigure-3.1.1.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter-logging\3.1.1\spring-boot-starter-logging-3.1.1.jar;D:\Program Files\maven_repository\ch\qos\logback\logback-classic\1.4.8\logback-classic-1.4.8.jar;D:\Program Files\maven_repository\ch\qos\logback\logback-core\1.4.8\logback-core-1.4.8.jar;D:\Program Files\maven_repository\org\apache\logging\log4j\log4j-to-slf4j\2.20.0\log4j-to-slf4j-2.20.0.jar;D:\Program Files\maven_repository\org\apache\logging\log4j\log4j-api\2.20.0\log4j-api-2.20.0.jar;D:\Program Files\maven_repository\org\slf4j\jul-to-slf4j\2.0.7\jul-to-slf4j-2.0.7.jar;D:\Program Files\maven_repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\Program Files\maven_repository\org\springframework\spring-core\6.0.10\spring-core-6.0.10.jar;D:\Program Files\maven_repository\org\springframework\spring-jcl\6.0.10\spring-jcl-6.0.10.jar;D:\Program Files\maven_repository\org\yaml\snakeyaml\1.33\snakeyaml-1.33.jar;D:\Program Files\maven_repository\org\springframework\ai\spring-ai-pdf-document-reader\1.0.0\spring-ai-pdf-document-reader-1.0.0.jar;D:\Program Files\maven_repository\org\springframework\ai\spring-ai-commons\1.0.0\spring-ai-commons-1.0.0.jar;D:\Program Files\maven_repository\io\micrometer\micrometer-core\1.11.1\micrometer-core-1.11.1.jar;D:\Program Files\maven_repository\io\micrometer\micrometer-commons\1.11.1\micrometer-commons-1.11.1.jar;D:\Program Files\maven_repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;D:\Program Files\maven_repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;D:\Program Files\maven_repository\io\micrometer\context-propagation\1.1.3\context-propagation-1.1.3.jar;D:\Program Files\maven_repository\com\fasterxml\jackson\module\jackson-module-jsonSchema\2.15.2\jackson-module-jsonSchema-2.15.2.jar;D:\Program Files\maven_repository\javax\validation\validation-api\1.1.0.Final\validation-api-1.1.0.Final.jar;D:\Program Files\maven_repository\com\knuddels\jtokkit\1.1.0\jtokkit-1.1.0.jar;D:\Program Files\maven_repository\org\apache\pdfbox\pdfbox\3.0.3\pdfbox-3.0.3.jar;D:\Program Files\maven_repository\org\apache\pdfbox\pdfbox-io\3.0.3\pdfbox-io-3.0.3.jar;D:\Program Files\maven_repository\org\apache\pdfbox\fontbox\3.0.3\fontbox-3.0.3.jar;D:\Program Files\maven_repository\com\mysql\mysql-connector-j\8.3.0\mysql-connector-j-8.3.0.jar;D:\Program Files\maven_repository\com\google\protobuf\protobuf-java\3.25.1\protobuf-java-3.25.1.jar;D:\Program Files\maven_repository\com\alibaba\druid-spring-boot-starter\1.2.18\druid-spring-boot-starter-1.2.18.jar;D:\Program Files\maven_repository\com\alibaba\druid\1.2.18\druid-1.2.18.jar;D:\Program Files\maven_repository\org\slf4j\slf4j-api\2.0.7\slf4j-api-2.0.7.jar;D:\Program Files\maven_repository\org\glassfish\jaxb\jaxb-runtime\4.0.3\jaxb-runtime-4.0.3.jar;D:\Program Files\maven_repository\org\glassfish\jaxb\jaxb-core\4.0.3\jaxb-core-4.0.3.jar;D:\Program Files\maven_repository\org\eclipse\angus\angus-activation\2.0.1\angus-activation-2.0.1.jar;D:\Program Files\maven_repository\org\glassfish\jaxb\txw2\4.0.3\txw2-4.0.3.jar;D:\Program Files\maven_repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter-validation\3.1.0\spring-boot-starter-validation-3.1.0.jar;D:\Program Files\maven_repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.10\tomcat-embed-el-10.1.10.jar;D:\Program Files\maven_repository\org\hibernate\validator\hibernate-validator\8.0.0.Final\hibernate-validator-8.0.0.Final.jar;D:\Program Files\maven_repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\Program Files\maven_repository\org\jboss\logging\jboss-logging\3.5.1.Final\jboss-logging-3.5.1.Final.jar;D:\Program Files\maven_repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;D:\Program Files\maven_repository\com\baomidou\mybatis-plus-boot-starter\3.5.5\mybatis-plus-boot-starter-3.5.5.jar;D:\Program Files\maven_repository\com\baomidou\mybatis-plus\3.5.5\mybatis-plus-3.5.5.jar;D:\Program Files\maven_repository\com\baomidou\mybatis-plus-core\3.5.5\mybatis-plus-core-3.5.5.jar;D:\Program Files\maven_repository\com\baomidou\mybatis-plus-annotation\3.5.5\mybatis-plus-annotation-3.5.5.jar;D:\Program Files\maven_repository\com\baomidou\mybatis-plus-extension\3.5.5\mybatis-plus-extension-3.5.5.jar;D:\Program Files\maven_repository\org\mybatis\mybatis\3.5.15\mybatis-3.5.15.jar;D:\Program Files\maven_repository\com\github\jsqlparser\jsqlparser\4.6\jsqlparser-4.6.jar;D:\Program Files\maven_repository\org\mybatis\mybatis-spring\2.1.2\mybatis-spring-2.1.2.jar;D:\Program Files\maven_repository\com\baomidou\mybatis-plus-spring-boot-autoconfigure\3.5.5\mybatis-plus-spring-boot-autoconfigure-3.5.5.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter-jdbc\3.1.1\spring-boot-starter-jdbc-3.1.1.jar;D:\Program Files\maven_repository\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;D:\Program Files\maven_repository\org\springframework\spring-jdbc\6.0.10\spring-jdbc-6.0.10.jar;D:\Program Files\maven_repository\org\springframework\spring-tx\6.0.10\spring-tx-6.0.10.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter-aop\3.1.1\spring-boot-starter-aop-3.1.1.jar;D:\Program Files\maven_repository\org\springframework\spring-aop\6.0.10\spring-aop-6.0.10.jar;D:\Program Files\maven_repository\org\springframework\spring-beans\6.0.10\spring-beans-6.0.10.jar;D:\Program Files\maven_repository\org\aspectj\aspectjweaver\1.9.19\aspectjweaver-1.9.19.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter-web\3.1.1\spring-boot-starter-web-3.1.1.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter-json\3.1.1\spring-boot-starter-json-3.1.1.jar;D:\Program Files\maven_repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.2\jackson-module-parameter-names-2.15.2.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter-tomcat\3.1.1\spring-boot-starter-tomcat-3.1.1.jar;D:\Program Files\maven_repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.10\tomcat-embed-core-10.1.10.jar;D:\Program Files\maven_repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.10\tomcat-embed-websocket-10.1.10.jar;D:\Program Files\maven_repository\org\springframework\spring-web\6.0.10\spring-web-6.0.10.jar;D:\Program Files\maven_repository\io\micrometer\micrometer-observation\1.11.1\micrometer-observation-1.11.1.jar;D:\Program Files\maven_repository\org\springframework\spring-webmvc\6.0.10\spring-webmvc-6.0.10.jar;D:\Program Files\maven_repository\org\springframework\spring-expression\6.0.10\spring-expression-6.0.10.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter-test\3.1.1\spring-boot-starter-test-3.1.1.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-test\3.1.1\spring-boot-test-3.1.1.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-test-autoconfigure\3.1.1\spring-boot-test-autoconfigure-3.1.1.jar;D:\Program Files\maven_repository\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;D:\Program Files\maven_repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.0\jakarta.xml.bind-api-4.0.0.jar;D:\Program Files\maven_repository\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;D:\Program Files\maven_repository\net\minidev\json-smart\2.4.11\json-smart-2.4.11.jar;D:\Program Files\maven_repository\net\minidev\accessors-smart\2.4.11\accessors-smart-2.4.11.jar;D:\Program Files\maven_repository\org\ow2\asm\asm\9.3\asm-9.3.jar;D:\Program Files\maven_repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;D:\Program Files\maven_repository\net\bytebuddy\byte-buddy\1.14.5\byte-buddy-1.14.5.jar;D:\Program Files\maven_repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\Program Files\maven_repository\org\junit\jupiter\junit-jupiter\5.9.3\junit-jupiter-5.9.3.jar;D:\Program Files\maven_repository\org\junit\jupiter\junit-jupiter-params\5.9.3\junit-jupiter-params-5.9.3.jar;D:\Program Files\maven_repository\org\junit\jupiter\junit-jupiter-engine\5.9.3\junit-jupiter-engine-5.9.3.jar;D:\Program Files\maven_repository\org\junit\platform\junit-platform-engine\1.9.3\junit-platform-engine-1.9.3.jar;D:\Program Files\maven_repository\org\mockito\mockito-core\4.8.1\mockito-core-4.8.1.jar;D:\Program Files\maven_repository\net\bytebuddy\byte-buddy-agent\1.14.5\byte-buddy-agent-1.14.5.jar;D:\Program Files\maven_repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\Program Files\maven_repository\org\mockito\mockito-junit-jupiter\4.8.1\mockito-junit-jupiter-4.8.1.jar;D:\Program Files\maven_repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\Program Files\maven_repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\Program Files\maven_repository\org\springframework\spring-test\6.0.10\spring-test-6.0.10.jar;D:\Program Files\maven_repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\Program Files\maven_repository\com\langfuse\langfuse-java\0.1.0\langfuse-java-0.1.0.jar;D:\Program Files\maven_repository\com\fasterxml\jackson\core\jackson-core\2.15.2\jackson-core-2.15.2.jar;D:\Program Files\maven_repository\com\fasterxml\jackson\core\jackson-databind\2.15.2\jackson-databind-2.15.2.jar;D:\Program Files\maven_repository\com\fasterxml\jackson\core\jackson-annotations\2.15.2\jackson-annotations-2.15.2.jar;D:\Program Files\maven_repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.2\jackson-datatype-jdk8-2.15.2.jar;D:\Program Files\maven_repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.2\jackson-datatype-jsr310-2.15.2.jar;D:\Program Files\maven_repository\com\squareup\okhttp3\okhttp\4.10.0\okhttp-4.10.0.jar;D:\Program Files\maven_repository\com\squareup\okio\okio-jvm\3.0.0\okio-jvm-3.0.0.jar;D:\Program Files\maven_repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.8.22\kotlin-stdlib-jdk8-1.8.22.jar;D:\Program Files\maven_repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.8.22\kotlin-stdlib-jdk7-1.8.22.jar;D:\Program Files\maven_repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.8.22\kotlin-stdlib-common-1.8.22.jar;D:\Program Files\maven_repository\org\jetbrains\kotlin\kotlin-stdlib\1.8.22\kotlin-stdlib-1.8.22.jar;D:\Program Files\maven_repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;D:\Program Files\maven_repository\org\junit\jupiter\junit-jupiter-api\5.9.3\junit-jupiter-api-5.9.3.jar;D:\Program Files\maven_repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\Program Files\maven_repository\org\junit\platform\junit-platform-commons\1.9.3\junit-platform-commons-1.9.3.jar;D:\Program Files\maven_repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-configuration-processor\3.1.1\spring-boot-configuration-processor-3.1.1.jar;D:\Program Files\maven_repository\org\projectlombok\lombok\1.18.28\lombok-1.18.28.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="org.jboss.logging.provider" value="slf4j"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="D:\Program Files\Java\jdk-17\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire1492032365084963684\surefirebooter-20250724123559139_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire1492032365084963684 2025-07-24T12-35-58_919-jvmRun1 surefire-20250724123559139_1tmp surefire_0-20250724123559139_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="D:\work\beacon-tower\beacon-tower-ai-test\target\test-classes;D:\work\beacon-tower\beacon-tower-ai-test\target\classes;D:\Program Files\maven_repository\org\springframework\retry\spring-retry\2.0.2\spring-retry-2.0.2.jar;D:\Program Files\maven_repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2022.0.0.0-RC2\spring-cloud-starter-alibaba-nacos-discovery-2022.0.0.0-RC2.jar;D:\Program Files\maven_repository\com\alibaba\cloud\spring-cloud-alibaba-commons\2022.0.0.0-RC2\spring-cloud-alibaba-commons-2022.0.0.0-RC2.jar;D:\Program Files\maven_repository\com\alibaba\nacos\nacos-client\2.2.1\nacos-client-2.2.1.jar;D:\Program Files\maven_repository\com\alibaba\nacos\nacos-auth-plugin\2.2.1\nacos-auth-plugin-2.2.1.jar;D:\Program Files\maven_repository\com\alibaba\nacos\nacos-encryption-plugin\2.2.1\nacos-encryption-plugin-2.2.1.jar;D:\Program Files\maven_repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;D:\Program Files\maven_repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;D:\Program Files\maven_repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\Program Files\maven_repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;D:\Program Files\maven_repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;D:\Program Files\maven_repository\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;D:\Program Files\maven_repository\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;D:\Program Files\maven_repository\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;D:\Program Files\maven_repository\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;D:\Program Files\maven_repository\com\alibaba\spring\spring-context-support\1.0.11\spring-context-support-1.0.11.jar;D:\Program Files\maven_repository\org\springframework\cloud\spring-cloud-commons\4.0.3\spring-cloud-commons-4.0.3.jar;D:\Program Files\maven_repository\org\springframework\security\spring-security-crypto\6.1.1\spring-security-crypto-6.1.1.jar;D:\Program Files\maven_repository\org\springframework\cloud\spring-cloud-context\4.0.3\spring-cloud-context-4.0.3.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter\3.1.1\spring-boot-starter-3.1.1.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot\3.1.1\spring-boot-3.1.1.jar;D:\Program Files\maven_repository\org\springframework\spring-context\6.0.10\spring-context-6.0.10.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-autoconfigure\3.1.1\spring-boot-autoconfigure-3.1.1.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter-logging\3.1.1\spring-boot-starter-logging-3.1.1.jar;D:\Program Files\maven_repository\ch\qos\logback\logback-classic\1.4.8\logback-classic-1.4.8.jar;D:\Program Files\maven_repository\ch\qos\logback\logback-core\1.4.8\logback-core-1.4.8.jar;D:\Program Files\maven_repository\org\apache\logging\log4j\log4j-to-slf4j\2.20.0\log4j-to-slf4j-2.20.0.jar;D:\Program Files\maven_repository\org\apache\logging\log4j\log4j-api\2.20.0\log4j-api-2.20.0.jar;D:\Program Files\maven_repository\org\slf4j\jul-to-slf4j\2.0.7\jul-to-slf4j-2.0.7.jar;D:\Program Files\maven_repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\Program Files\maven_repository\org\springframework\spring-core\6.0.10\spring-core-6.0.10.jar;D:\Program Files\maven_repository\org\springframework\spring-jcl\6.0.10\spring-jcl-6.0.10.jar;D:\Program Files\maven_repository\org\yaml\snakeyaml\1.33\snakeyaml-1.33.jar;D:\Program Files\maven_repository\org\springframework\ai\spring-ai-pdf-document-reader\1.0.0\spring-ai-pdf-document-reader-1.0.0.jar;D:\Program Files\maven_repository\org\springframework\ai\spring-ai-commons\1.0.0\spring-ai-commons-1.0.0.jar;D:\Program Files\maven_repository\io\micrometer\micrometer-core\1.11.1\micrometer-core-1.11.1.jar;D:\Program Files\maven_repository\io\micrometer\micrometer-commons\1.11.1\micrometer-commons-1.11.1.jar;D:\Program Files\maven_repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;D:\Program Files\maven_repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;D:\Program Files\maven_repository\io\micrometer\context-propagation\1.1.3\context-propagation-1.1.3.jar;D:\Program Files\maven_repository\com\fasterxml\jackson\module\jackson-module-jsonSchema\2.15.2\jackson-module-jsonSchema-2.15.2.jar;D:\Program Files\maven_repository\javax\validation\validation-api\1.1.0.Final\validation-api-1.1.0.Final.jar;D:\Program Files\maven_repository\com\knuddels\jtokkit\1.1.0\jtokkit-1.1.0.jar;D:\Program Files\maven_repository\org\apache\pdfbox\pdfbox\3.0.3\pdfbox-3.0.3.jar;D:\Program Files\maven_repository\org\apache\pdfbox\pdfbox-io\3.0.3\pdfbox-io-3.0.3.jar;D:\Program Files\maven_repository\org\apache\pdfbox\fontbox\3.0.3\fontbox-3.0.3.jar;D:\Program Files\maven_repository\com\mysql\mysql-connector-j\8.3.0\mysql-connector-j-8.3.0.jar;D:\Program Files\maven_repository\com\google\protobuf\protobuf-java\3.25.1\protobuf-java-3.25.1.jar;D:\Program Files\maven_repository\com\alibaba\druid-spring-boot-starter\1.2.18\druid-spring-boot-starter-1.2.18.jar;D:\Program Files\maven_repository\com\alibaba\druid\1.2.18\druid-1.2.18.jar;D:\Program Files\maven_repository\org\slf4j\slf4j-api\2.0.7\slf4j-api-2.0.7.jar;D:\Program Files\maven_repository\org\glassfish\jaxb\jaxb-runtime\4.0.3\jaxb-runtime-4.0.3.jar;D:\Program Files\maven_repository\org\glassfish\jaxb\jaxb-core\4.0.3\jaxb-core-4.0.3.jar;D:\Program Files\maven_repository\org\eclipse\angus\angus-activation\2.0.1\angus-activation-2.0.1.jar;D:\Program Files\maven_repository\org\glassfish\jaxb\txw2\4.0.3\txw2-4.0.3.jar;D:\Program Files\maven_repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter-validation\3.1.0\spring-boot-starter-validation-3.1.0.jar;D:\Program Files\maven_repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.10\tomcat-embed-el-10.1.10.jar;D:\Program Files\maven_repository\org\hibernate\validator\hibernate-validator\8.0.0.Final\hibernate-validator-8.0.0.Final.jar;D:\Program Files\maven_repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\Program Files\maven_repository\org\jboss\logging\jboss-logging\3.5.1.Final\jboss-logging-3.5.1.Final.jar;D:\Program Files\maven_repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;D:\Program Files\maven_repository\com\baomidou\mybatis-plus-boot-starter\3.5.5\mybatis-plus-boot-starter-3.5.5.jar;D:\Program Files\maven_repository\com\baomidou\mybatis-plus\3.5.5\mybatis-plus-3.5.5.jar;D:\Program Files\maven_repository\com\baomidou\mybatis-plus-core\3.5.5\mybatis-plus-core-3.5.5.jar;D:\Program Files\maven_repository\com\baomidou\mybatis-plus-annotation\3.5.5\mybatis-plus-annotation-3.5.5.jar;D:\Program Files\maven_repository\com\baomidou\mybatis-plus-extension\3.5.5\mybatis-plus-extension-3.5.5.jar;D:\Program Files\maven_repository\org\mybatis\mybatis\3.5.15\mybatis-3.5.15.jar;D:\Program Files\maven_repository\com\github\jsqlparser\jsqlparser\4.6\jsqlparser-4.6.jar;D:\Program Files\maven_repository\org\mybatis\mybatis-spring\2.1.2\mybatis-spring-2.1.2.jar;D:\Program Files\maven_repository\com\baomidou\mybatis-plus-spring-boot-autoconfigure\3.5.5\mybatis-plus-spring-boot-autoconfigure-3.5.5.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter-jdbc\3.1.1\spring-boot-starter-jdbc-3.1.1.jar;D:\Program Files\maven_repository\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;D:\Program Files\maven_repository\org\springframework\spring-jdbc\6.0.10\spring-jdbc-6.0.10.jar;D:\Program Files\maven_repository\org\springframework\spring-tx\6.0.10\spring-tx-6.0.10.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter-aop\3.1.1\spring-boot-starter-aop-3.1.1.jar;D:\Program Files\maven_repository\org\springframework\spring-aop\6.0.10\spring-aop-6.0.10.jar;D:\Program Files\maven_repository\org\springframework\spring-beans\6.0.10\spring-beans-6.0.10.jar;D:\Program Files\maven_repository\org\aspectj\aspectjweaver\1.9.19\aspectjweaver-1.9.19.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter-web\3.1.1\spring-boot-starter-web-3.1.1.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter-json\3.1.1\spring-boot-starter-json-3.1.1.jar;D:\Program Files\maven_repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.2\jackson-module-parameter-names-2.15.2.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter-tomcat\3.1.1\spring-boot-starter-tomcat-3.1.1.jar;D:\Program Files\maven_repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.10\tomcat-embed-core-10.1.10.jar;D:\Program Files\maven_repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.10\tomcat-embed-websocket-10.1.10.jar;D:\Program Files\maven_repository\org\springframework\spring-web\6.0.10\spring-web-6.0.10.jar;D:\Program Files\maven_repository\io\micrometer\micrometer-observation\1.11.1\micrometer-observation-1.11.1.jar;D:\Program Files\maven_repository\org\springframework\spring-webmvc\6.0.10\spring-webmvc-6.0.10.jar;D:\Program Files\maven_repository\org\springframework\spring-expression\6.0.10\spring-expression-6.0.10.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-starter-test\3.1.1\spring-boot-starter-test-3.1.1.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-test\3.1.1\spring-boot-test-3.1.1.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-test-autoconfigure\3.1.1\spring-boot-test-autoconfigure-3.1.1.jar;D:\Program Files\maven_repository\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;D:\Program Files\maven_repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.0\jakarta.xml.bind-api-4.0.0.jar;D:\Program Files\maven_repository\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;D:\Program Files\maven_repository\net\minidev\json-smart\2.4.11\json-smart-2.4.11.jar;D:\Program Files\maven_repository\net\minidev\accessors-smart\2.4.11\accessors-smart-2.4.11.jar;D:\Program Files\maven_repository\org\ow2\asm\asm\9.3\asm-9.3.jar;D:\Program Files\maven_repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;D:\Program Files\maven_repository\net\bytebuddy\byte-buddy\1.14.5\byte-buddy-1.14.5.jar;D:\Program Files\maven_repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\Program Files\maven_repository\org\junit\jupiter\junit-jupiter\5.9.3\junit-jupiter-5.9.3.jar;D:\Program Files\maven_repository\org\junit\jupiter\junit-jupiter-params\5.9.3\junit-jupiter-params-5.9.3.jar;D:\Program Files\maven_repository\org\junit\jupiter\junit-jupiter-engine\5.9.3\junit-jupiter-engine-5.9.3.jar;D:\Program Files\maven_repository\org\junit\platform\junit-platform-engine\1.9.3\junit-platform-engine-1.9.3.jar;D:\Program Files\maven_repository\org\mockito\mockito-core\4.8.1\mockito-core-4.8.1.jar;D:\Program Files\maven_repository\net\bytebuddy\byte-buddy-agent\1.14.5\byte-buddy-agent-1.14.5.jar;D:\Program Files\maven_repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\Program Files\maven_repository\org\mockito\mockito-junit-jupiter\4.8.1\mockito-junit-jupiter-4.8.1.jar;D:\Program Files\maven_repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\Program Files\maven_repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\Program Files\maven_repository\org\springframework\spring-test\6.0.10\spring-test-6.0.10.jar;D:\Program Files\maven_repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\Program Files\maven_repository\com\langfuse\langfuse-java\0.1.0\langfuse-java-0.1.0.jar;D:\Program Files\maven_repository\com\fasterxml\jackson\core\jackson-core\2.15.2\jackson-core-2.15.2.jar;D:\Program Files\maven_repository\com\fasterxml\jackson\core\jackson-databind\2.15.2\jackson-databind-2.15.2.jar;D:\Program Files\maven_repository\com\fasterxml\jackson\core\jackson-annotations\2.15.2\jackson-annotations-2.15.2.jar;D:\Program Files\maven_repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.2\jackson-datatype-jdk8-2.15.2.jar;D:\Program Files\maven_repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.2\jackson-datatype-jsr310-2.15.2.jar;D:\Program Files\maven_repository\com\squareup\okhttp3\okhttp\4.10.0\okhttp-4.10.0.jar;D:\Program Files\maven_repository\com\squareup\okio\okio-jvm\3.0.0\okio-jvm-3.0.0.jar;D:\Program Files\maven_repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.8.22\kotlin-stdlib-jdk8-1.8.22.jar;D:\Program Files\maven_repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.8.22\kotlin-stdlib-jdk7-1.8.22.jar;D:\Program Files\maven_repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.8.22\kotlin-stdlib-common-1.8.22.jar;D:\Program Files\maven_repository\org\jetbrains\kotlin\kotlin-stdlib\1.8.22\kotlin-stdlib-1.8.22.jar;D:\Program Files\maven_repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;D:\Program Files\maven_repository\org\junit\jupiter\junit-jupiter-api\5.9.3\junit-jupiter-api-5.9.3.jar;D:\Program Files\maven_repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\Program Files\maven_repository\org\junit\platform\junit-platform-commons\1.9.3\junit-platform-commons-1.9.3.jar;D:\Program Files\maven_repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\Program Files\maven_repository\org\springframework\boot\spring-boot-configuration-processor\3.1.1\spring-boot-configuration-processor-3.1.1.jar;D:\Program Files\maven_repository\org\projectlombok\lombok\1.18.28\lombok-1.18.28.jar;"/>
    <property name="test" value="StageDataStorageTest"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk-17"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="D:\work\beacon-tower\beacon-tower-ai-test"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire1492032365084963684\surefirebooter-20250724123559139_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="17.0.15+9-LTS-241"/>
    <property name="user.name" value="xuewen.wang"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="D:\Program Files\maven_repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="com.zaxxer.hikari.pool_number" value="1"/>
    <property name="java.version" value="17.0.15"/>
    <property name="user.dir" value="D:\work\beacon-tower\beacon-tower-ai-test"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="4808"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="D:\Program Files\Java\jdk-17\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.6\libnvvp;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.6\cudnn\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.6\extras\CUPTI\lib64;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\NVIDIA Corporation\Nsight Compute 2022.1.1\;D:\LenovoSoftstore\Bandizip\;D:\engine\Java\jdk1.8.0_291\bin;D:\engine\Maven\apache-maven-3.8.6\bin;D:\engine\MinGW\mingw64\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;D:\engine\NetSarang\Xshell 7\;D:\LenovoSoftstore\Tesseract-OCR;D:\Program Files\nvm;D:\Program Files\nodejs;D:\Program Files\Java\jdk-17\bin;C:\Program Files\Docker\Docker\resources\bin;D:\Program Files\apache-maven-3.9.9\bin;d:\Program Files\cursor\resources\app\bin;D:\Program Files\Git\cmd;D:\Program Files\nacos;D:\Program Files\MySQL\MySQL Server 8.4\bin;D:\Program Files\Redis\;D:\work\mcp\cunzhi;D:\Program Files\Python313\Scripts\;D:\Program Files\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\nvm;D:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\Program Files\cursor\resources\app\bin;D:\Program Files\Microsoft VS Code\bin;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.15+9-LTS-241"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testStageDataStorage" classname="com.kf.aitest.service.StageDataStorageTest" time="5.601">
    <system-out><![CDATA[12:36:00.484 [main] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils -- Could not detect default configuration classes for test class [com.kf.aitest.service.StageDataStorageTest]: StageDataStorageTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
12:36:00.647 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper -- Found @SpringBootConfiguration com.kf.aitest.BeaconTowerAiTestApplication for test class com.kf.aitest.service.StageDataStorageTest
2025-07-24T12:36:01.628+08:00  WARN 4808 --- [           main] c.a.nacos.client.logging.NacosLogging    : Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.1.1)

2025-07-24T12:36:01.719+08:00  WARN 4808 --- [           main] c.a.nacos.client.logging.NacosLogging    : Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
2025-07-24T12:36:01.732+08:00  INFO 4808 --- [           main] c.k.aitest.service.StageDataStorageTest  : Starting StageDataStorageTest using Java 17.0.15 with PID 4808 (started by xuewen.wang in D:\work\beacon-tower\beacon-tower-ai-test)
2025-07-24T12:36:01.733+08:00 DEBUG 4808 --- [           main] c.k.aitest.service.StageDataStorageTest  : Running with Spring Boot v3.1.1, Spring v6.0.10
2025-07-24T12:36:01.734+08:00  INFO 4808 --- [           main] c.k.aitest.service.StageDataStorageTest  : The following 1 profile is active: "test"
2025-07-24T12:36:03.131+08:00  INFO 4808 --- [           main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=e3b5c672-8f14-3d6d-8614-3747dd3995fd
2025-07-24T12:36:03.557+08:00 DEBUG 4808 --- [           main] c.k.a.s.impl.PromptTemplateServiceImpl   : 成功加载提示词模板: recognize
2025-07-24T12:36:03.559+08:00 DEBUG 4808 --- [           main] c.k.a.s.impl.PromptTemplateServiceImpl   : 成功加载提示词模板: extraction
2025-07-24T12:36:03.562+08:00 DEBUG 4808 --- [           main] c.k.a.s.impl.PromptTemplateServiceImpl   : 成功加载提示词模板: structured
2025-07-24T12:36:03.563+08:00 DEBUG 4808 --- [           main] c.k.a.s.impl.PromptTemplateServiceImpl   : 成功加载提示词模板: transformer
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        3.5.5 
2025-07-24T12:36:06.215+08:00  INFO 4808 --- [           main] c.k.aitest.service.StageDataStorageTest  : Started StageDataStorageTest in 5.34 seconds (process running for 6.664)
=== 测试阶段数据存储功能 ===
测试参数: taskId=test-stage-task-1753331766802, userId=test_user_stage, disableChunking=true
2025-07-24T12:36:06.809+08:00  INFO 4808 --- [ataComparison-1] c.k.a.s.impl.DataComparisonServiceImpl   : 开始数据对比任务: taskId=test-stage-task-1753331766802, ids=[test-stage-id-1]

================================================================================
🎬 数据对比任务开始
📋 任务ID: test-stage-task-1753331766802
📊 待处理ID数量: 1
⏰ 开始时间: 2025-07-24 12:36:06
================================================================================
2025-07-24T12:36:06.811+08:00  INFO 4808 --- [ataComparison-1] c.k.a.service.ComparisonProgressManager  : 初始化任务连接: taskId=test-stage-task-1753331766802, status=PENDING
2025-07-24T12:36:06.812+08:00 DEBUG 4808 --- [ataComparison-1] c.k.a.service.ComparisonProgressManager  : 缓存SSE事件: taskId=test-stage-task-1753331766802, event=progress, status=PENDING, 队列大小=1
2025-07-24T12:36:06.817+08:00  INFO 4808 --- [onPool-worker-1] c.k.a.s.impl.DataComparisonServiceImpl   : 开始处理ID: taskId=test-stage-task-1753331766802, id=test-stage-id-1, index=0
2025-07-24T12:36:06.818+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.ComparisonProgressManager  : 缓存SSE事件: taskId=test-stage-task-1753331766802, event=progress, status=PENDING, 队列大小=2

🚀 开始处理阶段: 文档识别阶段 (ID: test-stage-id-1)
----------------------------------------
2025-07-24T12:36:06.818+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.ComparisonProgressManager  : 缓存SSE事件: taskId=test-stage-task-1753331766802, event=progress, status=PENDING, 队列大小=3
2025-07-24T12:36:06.827+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.impl.DataFetchServiceImpl  : 构建的URL: https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=test-stage-id-1/recognize/content/content.md
2025-07-24T12:36:06.828+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.impl.DataFetchServiceImpl  : 正在获取数据: https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=test-stage-id-1/recognize/content/content.md
2025-07-24T12:36:07.160+08:00 ERROR 4808 --- [onPool-worker-1] c.k.a.service.impl.DataFetchServiceImpl  : 从URL获取数据失败: https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=test-stage-id-1/recognize/content/content.md, error: 400 Bad Request: "{"error":"文件不存在"}"
2025-07-24T12:36:07.161+08:00 ERROR 4808 --- [onPool-worker-1] c.k.a.service.impl.DataFetchServiceImpl  : 获取阶段数据失败: id=test-stage-id-1, stage=recognize, error=获取数据失败: 400 Bad Request: "{"error":"文件不存在"}"

java.lang.RuntimeException: 获取数据失败: 400 Bad Request: "{"error":"文件不存在"}"
	at com.kf.aitest.service.impl.DataFetchServiceImpl.fetchDataFromUrl(DataFetchServiceImpl.java:140) ~[classes/:na]
	at com.kf.aitest.service.impl.DataFetchServiceImpl.fetchStageData(DataFetchServiceImpl.java:59) ~[classes/:na]
	at com.kf.aitest.service.impl.DataComparisonServiceImpl.processId(DataComparisonServiceImpl.java:159) ~[classes/:na]
	at com.kf.aitest.service.impl.DataComparisonServiceImpl.lambda$startComparison$0(DataComparisonServiceImpl.java:92) ~[classes/:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768) ~[na:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165) ~[na:na]
Caused by: org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":"文件不存在"}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:183) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:137) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:915) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:864) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:764) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:646) ~[spring-web-6.0.10.jar:6.0.10]
	at com.kf.aitest.service.impl.DataFetchServiceImpl.fetchDataFromUrl(DataFetchServiceImpl.java:123) ~[classes/:na]
	... 10 common frames omitted


------------------------------------------------------------
📊 阶段评估结果 - 文档识别阶段 (ID: test-stage-id-1)
------------------------------------------------------------
🔹 阶段名称: recognize
🔹 数据类型: md
🔹 处理状态: ❌ 失败
🔹 处理时间: 2025-07-24 12:36:06
🔹 处理耗时: 348 ms

🤖 AI评估结果: 未进行AI评估

❌ 错误信息: 获取数据失败: 400 Bad Request: "{"error":"文件不存在"}"
------------------------------------------------------------
2025-07-24T12:36:07.167+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.ComparisonProgressManager  : 缓存SSE事件: taskId=test-stage-task-1753331766802, event=stage_complete, status=PENDING, 队列大小=4

🚀 开始处理阶段: 信息提取阶段 (ID: test-stage-id-1)
----------------------------------------
2025-07-24T12:36:07.167+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.ComparisonProgressManager  : 缓存SSE事件: taskId=test-stage-task-1753331766802, event=progress, status=PENDING, 队列大小=5
2025-07-24T12:36:07.167+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.impl.DataFetchServiceImpl  : 构建的URL: https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=test-stage-id-1/extraction/extract_result.json
2025-07-24T12:36:07.168+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.impl.DataFetchServiceImpl  : 正在获取数据: https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=test-stage-id-1/extraction/extract_result.json
2025-07-24T12:36:07.186+08:00 ERROR 4808 --- [onPool-worker-1] c.k.a.service.impl.DataFetchServiceImpl  : 从URL获取数据失败: https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=test-stage-id-1/extraction/extract_result.json, error: 400 Bad Request: "{"error":"文件不存在"}"
2025-07-24T12:36:07.186+08:00 ERROR 4808 --- [onPool-worker-1] c.k.a.service.impl.DataFetchServiceImpl  : 获取阶段数据失败: id=test-stage-id-1, stage=extraction, error=获取数据失败: 400 Bad Request: "{"error":"文件不存在"}"

java.lang.RuntimeException: 获取数据失败: 400 Bad Request: "{"error":"文件不存在"}"
	at com.kf.aitest.service.impl.DataFetchServiceImpl.fetchDataFromUrl(DataFetchServiceImpl.java:140) ~[classes/:na]
	at com.kf.aitest.service.impl.DataFetchServiceImpl.fetchStageData(DataFetchServiceImpl.java:59) ~[classes/:na]
	at com.kf.aitest.service.impl.DataComparisonServiceImpl.processId(DataComparisonServiceImpl.java:159) ~[classes/:na]
	at com.kf.aitest.service.impl.DataComparisonServiceImpl.lambda$startComparison$0(DataComparisonServiceImpl.java:92) ~[classes/:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768) ~[na:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165) ~[na:na]
Caused by: org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":"文件不存在"}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:183) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:137) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:915) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:864) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:764) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:646) ~[spring-web-6.0.10.jar:6.0.10]
	at com.kf.aitest.service.impl.DataFetchServiceImpl.fetchDataFromUrl(DataFetchServiceImpl.java:123) ~[classes/:na]
	... 10 common frames omitted


------------------------------------------------------------
📊 阶段评估结果 - 信息提取阶段 (ID: test-stage-id-1)
------------------------------------------------------------
🔹 阶段名称: extraction
🔹 数据类型: json
🔹 处理状态: ❌ 失败
🔹 处理时间: 2025-07-24 12:36:07
🔹 处理耗时: 20 ms

🤖 AI评估结果: 未进行AI评估

❌ 错误信息: 获取数据失败: 400 Bad Request: "{"error":"文件不存在"}"
------------------------------------------------------------
2025-07-24T12:36:07.188+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.ComparisonProgressManager  : 缓存SSE事件: taskId=test-stage-task-1753331766802, event=stage_complete, status=PENDING, 队列大小=6

🚀 开始处理阶段: 结构化处理阶段 (ID: test-stage-id-1)
----------------------------------------
2025-07-24T12:36:07.188+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.ComparisonProgressManager  : 缓存SSE事件: taskId=test-stage-task-1753331766802, event=progress, status=PENDING, 队列大小=7
2025-07-24T12:36:07.189+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.impl.DataFetchServiceImpl  : 构建的URL: https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=test-stage-id-1/structured/structured.json
2025-07-24T12:36:07.189+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.impl.DataFetchServiceImpl  : 正在获取数据: https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=test-stage-id-1/structured/structured.json
2025-07-24T12:36:07.207+08:00 ERROR 4808 --- [onPool-worker-1] c.k.a.service.impl.DataFetchServiceImpl  : 从URL获取数据失败: https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=test-stage-id-1/structured/structured.json, error: 400 Bad Request: "{"error":"文件不存在"}"
2025-07-24T12:36:07.207+08:00 ERROR 4808 --- [onPool-worker-1] c.k.a.service.impl.DataFetchServiceImpl  : 获取阶段数据失败: id=test-stage-id-1, stage=structured, error=获取数据失败: 400 Bad Request: "{"error":"文件不存在"}"

java.lang.RuntimeException: 获取数据失败: 400 Bad Request: "{"error":"文件不存在"}"
	at com.kf.aitest.service.impl.DataFetchServiceImpl.fetchDataFromUrl(DataFetchServiceImpl.java:140) ~[classes/:na]
	at com.kf.aitest.service.impl.DataFetchServiceImpl.fetchStageData(DataFetchServiceImpl.java:59) ~[classes/:na]
	at com.kf.aitest.service.impl.DataComparisonServiceImpl.processId(DataComparisonServiceImpl.java:159) ~[classes/:na]
	at com.kf.aitest.service.impl.DataComparisonServiceImpl.lambda$startComparison$0(DataComparisonServiceImpl.java:92) ~[classes/:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768) ~[na:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165) ~[na:na]
Caused by: org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":"文件不存在"}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:183) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:137) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:915) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:864) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:764) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:646) ~[spring-web-6.0.10.jar:6.0.10]
	at com.kf.aitest.service.impl.DataFetchServiceImpl.fetchDataFromUrl(DataFetchServiceImpl.java:123) ~[classes/:na]
	... 10 common frames omitted


------------------------------------------------------------
📊 阶段评估结果 - 结构化处理阶段 (ID: test-stage-id-1)
------------------------------------------------------------
🔹 阶段名称: structured
🔹 数据类型: json
🔹 处理状态: ❌ 失败
🔹 处理时间: 2025-07-24 12:36:07
🔹 处理耗时: 20 ms

🤖 AI评估结果: 未进行AI评估

❌ 错误信息: 获取数据失败: 400 Bad Request: "{"error":"文件不存在"}"
------------------------------------------------------------
2025-07-24T12:36:07.209+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.ComparisonProgressManager  : 缓存SSE事件: taskId=test-stage-task-1753331766802, event=stage_complete, status=PENDING, 队列大小=8

🚀 开始处理阶段: 数据转换阶段 (ID: test-stage-id-1)
----------------------------------------
2025-07-24T12:36:07.209+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.ComparisonProgressManager  : 缓存SSE事件: taskId=test-stage-task-1753331766802, event=progress, status=PENDING, 队列大小=9
2025-07-24T12:36:07.209+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.impl.DataFetchServiceImpl  : 构建的URL: https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=test-stage-id-1/transformer/transformer.json
2025-07-24T12:36:07.209+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.impl.DataFetchServiceImpl  : 正在获取数据: https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=test-stage-id-1/transformer/transformer.json
2025-07-24T12:36:07.227+08:00 ERROR 4808 --- [onPool-worker-1] c.k.a.service.impl.DataFetchServiceImpl  : 从URL获取数据失败: https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=test-stage-id-1/transformer/transformer.json, error: 400 Bad Request: "{"error":"文件不存在"}"
2025-07-24T12:36:07.228+08:00 ERROR 4808 --- [onPool-worker-1] c.k.a.service.impl.DataFetchServiceImpl  : 获取阶段数据失败: id=test-stage-id-1, stage=transformer, error=获取数据失败: 400 Bad Request: "{"error":"文件不存在"}"

java.lang.RuntimeException: 获取数据失败: 400 Bad Request: "{"error":"文件不存在"}"
	at com.kf.aitest.service.impl.DataFetchServiceImpl.fetchDataFromUrl(DataFetchServiceImpl.java:140) ~[classes/:na]
	at com.kf.aitest.service.impl.DataFetchServiceImpl.fetchStageData(DataFetchServiceImpl.java:59) ~[classes/:na]
	at com.kf.aitest.service.impl.DataComparisonServiceImpl.processId(DataComparisonServiceImpl.java:159) ~[classes/:na]
	at com.kf.aitest.service.impl.DataComparisonServiceImpl.lambda$startComparison$0(DataComparisonServiceImpl.java:92) ~[classes/:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768) ~[na:na]
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622) ~[na:na]
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165) ~[na:na]
Caused by: org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":"文件不存在"}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:183) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:137) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:915) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:864) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:764) ~[spring-web-6.0.10.jar:6.0.10]
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:646) ~[spring-web-6.0.10.jar:6.0.10]
	at com.kf.aitest.service.impl.DataFetchServiceImpl.fetchDataFromUrl(DataFetchServiceImpl.java:123) ~[classes/:na]
	... 10 common frames omitted


------------------------------------------------------------
📊 阶段评估结果 - 数据转换阶段 (ID: test-stage-id-1)
------------------------------------------------------------
🔹 阶段名称: transformer
🔹 数据类型: json
🔹 处理状态: ❌ 失败
🔹 处理时间: 2025-07-24 12:36:07
🔹 处理耗时: 20 ms

🤖 AI评估结果: 未进行AI评估

❌ 错误信息: 获取数据失败: 400 Bad Request: "{"error":"文件不存在"}"
------------------------------------------------------------
2025-07-24T12:36:07.230+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.ComparisonProgressManager  : 缓存SSE事件: taskId=test-stage-task-1753331766802, event=stage_complete, status=PENDING, 队列大小=10
2025-07-24T12:36:07.238+08:00  INFO 4808 --- [onPool-worker-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-24T12:36:07.542+08:00  INFO 4808 --- [onPool-worker-1] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5c42c8b2
2025-07-24T12:36:07.546+08:00  INFO 4808 --- [onPool-worker-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-24T12:36:07.553+08:00  INFO 4808 --- [onPool-worker-1] k.a.s.i.DataComparisonStorageServiceImpl : 保存对比结果: taskId=test-stage-task-1753331766802, userId=test_user_stage
2025-07-24T12:36:07.678+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.dao.TDataComparisonMapper.insert   : ==>  Preparing: INSERT INTO t_data_comparison ( task_id, user_id, comparison_ids, overall_status, total_duration, start_time, end_time, success_stage_count, total_stage_count, error_messages, create_time, update_time, status ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-07-24T12:36:07.890+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.dao.TDataComparisonMapper.insert   : ==> Parameters: test-stage-task-1753331766802(String), test_user_stage(String), test-stage-id-1(String), FAILED(String), 412(Long), 2025-07-24T12:36:06.817989800(LocalDateTime), 2025-07-24T12:36:07.230597(LocalDateTime), 0(Integer), 4(Integer), ["阶段recognize失败: 获取数据失败: 400 Bad Request: \"{\"error\":\"文件不存在\"}\"","阶段extraction失败: 获取数据失败: 400 Bad Request: \"{\"error\":\"文件不存在\"}\"","阶段structured失败: 获取数据失败: 400 Bad Request: \"{\"error\":\"文件不存在\"}\"","阶段transformer失败: 获取数据失败: 400 Bad Request: \"{\"error\":\"文件不存在\"}\""](String), null, null, 0(Integer)
2025-07-24T12:36:07.900+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.dao.TDataComparisonMapper.insert   : <==    Updates: 1
2025-07-24T12:36:07.915+08:00  INFO 4808 --- [onPool-worker-1] k.a.s.i.DataComparisonStorageServiceImpl : 对比结果保存成功: id=2
2025-07-24T12:36:07.920+08:00  INFO 4808 --- [onPool-worker-1] c.k.a.s.impl.DataComparisonServiceImpl   : 对比结果已保存到数据库: taskId=test-stage-task-1753331766802, id=test-stage-id-1, comparisonId=2
2025-07-24T12:36:07.920+08:00  INFO 4808 --- [onPool-worker-1] k.a.s.i.DataComparisonStorageServiceImpl : 批量保存阶段结果: comparisonId=2, count=4
2025-07-24T12:36:07.921+08:00  INFO 4808 --- [onPool-worker-1] k.a.s.i.DataComparisonStorageServiceImpl : 保存阶段结果: comparisonId=2, dataId=data_1753331767920, stage=recognize
2025-07-24T12:36:07.923+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.d.T.insert                         : ==>  Preparing: INSERT INTO t_data_comparison_stage ( comparison_id, data_id, stage_name, data_type, stage_status, error_message, duration, fetch_time, create_time, update_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-07-24T12:36:07.924+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.d.T.insert                         : ==> Parameters: 2(Long), data_1753331767920(String), recognize(String), md(String), FAILED(String), 获取数据失败: 400 Bad Request: "{"error":"文件不存在"}"(String), 348(Long), 2025-07-24T12:36:06.818989400(LocalDateTime), null, null
2025-07-24T12:36:07.929+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.d.T.insert                         : <==    Updates: 1
2025-07-24T12:36:07.930+08:00  INFO 4808 --- [onPool-worker-1] k.a.s.i.DataComparisonStorageServiceImpl : 阶段结果保存成功: id=1
2025-07-24T12:36:07.930+08:00  INFO 4808 --- [onPool-worker-1] k.a.s.i.DataComparisonStorageServiceImpl : 保存阶段结果: comparisonId=2, dataId=data_1753331767930, stage=extraction
2025-07-24T12:36:07.931+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.d.T.insert                         : ==>  Preparing: INSERT INTO t_data_comparison_stage ( comparison_id, data_id, stage_name, data_type, stage_status, error_message, duration, fetch_time, create_time, update_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-07-24T12:36:07.934+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.d.T.insert                         : ==> Parameters: 2(Long), data_1753331767930(String), extraction(String), json(String), FAILED(String), 获取数据失败: 400 Bad Request: "{"error":"文件不存在"}"(String), 20(Long), 2025-07-24T12:36:07.167618(LocalDateTime), null, null
2025-07-24T12:36:07.935+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.d.T.insert                         : <==    Updates: 1
2025-07-24T12:36:07.936+08:00  INFO 4808 --- [onPool-worker-1] k.a.s.i.DataComparisonStorageServiceImpl : 阶段结果保存成功: id=2
2025-07-24T12:36:07.936+08:00  INFO 4808 --- [onPool-worker-1] k.a.s.i.DataComparisonStorageServiceImpl : 保存阶段结果: comparisonId=2, dataId=data_1753331767936, stage=structured
2025-07-24T12:36:07.937+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.d.T.insert                         : ==>  Preparing: INSERT INTO t_data_comparison_stage ( comparison_id, data_id, stage_name, data_type, stage_status, error_message, duration, fetch_time, create_time, update_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-07-24T12:36:07.941+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.d.T.insert                         : ==> Parameters: 2(Long), data_1753331767936(String), structured(String), json(String), FAILED(String), 获取数据失败: 400 Bad Request: "{"error":"文件不存在"}"(String), 20(Long), 2025-07-24T12:36:07.188657400(LocalDateTime), null, null
2025-07-24T12:36:07.943+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.d.T.insert                         : <==    Updates: 1
2025-07-24T12:36:07.943+08:00  INFO 4808 --- [onPool-worker-1] k.a.s.i.DataComparisonStorageServiceImpl : 阶段结果保存成功: id=3
2025-07-24T12:36:07.943+08:00  INFO 4808 --- [onPool-worker-1] k.a.s.i.DataComparisonStorageServiceImpl : 保存阶段结果: comparisonId=2, dataId=data_1753331767943, stage=transformer
2025-07-24T12:36:07.944+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.d.T.insert                         : ==>  Preparing: INSERT INTO t_data_comparison_stage ( comparison_id, data_id, stage_name, data_type, stage_status, error_message, duration, fetch_time, create_time, update_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-07-24T12:36:07.945+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.d.T.insert                         : ==> Parameters: 2(Long), data_1753331767943(String), transformer(String), json(String), FAILED(String), 获取数据失败: 400 Bad Request: "{"error":"文件不存在"}"(String), 20(Long), 2025-07-24T12:36:07.209292100(LocalDateTime), null, null
2025-07-24T12:36:07.947+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.d.T.insert                         : <==    Updates: 1
2025-07-24T12:36:07.947+08:00  INFO 4808 --- [onPool-worker-1] k.a.s.i.DataComparisonStorageServiceImpl : 阶段结果保存成功: id=4
2025-07-24T12:36:07.947+08:00  INFO 4808 --- [onPool-worker-1] k.a.s.i.DataComparisonStorageServiceImpl : 批量保存阶段结果完成: count=4
2025-07-24T12:36:07.950+08:00  INFO 4808 --- [onPool-worker-1] c.k.a.s.impl.DataComparisonServiceImpl   : 阶段数据已保存到数据库: comparisonId=2, stageCount=4
2025-07-24T12:36:07.950+08:00 DEBUG 4808 --- [onPool-worker-1] c.k.a.service.ComparisonProgressManager  : 缓存SSE事件: taskId=test-stage-task-1753331766802, event=comparison_result, status=PENDING, 队列大小=11
2025-07-24T12:36:07.950+08:00  INFO 4808 --- [onPool-worker-1] c.k.a.s.impl.DataComparisonServiceImpl   : 完成ID处理: id=test-stage-id-1, status=FAILED, successStages=0/4
2025-07-24T12:36:07.950+08:00 DEBUG 4808 --- [ataComparison-1] c.k.a.service.ComparisonProgressManager  : 缓存SSE事件: taskId=test-stage-task-1753331766802, event=complete, status=PENDING, 队列大小=12
2025-07-24T12:36:08.964+08:00  INFO 4808 --- [ataComparison-1] c.k.a.service.ComparisonProgressManager  : 清理SSE资源: taskId=test-stage-task-1753331766802, 状态=PENDING, 缓存消息数量=12
2025-07-24T12:36:08.964+08:00  INFO 4808 --- [ataComparison-1] c.k.a.s.impl.DataComparisonServiceImpl   : 数据对比任务完成: taskId=test-stage-task-1753331766802

================================================================================
🎉 数据对比任务完成
📋 任务ID: test-stage-task-1753331766802
⏰ 完成时间: 2025-07-24 12:36:08
================================================================================
✅ 阶段数据存储测试完成
请检查数据库表:
1. t_data_comparison - 主表数据
2. t_data_comparison_stage - 阶段数据
]]></system-out>
  </testcase>
  <testcase name="testChunkingDisabled" classname="com.kf.aitest.service.StageDataStorageTest" time="0.004">
    <system-out><![CDATA[=== 测试禁用分片功能 ===
测试1 - 禁用分片: disableChunking=true
测试2 - 启用分片: disableChunking=false
测试3 - 默认值: disableChunking=false
✅ 分片设置测试通过
]]></system-out>
  </testcase>
  <testcase name="testUserIdAndChunkingSettings" classname="com.kf.aitest.service.StageDataStorageTest" time="0.003">
    <system-out><![CDATA[=== 测试用户ID和分片设置组合 ===
场景1 - 完整设置: userId=explicit_user, disableChunking=true
场景2 - 只设置用户ID: userId=user_only, disableChunking=false
场景3 - 只设置分片选项: userId=null, disableChunking=false
✅ 组合设置测试完成
]]></system-out>
  </testcase>
</testsuite>