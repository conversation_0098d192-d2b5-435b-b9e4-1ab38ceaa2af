<template>
    <el-select
        v-model="internalValue"
        placeholder="请选择动作类型"
        size="default"
        @change="handleChange"
    >
        <el-option
            v-for="item in props.actionTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
        />
    </el-select>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits } from 'vue'

const props = defineProps<{
    actionTypeOptions: Array<{label: string, value: string}>
}>()

const emits = defineEmits<{
    (e: 'update:modelValue', val: string): void
    (e: 'change', val: string): void
}>()

// 设置默认值为第一个选项的值（如果存在）
const internalValue = ref(props.actionTypeOptions[0]?.value || '')

// 当下拉框选中值变化时，emit 给父组件
function handleChange(value: string) {
    emits('update:modelValue', value)
    emits('change', value)
}

</script>
