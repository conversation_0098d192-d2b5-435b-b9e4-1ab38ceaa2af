<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kf.userservice.dao.TMenuDao">
    <resultMap id="BaseResultMap" type="com.kf.userservice.entity.TMenu">
        <!--@Table t_menu-->
        <result property="menuId" column="menu_id" jdbcType="INTEGER"/>
        <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
        <result property="menuName" column="menu_name" jdbcType="VARCHAR"/>
        <result property="path" column="path" jdbcType="VARCHAR"/>
        <result property="component" column="component" jdbcType="VARCHAR"/>
        <result property="redirect" column="redirect" jdbcType="VARCHAR"/>
        <result property="perms" column="perms" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="icon" column="icon" jdbcType="VARCHAR"/>
        <result property="activeMenu" column="active_menu" jdbcType="VARCHAR"/>
        <result property="alwaysShow" column="always_show" jdbcType="INTEGER"/>
        <result property="hidden" column="hidden" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="orderMun" column="order_mun" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询菜单列表-->
    <select id="queryMenuByUserId" resultMap="BaseResultMap">
        SELECT menu_id,
               parent_id,
               menu_name,
               path,
               component,
               redirect,
               perms,
               title,
               icon,
               active_menu,
               always_show,
               hidden,
               type,
               order_mun,
               create_time,
               modify_time,
               status
        FROM t_menu
        WHERE status = 0
          AND menu_id IN
              (SELECT menu_id
               FROM t_role_menu
               WHERE role_id IN
                     (SELECT role_id
                      FROM t_user_role
                      WHERE user_id = #{userId}))
        ORDER BY order_mun
    </select>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select menu_id,
               parent_id,
               menu_name,
               path,
               component,
               redirect,
               perms,
               title,
               icon,
               active_menu,
               always_show,
               hidden,
               type,
               order_mun,
               create_time,
               modify_time,
               status
        from t_menu
        where menu_id = #{menuId}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from t_menu
        <where>
            <if test="menuId != null">
                and menu_id = #{menuId}
            </if>
            <if test="parentId != null">
                and parent_id = #{parentId}
            </if>
            <if test="menuName != null and menuName != ''">
                and menu_name = #{menuName}
            </if>
            <if test="path != null and path != ''">
                and path = #{path}
            </if>
            <if test="component != null and component != ''">
                and component = #{component}
            </if>
            <if test="redirect != null and redirect != ''">
                and redirect = #{redirect}
            </if>
            <if test="perms != null and perms != ''">
                and perms = #{perms}
            </if>
            <if test="title != null and title != ''">
                and title = #{title}
            </if>
            <if test="icon != null and icon != ''">
                and icon = #{icon}
            </if>
            <if test="activeMenu != null and activeMenu != ''">
                and active_menu = #{activeMenu}
            </if>
            <if test="alwaysShow != null">
                and always_show = #{alwaysShow}
            </if>
            <if test="hidden != null">
                and hidden = #{hidden}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="orderMun != null">
                and order_mun = #{orderMun}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="menuId" useGeneratedKeys="true">
        insert into t_menu(parent_id, menu_name, path, component, redirect, perms, title, icon, active_menu,
                           always_show, hidden, type, order_mun, create_time, modify_time, status)
        values (#{parentId}, #{menuName}, #{path}, #{component}, #{redirect}, #{perms}, #{title}, #{icon},
                #{activeMenu}, #{alwaysShow}, #{hidden}, #{type}, #{orderMun}, #{createTime}, #{modifyTime}, #{status})
    </insert>

    <insert id="insertBatch" keyProperty="menuId" useGeneratedKeys="true">
        insert into t_menu(parent_id, menu_name, path, component, redirect, perms, title, icon, active_menu,
                           always_show, hidden, type, order_mun, create_time, modify_time, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.parentId}, #{entity.menuName}, #{entity.path}, #{entity.component}, #{entity.redirect},
             #{entity.perms}, #{entity.title}, #{entity.icon}, #{entity.activeMenu}, #{entity.alwaysShow},
             #{entity.hidden}, #{entity.type}, #{entity.orderMun}, #{entity.createTime}, #{entity.modifyTime},
             #{entity.status})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="menuId" useGeneratedKeys="true">
        insert into t_menu(parent_id, menu_name, path, component, redirect, perms, title, icon, active_menu,
                           always_show, hidden, type, order_mun, create_time, modify_time, status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.parentId}, #{entity.menuName}, #{entity.path}, #{entity.component}, #{entity.redirect},
             #{entity.perms}, #{entity.title}, #{entity.icon}, #{entity.activeMenu}, #{entity.alwaysShow},
             #{entity.hidden}, #{entity.type}, #{entity.orderMun}, #{entity.createTime}, #{entity.modifyTime},
             #{entity.status})
        </foreach>
        on duplicate key update parent_id   = values(parent_id),
                                menu_name   = values(menu_name),
                                path        = values(path),
                                component   = values(component),
                                redirect    = values(redirect),
                                perms       = values(perms),
                                title       = values(title),
                                icon        = values(icon),
                                active_menu = values(active_menu),
                                always_show = values(always_show),
                                hidden      = values(hidden),
                                type        = values(type),
                                order_mun   = values(order_mun),
                                create_time = values(create_time),
                                modify_time = values(modify_time),
                                status      = values(status)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update t_menu
        <set>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="menuName != null and menuName != ''">
                menu_name = #{menuName},
            </if>
            <if test="path != null and path != ''">
                path = #{path},
            </if>
            <if test="component != null and component != ''">
                component = #{component},
            </if>
            <if test="redirect != null and redirect != ''">
                redirect = #{redirect},
            </if>
            <if test="perms != null and perms != ''">
                perms = #{perms},
            </if>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="icon != null and icon != ''">
                icon = #{icon},
            </if>
            <if test="activeMenu != null and activeMenu != ''">
                active_menu = #{activeMenu},
            </if>
            <if test="alwaysShow != null">
                always_show = #{alwaysShow},
            </if>
            <if test="hidden != null">
                hidden = #{hidden},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="orderMun != null">
                order_mun = #{orderMun},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
        </set>
        where menu_id = #{menuId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from t_menu
        where menu_id = #{menuId}
    </delete>
</mapper>

