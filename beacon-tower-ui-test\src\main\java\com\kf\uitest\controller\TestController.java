package com.kf.uitest.controller;

import com.kf.uitest.dto.TestExecutionRecordDTO;
import com.kf.uitest.dto.TestExecutionRequest;
import com.kf.uitest.exception.TestExecutionException;
import com.kf.uitest.executor.AsyncTestExecutor;
import com.kf.uitest.model.TestResult;
import com.kf.uitest.service.UiTestExecutionRecordService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/api/test")
@Slf4j
@Validated
public class TestController {

    @Resource
    private UiTestExecutionRecordService executionRecordService;

    @Resource
    private AsyncTestExecutor asyncTestExecutor;

    /**
     * 启动测试执行
     */
    @PostMapping("/start")
    public ResponseEntity<TestExecutionRecordDTO> startTest(
            @RequestBody @Valid TestExecutionRequest request) {
        log.info("Starting test execution with request: {}", request);
        try {
            // 1. 创建执行记录
            String executionId = UUID.randomUUID().toString();
            TestExecutionRecordDTO record = executionRecordService.create(executionId, request.getTestCaseIds());

            // 2. 异步执行测试
            asyncTestExecutor.execute(executionId, request);

            return ResponseEntity.ok(record);
        } catch (Exception e) {
            log.error("Failed to start test execution", e);
            throw new TestExecutionException("Failed to start test execution", e);
        }
    }

    /**
     * 获取测试执行结果
     */
    @GetMapping("/result/{executionId}")
    public ResponseEntity<TestResult> getTestResult(@PathVariable @NotBlank String executionId) {
        TestResult result = executionRecordService.getResult(executionId);
        return ResponseEntity.ok(result);
    }

    /**
     * 停止测试执行
     */
    @PostMapping("/stop/{executionId}")
    public ResponseEntity<Void> stopTest(@PathVariable @NotBlank String executionId) {
        // TODO: 实现停止测试执行的逻辑
        return ResponseEntity.ok().build();
    }

    /**
     * 重试失败的测试
     */
    @PostMapping("/retry/{executionId}")
    public ResponseEntity<TestResult> retryFailedTests(@PathVariable @NotBlank String executionId) {
        // TODO: 实现重试失败测试的逻辑
        return ResponseEntity.ok(new TestResult(executionId));
    }

    /**
     * 获取测试执行状态
     */
    @GetMapping("/status/{executionId}")
    public ResponseEntity<String> getTestStatus(@PathVariable @NotBlank String executionId) {
        TestExecutionRecordDTO record = executionRecordService.getRecord(executionId);
        return ResponseEntity.ok(record.getStatus());
    }
}
