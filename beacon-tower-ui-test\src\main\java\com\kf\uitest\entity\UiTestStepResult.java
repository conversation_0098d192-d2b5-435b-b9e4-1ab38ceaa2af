package com.kf.uitest.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ui_test_step_result")
public class UiTestStepResult extends Model<UiTestStepResult> {

    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @TableField("test_case_result_id")
    private String testCaseResultId;

    @TableField("step_id")
    private String stepId;

    @TableField("step_order")
    private Integer stepOrder;

    @TableField("status")
    private Integer status = 0;

    @TableField("start_time")
    private LocalDateTime startTime;

    @TableField("end_time")
    private LocalDateTime endTime;

    @TableField("error_message")
    private String errorMessage;

    @TableField("screenshot_path")
    private String screenshotPath;

    @TableField("console_log")
    private String consoleLog;

    @TableField("network_log")
    private String networkLog;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;
}
