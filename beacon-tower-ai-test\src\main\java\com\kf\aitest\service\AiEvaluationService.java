package com.kf.aitest.service;

import com.kf.aitest.dto.StageDataDTO;
import com.kf.aitest.dto.DataComparisonResultDTO;

/**
 * AI评估服务接口
 */
public interface AiEvaluationService {

    /**
     * 评估单个阶段的数据对比
     *
     * @param taskId 任务ID，用于SSE推送
     * @param stageData 阶段数据
     * @return 更新后的阶段数据（包含AI评估结果）
     */
    StageDataDTO evaluateStageData(String taskId, StageDataDTO stageData);

    /**
     * 评估整体对比结果
     *
     * @param taskId 任务ID，用于SSE推送
     * @param comparisonResult 对比结果
     * @return 更新后的对比结果（包含整体AI评估）
     */
    DataComparisonResultDTO evaluateOverallResult(String taskId, DataComparisonResultDTO comparisonResult);

    /**
     * 构建阶段评估提示词
     *
     * @param stageName 阶段名称
     * @param uatData UAT数据
     * @param testData TEST数据
     * @return 完整提示词
     */
    String buildStagePrompt(String stageName, Object uatData, Object testData);

}
