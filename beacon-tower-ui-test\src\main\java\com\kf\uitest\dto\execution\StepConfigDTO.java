package com.kf.uitest.dto.execution;

import com.kf.uitest.enums.ActionType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StepConfigDTO {
    /**
     * 操作类型
     * @see com.kf.uitest.enums.ActionType
     */
    private ActionType action;
    
    /**
     * 目标元素定位配置
     */
    private String locator;
    
    /**
     * 输入值/操作参数
     */
    private String value;
    
    /**
     * 超时时间（秒）
     * 默认：30
     */
    private Integer timeout;
    
    /**
     * 重试次数
     * 默认：3
     */
    private Integer retryTimes;
    
    /**
     * 重试间隔（毫秒）
     * 默认：1000
     */
    private Long retryInterval;
    
    /**
     * 断言配置
     */
    private List<AssertionConfigDTO> assertions;
} 