package com.kf.uitest.model;

import com.kf.uitest.enums.TestStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupResult {
    private String groupId;
    private TestStatus status;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Long duration;
    private String errorMessage;
    
    @Builder.Default
    private List<CaseResult> caseResults = new ArrayList<>();
    
    public GroupResult(String groupId) {
        this.groupId = groupId;
        this.status = TestStatus.RUNNING;
        this.startTime = LocalDateTime.now();
    }
    
    public void addCaseResult(CaseResult caseResult) {
        this.caseResults.add(caseResult);
        updateStatus();
    }
    
    public void complete() {
        this.endTime = LocalDateTime.now();
        this.duration = ChronoUnit.MILLIS.between(startTime, endTime);
        updateStatus();
    }
    
    private void updateStatus() {
        if (caseResults.isEmpty()) {
            return;
        }
        
        boolean hasFailures = caseResults.stream()
            .anyMatch(result -> result.getStatus() == TestStatus.FAILED);
            
        this.status = hasFailures ? TestStatus.FAILED : TestStatus.SUCCESS;
    }
} 