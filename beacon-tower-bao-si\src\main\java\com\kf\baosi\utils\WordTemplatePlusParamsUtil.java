package com.kf.baosi.utils;


import java.util.HashMap;
import java.util.Map;

public class WordTemplatePlusParamsUtil {

    private static final String accessKey = "VQf9KNJP";
    private static final String secretKey = "54286d7d9fac53e0ce125a59189987d74f368657";

    public static Map<String, Object> buildWordTemplateParams(String fileId, String fileName, String jsonMap) {
        Map<String, Object> extraParams = new HashMap<>();
        extraParams.put("convertPdf", false);
        extraParams.put("fileId", fileId);
        extraParams.put("fileName", fileName);
        extraParams.put("jsonMap", JsonUtil.toJson(JsonUtil.fromJson(jsonMap, Object.class)));
        return buildParams(extraParams);
    }

    public static Map<String, Object> buildWordTemplateParams(String fileId, String jsonMap) {
        Map<String, Object> extraParams = new HashMap<>();
        extraParams.put("convertPdf", false);
        extraParams.put("fileId", fileId);
        extraParams.put("jsonMap", JsonUtil.toJson(JsonUtil.fromJson(jsonMap, Object.class)));
        return buildParams(extraParams);
    }

    public static Map<String, Object> buildTaskWordTemplateParams(String taskId) {
        Map<String, Object> extraParams = new HashMap<>();
        extraParams.put("taskId", taskId);
        return buildParams(extraParams);
    }

    private static Map<String, Object> buildParams(Map<String, Object> extraParams) {
        long time = System.currentTimeMillis();
        Map<String, Object> params = new HashMap<>();
        params.put("accessKey", accessKey);
        params.put("secretKey", secretKey);
        params.put("timestamp", time);
        if (extraParams != null) {
            params.putAll(extraParams);
        }
        String sign = wordTemplatePlusSignUtil.buildSign(params);
        params.remove("secretKey");
        params.put("sign", sign);
        return params;
    }
}
