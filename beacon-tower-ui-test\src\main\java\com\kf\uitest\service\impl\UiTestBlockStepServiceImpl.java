package com.kf.uitest.service.impl;

import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.kf.uitest.dao.UiTestBlockStepMapper;
import com.kf.uitest.entity.UiTestBlockStep;
import com.kf.uitest.service.UiTestBlockStepService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class UiTestBlockStepServiceImpl implements UiTestBlockStepService {
    
    @Resource
    private UiTestBlockStepMapper blockStepMapper;
    
    @Override
    public List<UiTestBlockStep> findByBlockId(String blockId) {
        return blockStepMapper.findByBlockId(blockId);
    }
    
    @Override
    public UiTestBlockStep create(UiTestBlockStep blockStep) {
        initializeBlockStep(blockStep);
        blockStepMapper.insert(blockStep);
        return blockStep;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UiTestBlockStep> batchCreate(List<UiTestBlockStep> blockSteps) {
        if (CollectionUtils.isEmpty(blockSteps)) {
            return Collections.emptyList();
        }
        
        String blockId = blockSteps.get(0).getBlockId();
        Integer startOrder = getNextOrder(blockId);
        
        for (int i = 0; i < blockSteps.size(); i++) {
            UiTestBlockStep blockStep = blockSteps.get(i);
            initializeBlockStep(blockStep);
            blockStep.setStepOrder(startOrder + i);
        }
        
        Db.saveBatch(blockSteps);
        return blockSteps;
    }
    
    @Override
    public boolean update(UiTestBlockStep blockStep) {
        blockStep.setUpdateTime(LocalDateTime.now());
        return blockStepMapper.updateById(blockStep) > 0;
    }
    
    @Override
    public boolean delete(String id) {
        return blockStepMapper.deleteById(id) > 0;
    }
    
    @Override
    public boolean deleteByBlockId(String blockId) {
        return Db.lambdaUpdate(UiTestBlockStep.class)
                .eq(UiTestBlockStep::getBlockId, blockId)
                .remove();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStepOrder(String blockId, String stepId, Integer newOrder) {
        UiTestBlockStep blockStep = blockStepMapper.selectById(stepId);
        if (blockStep == null || !blockStep.getBlockId().equals(blockId)) {
            return false;
        }
        
        Integer oldOrder = blockStep.getStepOrder();
        if (oldOrder.equals(newOrder)) {
            return true;
        }
        
        // 使用Db工具类更新顺序
        if (newOrder > oldOrder) {
            // 向后移动
            Db.lambdaUpdate(UiTestBlockStep.class)
                    .eq(UiTestBlockStep::getBlockId, blockId)
                    .gt(UiTestBlockStep::getStepOrder, oldOrder)
                    .le(UiTestBlockStep::getStepOrder, newOrder)
                    .setSql("step_order = step_order - 1")
                    .update();
        } else {
            // 向前移动
            Db.lambdaUpdate(UiTestBlockStep.class)
                    .eq(UiTestBlockStep::getBlockId, blockId)
                    .ge(UiTestBlockStep::getStepOrder, newOrder)
                    .lt(UiTestBlockStep::getStepOrder, oldOrder)
                    .setSql("step_order = step_order + 1")
                    .update();
        }
        
        blockStep.setStepOrder(newOrder);
        return update(blockStep);
    }
    
    private void initializeBlockStep(UiTestBlockStep blockStep) {
        LocalDateTime now = LocalDateTime.now();
        blockStep.setCreateTime(now);
        blockStep.setUpdateTime(now);
        
        if (blockStep.getStepOrder() == null) {
            blockStep.setStepOrder(getNextOrder(blockStep.getBlockId()));
        }
    }
    
    private Integer getNextOrder(String blockId) {
        return Db.lambdaQuery(UiTestBlockStep.class)
                .eq(UiTestBlockStep::getBlockId, blockId)
                .orderByDesc(UiTestBlockStep::getStepOrder)
                .last("LIMIT 1")
                .oneOpt()
                .map(step -> step.getStepOrder() + 1)
                .orElse(1);
    }
} 