package com.kf.uitest.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Getter
public enum HookParameterKey {
    // 断言相关参数
    ASSERT_TYPE("assertType", "断言类型", Arrays.asList("equals", "contains", "startsWith")),
    ASSERT_IGNORE_CASE("assertIgnoreCase", "忽略大小写", Arrays.asList("true", "false")),
    EXPECTED_VALUE("expectedValue", "期望值"),
    COMPARE_TYPE("compareType", "比较类型", Arrays.asList(
        "equals", "contains", "startswith", "endswith", 
        "greaterthan", "lessthan", "matches")),
    
    // 元素状态相关参数
    ELEMENT_STATE("elementState", "元素状态", Arrays.asList("visible", "hidden", "enabled", "disabled")),
    FRAME_SELECTOR("frameSelector", "Frame选择器"),
    
    // 数据库操作相关参数
    DB_SOURCE("dbSource", "数据源名称"),
    PARAMS("params", "参数"),
    DB_PARAMS("dbParams", "SQL参数"),
    RESULT_TYPE("resultType", "结果类型", Arrays.asList("single", "list", "count")),
    
    // 变量和提取相关参数
    VARIABLE_NAME("variableName", "变量名称"),
    VARIABLE_SCOPE("variableScope", "变量作用域", Arrays.asList("step", "case", "global")),
    EXTRACT_EXPRESSION("extractExpression", "提取表达式"),
    EXTRACT_SOURCE("extractSource", "提取来源", Arrays.asList("text", "value", "attribute")),
    ATTRIBUTE_NAME("attributeName", "属性名称"),
    
    // JavaScript相关参数
    JS_ARGS("jsArgs", "JavaScript参数"),
    JS_TIMEOUT("jsTimeout", "JavaScript执行超时"),
    
    // Python相关参数
    PYTHON_ARGS("pythonArgs", "Python参数"),
    PYTHON_ENV("pythonEnv", "Python环境变量"),
    
    // 通用参数
    CUSTOM_MESSAGE("customMessage", "自定义消息"),
    CONDITION("condition", "条件表达式"),
    VALIDATION("validation", "验证规则"),
    
    // 响应提取相关参数
    RESPONSE_TIMEOUT("responseTimeout", "响应超时时间(毫秒)"),
    URL_PATTERN("urlPattern", "URL匹配模式"),
    
    // 等待相关
    WAIT_TIME("waitTime", "等待时间(毫秒)"),
    TIMEOUT("timeout", "超时时间(毫秒)"),
    
    // 数据库相关
    SQL("sql", "SQL语句"),
    
    // 元素相关
    SELECTOR("selector", "元素选择器"),
    SHOULD_BE_VISIBLE("shouldBeVisible", "是否应该可见"),
    SHOULD_BE_ENABLED("shouldBeEnabled", "是否应该可用");

    private final String key;            // 参数键
    private final String description;    // 参数描述
    private final List<String> allowedValues;  // 允许的值列表（可选）

    /**
     * 构造函数 - 无允许值列表
     */
    HookParameterKey(String key, String description) {
        this(key, description, null);
    }

    /**
     * 构造函数 - 带允许值列表
     */
    HookParameterKey(String key, String description, List<String> allowedValues) {
        this.key = key;
        this.description = description;
        this.allowedValues = allowedValues;
    }

    /**
     * 检查值是否有效
     */
    public boolean isValidValue(String value) {
        return allowedValues == null || allowedValues.contains(value);
    }

    /**
     * 获取所有允许的值
     */
    public List<String> getAllowedValues() {
        return allowedValues != null ? Collections.unmodifiableList(allowedValues) : Collections.emptyList();
    }

    /**
     * 根据key获取枚举
     */
    public static Optional<HookParameterKey> fromKey(String key) {
        return Arrays.stream(values())
                .filter(param -> param.getKey().equals(key))
                .findFirst();
    }
}