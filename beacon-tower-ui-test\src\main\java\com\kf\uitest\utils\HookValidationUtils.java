package com.kf.uitest.utils;

import java.util.Set;

public class HookValidationUtils {

    // 场景级别允许的动作类型
    private static final Set<String> SCENARIO_ACTIONS = Set.of(
            "RUN_CASE",
            "RUN_SCENARIO"
    );

    // 用例级别允许的动作类型
    private static final Set<String> CASE_ACTIONS = Set.of(
            "RUN_CASE",
            "RUN_SCENARIO",
            "WAIT_TIME",
            "WAIT_ELEMENT",
            "WAIT_DISAPPEAR"
    );

    // 步骤级别允许的动作类型
    private static final Set<String> STEP_ACTIONS = Set.of(
            "ASSERT_URL",
            "ASSERT_TEXT",
            "ASSERT_ELEMENT_VISIBLE",
            "ASSERT_ELEMENT_ENABLED",
            "ASSERT_DB",
            "EXTRACT_ELEMENT",
            "EXTRACT_RESPONSE",
            "EXTRACT_DB",
            "WAIT_TIME",
            "WAIT_ELEMENT",
            "WAIT_DISAPPEAR",
            "DB_EXECUTE",
            "DB_SELECT",
            "DB_UPDATE",
            "EXECUTE_JAVASCRIPT",
            "EXECUTE_PYTHON"
    );

    /**
     * 验证动作类型是否合法
     */
    public static boolean isValidAction(String ownerType, String actionType) {
        if (ownerType == null || actionType == null) {
            return false;
        }

        return switch (ownerType.toUpperCase()) {
            case "SCENARIO" -> SCENARIO_ACTIONS.contains(actionType.toUpperCase());
            case "CASE" -> CASE_ACTIONS.contains(actionType.toUpperCase());
            case "STEP" -> STEP_ACTIONS.contains(actionType.toUpperCase());
            default -> false;
        };
    }
}