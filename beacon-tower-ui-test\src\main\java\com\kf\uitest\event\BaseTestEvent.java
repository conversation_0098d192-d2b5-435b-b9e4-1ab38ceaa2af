package com.kf.uitest.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public abstract class BaseTestEvent {
    private String executionId;
    private LocalDateTime timestamp;

    public BaseTestEvent(String executionId) {
        this.executionId = executionId;
        this.timestamp = LocalDateTime.now();
    }
}