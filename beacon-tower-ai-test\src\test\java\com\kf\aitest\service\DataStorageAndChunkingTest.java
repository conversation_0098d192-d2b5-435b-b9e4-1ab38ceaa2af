package com.kf.aitest.service;

import com.kf.aitest.dto.DataComparisonRequestDTO;
import com.kf.aitest.dto.StageDataDTO;
import com.kf.aitest.service.impl.AiEvaluationServiceImpl;
import com.kf.aitest.service.impl.DataComparisonStorageServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * 数据存储和分片功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class DataStorageAndChunkingTest {

    @Autowired
    private DataComparisonStorageService storageService;
    
    @Autowired
    private AiEvaluationServiceImpl aiEvaluationService;

    @Test
    public void testDataStorageWithUserId() {
        System.out.println("=== 测试数据存储功能 ===");
        
        // 创建测试请求
        DataComparisonRequestDTO request = new DataComparisonRequestDTO();
        request.setUserId("test_user_123");
        request.setIds(Arrays.asList("test-id-1"));
        request.setEnableAiEvaluation(true);
        
        System.out.printf("请求用户ID: %s%n", request.getUserId());
        
        // 验证用户ID不为null
        assert request.getUserId() != null : "用户ID不应为null";
        assert !"".equals(request.getUserId()) : "用户ID不应为空字符串";
        
        System.out.println("✅ 用户ID设置正确，数据存储应该正常工作");
    }
    
    @Test
    public void testChunkingDisabled() {
        System.out.println("=== 测试禁用分片功能 ===");
        
        // 创建测试数据
        StageDataDTO stageData = new StageDataDTO();
        stageData.setStageName("recognize");
        stageData.setStatus("SUCCESS");
        stageData.setFetchTime(LocalDateTime.now());
        
        // 创建大量数据来触发分片
        StringBuilder largeData = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            largeData.append("这是一段很长的测试数据，用于触发分片机制。");
        }
        
        stageData.setUatData(largeData.toString());
        stageData.setTestData(largeData.toString());
        
        System.out.printf("测试数据长度: %d 字符%n", largeData.length());
        
        // 测试启用分片（默认行为）
        System.out.println("\n--- 测试启用分片 ---");
        StageDataDTO result1 = aiEvaluationService.evaluateStageData("test-task-1", stageData, false);
        System.out.printf("启用分片结果: 阶段=%s, 状态=%s%n", result1.getStageName(), result1.getStatus());
        
        // 测试禁用分片
        System.out.println("\n--- 测试禁用分片 ---");
        StageDataDTO result2 = aiEvaluationService.evaluateStageData("test-task-2", stageData, true);
        System.out.printf("禁用分片结果: 阶段=%s, 状态=%s%n", result2.getStageName(), result2.getStatus());
        
        System.out.println("✅ 分片控制功能测试完成");
    }
    
    @Test
    public void testRequestDTOChunkingOption() {
        System.out.println("=== 测试请求DTO分片选项 ===");
        
        // 测试默认值
        DataComparisonRequestDTO request1 = new DataComparisonRequestDTO();
        System.out.printf("默认禁用分片设置: %s%n", request1.getDisableChunking());
        assert !request1.getDisableChunking() : "默认应该启用分片";
        
        // 测试设置禁用分片
        DataComparisonRequestDTO request2 = new DataComparisonRequestDTO();
        request2.setDisableChunking(true);
        System.out.printf("设置禁用分片: %s%n", request2.getDisableChunking());
        assert request2.getDisableChunking() : "应该禁用分片";
        
        System.out.println("✅ 请求DTO分片选项测试通过");
    }
    
    @Test
    public void testUserIdHandling() {
        System.out.println("=== 测试用户ID处理逻辑 ===");
        
        // 测试场景1：请求中已有userId
        DataComparisonRequestDTO request1 = new DataComparisonRequestDTO();
        request1.setUserId("existing_user");
        
        // 模拟控制器逻辑
        String headerUserId = "header_user";
        if (request1.getUserId() == null) {
            request1.setUserId(headerUserId != null ? headerUserId : "system_user");
        }
        
        System.out.printf("场景1 - 请求已有userId: %s%n", request1.getUserId());
        assert "existing_user".equals(request1.getUserId()) : "应该保持原有userId";
        
        // 测试场景2：请求中没有userId，从header获取
        DataComparisonRequestDTO request2 = new DataComparisonRequestDTO();
        
        if (request2.getUserId() == null) {
            request2.setUserId(headerUserId != null ? headerUserId : "system_user");
        }
        
        System.out.printf("场景2 - 从header获取userId: %s%n", request2.getUserId());
        assert "header_user".equals(request2.getUserId()) : "应该使用header中的userId";
        
        // 测试场景3：请求和header都没有userId
        DataComparisonRequestDTO request3 = new DataComparisonRequestDTO();
        String nullHeaderUserId = null;
        
        if (request3.getUserId() == null) {
            request3.setUserId(nullHeaderUserId != null ? nullHeaderUserId : "system_user");
        }
        
        System.out.printf("场景3 - 使用默认userId: %s%n", request3.getUserId());
        assert "system_user".equals(request3.getUserId()) : "应该使用默认userId";
        
        System.out.println("✅ 用户ID处理逻辑测试通过");
    }
}
