<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kf.accuratetest.dao.UserInterfaceAnnotationTaskDao">

    <resultMap id="BaseResultMap" type="com.kf.accuratetest.entity.UserInterfaceAnnotationTask">
        <!--@Table t_user_interface_annotation_task-->
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="repositoryUrl" column="repository_url" jdbcType="VARCHAR"/>
        <result property="branch" column="branch" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryByUserId" resultMap="BaseResultMap">
        select
        user_id, task_id, repository_url, branch, create_time, update_time, is_deleted
        from t_user_interface_annotation_task
        where user_id = #{userId}
        AND is_deleted = 0
        ORDER BY update_time DESC
    </select>

    <!--查询单个-->
    <select id="queryByTaskId" resultMap="BaseResultMap">
        select
        user_id, task_id, repository_url, branch, create_time, update_time, is_deleted
        from t_user_interface_annotation_task
        where task_id = #{taskId}
        AND is_deleted = 0
        ORDER BY update_time DESC
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
        user_id, task_id, repository_url, branch, create_time, update_time, is_deleted
        from t_user_interface_annotation_task
        <where>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="taskId != null and taskId != ''">
                and task_id = #{taskId}
            </if>
            <if test="repositoryUrl != null and repositoryUrl != ''">
                and repository_url = #{repositoryUrl}
            </if>
            <if test="branch != null and branch != ''">
                and branch = #{branch}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
    select
    user_id, task_id, repository_url, branch, create_time, update_time, is_deleted
    from t_user_interface_annotation_task
    <where>
        <if test="userId != null and userId != ''">
            and user_id = #{userId}
        </if>
        <if test="taskId != null and taskId != ''">
            and task_id = #{taskId}
        </if>
        <if test="repositoryUrl != null and repositoryUrl != ''">
            and repository_url = #{repositoryUrl}
        </if>
        <if test="branch != null and branch != ''">
            and branch = #{branch}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="isDeleted != null">
            and is_deleted = #{isDeleted}
        </if>
    </where>
    </select>
    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from t_user_interface_annotation_task
        <where>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="taskId != null and taskId != ''">
                and task_id = #{taskId}
            </if>
            <if test="repositoryUrl != null and repositoryUrl != ''">
                and repository_url = #{repositoryUrl}
            </if>
            <if test="branch != null and branch != ''">
                and branch = #{branch}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="" useGeneratedKeys="true">
        insert into t_user_interface_annotation_task(user_id, task_id, repository_url, branch, create_time, update_time, is_deleted)
        values (#{userId}, #{taskId}, #{repositoryUrl}, #{branch}, #{createTime}, #{updateTime}, #{isDeleted})
    </insert>

    <insert id="insertBatch" keyProperty="" useGeneratedKeys="true">
        insert into t_user_interface_annotation_task(user_id, task_id, repository_url, branch, create_time, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.taskId}, #{entity.repositoryUrl}, #{entity.branch}, #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="" useGeneratedKeys="true">
        insert into t_user_interface_annotation_task(user_id, task_id, repository_url, branch, create_time, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.taskId}, #{entity.repositoryUrl}, #{entity.branch}, #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
        on duplicate key update
        user_id = values(user_id),
        task_id = values(task_id),
        repository_url = values(repository_url),
        branch = values(branch),
        create_time = values(create_time),
        update_time = values(update_time),
        is_deleted = values(is_deleted)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update t_user_interface_annotation_task
        <set>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="taskId != null and taskId != ''">
                task_id = #{taskId},
            </if>
            <if test="repositoryUrl != null and repositoryUrl != ''">
                repository_url = #{repositoryUrl},
            </if>
            <if test="branch != null and branch != ''">
                branch = #{branch},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
        </set>
        where task_id = #{taskId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from t_user_interface_annotation_task where task_id = #{taskId}
    </delete>

</mapper>

