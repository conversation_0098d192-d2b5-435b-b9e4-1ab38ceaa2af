package com.kf.baosi.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.kf.baosi.utils.HtmlUnescapeDeserializer;
import lombok.Data;

/**
 * 测试运行数据
 */
@Data
public class TestRun {

    // 测试计划key
    private String testPlanKey;

    // 测试用例key
    private String testCaseKey;

    // 测试周期id
    private String testCycleId;

    // 测试用例id
    private String testCaseId;

    // testRunDetails
    private TestRunDetails testRunDetails;

    // 测试运行id
    private String id;

    // 测试运行总结果
    private String status;

    // 标题 解决html转义问题
    @JsonDeserialize(using = HtmlUnescapeDeserializer.class)
    private String summary;

    // 测试周期标题
    private String testCycleSummary;

    // 测试计划标题
    private String testPlanSummary;

    // 执行时间 2024/10/05 05:35
    private String executionOn;

    // 执行人
    private String executedBy;

}
