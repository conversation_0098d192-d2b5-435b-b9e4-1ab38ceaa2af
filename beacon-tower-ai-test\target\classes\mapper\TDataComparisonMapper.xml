<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kf.aitest.dao.TDataComparisonMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.kf.aitest.entity.TDataComparison">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="file_md5" property="fileMd5" jdbcType="VARCHAR"/>
        <result column="comparison_ids" property="comparisonIds" jdbcType="VARCHAR"/>
        <result column="overall_status" property="overallStatus" jdbcType="VARCHAR"/>
        <result column="overall_score" property="overallScore" jdbcType="INTEGER"/>
        <result column="overall_ai_evaluation" property="overallAiEvaluation" jdbcType="LONGVARCHAR"/>
        <result column="total_duration" property="totalDuration" jdbcType="BIGINT"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="success_stage_count" property="successStageCount" jdbcType="INTEGER"/>
        <result column="total_stage_count" property="totalStageCount" jdbcType="INTEGER"/>
        <result column="error_messages" property="errorMessages" jdbcType="LONGVARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, task_id, user_id, file_md5, comparison_ids, overall_status, overall_score,
        overall_ai_evaluation, total_duration, start_time, end_time, success_stage_count,
        total_stage_count, error_messages, create_time, update_time, status
    </sql>

    <!-- 分页查询数据对比记录 -->
    <select id="selectComparisonPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_data_comparison
        <where>
            status = 0
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            <if test="taskId != null and taskId != ''">
                AND task_id = #{taskId}
            </if>
            <if test="overallStatus != null and overallStatus != ''">
                AND overall_status = #{overallStatus}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据任务ID查询对比记录 -->
    <select id="selectByTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_data_comparison
        WHERE task_id = #{taskId} AND status = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据用户ID统计对比记录数量 -->
    <select id="countByUserId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_data_comparison
        WHERE user_id = #{userId} AND status = 0
    </select>

    <!-- 查询用户的最近对比记录 -->
    <select id="selectRecentByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_data_comparison
        WHERE user_id = #{userId} AND status = 0
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 统计各状态的对比记录数量 -->
    <select id="selectStatusStatistics" resultMap="BaseResultMap">
        SELECT
        overall_status,
        COUNT(*) as count
        FROM t_data_comparison
        WHERE status = 0
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        GROUP BY overall_status
        ORDER BY count DESC
    </select>

</mapper>
