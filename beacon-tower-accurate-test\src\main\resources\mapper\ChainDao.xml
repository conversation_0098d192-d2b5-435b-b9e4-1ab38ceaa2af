<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kf.accuratetest.dao.ChainDao">

    <resultMap type="com.kf.accuratetest.entity.Chain" id="TChainMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="messageId" column="message_id" jdbcType="VARCHAR"/>
        <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
        <result property="interfaceName" column="interface_name" jdbcType="VARCHAR"/>
        <result property="chain" column="chain" jdbcType="VARCHAR"/>
        <result property="source" column="source" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>
    <!--根据messageId查询-->
    <select id="queryByMessageId" resultMap="TChainMap">
        SELECT *
        FROM t_chain
        WHERE message_id = #{messageId}
    </select>

    <!--根据projectId查询-->
    <select id="queryByProjectId" resultMap="TChainMap">
        SELECT interface_name,chain
        FROM t_chain
        WHERE project_id = #{projectId}
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into t_chain(message_id, project_id, interface_name, chain, source, create_time, update_time,
                                   is_deleted)
        values (#{messageId}, #{projectId}, #{interfaceName}, #{chain}, #{source}, #{createTime}, #{updateTime},
                #{isDeleted})
    </insert>

    <update id="update">
        update t_chain
        <set>
            is_deleted = 1,update_time = #{updateTime}
        </set>
        where project_id = #{projectId} and interface_name = #{interfaceName} and source = #{source}
    </update>

    <delete id="delete">
        delete from t_chain
        where project_id = #{projectId} and source = #{source}
        <if test="interfaceName != null">
            and interface_name = #{interfaceName}
        </if>

    </delete>
</mapper>