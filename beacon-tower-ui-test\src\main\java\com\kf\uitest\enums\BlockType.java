package com.kf.uitest.enums;

import lombok.Getter;

/**
 * 步骤组类型枚举
 */
@Getter
public enum BlockType {
    /**
     * 普通步骤组
     * 按顺序执行的步骤集合
     */
    NORMAL("普通步骤组", "按顺序执行的步骤集合"),

    /**
     * 循环步骤组
     * 根据循环条件重复执行的步骤集合
     * 支持：固定次数循环、条件循环、集合遍历
     */
    LOOP("循环步骤组", "根据循环条件重复执行的步骤集合"),

    /**
     * 条件步骤组
     * 根据条件判断执行不同分支的步骤集合
     * 支持：if-else、switch-case 结构
     */
    CONDITION("条件步骤组", "根据条件判断执行不同分支的步骤集合");

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 类型描述
     */
    private final String description;

    BlockType(String name, String description) {
        this.name = name;
        this.description = description;
    }

    /**
     * 判断是否为循环块
     */
    public boolean isLoop() {
        return this == LOOP;
    }

    /**
     * 判断是否为条件块
     */
    public boolean isCondition() {
        return this == CONDITION;
    }

    /**
     * 判断是否为普通块
     */
    public boolean isNormal() {
        return this == NORMAL;
    }
} 