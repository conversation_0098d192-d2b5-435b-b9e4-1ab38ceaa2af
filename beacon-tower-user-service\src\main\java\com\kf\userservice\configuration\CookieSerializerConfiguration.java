package com.kf.userservice.configuration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.session.web.http.CookieSerializer;
import org.springframework.session.web.http.DefaultCookieSerializer;

@Slf4j
@Configuration
public class CookieSerializerConfiguration {

    /**
     * 在主域中储存Cookie，子域中共享Cookie
     */
    @Bean
    public CookieSerializer cookieSerializer() {

        // 默认 Cookie 序列化
        DefaultCookieSerializer defaultCookieSerializer = new DefaultCookieSerializer();

        // Cookie名字，默认为 SESSION
//        defaultCookieSerializer.setCookieName("SESSION");

        //设置超时时间是一年
//        defaultCookieSerializer.setCookieMaxAge(60 * 60 * 24 * 365);

        // 域，这允许跨子域共享cookie，默认设置是使用当前域。
        defaultCookieSerializer.setDomainName("localhost");

        // Cookie的路径。
        defaultCookieSerializer.setCookiePath("/");
        // 序列化
//        defaultCookieSerializer.setUseBase64Encoding(false);


        return defaultCookieSerializer;
    }

}


