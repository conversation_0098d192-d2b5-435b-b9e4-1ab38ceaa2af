package com.kf.baosi.dto.verifyDocument.OQT;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


@Data
public class OQTTestCaseListParamsWrapper {

    // 测试用例编号
    @JsonProperty("testCaseId")
    private String testCaseId;

    // 测试用例标题
    @JsonProperty("testCaseName")
    private String testCaseName;

    // 先决条件
    @JsonProperty("precondition")
    private String precondition = "NA";

    // 测试步骤 PS：该list的key值（文档填充时模板中的key）
    private OQTTestCaseInfoWrapper testCaseInfo;

    // 测试用例等级
    @JsonProperty("testCaseLevel")
    private String testCaseLevel;

    // 测试者
    @JsonProperty("tester")
    private String tester;

    // 结论
    @JsonProperty("conclusion")
    private String conclusion;

    // 测试日期
    @JsonProperty("testDate")
    private String testDate;

    // 审核者
    @JsonProperty("auditor")
    private String auditor;

    // 审核日期
    @JsonProperty("auditDate")
    private String auditDate;

    // 测试截图 该list的key值（填充时的key的名称）
    private OQTTestCasePictureWrapper imageList;

}
