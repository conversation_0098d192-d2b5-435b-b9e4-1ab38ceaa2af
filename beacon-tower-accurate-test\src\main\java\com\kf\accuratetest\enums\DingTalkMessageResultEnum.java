package com.kf.accuratetest.enums;

import lombok.Getter;

@Getter
public enum DingTalkMessageResultEnum {
    SUCCESS("0", "消息发送成功"),
    GROUP_DISBANDED("400013", "群已被解散，请向其他群发消息"),
    ACCESS_TOKEN_NOT_EXIST("400101", "access_token不存在，请确认access_token拼写是否正确"),
    ROBOT_DISABLED("400102", "机器人已停用，请联系管理员启用机器人"),
    UNSUPPORTED_MSG_TYPE("400105", "不支持的消息类型，请使用文档中支持的消息类型"),
    ROBOT_NOT_EXIST("400106", "机器人不存在，请确认机器人是否在群中"),
    SEND_SPEED_LIMITED("410100", "发送速度太快而限流，请降低发送速度"),
    UNSAFE_EXTERNAL_LINK("430101", "含有不安全的外链，请确认发送的内容合法"),
    INAPPROPRIATE_TEXT("430102", "含有不合适的文本，请确认发送的内容合法"),
    INAPPROPRIATE_IMAGE("430103", "含有不合适的图片，请确认发送的内容合法"),
    INAPPROPRIATE_CONTENT("430104", "含有不合适的内容，请确认发送的内容合法"),
    TITLE_MISSING("400402", "标题缺失，请输入标题"),
    //    KEYWORDS_NOT_IN_CONTENT("310000", "消息中的关键词不在内容中，请确认发送的内容合法"),
    UNKNOWN_ERROR("-1", "未知错误");
    private final String code;
    private final String message;

    DingTalkMessageResultEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getMessage(String code) {
        for (DingTalkMessageResultEnum dingTalkMessageResultEnum : DingTalkMessageResultEnum.values()) {
            if (dingTalkMessageResultEnum.getCode().equals(code)) {
                return dingTalkMessageResultEnum.getMessage();
            }
        }
        return UNKNOWN_ERROR.getMessage();
    }
}
