package com.kf.aitest.dto.ark;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 火山引擎ARK API响应模型
 */
@Data
public class ArkApiResponse {
    
    /**
     * 响应ID
     */
    private String id;
    
    /**
     * 对象类型
     */
    private String object;
    
    /**
     * 创建时间戳
     */
    private Long created;
    
    /**
     * 模型ID
     */
    private String model;
    
    /**
     * 选择列表
     */
    private List<Choice> choices;
    
    /**
     * 使用情况统计
     */
    private Usage usage;
    
    /**
     * 错误信息
     */
    private Error error;
    
    /**
     * 选择对象
     */
    @Data
    public static class Choice {
        /**
         * 选择索引
         */
        private Integer index;
        
        /**
         * 消息内容
         */
        private Message message;
        
        /**
         * 完成原因：stop、length、content_filter
         */
        @JsonProperty("finish_reason")
        private String finishReason;
    }
    
    /**
     * 消息对象
     */
    @Data
    public static class Message {
        /**
         * 角色
         */
        private String role;
        
        /**
         * 内容
         */
        private String content;
    }
    
    /**
     * 使用情况统计
     */
    @Data
    public static class Usage {
        /**
         * 提示token数
         */
        @JsonProperty("prompt_tokens")
        private Integer promptTokens;
        
        /**
         * 完成token数
         */
        @JsonProperty("completion_tokens")
        private Integer completionTokens;
        
        /**
         * 总token数
         */
        @JsonProperty("total_tokens")
        private Integer totalTokens;
    }
    
    /**
     * 错误对象
     */
    @Data
    public static class Error {
        /**
         * 错误消息
         */
        private String message;
        
        /**
         * 错误类型
         */
        private String type;
        
        /**
         * 错误参数
         */
        private String param;
        
        /**
         * 错误代码
         */
        private String code;
    }
}
