package com.kf.aitest.service;

import com.kf.aitest.dto.DataComparisonRequestDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;

/**
 * 阶段数据存储测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class StageDataStorageTest {

    @Autowired
    private DataComparisonService dataComparisonService;

    @Test
    public void testStageDataStorage() {
        System.out.println("=== 测试阶段数据存储功能 ===");
        
        // 创建测试请求
        DataComparisonRequestDTO request = new DataComparisonRequestDTO();
        request.setUserId("test_user_stage");
        request.setIds(Arrays.asList("test-stage-id-1"));
        request.setEnableAiEvaluation(true);
        request.setDisableChunking(true); // 禁用分片
        
        String taskId = "test-stage-task-" + System.currentTimeMillis();
        
        System.out.printf("测试参数: taskId=%s, userId=%s, disableChunking=%s%n", 
                taskId, request.getUserId(), request.getDisableChunking());
        
        try {
            // 启动对比任务
            dataComparisonService.startComparison(taskId, request);
            
            // 等待一段时间让任务完成
            Thread.sleep(5000);
            
            System.out.println("✅ 阶段数据存储测试完成");
            System.out.println("请检查数据库表:");
            System.out.println("1. t_data_comparison - 主表数据");
            System.out.println("2. t_data_comparison_stage - 阶段数据");
            
        } catch (Exception e) {
            System.err.printf("测试失败: %s%n", e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testChunkingDisabled() {
        System.out.println("=== 测试禁用分片功能 ===");
        
        // 创建测试请求 - 禁用分片
        DataComparisonRequestDTO request1 = new DataComparisonRequestDTO();
        request1.setUserId("test_user_no_chunk");
        request1.setIds(Arrays.asList("test-no-chunk-id"));
        request1.setEnableAiEvaluation(true);
        request1.setDisableChunking(true);
        
        System.out.printf("测试1 - 禁用分片: disableChunking=%s%n", request1.getDisableChunking());
        
        // 创建测试请求 - 启用分片
        DataComparisonRequestDTO request2 = new DataComparisonRequestDTO();
        request2.setUserId("test_user_with_chunk");
        request2.setIds(Arrays.asList("test-with-chunk-id"));
        request2.setEnableAiEvaluation(true);
        request2.setDisableChunking(false);
        
        System.out.printf("测试2 - 启用分片: disableChunking=%s%n", request2.getDisableChunking());
        
        // 验证默认值设置
        DataComparisonRequestDTO request3 = new DataComparisonRequestDTO();
        request3.setUserId("test_user_default");
        request3.setIds(Arrays.asList("test-default-id"));
        request3.setEnableAiEvaluation(true);
        // 不设置disableChunking，测试默认值
        
        System.out.printf("测试3 - 默认值: disableChunking=%s%n", request3.getDisableChunking());
        
        // 验证默认值应该是false
        assert request3.getDisableChunking() != null && !request3.getDisableChunking() : 
                "默认应该启用分片（disableChunking=false）";
        
        System.out.println("✅ 分片设置测试通过");
    }
    
    @Test
    public void testUserIdAndChunkingSettings() {
        System.out.println("=== 测试用户ID和分片设置组合 ===");
        
        // 测试场景1：完整设置
        DataComparisonRequestDTO request1 = new DataComparisonRequestDTO();
        request1.setUserId("explicit_user");
        request1.setDisableChunking(true);
        request1.setIds(Arrays.asList("test-id-1"));
        request1.setEnableAiEvaluation(true);
        
        System.out.printf("场景1 - 完整设置: userId=%s, disableChunking=%s%n", 
                request1.getUserId(), request1.getDisableChunking());
        
        // 测试场景2：只设置用户ID
        DataComparisonRequestDTO request2 = new DataComparisonRequestDTO();
        request2.setUserId("user_only");
        request2.setIds(Arrays.asList("test-id-2"));
        request2.setEnableAiEvaluation(true);
        // disableChunking使用默认值
        
        System.out.printf("场景2 - 只设置用户ID: userId=%s, disableChunking=%s%n", 
                request2.getUserId(), request2.getDisableChunking());
        
        // 测试场景3：只设置分片选项
        DataComparisonRequestDTO request3 = new DataComparisonRequestDTO();
        request3.setDisableChunking(false);
        request3.setIds(Arrays.asList("test-id-3"));
        request3.setEnableAiEvaluation(true);
        // userId将由控制器设置默认值
        
        System.out.printf("场景3 - 只设置分片选项: userId=%s, disableChunking=%s%n", 
                request3.getUserId(), request3.getDisableChunking());
        
        System.out.println("✅ 组合设置测试完成");
    }
}
