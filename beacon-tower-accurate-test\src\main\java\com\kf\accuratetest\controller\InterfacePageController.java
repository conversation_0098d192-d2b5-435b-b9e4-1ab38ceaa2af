package com.kf.accuratetest.controller;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kf.accuratetest.common.RequireHeader;
import com.kf.accuratetest.common.ResponseDoMain;
import com.kf.accuratetest.dto.InterfacePageBatchImportDTO;
import com.kf.accuratetest.dto.InterfacePageDTO;
import com.kf.accuratetest.entity.InterfacePage;
import com.kf.accuratetest.service.InterfacePageService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/interfacePage")
@Tag(name = "接口页面相关接口", description = "接口页面相关接口")
public class InterfacePageController {
    @Resource
    private InterfacePageService interfacePageService;

    /**
     * 分页查询
     *
     * @param tInterfacePage 筛选条件
     * @return 查询结果
     */
    @GetMapping("/getPageList")
    @Operation(summary = "获得页面与接口关联列表", description = "分页查询页面与接口关联列表")
    public ResponseDoMain queryByPage(HttpServletRequest request, InterfacePageDTO tInterfacePage) {
        InterfacePage InterfacePage = new InterfacePage();
        InterfacePage.setUserId(request.getHeader("userId"));
        InterfacePage.setProjectName(tInterfacePage.getProjectName());
        InterfacePage.setInterfaceName(tInterfacePage.getInterfaceName());
        InterfacePage.setPageUrl(tInterfacePage.getPageUrl());
        IPage<InterfacePage> iPage = interfacePageService.listInterfacePages(InterfacePage, tInterfacePage.getCurrent(), tInterfacePage.getSize());
        List<InterfacePage> InterfacePages = iPage.getRecords();
        long total = iPage.getTotal();
        long pages = iPage.getPages();
        long current = iPage.getCurrent();
        List<InterfacePageDTO> interfacePageDTOS = new ArrayList<>();
        for (InterfacePage tif : InterfacePages) {
            InterfacePageDTO interfacePageDTO1 = new InterfacePageDTO();
            interfacePageDTO1.setId(tif.getId());
            interfacePageDTO1.setProjectName(tif.getProjectName());
            interfacePageDTO1.setInterfaceName(tif.getInterfaceName());
            interfacePageDTO1.setPageUrl(tif.getPageUrl());
            interfacePageDTO1.setCreateTime(DateUtil.format(tif.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            interfacePageDTO1.setUpdateTime(DateUtil.format(tif.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
            interfacePageDTO1.setStatus(tif.getStatus());
            interfacePageDTOS.add(interfacePageDTO1);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("total", total);
        map.put("pageList", interfacePageDTOS);
        return ResponseDoMain.success(map);
    }

    /**
     * 上传文件进行解析并导入，支持Excel
     */
    @PostMapping("/pageUpload")
    @Hidden
    public ResponseDoMain fileUpload(HttpServletRequest request, @RequestParam("file") @Parameter(description="excel文件") MultipartFile file) {
        if (file.isEmpty()) {
            return ResponseDoMain.custom("文件是空的!", false, "", 400);
        }
        String fileName = file.getOriginalFilename();
        String suffixName;
        if (fileName != null) {
            suffixName = fileName.substring(fileName.lastIndexOf("."));
        } else {
            return ResponseDoMain.custom("文件名为空!", false, "", 400);
        }
        //判断文件后缀名是否为excel，如果不是则不允许上传
        if (!suffixName.endsWith(".xls") && !suffixName.endsWith(".xlsx")) {
            return ResponseDoMain.custom("文件类型错误!", false, "", 400);
        }
        interfacePageService.pageUpload(file, request.getHeader("userId"));
//        fileService.xmlInsert(file, getCurUser().getUserId().toString());
        return ResponseDoMain.custom("上传成功", true, "", 200);
    }

    /**
     * 上传文件进行解析，支持Excel
     */
    @RequireHeader("userId")
    @PostMapping("/checkFileData")
    @Operation(summary = "检查文件数据", description = "上传文件进行解析，支持Excel")
    public ResponseDoMain checkFileData(HttpServletRequest request, @RequestParam("file") @Parameter(description="excel文件") MultipartFile file) {
        if (file.isEmpty()) {
            return ResponseDoMain.custom("文件是空的!", false, "", 400);
        }
        String fileName = file.getOriginalFilename();
        String suffixName;
        if (fileName != null) {
            suffixName = fileName.substring(fileName.lastIndexOf("."));
        } else {
            return ResponseDoMain.custom("文件名为空!", false, "", 400);
        }
        //判断文件后缀名是否为excel，如果不是则不允许上传
        if (!suffixName.endsWith(".xls") && !suffixName.endsWith(".xlsx")) {
            return ResponseDoMain.custom("文件类型错误!", false, "", 400);
        }
        return ResponseDoMain.custom("上传成功", true, interfacePageService.checkFileData(file, request.getHeader("userId")), 200);
    }

    /**
     * 增量添加数据
     */
    @RequireHeader("userId")
    @PostMapping("/incrementalAdd")
    @Operation(summary = "批量添加页面与接口关联", description = "添加页面与接口关联，增量添加数据")
    public ResponseDoMain incrementalAdd(HttpServletRequest request, @RequestBody List<InterfacePageBatchImportDTO> dataList) {
        if (dataList.isEmpty()) {
            return ResponseDoMain.custom("数据为空!", false, "", 400);
        }
        return ResponseDoMain.custom("上传成功", true, interfacePageService.incrementalAdd(dataList, request.getHeader("userId")), 200);
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("{id}")
    @Operation(summary = "查询页面与接口关联", description = "通过id查询页面与接口关联")
    @Parameters({
            @Parameter(name = "id", description = "id")
    })
    public ResponseEntity<InterfacePage> queryById(@PathVariable("id") Long id) {
        return ResponseEntity.ok(this.interfacePageService.queryById(id));
    }

    /**
     * 新增数据
     *
     * @param tInterfacePageDTO 实体
     * @return 新增结果
     */
    @PostMapping("/addInterfacePage")
    @Operation(summary = "新增页面与接口关联", description = "新增页面与接口关联")
    public ResponseDoMain add(HttpServletRequest request, @RequestBody InterfacePageDTO tInterfacePageDTO) {
        InterfacePage interfacePage = new InterfacePage();
        interfacePage.setUserId(request.getHeader("userId"));
        interfacePage.setProjectName(tInterfacePageDTO.getProjectName());
        interfacePage.setInterfaceName(tInterfacePageDTO.getInterfaceName());
        interfacePage.setPageUrl(tInterfacePageDTO.getPageUrl());
        interfacePage.setCreateTime(DateUtil.date());
        interfacePage.setUpdateTime(DateUtil.date());
        interfacePage.setStatus(0);
        interfacePage.setIsDeleted(0);
        return ResponseDoMain.success(this.interfacePageService.insert(interfacePage));
    }

    /**
     * 编辑数据
     *
     * @param tInterfacePageDTO tInterfacePageDTO
     * @return 编辑结果
     */
    @PostMapping("/editInterfacePage")
    @Operation(summary = "编辑页面与接口关联", description = "通过id编辑页面与接口关联数据")
    public ResponseDoMain edit(@RequestBody InterfacePageDTO tInterfacePageDTO) {
        InterfacePage interfacePage = new InterfacePage();
        interfacePage.setId(tInterfacePageDTO.getId());
        interfacePage.setProjectName(tInterfacePageDTO.getProjectName());
        interfacePage.setInterfaceName(tInterfacePageDTO.getInterfaceName());
        interfacePage.setPageUrl(tInterfacePageDTO.getPageUrl());
        interfacePage.setStatus(tInterfacePageDTO.getStatus());
        interfacePage.setUpdateTime(DateUtil.date());
        return ResponseDoMain.success(this.interfacePageService.update(interfacePage));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除是否成功
     */
    @DeleteMapping("/deleteInterfacePage")
    @Operation(summary = "删除页面与接口关联", description = "通过id删除页面与接口关联数据")
    public ResponseDoMain deleteById(@RequestParam(name = "id") @Parameter(description="id")  Integer id) {
        boolean isDeleted = interfacePageService.deleteById(id);
        if (isDeleted) {
            return ResponseDoMain.success("删除成功");
        } else {
            return ResponseDoMain.custom("删除失败", false, "", 400);
        }
    }

}

