package com.kf.userservice.dao;

import com.kf.userservice.entity.TEmailVerificationCode;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TEmailVerificationCodeDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TEmailVerificationCode queryById(Long id);

    /**
     * 通过ID查询单条数据
     *
     * @param email 邮箱
     * @return 实例对象
     */
    TEmailVerificationCode queryByEmail(String email);

    /**
     * 统计总行数
     *
     * @param tEmailVerificationCode 查询条件
     * @return 总行数
     */
    long count(TEmailVerificationCode tEmailVerificationCode);

    /**
     * 新增数据
     *
     * @param tEmailVerificationCode 实例对象
     * @return 影响行数
     */
    int insert(TEmailVerificationCode tEmailVerificationCode);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<TEmailVerificationCode> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<TEmailVerificationCode> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<TEmailVerificationCode> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<TEmailVerificationCode> entities);

    /**
     * 修改数据
     *
     * @param tEmailVerificationCode 实例对象
     * @return 影响行数
     */
    int update(TEmailVerificationCode tEmailVerificationCode);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

}

