package com.kf.uitest.service;

import com.kf.uitest.entity.UiTestStep;
import com.kf.uitest.exception.EntityNotFoundException;

import java.util.List;

public interface UiTestStepService {
    /**
     * 根据块ID查找所有步骤
     */
    List<UiTestStep> findByBlockId(String blockId);

    /**
     * 根据ID获取步骤
     */
    UiTestStep getById(String stepId);

    /**
     * 创建步骤
     */
    UiTestStep create(UiTestStep step);

    /**
     * 更新步骤
     */
    boolean update(UiTestStep step);

    /**
     * 删除步骤
     */
    boolean delete(String stepId);

    /**
     * 调整步骤顺序
     */
    boolean updateStepOrder(String stepId, Integer newOrder);

    /**
     * 根据用例ID查找所有步骤
     * 通过关联的block查找步骤
     * @param caseId 用例ID
     * @return 步骤列表
     */
    List<UiTestStep> findByCaseId(String caseId);
}
