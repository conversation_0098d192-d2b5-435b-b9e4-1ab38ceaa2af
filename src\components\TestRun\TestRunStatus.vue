<template>
    <el-dropdown @command="handleCommand" trigger="click">
        <el-button size="small" :style="buttonStyle" :loading="props.isLoading">
            {{ selectedText }}
            <el-icon class="el-icon--right"><el-icon-arrow-down /></el-icon>
        </el-button>
        <template #dropdown>
            <el-dropdown-menu>
                <el-dropdown-item
                    v-for="(text, key) in statusMap"
                    :key="key"
                    :command="key"
                >
                    {{ text }}
                </el-dropdown-item>
            </el-dropdown-menu>
        </template>
    </el-dropdown>
</template>

<script setup>
import { computed, ref, watch } from 'vue'

const props = defineProps({
    initialText: {
        type: String,
        required: true
    },
    isLoading: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['selectionChange'])

const statusMap = {
    'Passed': '通过',
    'Failed': '失败',
    'Blocked': '锁定',
    'Not Tested': '未执行',
    'NA': '不适用'
}

const colorMap = {
    '通过': 'green',
    '失败': 'red',
    '锁定': '#FFA500',
    '未执行': 'grey',
    '不适用': 'purple'
}
const selectedText = ref(props.initialText)
const previousText = ref(props.initialText)
const buttonStyle = computed(() => ({
    backgroundColor: colorMap[selectedText.value] || 'transparent',
    color: 'white',
    border: 'none'
}))

// 监听父组件传递的initialText变化
watch(() => props.initialText, (newVal) => {
    selectedText.value = newVal
})
// 监听 isLoading 的变化
watch(() => props.isLoading, (newVal, oldVal) => {
    if (oldVal && !newVal) {
        // 加载结束
        if (selectedText.value !== props.initialText) {
            // 操作失败，恢复 selectedText
            selectedText.value = previousText.value
        }
    }
})
const handleCommand = (command) => {
    if (statusMap[command] === selectedText.value) {
        return
    }
    emit('selectionChange', command)
}

</script>

<style lang="postcss" scoped>
</style>
