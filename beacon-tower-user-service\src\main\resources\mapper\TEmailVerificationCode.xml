<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kf.userservice.dao.TEmailVerificationCodeDao">
    <resultMap id="BaseResultMap" type="com.kf.userservice.entity.TEmailVerificationCode">
        <!--@Table t_email_verification_code-->
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="expireTime" column="expire_time" jdbcType="TIMESTAMP"/>
        <result property="sign" column="sign" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select id,
               email,
               code,
               create_time,
               update_time,
               expire_time,
               sign,
               is_deleted
        from t_email_verification_code
        where id = #{id}
    </select>

    <select id="queryByEmail" resultMap="BaseResultMap">
        select id,
               email,
               code,
               create_time,
               update_time,
               expire_time,
               sign,
               is_deleted
        from t_email_verification_code
        where email = #{email} and is_deleted = 0 order by create_time desc limit 1
    </select>
    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select id,
               email,
               code,
               create_time,
               update_time,
               expire_time,
               sign,
               is_deleted
        from t_email_verification_code
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="expireTime != null">
                and expire_time = #{expireTime}
            </if>
            <if test="sign != null and sign != ''">
                and sign = #{sign}
            </if>
            <if test="isDeleted != null and isDeleted != ''">
                and is_deleted = #{isDeleted}
            </if>
        </where>
    </select>
    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from t_email_verification_code
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="expireTime != null">
                and expire_time = #{expireTime}
            </if>
            <if test="sign != null and sign != ''">
                and sign = #{sign}
            </if>
            <if test="isDeleted != null and isDeleted != ''">
                and is_deleted = #{isDeleted}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into t_email_verification_code(email, code, create_time, update_time, expire_time, sign, is_deleted)
        values (#{email}, #{code}, #{createTime}, #{updateTime}, #{expireTime}, #{sign}, #{isDeleted})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into t_email_verification_code(email, code, create_time, update_time, expire_time,sign, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.email}, #{entity.code}, #{entity.createTime}, #{entity.updateTime}, #{entity.expireTime},#{entity.sign},
             #{entity.isDeleted})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into t_email_verification_code(email, code, create_time, update_time, expire_time,sign, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.email}, #{entity.code}, #{entity.createTime}, #{entity.updateTime}, #{entity.expireTime},#{entity.sign},
             #{entity.isDeleted})
        </foreach>
        on duplicate key update email       = values(email),
                                code        = values(code),
                                create_time = values(create_time),
                                update_time = values(update_time),
                                expire_time = values(expire_time),
                                sign        = values(sign),
                                is_deleted  = values(is_deleted)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update t_email_verification_code
        <set>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="code != null and code != ''">
                code = #{code},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="expireTime != null">
                expire_time = #{expireTime},
            </if>
            <if test="sign != null and sign != ''">
                sign = #{sign},
            </if>
            <if test="isDeleted != null and isDeleted != ''">
                is_deleted = #{isDeleted},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from t_email_verification_code
        where id = #{id}
    </delete>
</mapper>

