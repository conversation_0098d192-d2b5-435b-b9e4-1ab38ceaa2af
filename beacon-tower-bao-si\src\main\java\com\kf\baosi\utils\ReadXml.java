package com.kf.baosi.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.kf.baosi.entity.NodeObj;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.kf.baosi.enums.xMindEnum.getDefaultPriorityEnum;
import static com.kf.baosi.enums.xMindEnum.getEnum;

@Slf4j
public class ReadXml {

    public static List<List<String>> read(String xmlPath) {
        //检查content.json是否存在,如果存在则使用json解析,否则使用xml解析
        File file = new File(xmlPath + File.separator + "content.json");
        if (file.exists()) return readJson(xmlPath + File.separator + "content.json");
        else return readXml(xmlPath + File.separator + "content.xml");
    }

    public static List<List<String>> readXml(String xmlPath) {

        // 所有用例集合
        List<List<String>> allCaseList = null;
        // 创建SAXReader的对象reader
        SAXReader reader = new SAXReader();
        try {
            // 通过reader对象的read方法加载xml文件，获取document对象
            Document document = reader.read(new File(xmlPath));
            // 通过document对象获取根节点，即xml中的<xmap-content>
            Element rootNode = document.getRootElement();
            // 获取rootNode下的子节点，即xml中的<sheet>
            Element sheetNode = rootNode.element("sheet");
            // 获取sheetNode下的子节点，即xml中的<topic>，获取中心主题
            Element centerTopicNode = sheetNode.element("topic");
            // 删除<topic>下的子节点<extensions>
            centerTopicNode.remove(centerTopicNode.element("extensions"));
            //获取<topic>下的子节点<children>
            Element childrenNode = centerTopicNode.element("children");
            String centerTopicId = centerTopicNode.attributeValue("id");
            String titleText = centerTopicNode.element("title").getText();
            NodeObj nodeObj = new NodeObj();
            nodeObj.setPId("centerTopicNoPID");
            nodeObj.setId(centerTopicId);
            nodeObj.setTitleText(titleText);
            // 创建一个集合，获取所有<title>节点，并转换为NodeObj
            List<NodeObj> allObjList = new ArrayList<>();
            allObjList.add(nodeObj);
            // 调用方法getAllNeedNodes获取所有title节点
            getAllObjForXml(childrenNode, allObjList, centerTopicId);
            // 所有用例集合
            allCaseList = new ArrayList<>();
            // 调用getLeafObjList方法遍历集合，得到所有叶子对象
            // 通过叶子对象获取父对象，递归找到该用例下所有对象，组成一条用例
            // 将所有用例放入总用例集合
            getLeafObjList(allObjList, allCaseList);
            //遍历所有allCaseList，判断最后一个元素是否为null，如果是null则获取默认值填到最后一个元素
            for (List<String> caseList : allCaseList) {
                if (caseList.get(caseList.size() - 1).isEmpty()) {
                    caseList.set(caseList.size() - 1, getDefaultPriorityEnum());
                }
            }
        } catch (DocumentException e) {
            log.error("读取xml文件失败", e);
        }
        return allCaseList;
    }

    public static List<List<String>> readJson(String xmlPath) {
        String jsonStr = FileUtil.readUtf8String(xmlPath);
        // 通过JSONObject.parseObject方法将json字符串转换为json对象
        JSONObject jsonObject = JSONUtil.parseArray(jsonStr).getJSONObject(0);
        //获取jsonObject中的rootTopic节点
        JSONObject rootTopic = jsonObject.getJSONObject("rootTopic");
        //获取rootTopic中的children节点
        JSONObject children = rootTopic.getJSONObject("children");
        //创建一个集合，获取所有<title>节点，并转换为NodeObj
        List<NodeObj> allObjList = new ArrayList<>();
        NodeObj nodeObj = new NodeObj();
        //获得id
        String id = rootTopic.getStr("id");
        //获得title
        String title = rootTopic.getStr("title");
        nodeObj.setId(id);
        nodeObj.setTitleText(title);
        nodeObj.setPId("centerTopicNoPID");
        allObjList.add(nodeObj);
        getAllObjForJson(children, allObjList, id);
        List<List<String>> allCaseList = new ArrayList<>();
        // 调用getLeafObjList方法遍历集合，得到所有叶子对象
        // 通过叶子对象获取父对象，递归找到该用例下所有对象，组成一条用例
        // 将所有用例放入总用例集合
        getLeafObjList(allObjList, allCaseList);
        for (List<String> caseList : allCaseList) {
            if (caseList.get(caseList.size() - 1).isEmpty()) {
                caseList.set(caseList.size() - 1, getDefaultPriorityEnum());
            }
        }
        return allCaseList;
    }

    /**
     * 获取所有title节点，并转换为NodeObj
     * 递归获取所有title节点 json节点关系为：theme → rootTopic → children → attached →
     * children → children.....
     */
    public static void getAllObjForJson(JSONObject topChildren, List<NodeObj> allObjList, String pid) {
        if (topChildren == null) {
            return;
        }
        //获得children中的attached节点
        JSONArray attaches = topChildren.getJSONArray("attached");
        if (attaches == null) {
            return;
        }
        //递归遍历attached集合
        for (int i = 0; i < attaches.size(); i++) {
            NodeObj jsonObj = new NodeObj();
            JSONObject topic = attaches.getJSONObject(i);
            try {
                //获得children中的markers节点
                JSONArray markers = attaches.getJSONObject(i).getJSONArray("markers");
                if (markers != null) {
                    //遍历markers集合，如果有markerId为priority的节点，则获取该节点的值
                    for (int j = 0; j < markers.size(); j++) {
                        JSONObject marker = markers.getJSONObject(j);
                        String markerId = marker.getStr("markerId");
                        jsonObj.setMarkers(getEnum(markerId));
                    }
                }
                //这里是获得备注信息，相当于前置条件
                JSONObject notes = topic.getJSONObject("notes");
                JSONObject plainText = notes.getJSONObject("plain");
                String plainTextStr = plainText.getStr("content");
                jsonObj.setPlain(plainTextStr);
            } catch (Exception ignored) {
            }
            //获取ID
            String id = topic.getStr("id");
            //获取title
            String title = topic.getStr("title");
            //获得children节点
            JSONObject children = topic.getJSONObject("children");
            //获得父节点的ID
            jsonObj.setId(id);
            jsonObj.setTitleText(title);
            jsonObj.setPId(pid);
            allObjList.add(jsonObj);
            getAllObjForJson(children, allObjList, id);
        }
    }

    /**
     * 获取所有title节点，并转换为NodeObj
     * 递归获取所有title节点 xml节点关系为：topic → title/children →
     * topics → topic → title/children → .....
     */
    public static void getAllObjForXml(Element topChildren, List<NodeObj> allObjList, String pid) {
        //判断是否有子节点
        if (topChildren == null) {
            return;
        }
        Element topics = topChildren.element("topics");
        List<Element> topicList = topics.elements();
        for (Element topic : topicList) {
            NodeObj nodeObj = new NodeObj();
            try {
                Element markerRefs = topic.element("marker-refs");
                List<Element> markerRefsList = markerRefs.elements();
                for (Element markerRef : markerRefsList) {
                    String markerId = markerRef.attributeValue("marker-id");
                    nodeObj.setMarkers(getEnum(markerId));
                }
            } catch (Exception ignored) {
            }
            try {
                Element notes = topic.element("notes");
                Element plainText = notes.element("plain");
                String plainTextStr = plainText.getText();
                nodeObj.setPlain(plainTextStr);
            } catch (Exception ignored) {
            }
            String id = topic.attributeValue("id");
            String titleText = topic.element("title").getText();
            Element children = topic.element("children");
            nodeObj.setId(id);
            nodeObj.setTitleText(titleText);
            nodeObj.setPId(pid);
            allObjList.add(nodeObj);
            getAllObjForXml(children, allObjList, id);
        }
    }

    /**
     * 获取所有叶子对象
     *
     * @param allObjList  所有节点对象集合
     * @param allCaseList 所有用例集合
     */
    public static void getLeafObjList(List<NodeObj> allObjList, List<List<String>> allCaseList) {

        // 复制该集合
        List<NodeObj> copyObjList = new ArrayList<>(allObjList);

        // 遍历两个集合，找出叶子对象
        for (NodeObj leafObj : copyObjList) {
            // 假设leafObj是叶子对象，flag为true
            boolean flag = true;
            for (NodeObj all : allObjList) {
                // id = pid，说明不是叶子对象，flag=false
                if (leafObj.getId().equals(all.getPId())) {
                    // 方案二：也可以给外层循环命名，当id = pid时，continue外层循环，对下一个copy对象进行遍历
                    flag = false;
                }
            }
            // 当leafObj是叶子对象的时候，寻找它的父对象组成一条用例
            if (flag) {
                // 单条用例
                List<String> caseList = new ArrayList<>();
                // 先将叶子集合放入单条用例集合中
                caseList.add(leafObj.getTitleText());
                StringBuilder marksBuilder = new StringBuilder();
                StringBuilder plainTextBuilder = new StringBuilder();
                // 调用getItParent()，寻找它的父对象组成一条用例
                getItParent(leafObj, allObjList, caseList, marksBuilder, plainTextBuilder);
                // 此时用例集合中元素的顺序为： 叶子对象 → 父对象 → ... → 根对象，因此需要通过Collections.reverse()倒序排列
                Collections.reverse(caseList);
                //在caseList的第二个位置插入备注信息（前置条件）
                caseList.add(3, plainTextBuilder.toString());
                caseList.add(marksBuilder.toString());
                // 加入总用例集合
                allCaseList.add(caseList);
            }
        }
    }

    /**
     * 递归， 通过叶子对象获取父对象，递归找到该用例下所有对象，加入到用例集合中，组成一条用例
     *
     * @param leafObj    叶子对象
     * @param allObjList 所有对象集合
     * @param caseList   测试用例集合（已在toCase方法中创建）
     */
    public static void getItParent(NodeObj leafObj, List<NodeObj> allObjList, List<String> caseList, StringBuilder marks, StringBuilder plainText) {
        // 遍历所有对象集合
        for (NodeObj allObj : allObjList) {
            // 遇到img,note等其他(id,parentId)为空的节点时跳过
            if (leafObj.getId() == null) {
                continue;
            }
            // 如果是leafObj的父对象，则加入caseList单条用例集合
            if (leafObj.getPId().equals(allObj.getId())) {
                caseList.add(allObj.getTitleText());
                //判断leafObj的markers是否为空
                if (leafObj.getMarkers() != null) {
                    marks.append(leafObj.getMarkers());
                }
                //判断leafObj的plainText是否为空
                if (leafObj.getPlain() != null) {
                    plainText.append(leafObj.getPlain());
                }
                // 递归，继续寻找父对象的父对象
                getItParent(allObj, allObjList, caseList, marks, plainText);
            }
        }
    }

}
