package com.kf.accuratetest.common;

import com.kf.accuratetest.entity.Result;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;

//@Component
@Slf4j
public class ResultHandler {

    public static final Map<String, LinkedBlockingQueue<Result>> queue = new ConcurrentHashMap<>();

    public static void createQueue(String taskId) {
        LinkedBlockingQueue<Result> queue = ResultHandler.queue.get(taskId);
        if (queue == null) {
            queue = new LinkedBlockingQueue<>();
            ResultHandler.queue.put(taskId, queue);
        } else {
            queue.clear();
        }
    }

    public static void add(String taskId, String content) {
        add(taskId, content, true);
    }

    public static void endAdd(String taskId, String content) {
        add(taskId, content, false);
    }

    public static void clear(String taskId) {
        LinkedBlockingQueue<Result> queue = ResultHandler.queue.get(taskId);
        if (queue != null) {
            //销毁队列
            ResultHandler.queue.remove(taskId);
        }

    }

    private static void add(String taskId, String content, Boolean hasNext) {
        Result result = new Result();
        result.setContent(content);
        result.setHasNext(hasNext);
        queue.get(taskId).offer(result);
    }

    public static Map<StringBuilder, Boolean> get(String taskId) {
        StringBuilder buffer = new StringBuilder();
        Map<StringBuilder, Boolean> map = new HashMap<>();
        try {
            LinkedBlockingQueue<Result> queue = ResultHandler.queue.get(taskId);
            if (queue == null) {
                log.info("获取队列中的数据失败，队列为空");
                return null;
            }
            if (!queue.isEmpty()) {
                //为了优化前端性能，每次取出队列中的前10条数据
                for (int i = 0; i < 10; i++) {
                    Result result = queue.poll();
                    if (result != null) {
                        buffer.append(result.getContent()).append("\n");
                        map.put(buffer, result.getHasNext());
                    }
                }
            }
            return map;
        } catch (Exception e) {
            log.error("获取队列中的数据失败：{}", e.getMessage());
            return null;
        }

    }
}
