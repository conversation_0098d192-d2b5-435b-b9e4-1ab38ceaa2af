/**
 * 用户服务 API
 */

import request from '@/utils/request'
import type { AxiosResponse } from 'axios'
import type { IMenubarList } from '@/type/store/layout'
import type {
    LoginParam,
    IGetUser,
    RegisterParam,
    EmailParam,
    CheckVerificationCode,
    ResetPassWord,
    ResetUserPassword
} from './types'

const serviceName = '/api/beacon-tower'
const userServiceName = 'user-service'

// 用户服务API端点
const userServiceApi = {
    // 登录
    login: `${serviceName}/${userServiceName}/login`,
    // 注册
    register: `${serviceName}/${userServiceName}/user/register`,
    // 获取路由列表
    getRouterList: `${serviceName}/${userServiceName}/menu/route`,
    // 获取注册时验证码
    verificationCode: `${serviceName}/${userServiceName}/user/getVerificationCode`,
    // 重置密码时获取验证码
    resetPassWordVerificationCode: `${serviceName}/${userServiceName}/user/gerResetPassWordVerificationCode`,
    // 重置密码时校验验证码是否正确
    checkVerificationCode: `${serviceName}/${userServiceName}/user/checkVerificationCode`,
    // 登录页重置密码
    resetPassWord: `${serviceName}/${userServiceName}/user/resetPassWord`,
    // 个人中心重置密码
    resetUserPassword: `${serviceName}/${userServiceName}/user/resetUserPassword`
}

/**
 * 用户登录
 */
export function login(param: LoginParam): Promise<AxiosResponse<IResponse<IGetUser>>> {
    return request({
        url: userServiceApi.login,
        method: 'post',
        data: param
    })
}

/**
 * 用户注册
 */
export function register(param: RegisterParam): Promise<AxiosResponse<IResponse<string>>> {
    return request({
        url: userServiceApi.register,
        method: 'post',
        data: param
    })
}

/**
 * 获取验证码
 */
export function verificationCode(param: EmailParam): Promise<AxiosResponse<IResponse>> {
    return request({
        url: userServiceApi.verificationCode,
        method: 'get',
        params: param
    })
}

/**
 * 获取路由列表
 */
export function getRouterList(): Promise<AxiosResponse<IResponse<Array<IMenubarList>>>> {
    return request({
        url: userServiceApi.getRouterList,
        method: 'get'
    })
}

/**
 * 重置密码时获取验证码
 */
export function resetPassWordVerificationCode(email: string): Promise<AxiosResponse<IResponse>> {
    return request({
        url: userServiceApi.resetPassWordVerificationCode,
        method: 'post',
        params: { email: email }
    })
}

/**
 * 校验验证码
 */
export function checkVerificationCode(data: CheckVerificationCode): Promise<AxiosResponse<IResponse>> {
    return request({
        url: userServiceApi.checkVerificationCode,
        method: 'post',
        data: data
    })
}

/**
 * 重置密码
 */
export function resetPassWord(data: ResetPassWord): Promise<AxiosResponse<IResponse>> {
    return request({
        url: userServiceApi.resetPassWord,
        method: 'post',
        data: data
    })
}

/**
 * 个人中心重置密码
 */
export function resetUserPassword(data: ResetUserPassword): Promise<AxiosResponse<IResponse>> {
    return request({
        url: userServiceApi.resetUserPassword,
        method: 'post',
        data: data
    })
}
