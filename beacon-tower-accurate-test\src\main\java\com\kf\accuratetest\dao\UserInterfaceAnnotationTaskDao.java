package com.kf.accuratetest.dao;

import com.kf.accuratetest.entity.UserInterfaceAnnotationTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserInterfaceAnnotationTaskDao {

    /**
     * 通过ID查询单条数据
     *
     * @param userId 用户ID
     * @return 实例对象
     */
    List<UserInterfaceAnnotationTask> queryByUserId(String userId);

    /**
     * 通过taskId查询单条数据
     *
     * @param taskId 用户ID
     * @return 实例对象
     */
    UserInterfaceAnnotationTask queryByTaskId(String taskId);

    /**
     * 统计总行数
     *
     * @param userInterfaceAnnotationTask 查询条件
     * @return 总行数
     */
    long count(UserInterfaceAnnotationTask userInterfaceAnnotationTask);

    /**
     * 新增数据
     *
     * @param userInterfaceAnnotationTask 实例对象
     * @return 影响行数
     */
    int insert(UserInterfaceAnnotationTask userInterfaceAnnotationTask);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<TUserInterfaceAnnotationTask> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<UserInterfaceAnnotationTask> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<TUserInterfaceAnnotationTask> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<UserInterfaceAnnotationTask> entities);

    /**
     * 修改数据
     *
     * @param userInterfaceAnnotationTask 实例对象
     * @return 影响行数
     */
    int update(UserInterfaceAnnotationTask userInterfaceAnnotationTask);

    /**
     * 通过主键删除数据
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteById(String userId);

}

