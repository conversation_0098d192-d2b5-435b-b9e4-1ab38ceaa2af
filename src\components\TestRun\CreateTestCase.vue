<template>
    <el-dialog
        v-model="isVisible"
        title="创建测试用例"
        width="60%"
        @closed="handleClosed"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
        <el-form :model="form" ref="formRef" label-width="100px" :rules="rules">
            <!-- 标题 -->
            <el-form-item label="标题" prop="summary" required>
                <el-input v-model="form.summary" placeholder="请输入用例标题"></el-input>
            </el-form-item>

            <!-- 等级、版本、经办人在同一行 -->
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-form-item label="等级" prop="level" required>
                        <el-select v-model="form.level" placeholder="用例等级">
                            <el-option label="P0" value="P0"></el-option>
                            <el-option label="P1" value="P1"></el-option>
                            <el-option label="P2" value="P2"></el-option>
                            <el-option label="P3" value="P3"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="9">
                    <el-form-item label="修复版本" prop="fixVersions" required>
                        <el-select
                            v-model="form.fixVersions"
                            multiple
                            placeholder="请选择版本"
                            filterable
                            clearable
                            :reserve-keyword="false"
                            :loading="versionLoading"
                        >
                            <el-option
                                v-for="version in availableVersions"
                                :key="version"
                                :label="version"
                                :value="version"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="9">
                    <el-form-item label="经办人">
                        <el-input v-model="form.displayName" disabled></el-input>
                    </el-form-item>
                </el-col>
            </el-row>

            <!-- 测试用例集 -->
            <el-form-item label="用例集">
                <el-autocomplete
                    v-model="form.testSuitePath"
                    :fetch-suggestions="queryTestSuites"
                    placeholder="请选择或输入测试用例集"
                    @select="handleSelect"
                    trigger-on-focus
                    clearable
                >
                </el-autocomplete>
            </el-form-item>

            <!-- 前提条件 -->
            <el-form-item label="前提">
                <el-input
                    type="textarea"
                    v-model="form.precondition"
                    placeholder="请输入前提条件"
                    rows="2"
                ></el-input>
            </el-form-item>

            <!-- 测试用例步骤表格 -->
            <el-table :data="form.testCaseSteps">
                <el-table-column label="步骤" min-width="45%">
                    <template #default="{ row }">
                        <el-input
                            v-model="row.step"
                            type="textarea"
                            autosize
                            placeholder="请输入步骤"
                        ></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="预期结果" min-width="45%">
                    <template #default="{ row }">
                        <el-input
                            v-model="row.expectedResult"
                            type="textarea"
                            autosize
                            placeholder="请输入预期结果"
                        ></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="60px">
                    <template #default="{ $index }">
                        <el-button type="text" @click="deleteRow($index)" size="small" :icon="Delete"></el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-button type="text" @click="addRow" :icon="Plus" size="small" style="font-size: 14px">
                添加一行
            </el-button>
        </el-form>
        <template #footer>
            <el-button type="text" @click="handleCancel">取消</el-button>
            <el-button
                type="primary"
                @click="handleSave"
                :loading="saveLoading"
            >
                确定
            </el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { Delete, Plus } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus/lib/components'
import { createTestCase, getAllTestSuiteForProject, getAllVersionForProject } from '@/api/layout'
import { ElMessage } from 'element-plus'
import { validate } from '@/utils/formExtend'

// 定义接收的props
const props = defineProps<{
    jiraToken: string
    projectKey: string
    testPlanKey: string
    cycleId: string
    cycleName: string
    requirementKey: string
    version: string[]
    jiraName: string
    displayName: string
}>()

// 定义发出的事件
const emit = defineEmits<{
    (e: 'close'): void
    (e: 'save', payload: {
        requirementKey: string
    }): void
}>()

// 定义测试运行的数据结构
interface TestCaseStep {
    step: string
    expectedResult: string
}

interface TestRun {
    projectKey: string
    testPlanKey: string
    cycleId: string
    cycleName: string
    requirementKey: string
    fixVersions: string[]
    testSuitePath: string
    summary: string
    level: string
    precondition: string
    displayName: string
    assignee: string
    testCaseSteps: TestCaseStep[]
}

// 表单数据
const form = reactive<TestRun>({
    projectKey: props.projectKey,
    testPlanKey: props.testPlanKey,
    cycleId: props.cycleId,
    cycleName: props.cycleName,
    requirementKey: props.requirementKey,
    fixVersions: props.version,
    testSuitePath: '',
    summary: '',
    level: 'P2',
    displayName: props.displayName,
    assignee: props.jiraName,
    precondition: '',
    testCaseSteps: [
        {
            step: '',
            expectedResult: ''
        }
    ]
})

// 模态框可见性
const isVisible = ref(true)

// 表单引用
const formRef = ref<FormInstance>()

// 定义表单验证规则
const rules = {
    summary: [
        { required: true, message: '标题不能为空' }
    ],
    level: [
        { required: true, message: '用例等级不能为空' }
    ],
    version: [
        { required: true, message: '修复版本不能为空' }
    ]
}

// 添加一行
const addRow = () => {
    form.testCaseSteps.push({
        step: '',
        expectedResult: ''
    })
}

// 删除一行
const deleteRow = (index: number) => {
    form.testCaseSteps.splice(index, 1)
}

// 保存加载状态
const saveLoading = ref(false)

const handleSave = async () => {
    const isValid = await validate(formRef, false) as boolean
    // 如果全局校验没有通过，直接返回
    if (!isValid) {
        return
    }

    // 判断步骤和预期结果是否为空
    if (form.testCaseSteps.some(step => !step.step || !step.expectedResult)) {
        ElMessage.warning('请完善步骤和预期结果')
        return
    }

    saveLoading.value = true
    const payload: TestRun = { ...form }
    try {
        const res = await createTestCase(props.jiraToken, payload)
        emit('save', { requirementKey: form.requirementKey })
        ElMessage.success('保存成功')
        saveLoading.value = false
        isVisible.value = false
    } catch (error) {
        ElMessage.error('保存失败，请重试')
        saveLoading.value = false
    }
}
// 处理取消
const handleCancel = () => {
    // 重置表单
    formRef.value?.resetFields()
    isVisible.value = false
}

// 处理模态框关闭
const handleClosed = () => {
    emit('close')
    formRef.value?.resetFields()
}

// 测试用例集数据
const testSuites = ref<string[]>([])

// 获取测试用例集
const fetchTestSuites = async () => {
    try {
        const res = await getAllTestSuiteForProject(props.jiraToken, props.projectKey)
        testSuites.value = res.data.data as string[]
    } catch (error) {
        console.error('获取测试用例集失败:', error)
        ElMessage.error('获取测试用例集失败')
    }
}
fetchTestSuites()

// 处理el-autocomplete的建议查询
const queryTestSuites = (query: string, callback: (suggestions: any[]) => void) => {
    let results: string[] = []
    if (query === '') {
        // 当 query 为空时，显示所有测试用例集
        results = [...testSuites.value] // 返回所有测试用例集
    } else {
        // 根据输入过滤测试用例集
        results = testSuites.value.filter(suite => suite.toLowerCase().includes(query.toLowerCase()))
    }
    callback(results.map(suite => ({ value: suite })))
}

// 处理选择事件（可选）
const handleSelect = (item: { value: string }) => {
    form.testSuitePath = item.value
}

// 可用的版本列表和加载状态
const availableVersions = ref<string[]>([])
const versionLoading = ref(false)
const getAllVersion = async () => {
    const res = await getAllVersionForProject(props.projectKey, props.jiraToken)
    availableVersions.value = res.data.data as string[]
}
getAllVersion()
</script>

<style lang='postcss' scoped>
.el-form-item {
    margin-bottom: 16px;
}

.el-button {
    margin-right: 10px;
}
</style>
