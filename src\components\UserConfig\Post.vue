<template>
    <div class="post">
        <div class="user-block">
            <span class="description">{{ description }}</span>
        </div>
        <slot/>
    </div>
</template>

<script setup lang='ts'>
defineProps({
    description: String
})
</script>

<style scoped>
.post {
    font-size: 16px;
    color: #666;
}

.user-block {
    margin-bottom: 10px;

    .description {
        display: block;
        padding: 2px 0;
    }


    :after {
        clear: both;
    }

    span {
        font-weight: 500;
        font-size: 12px;
    }
}

</style>
