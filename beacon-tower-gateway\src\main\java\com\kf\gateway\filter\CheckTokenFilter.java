package com.kf.gateway.filter;

import cn.hutool.core.util.StrUtil;
import com.kf.gateway.common.ResponseDoMain;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.kf.gateway.utils.JsonUtil.toJson;

@Slf4j
@Component
@RequiredArgsConstructor
public class CheckTokenFilter implements GlobalFilter, Ordered {
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final String BEARER_PREFIX = "Bearer ";
    private static final String REDIS_SESSION_PREFIX = "spring:session:sessions:";
    private static final List<String> whiteList = Arrays.asList(
            "/user-service/login",
            "/user-service/refreshToken",
            "/user-service/user/register",
            "/user-service/user/getVerificationCode",
            "/user-service/user/resetPassWord",
            "/user-service/user/checkVerificationCode",
            "/user-service/user/gerResetPassWordVerificationCode",

            "/bao-si/jira/progress/**",
            "/bao-si/file/preview/**",
            "/actuator/**",
            "/accurate-test/interface/getAnnotation",
            "/accurate-test/analysisLog",
            "/accurate-test/hello",
            "/accurate-test/code/codeAnalysis",

            // knife4j swagger
            "/accurate-test/v3/api-docs",
            "/bao-si/v3/api-docs",
            "/user-service/v3/api-docs"
    );

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();

        if (request.getMethod() == HttpMethod.OPTIONS || isWhiteListed(request.getPath().value())) {
            return chain.filter(exchange);
        }

        String authHeader = request.getHeaders().getFirst("Authorization");
        if (StrUtil.isBlank(authHeader) || !authHeader.startsWith(BEARER_PREFIX)) {
            return unauthorized(response);
        }

        String authToken = authHeader.substring(BEARER_PREFIX.length());
        String tokenKey = REDIS_SESSION_PREFIX + authToken;
        String redisToken = (String) stringRedisTemplate.opsForHash().get(tokenKey, "sessionAttr:token");
        String userId = (String) stringRedisTemplate.opsForHash().get(tokenKey, "sessionAttr:userId");
        if (StrUtil.isBlank(redisToken) || !redisToken.replaceAll("\"", "").equals(authToken)) {
            return unauthorized(response);
        }

        // 刷新token过期时间为30天
        stringRedisTemplate.expire(tokenKey, 60 * 60 * 24 * 30, TimeUnit.SECONDS);
        request = request.mutate().header("token", authToken).header("userId", userId).build();
        return chain.filter(exchange.mutate().request(request).build());
    }

    private Mono<Void> unauthorized(ServerHttpResponse response) {
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        ResponseDoMain responseDoMain = ResponseDoMain.tokenExpired();
        String responseBody = toJson(responseDoMain);
        DataBuffer buffer = response.bufferFactory().wrap(responseBody.getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }

    private static final AntPathMatcher pathMatcher = new AntPathMatcher();

    private boolean isWhiteListed(String path) {
        return whiteList.stream().anyMatch(pattern -> pathMatcher.match(pattern, path));
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }
}
