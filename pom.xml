<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.1.1</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.kf</groupId>
    <artifactId>beacon-tower</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>beacon-tower</name>
    <description>beacon-tower</description>
    <modules>
        <module>beacon-tower-accurate-test</module>
        <module>beacon-tower-ai-test</module>
        <module>beacon-tower-api-test</module>
        <module>beacon-tower-gateway</module>
        <module>beacon-tower-user-service</module>
        <module>beacon-tower-sdk</module>
        <module>beacon-tower-bao-si</module>
        <module>beacon-tower-flow-playback</module>
        <module>beacon-tower-ui-test</module>
        <module>beacon-tower-mcp-clint</module>
    </modules>
    <properties>
        <revision>1.0</revision>
        <java.version>17</java.version>
        <spring-cloud.version>2022.0.3</spring-cloud.version>
        <dubbo.version>2.7.22</dubbo.version>
        <platform-plugin-sdk.version>1.6.0</platform-plugin-sdk.version>
        <shiro.version>1.11.0</shiro.version>
        <java-websocket.version>1.5.3</java-websocket.version>
        <easyexcel.version>3.1.1</easyexcel.version>
        <dom4j.version>2.1.3</dom4j.version>
        <guava.version>32.0.1-jre</guava.version>
        <pagehelper.version>5.3.2</pagehelper.version>
        <metersphere-jmeter-functions.version>1.5</metersphere-jmeter-functions.version>
        <quartz-starter.version>1.0.8</quartz-starter.version>
        <redisson-starter.version>3.21.3</redisson-starter.version>
        <guice.version>5.1.0</guice.version>
        <mybatis-starter.version>3.0.1</mybatis-starter.version>
        <reflections.version>0.10.2</reflections.version>
        <bcprov-jdk15on.version>1.70</bcprov-jdk15on.version>
        <commons-io.version>2.11.0</commons-io.version>
        <commons-text.version>1.10.0</commons-text.version>
        <xstream.version>1.4.20</xstream.version>
        <xmlbeans.version>3.1.0</xmlbeans.version>
        <swagger-parser.version>2.1.12</swagger-parser.version>
        <springdoc-openapi-ui.version>2.1.0</springdoc-openapi-ui.version>
        <rhino.version>1.7.14</rhino.version>
        <jsoup.version>1.15.3</jsoup.version>
        <commonmark.version>0.19.0</commonmark.version>
        <commons-compress.version>1.21</commons-compress.version>
        <htmlcleaner.version>2.26</htmlcleaner.version>
        <xmindjbehaveplugin.version>0.8</xmindjbehaveplugin.version>
        <metersphere-plugin-core.version>2.0</metersphere-plugin-core.version>
        <plexus.version>3.0.24</plexus.version>
        <common-random.version>1.0.14</common-random.version>
        <generex.version>1.0.2</generex.version>
        <json-lib.version>2.4</json-lib.version>
        <json-schema-validator.version>2.2.14</json-schema-validator.version>
        <xz.version>1.9</xz.version>
        <flatten.version>1.2.7</flatten.version>
        <jmeter.version>5.5</jmeter.version>
        <codehaus-groovy.version>3.0.11</codehaus-groovy.version>
        <jython.version>2.7.0</jython.version>
        <docker-java.version>3.2.14</docker-java.version>
        <jmeter-plugins-webdriver.version>4.8.3</jmeter-plugins-webdriver.version>
        <opentelemetry.version>1.24.0</opentelemetry.version>
        <oracle-database.version>19.7.0.0</oracle-database.version>
        <zookeeper.version>3.8.0</zookeeper.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <jmeter-plugins-dubbo.version>2.7.17</jmeter-plugins-dubbo.version>
        <hessian-lite.version>3.2.13</hessian-lite.version>
        <avro.version>1.11.1</avro.version>
        <dec.version>0.1.2</dec.version>
        <dingtalk-sdk.version>2.0.0</dingtalk-sdk.version>
        <org-json.version>20220924</org-json.version>
        <jmeter-plugins-dubbo.version>2.7.17</jmeter-plugins-dubbo.version>
        <nacos.version>2.2.3</nacos.version>
        <minio.version>8.5.3</minio.version>
        <hikaricp.version>5.0.1</hikaricp.version>
        <xmlgraphics-commons.version>2.7</xmlgraphics-commons.version>
        <commons-fileupload.version>1.5</commons-fileupload.version>
        <graalvmjs.version>22.3.1</graalvmjs.version>
        <httpclient.version>4.5.14</httpclient.version>
        <!-- frontend -->
        <frontend-maven-plugin.version>1.12.1</frontend-maven-plugin.version>
        <node.version>v16.10.0</node.version>
        <npm.version>8.12.1</npm.version>
        <!--        -->
        <skipAntRunForJenkins>false</skipAntRunForJenkins>
        <commons-dbcp2-version>2.9.0</commons-dbcp2-version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>2022.0.0.0-RC2</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.5.5</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>1.2.18</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>3.1.0</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.19</version>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>8.3.0</version>
            </dependency>
            <dependency>
                <groupId>io.projectreactor</groupId>
                <artifactId>reactor-test</artifactId>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka-test</artifactId>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>4.5.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <release>${java.version}</release>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>aliyun</id>
            <url>http://maven.aliyun.com/nexus/content/groups/public</url>
        </repository>
    </repositories>

</project>
