package com.kf.accuratetest.controller;

import com.kf.accuratetest.common.RequireHeader;
import com.kf.accuratetest.common.ResponseDoMain;
import com.kf.accuratetest.dto.InFromDTO;
import com.kf.accuratetest.entity.UserInform;
import com.kf.accuratetest.service.UserInformService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("config")
@Tag(name = "钉钉消息相关接口", description = "钉钉消息相关接口")
public class TUserInformController {
    @Resource
    private UserInformService userInfromService;

    /**
     * 通过主键查询单条数据
     *
     * @return 当前用户的所有信息
     */
    @GetMapping("inFromData")
    @RequireHeader("userId")
    @Operation(summary = "查询钉钉消息配置", description = "查询钉钉消息配置")
    public ResponseDoMain queryById(HttpServletRequest request) {
        List<UserInform> userInform = this.userInfromService.queryAllByLimit(request.getHeader("userId"));
        List<InFromDTO> inFromDTOList = new ArrayList<>();
        userInform.forEach(value -> {
            InFromDTO inFromDTO = new InFromDTO();
            inFromDTO.setGitPath(value.getGitUrl());
            inFromDTO.setWebHook(value.getObjectId());
            inFromDTO.setStatus(value.getStatus() == 0);
            inFromDTOList.add(inFromDTO);
        });
        return ResponseDoMain.custom("", true, inFromDTOList, 200);
    }

    /**
     * 新增数据
     *
     * @param fromList 实体
     * @return 新增结果
     */
    @PostMapping("saveInFrom")
    @RequireHeader("userId")
    @Operation(summary = "保存钉钉消息配置", description = "保存钉钉消息配置")
    public ResponseDoMain add(HttpServletRequest request, @RequestBody List<InFromDTO> fromList) {
//        if (fromList.isEmpty()) {
//            return ResponseDoMain.custom("参数错误", false, null, 200);
//        }
        int save = this.userInfromService.insert(fromList, request.getHeader("userId"));
        if (save > 0) {
            return ResponseDoMain.custom("保存成功", true, "", 200);
        }
        return ResponseDoMain.custom("保存失败", false, "", 200);
    }


}

