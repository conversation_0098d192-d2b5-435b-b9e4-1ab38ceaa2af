package com.kf.userservice.service.impl;

import com.kf.userservice.dao.TMenuDao;
import com.kf.userservice.entity.TMenu;
import com.kf.userservice.entity.router.MenuTree;
import com.kf.userservice.entity.router.Meta;
import com.kf.userservice.service.MenuService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("menuService")
public class MenuServiceImpl implements MenuService {
    @Resource
    private TMenuDao tMenuDao;

    /**
     * 通过UserID查询菜单
     *
     * @param userId userId
     * @return 菜单列表
     */
    @Override
    public List<MenuTree> queryMenuByUserId(String userId) {
        List<TMenu> tMenus = this.tMenuDao.queryMenuByUserId(userId);
        if (tMenus != null && !tMenus.isEmpty()) {
            Map<Integer, MenuTree> menuTrees = new LinkedHashMap<>();
            List<TMenu> menuPermissionList = new ArrayList<>();
            for (TMenu tMenu : tMenus) {
                if (tMenu.getType() == 0) {
                    MenuTree menuTree = new MenuTree();
                    int id = tMenu.getMenuId().intValue();
                    menuTree.setId(id);
                    menuTree.setParentId(tMenu.getParentId().byteValue());
                    menuTree.setName(tMenu.getMenuName());
                    menuTree.setPath(tMenu.getPath());
                    menuTree.setComponent(tMenu.getComponent());
                    menuTree.setRedirect(tMenu.getRedirect());
                    Meta meta = new Meta();
                    meta.setTitle(tMenu.getTitle());
                    meta.setIcon(tMenu.getIcon());
                    meta.setAlwaysShow(tMenu.getAlwaysShow());
                    meta.setActiveMenu(tMenu.getActiveMenu());
                    meta.setHidden(tMenu.getHidden());
                    meta.setPermission(new ArrayList<>());
                    menuTree.setMeta(meta);
                    menuTrees.put(id, menuTree);
                }
                if (tMenu.getType() == 1) {
                    menuPermissionList.add(tMenu);
                }
            }
            for (TMenu tMenu : menuPermissionList) {
                //判断menuTrees包含key
                if (menuTrees.containsKey(tMenu.getParentId().intValue())) {
                    MenuTree menuTree = menuTrees.get(tMenu.getParentId().intValue());
                    List<String> permission = menuTree.getMeta().getPermission();
                    permission.add(tMenu.getPerms());
                    menuTree.getMeta().setPermission(permission);
                }

            }
            List<MenuTree> menuTreeList = new ArrayList<>();
            for (Map.Entry<Integer, MenuTree> entry : menuTrees.entrySet()) {
                menuTreeList.add(entry.getValue());
            }
            return menuTreeList;
        }
        return null;
    }

    /**
     * 通过ID查询单条数据
     *
     * @param menuId 主键
     * @return 实例对象
     */
    @Override
    public TMenu queryById(Long menuId) {
        return this.tMenuDao.queryById(menuId);
    }


    /**
     * 新增数据
     *
     * @param tMenu 实例对象
     * @return 实例对象
     */
    @Override
    public TMenu insert(TMenu tMenu) {
        this.tMenuDao.insert(tMenu);
        return tMenu;
    }

    /**
     * 修改数据
     *
     * @param tMenu 实例对象
     * @return 实例对象
     */
    @Override
    public TMenu update(TMenu tMenu) {
        this.tMenuDao.update(tMenu);
        return this.queryById(tMenu.getMenuId());
    }

    /**
     * 通过主键删除数据
     *
     * @param menuId 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long menuId) {
        return this.tMenuDao.deleteById(menuId) > 0;
    }
}
