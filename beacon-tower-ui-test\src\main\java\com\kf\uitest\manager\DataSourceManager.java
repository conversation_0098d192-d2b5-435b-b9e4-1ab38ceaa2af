package com.kf.uitest.manager;

import com.kf.uitest.entity.UiTestDataSource;
import com.kf.uitest.utils.DataSourceValidator;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.datasource.DataSourceException;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class DataSourceManager {

    private final Map<String, DataSource> dataSourceCache = new ConcurrentHashMap<>();
    private final DataSourceValidator validator;

    public DataSourceManager(DataSourceValidator validator) {
        this.validator = validator;
    }

    public DataSource getDataSource(UiTestDataSource config) {
        return dataSourceCache.computeIfAbsent(config.getName(), name -> createDataSource(config));
    }

    private DataSource createDataSource(UiTestDataSource config) {
        try {
            validator.validate(config);

            HikariConfig hikariConfig = new HikariConfig();
            hikariConfig.setJdbcUrl(config.getUrl());
            hikariConfig.setUsername(config.getUsername());
            hikariConfig.setPassword(config.getPassword());
            hikariConfig.setDriverClassName(config.getDriverClass());

            hikariConfig.setMaximumPoolSize(5);
            hikariConfig.setMinimumIdle(1);
            hikariConfig.setIdleTimeout(300000);
            hikariConfig.setConnectionTimeout(20000);
            hikariConfig.setPoolName("UiTest-" + config.getName());

            return new HikariDataSource(hikariConfig);
        } catch (Exception e) {
            throw new DataSourceException("Failed to create data source: " + config.getName(), e);
        }
    }

    public void removeDataSource(String name) {
        DataSource dataSource = dataSourceCache.remove(name);
        if (dataSource instanceof HikariDataSource) {
            ((HikariDataSource) dataSource).close();
        }
    }

    public void clearAll() {
        dataSourceCache.forEach((name, dataSource) -> {
            if (dataSource instanceof HikariDataSource) {
                ((HikariDataSource) dataSource).close();
            }
        });
        dataSourceCache.clear();
    }
}