package com.kf.userservice.entity.router;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Meta {
    /**
     * title
     */
    private String title;

    /**
     * 图标
     */
    private String icon;

    /**
     * 当子路由只有一个的时候是否显示当前路由 0否 1是
     */
    private Integer alwaysShow;

    /**
     * 需要高亮的侧边栏菜单的path
     */
    private String activeMenu;

    /**
     * 是否隐藏路由 0否 1是
     */
    private Integer hidden;

    /**
     * 权限标识
     */
    private List<String> permission;
}
