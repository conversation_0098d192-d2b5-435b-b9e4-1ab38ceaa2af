package com.kf.uitest.dto.execution;

import com.kf.uitest.entity.UiTestHook;
import com.kf.uitest.enums.BlockType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StepBlockDTO {
    /**
     * 步骤组ID
     * 格式：UUID
     */
    private String id;
    
    /**
     * 步骤组名称
     * 描述步骤组的用途
     */
    private String name;
    
    /**
     * 步骤组类型
     * NORMAL: 普通步骤组
     * LOOP: 循环步骤组
     * CONDITION: 条件步骤组
     */
    private BlockType type;
    
    /**
     * 执行顺序
     * 在用例内的执行顺序
     */
    private Integer order;
    
    /**
     * 步骤组级别的前置钩子
     * 在步骤组开始执行前调用
     */
    private List<UiTestHook> preHooks;
    
    /**
     * 步骤组级别的后置钩子
     * 在步骤组执行完成后调用
     */
    private List<UiTestHook> postHooks;
    
    /**
     * 子步骤组列表
     * 用于支持步骤组嵌套
     */
    private List<StepBlockDTO> children;
    
    /**
     * 步骤列表
     * 当前步骤组包含的具体步骤
     */
    private List<ExecutionStepDTO> steps;
} 