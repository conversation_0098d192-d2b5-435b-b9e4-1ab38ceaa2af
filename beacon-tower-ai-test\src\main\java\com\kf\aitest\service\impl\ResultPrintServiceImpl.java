package com.kf.aitest.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kf.aitest.dto.DataComparisonResultDTO;
import com.kf.aitest.dto.StageDataDTO;
import com.kf.aitest.service.ResultPrintService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;

/**
 * 结果打印服务实现
 */
@Slf4j
@Service
public class ResultPrintServiceImpl implements ResultPrintService {
    
    @Autowired
    private ObjectMapper objectMapper;

    @Value("${ai.evaluation.enable-console-print:true}")
    private boolean enableConsolePrint;

    private static final String SEPARATOR = "=".repeat(80);
    private static final String SUB_SEPARATOR = "-".repeat(60);
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public void printStageResult(StageDataDTO stageData, String id) {
        if (!enableConsolePrint) return;

        System.out.println();
        System.out.println(SUB_SEPARATOR);
        System.out.printf("📊 阶段评估结果 - %s (ID: %s)%n", getStageDisplayName(stageData.getStageName()), id);
        System.out.println(SUB_SEPARATOR);
        
        // 基本信息
        System.out.printf("🔹 阶段名称: %s%n", stageData.getStageName());
        System.out.printf("🔹 数据类型: %s%n", stageData.getDataType());
        System.out.printf("🔹 处理状态: %s%n", getStatusDisplay(stageData.getStatus()));
        
        if (stageData.getFetchTime() != null) {
            System.out.printf("🔹 处理时间: %s%n", stageData.getFetchTime().format(TIME_FORMATTER));
        }
        
        if (stageData.getDuration() != null) {
            System.out.printf("🔹 处理耗时: %d ms%n", stageData.getDuration());
        }
        
        // AI评估结果
        if (stageData.getAiScore() != null || stageData.getAiEvaluation() != null) {
            System.out.println();
            System.out.println("🤖 AI评估结果:");
            
            if (stageData.getAiScore() != null) {
                System.out.printf("   📈 评分: %s/100%n", getScoreDisplay(stageData.getAiScore()));
            }
            
            if (stageData.getAiEvaluation() != null && !stageData.getAiEvaluation().trim().isEmpty()) {
                System.out.println("   💬 AI回答:");
                System.out.println("   " + SUB_SEPARATOR.substring(0, 40));
                
                // 格式化AI回答，每行前加缩进
                String[] lines = stageData.getAiEvaluation().split("\n");
                for (String line : lines) {
                    System.out.println("   " + line);
                }
                System.out.println("   " + SUB_SEPARATOR.substring(0, 40));
            }
        } else {
            System.out.println();
            System.out.println("🤖 AI评估结果: 未进行AI评估");
        }
        
        // 错误信息
        if (stageData.getErrorMessage() != null && !stageData.getErrorMessage().trim().isEmpty()) {
            System.out.println();
            System.out.printf("❌ 错误信息: %s%n", stageData.getErrorMessage());
        }
        
        System.out.println(SUB_SEPARATOR);
    }
    
    @Override
    public void printOverallResult(DataComparisonResultDTO comparisonResult) {
        if (!enableConsolePrint) return;

        System.out.println();
        System.out.println(SEPARATOR);
        System.out.printf("🎯 整体评估结果 (ID: %s)%n", comparisonResult.getId());
        System.out.println(SEPARATOR);
        
        // 基本统计信息
        System.out.printf("📊 处理统计:%n");
        System.out.printf("   🔹 总阶段数: %d%n", comparisonResult.getTotalStageCount());
        System.out.printf("   🔹 成功阶段数: %d%n", comparisonResult.getSuccessStageCount());
        System.out.printf("   🔹 整体状态: %s%n", getStatusDisplay(comparisonResult.getOverallStatus()));
        
        if (comparisonResult.getTotalDuration() != null) {
            System.out.printf("   🔹 总耗时: %d ms (%.2f 秒)%n", 
                    comparisonResult.getTotalDuration(), 
                    comparisonResult.getTotalDuration() / 1000.0);
        }
        
        // 时间信息
        if (comparisonResult.getStartTime() != null && comparisonResult.getEndTime() != null) {
            System.out.printf("   🔹 开始时间: %s%n", comparisonResult.getStartTime().format(TIME_FORMATTER));
            System.out.printf("   🔹 结束时间: %s%n", comparisonResult.getEndTime().format(TIME_FORMATTER));
        }
        
        // 各阶段评分汇总
        if (comparisonResult.getStageResults() != null && !comparisonResult.getStageResults().isEmpty()) {
            System.out.println();
            System.out.println("📈 各阶段评分汇总:");
            for (StageDataDTO stage : comparisonResult.getStageResults()) {
                String stageName = getStageDisplayName(stage.getStageName());
                String status = getStatusDisplay(stage.getStatus());
                String score = stage.getAiScore() != null ? getScoreDisplay(stage.getAiScore()) + "/100" : "未评分";
                System.out.printf("   🔸 %s: %s (评分: %s)%n", stageName, status, score);
            }
        }
        
        // 整体AI评估
        if (comparisonResult.getOverallScore() != null || comparisonResult.getOverallAiEvaluation() != null) {
            System.out.println();
            System.out.println("🤖 整体AI评估:");
            
            if (comparisonResult.getOverallScore() != null) {
                System.out.printf("   📈 综合评分: %s/100%n", getScoreDisplay(comparisonResult.getOverallScore()));
            }
            
            if (comparisonResult.getOverallAiEvaluation() != null && !comparisonResult.getOverallAiEvaluation().trim().isEmpty()) {
                System.out.println("   💬 整体评估:");
                System.out.println("   " + SUB_SEPARATOR);
                
                // 格式化AI回答，每行前加缩进
                String[] lines = comparisonResult.getOverallAiEvaluation().split("\n");
                for (String line : lines) {
                    System.out.println("   " + line);
                }
                System.out.println("   " + SUB_SEPARATOR);
            }
        }
        
        // 错误信息汇总
        if (comparisonResult.getErrors() != null && !comparisonResult.getErrors().isEmpty()) {
            System.out.println();
            System.out.println("❌ 错误信息汇总:");
            for (int i = 0; i < comparisonResult.getErrors().size(); i++) {
                System.out.printf("   %d. %s%n", i + 1, comparisonResult.getErrors().get(i));
            }
        }
        
        System.out.println(SEPARATOR);
    }
    
    @Override
    public void printStageSeparator(String stageName, String id) {
        if (!enableConsolePrint) return;

        System.out.println();
        System.out.printf("🚀 开始处理阶段: %s (ID: %s)%n", getStageDisplayName(stageName), id);
        System.out.println(SUB_SEPARATOR.substring(0, 40));
    }
    
    @Override
    public void printTaskStart(String taskId, int totalIds) {
        if (!enableConsolePrint) return;

        System.out.println();
        System.out.println(SEPARATOR);
        System.out.printf("🎬 数据对比任务开始%n");
        System.out.printf("📋 任务ID: %s%n", taskId);
        System.out.printf("📊 待处理ID数量: %d%n", totalIds);
        System.out.printf("⏰ 开始时间: %s%n", java.time.LocalDateTime.now().format(TIME_FORMATTER));
        System.out.println(SEPARATOR);
    }
    
    @Override
    public void printTaskComplete(String taskId) {
        if (!enableConsolePrint) return;

        System.out.println();
        System.out.println(SEPARATOR);
        System.out.printf("🎉 数据对比任务完成%n");
        System.out.printf("📋 任务ID: %s%n", taskId);
        System.out.printf("⏰ 完成时间: %s%n", java.time.LocalDateTime.now().format(TIME_FORMATTER));
        System.out.println(SEPARATOR);
    }
    
    /**
     * 获取阶段显示名称
     */
    private String getStageDisplayName(String stageName) {
        switch (stageName) {
            case "recognize":
                return "文档识别阶段";
            case "extraction":
                return "信息提取阶段";
            case "structured":
                return "结构化处理阶段";
            case "transformer":
                return "数据转换阶段";
            default:
                return stageName;
        }
    }
    
    /**
     * 获取状态显示
     */
    private String getStatusDisplay(String status) {
        if (status == null) return "未知";
        
        switch (status.toUpperCase()) {
            case "SUCCESS":
                return "✅ 成功";
            case "FAILED":
                return "❌ 失败";
            case "PARTIAL_SUCCESS":
                return "⚠️ 部分成功";
            case "TIMEOUT":
                return "⏰ 超时";
            default:
                return status;
        }
    }
    
    /**
     * 获取评分显示（带颜色标识）
     */
    private String getScoreDisplay(Integer score) {
        if (score == null) return "N/A";
        
        if (score >= 90) {
            return score + " 🟢"; // 优秀
        } else if (score >= 80) {
            return score + " 🟡"; // 良好
        } else if (score >= 60) {
            return score + " 🟠"; // 一般
        } else {
            return score + " 🔴"; // 较差
        }
    }
}
