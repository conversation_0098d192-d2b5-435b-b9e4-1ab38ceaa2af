package com.kf.baosi.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kf.baosi.dto.VerifyDocumentListDTO;
import com.kf.baosi.entity.TVerifyFile;
import org.apache.ibatis.annotations.Param;

public interface TVerifyFileMapper extends BaseMapper<TVerifyFile> {
    IPage<VerifyDocumentListDTO> getVerifyDocumentList(@Param("userId") String userId,
                                                       @Param("fileName") String fileName,
                                                       @Param("fileType") String fileType,
                                                       @Param("isComplete") String isComplete,
                                                       @Param("page") Page<?> page);
}
