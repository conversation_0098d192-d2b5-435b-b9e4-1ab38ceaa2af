import { createRouter, createWebHashHistory, type RouteRecordRaw, createWebHistory } from 'vue-router'
import { type IMenubarList } from '@/type/store/layout'
import { components } from '@/router/asyncRouter'

const Components: IObject<() => Promise<typeof import('*.vue')>> = Object.assign({}, components, {
    Layout: (() => import('@/layout/index.vue')) as unknown as () => Promise<typeof import('*.vue')>,
    Redirect: (() => import('@/layout/redirect.vue')) as unknown as () => Promise<typeof import('*.vue')>,
    LayoutBlank: (() => import('@/layout/blank.vue')) as unknown as () => Promise<typeof import('*.vue')>
})
const seigneur = '/seigneur'
// 静态路由页面
export const allowRouter: Array<IMenubarList> = [

    {
        name: 'Dashboard',
        path: `${seigneur}/`,
        component: Components['Layout'],
        redirect: `${seigneur}/Dashboard/Workplace`,
        meta: { title: '仪表盘', icon: 'el-icon-eleme' },
        children: [
            {
                name: 'Workplace',
                path: `${seigneur}/Dashboard/Workplace`,
                component: Components['Workplace'],
                meta: { title: '工作台', icon: 'el-icon-monitor' }
            }

        ]
    },
    {
        name: 'ErrorPage',
        path: `${seigneur}/ErrorPage`,
        meta: { title: '错误页面', icon: 'el-icon-eleme', hidden: true },
        component: Components.LayoutBlank,
        redirect: `${seigneur}/ErrorPage/404`,
        children: [
            {
                name: '401',
                path: `${seigneur}/ErrorPage/401`,
                component: Components['401'],
                meta: { title: '401', icon: 'el-icon-tools' }
            },
            {
                name: '404',
                path: `${seigneur}/ErrorPage/404`,
                component: Components['404'],
                meta: { title: '404', icon: 'el-icon-tools' }
            }
        ]
    },
    {
        name: 'RedirectPage',
        path: `${seigneur}/redirect`,
        component: Components['Layout'],
        meta: { title: '重定向页面', icon: 'el-icon-eleme', hidden: true },
        children: [
            {
                name: 'Redirect',
                path: `${seigneur}/redirect/:pathMatch(.*)*`,
                meta: {
                    title: '重定向页面',
                    icon: ''
                },
                component: Components.Redirect
            }
        ]
    },
    {
        name: 'Login',
        path: `${seigneur}/Login`,
        component: Components.LoginRegister,
        meta: { title: '登录', icon: 'el-icon-eleme', hidden: true }
    },
    {
        name: 'InterfaceDetailsList',
        path: `${seigneur}/InterfaceDetailsList/:taskId`,
        component: Components.InterfaceDetailsList,
        meta: { title: '接口列表', icon: 'el-icon-eleme', hidden: true }
    }
]

const router = createRouter({
    history: createWebHistory(), // createWebHistory createWebHashHistory
    routes: allowRouter as RouteRecordRaw[]
})

export default router