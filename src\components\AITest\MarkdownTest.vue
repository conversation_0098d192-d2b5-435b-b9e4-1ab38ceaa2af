<template>
  <div class="markdown-test">
    <el-card header="Markdown渲染组件测试">
      <el-row :gutter="20">
        <el-col :span="12">
          <h3>输入内容</h3>
          <el-input
            v-model="markdownContent"
            type="textarea"
            :rows="20"
            placeholder="请输入Markdown内容..."
          />
          <div class="mt-4">
            <el-button @click="loadSampleContent">加载示例内容</el-button>
            <el-button @click="toggleLoading">切换加载状态</el-button>
            <el-button @click="toggleTheme">切换主题</el-button>
          </div>
        </el-col>
        <el-col :span="12">
          <h3>渲染结果</h3>
          <div class="markdown-preview">
            <el-input
              v-model="markdownContent"
              type="textarea"
              :rows="20"
              placeholder="Markdown预览区域（暂时显示原始内容）"
              readonly
            />
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElCard, ElRow, ElCol, ElInput, ElButton, ElMessage } from 'element-plus'
// 暂时注释掉MarkdownRenderer相关代码，因为我们主要关注架构重构
// import MarkdownRenderer from '@/components/MarkdownRenderer/index.vue'
// import type { MarkdownRendererInstance } from '@/components/MarkdownRenderer/types'

// 响应式数据
const markdownContent = ref('')
const isLoading = ref(false)
const currentTheme = ref<'light' | 'dark' | 'auto'>('auto')
// const rendererRef = ref<MarkdownRendererInstance>()

// 示例内容
const sampleContent = `# Markdown渲染测试

## 基础语法测试

### 文本样式
这是**粗体文本**，这是*斜体文本*，这是~~删除线文本~~。

### 列表测试
#### 无序列表
- 项目1
- 项目2
  - 子项目2.1
  - 子项目2.2
- 项目3

#### 有序列表
1. 第一项
2. 第二项
3. 第三项

### 代码测试
行内代码：\`console.log('Hello World')\`

代码块：
\`\`\`javascript
// JavaScript代码示例
function greet(name) {
  console.log(\`Hello, \${name}!\`)
  return \`Welcome, \${name}\`
}

const user = 'Vue Developer'
greet(user)
\`\`\`

\`\`\`typescript
// TypeScript代码示例
interface User {
  id: number
  name: string
  email: string
}

const createUser = (userData: Partial<User>): User => {
  return {
    id: Date.now(),
    name: 'Unknown',
    email: '<EMAIL>',
    ...userData
  }
}
\`\`\`

### 表格测试
| 功能 | 状态 | 描述 |
|------|------|------|
| 文本渲染 | ✅ | 支持基础文本格式 |
| 代码高亮 | ✅ | 支持多种编程语言 |
| 表格渲染 | ✅ | 支持表格格式 |
| 链接渲染 | ✅ | 支持超链接 |

### 引用测试
> 这是一个引用块。
> 
> 引用可以包含多行内容，并且支持**格式化**。

### 链接测试
[Vue.js官网](https://vuejs.org/)
[Element Plus](https://element-plus.org/)

### 分割线
---

## 高级功能测试

### HTML支持
<div style="color: red; font-weight: bold;">这是HTML内容</div>

### 数学公式（如果支持）
E = mc²

### 任务列表
- [x] 完成基础功能
- [x] 添加代码高亮
- [ ] 添加数学公式支持
- [ ] 添加图表支持
`

// 事件处理
const onRendered = (html: string) => {
    console.log('Markdown rendered:', html.length, 'characters')
    ElMessage.success('渲染完成')
}

const onError = (error: Error) => {
    console.error('Render error:', error)
    ElMessage.error(`渲染失败: ${error.message}`)
}

const loadSampleContent = () => {
    markdownContent.value = sampleContent
}

const toggleLoading = () => {
    isLoading.value = !isLoading.value
}

const toggleTheme = () => {
    const themes: Array<'light' | 'dark' | 'auto'> = ['light', 'dark', 'auto']
    const currentIndex = themes.indexOf(currentTheme.value)
    currentTheme.value = themes[(currentIndex + 1) % themes.length]
    ElMessage.info(`主题切换为: ${currentTheme.value}`)
}
</script>

<style scoped>
.markdown-test {
  padding: 20px;
}

.mt-4 {
  margin-top: 16px;
}
</style>
