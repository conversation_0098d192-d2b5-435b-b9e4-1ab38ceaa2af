package com.kf.userservice.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TMenu implements Serializable {
    private static final long serialVersionUID = -92269810496265136L;
    /**
     * 菜单/按钮ID
     */
    private Long menuId;
    /**
     * 上级菜单ID
     */
    private Long parentId;
    /**
     * 菜单/按钮名称
     */
    private String menuName;
    /**
     * 路由path
     */
    private String path;
    /**
     * 路由组件component
     */
    private String component;
    /**
     * 路由重定向
     */
    private String redirect;
    /**
     * 权限标识
     */
    private String perms;
    /**
     * title
     */
    private String title;
    /**
     * 图标
     */
    private String icon;
    /**
     * 需要高亮的侧边栏菜单的path
     */
    private String activeMenu;
    /**
     * 当子路由只有一个的时候是否显示当前路由 0否 1是
     */
    private Integer alwaysShow;
    /**
     * 是否隐藏路由 0否 1是
     */
    private Integer hidden;
    /**
     * 类型 0菜单 1按钮
     */
    private Integer type;
    /**
     * 排序
     */
    private Integer orderMun;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date modifyTime;
    /**
     * 状态 0正常 1禁用
     */
    private Integer status;

}

