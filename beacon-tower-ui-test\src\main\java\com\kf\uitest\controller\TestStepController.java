package com.kf.uitest.controller;

import com.kf.uitest.common.ResponseDoMain;
import com.kf.uitest.enums.ActionType;
import com.kf.uitest.service.UiTestStepService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/test-step")
public class TestStepController {

    @Resource
    private UiTestStepService uiTestStepService;

    /**
     * 获取测试用例步骤
     *
     * @return 树结构的数据列表
     */
    @GetMapping("/getTestSteps")
    public ResponseDoMain getTestSteps(@RequestParam("testCaseId") String testCaseId) {
        return ResponseDoMain.custom("", true, uiTestStepService.findByCaseId(testCaseId), 200);
    }

    /**
     * 获取所有动作类型选项
     *
     * @return 动作类型选项列表
     */
    @GetMapping("/getActionTypes")
    public ResponseDoMain getActionTypes() {
        List<Map<String, String>> actionTypes = Arrays.stream(ActionType.values())
                .map(type -> {
                    Map<String, String> option = new HashMap<>();
                    option.put("label", type.getDescription());
                    option.put("value", type.name());
                    return option;
                })
                .collect(Collectors.toList());
        return ResponseDoMain.custom("", true, actionTypes, 200);
    }

}
