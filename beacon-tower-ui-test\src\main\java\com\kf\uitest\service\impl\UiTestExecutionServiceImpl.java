package com.kf.uitest.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.JsonPath;
import com.kf.uitest.dto.execution.*;
import com.kf.uitest.entity.UiTestHook;
import com.kf.uitest.enums.AssertionOperator;
import com.kf.uitest.enums.HookActionType;
import com.kf.uitest.enums.StepStatus;
import com.kf.uitest.enums.TestStatus;
import com.kf.uitest.exception.TestExecutionException;
import com.kf.uitest.manager.BrowserManager;
import com.kf.uitest.model.*;
import com.kf.uitest.service.*;
import com.microsoft.playwright.Browser;
import com.microsoft.playwright.ElementHandle;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Response;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.RequestOptions;
import com.microsoft.playwright.options.WaitUntilState;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.python.core.PyObject;
import org.python.util.PythonInterpreter;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
@AllArgsConstructor
public class UiTestExecutionServiceImpl implements UiTestExecutionService {

    private final BrowserManager browserManager;
    private final UiTestEnvironmentVariableService variableService;
    @Resource
    private UiTestDataSource uiTestDataSource;

    @Override
    public TestResult executeTestPlan(List<ExecutionGroupDTO> executionGroups, TestExecutionContext context) {
        TestResult result = new TestResult(context.getExecutionId());
        result.setStartTime(LocalDateTime.now());
        
        try {
            if (context.isConcurrent()) {
                executeConcurrently(executionGroups, context, result);
            } else {
                executeSequentially(executionGroups, context, result);
            }
        } catch (Exception e) {
            log.error("测试执行失败: {}", context.getExecutionId(), e);
            result.setStatus(TestStatus.FAILED);
            result.setErrorMessage(e.getMessage());
        } finally {
            result.setEndTime(LocalDateTime.now());
            // 持久化变量
            saveVariables(context);
        }
        
        return result;
    }

    private void executeConcurrently(List<ExecutionGroupDTO> executionGroups, TestExecutionContext context, TestResult result) {
        ExecutorService executor = Executors.newFixedThreadPool(executionGroups.size());
        try {
            List<Future<GroupResult>> futures = executionGroups.stream()
                .map(group -> executor.submit(() -> executeGroup(group, context)))
                .toList();

            for (Future<GroupResult> future : futures) {
                GroupResult groupResult = future.get();
                result.addGroupResult(groupResult);
            }
        } catch (Exception e) {
            throw new TestExecutionException("并发执行失败", e);
        } finally {
            executor.shutdownNow();
        }
    }

    private void executeSequentially(List<ExecutionGroupDTO> executionGroups, TestExecutionContext context, TestResult result) {
        for (ExecutionGroupDTO group : executionGroups) {
            GroupResult groupResult = executeGroup(group, context);
            result.addGroupResult(groupResult);
        }
    }

    private GroupResult executeGroup(ExecutionGroupDTO group, TestExecutionContext context) {
        GroupResult result = new GroupResult(group.getId());
        Browser browser = null;
        
        try {
            // 创建或获取浏览器实例
            browser = getBrowserInstance(context, group.getId());
            
            // 执行组内的所有用例
            for (ExecutionCaseDTO caseDTO : group.getCases()) {
                CaseResult caseResult = executeCase(caseDTO, browser, context);
                result.addCaseResult(caseResult);
                
                if (!context.isContinueOnCaseFailure() && caseResult.getStatus() == TestStatus.FAILED) {
                    break;
                }
            }
        } finally {
            // 关闭浏览器实例
            closeBrowserInstance(context, group.getId(), browser);
        }
        
        return result;
    }

    private Browser getBrowserInstance(TestExecutionContext context, String groupId) {
        BrowserManager.BrowserOptions options = BrowserManager.BrowserOptions.builder()
            .headless(true)
            .executionSpeed(context.getExecutionSpeed())
            .shareBrowser(context.isShareBrowser())
            .build();
            
        return browserManager.getBrowser(
            context.isShareBrowser() ? context.getExecutionId() : groupId,
            context.getBrowserEngine(),
            options
        );
    }

    private void closeBrowserInstance(TestExecutionContext context, String groupId, Browser browser) {
        if (browser != null && !context.isShareBrowser()) {
            browserManager.closeBrowser(groupId, false);
        }
    }

    private void saveVariables(TestExecutionContext context) {
        Map<String, Object> persistentVars = context.getVariables();
        if (!persistentVars.isEmpty()) {
            variableService.updateVariables(context.getEnvironmentId(), persistentVars);
        }
    }

    private CaseResult executeCase(ExecutionCaseDTO caseDTO, Browser browser, TestExecutionContext context) {
        CaseResult result = new CaseResult(caseDTO.getId());
        
        // 为每个用例创建一个新页面
        Page page = null;
        
        try {
            // 为每个用例创建一个新页面
            page = browser.newPage();
            
            // 1. 执行用例前置钩子
            if (!executeHooks(caseDTO.getPreHooks(), browser, context, page)) {
                result.setStatus(TestStatus.FAILED);
                result.setErrorMessage("用例前置钩子执行失败");
                return result;
            }
            
            // 2. 执行用例中的所有步骤块
            for (StepBlockDTO block : caseDTO.getStepBlocks()) {
                try {
                    executeStepBlock(block, browser, context, result, page);
                    
                    // 如果块执行失败且不继续执行，则退出
                    if (!context.isContinueOnStepFailure() && result.getStatus() == TestStatus.FAILED) {
                        break;
                    }
                } catch (Exception e) {
                    log.error("步骤块执行失败: {}", block.getId(), e);
                    if (!context.isContinueOnStepFailure()) {
                        throw e;
                    }
                }
            }
            
            // 3. 执行用例后置钩子
            executeHooks(caseDTO.getPostHooks(), browser, context, page);
            
        } catch (Exception e) {
            log.error("用例执行失败: {}", caseDTO.getId(), e);
            result.setStatus(TestStatus.FAILED);
            result.setErrorMessage(e.getMessage());
            
            // 检查是否需要重试
            if (context.canRetryCaseExecution(caseDTO.getId())) {
                context.incrementCaseRetryCount(caseDTO.getId());
                log.info("重试用例执行: {}, 尝试次数: {}", caseDTO.getId(), 
                    context.getCaseRetryCount().get(caseDTO.getId()));
                return executeCase(caseDTO, browser, context);
            }
        } finally {
            result.complete();
            
            // 关闭当前页面
            if (page != null) {
                page.close();
            }
        }
        
        return result;
    }

    private void executeStepBlock(StepBlockDTO block, Browser browser, TestExecutionContext context, CaseResult caseResult, Page page) {
        switch (block.getType()) {
            case NORMAL:
                executeNormalStepBlock(block, browser, context, caseResult, page);
                break;
            case LOOP:
                executeLoopStepBlock(block, browser, context, caseResult, page);
                break;
            case CONDITION:
                executeConditionStepBlock(block, browser, context, caseResult, page);
                break;
            default:
                throw new TestExecutionException("不支持的块类型: " + block.getType());
        }
    }

    private void executeNormalStepBlock(StepBlockDTO block, Browser browser, TestExecutionContext context, CaseResult caseResult, Page page) {
        // 执行块中的所有步骤
        for (ExecutionStepDTO step : block.getSteps()) {
            StepExecutionResult stepResult = executeStep(step, browser, context, page);
            caseResult.addStepResult(stepResult);
            
            // 如果步骤执行失败且不继续执行，则退出
            if (!context.isContinueOnStepFailure() && TestStatus.FAILED.equals(stepResult.getStatus())) {
                break;
            }
        }
    }

    private void executeLoopStepBlock(StepBlockDTO block, Browser browser, TestExecutionContext context, CaseResult caseResult, Page page) {
        // 执行前置钩子（作为循环条件 - while-do模式）
        List<UiTestHook> preHooks = block.getPreHooks();
        boolean shouldContinue = true;
        
        // 初始化循环计数器
        context.getLoopIterations().computeIfAbsent(block.getId(), k -> new java.util.concurrent.atomic.AtomicInteger(0));
        
        // while-do模式：先检查条件，再执行循环体
        if (!preHooks.isEmpty()) {
            shouldContinue = executeHooks(preHooks, browser, context, page);
        }
        
        while (shouldContinue && context.getLoopIterations().get(block.getId()).get() < getMaxLoopCount(block)) {
                // 执行块中的所有步骤
                for (ExecutionStepDTO step : block.getSteps()) {
                StepExecutionResult stepResult = executeStep(step, browser, context, page);
                    caseResult.addStepResult(stepResult);
                    
                if (!context.isContinueOnStepFailure() && TestStatus.FAILED.equals(stepResult.getStatus())) {
                        return;
                    }
                }
                
                // 增加循环计数
            context.getLoopIterations().get(block.getId()).incrementAndGet();
            
            // 检查后置钩子（作为循环条件 - do-while模式）
            List<UiTestHook> postHooks = block.getPostHooks();
            if (!postHooks.isEmpty()) {
                shouldContinue = executeHooks(postHooks, browser, context, page);
            } else if (!preHooks.isEmpty()) {
                // 如果没有后置钩子但有前置钩子，则重新检查前置钩子
                shouldContinue = executeHooks(preHooks, browser, context, page);
            }
        }
    }

    private void executeConditionStepBlock(StepBlockDTO block, Browser browser, TestExecutionContext context, CaseResult caseResult, Page page) {
        // 条件块必须有前置钩子作为条件判断
        List<UiTestHook> preHooks = block.getPreHooks();
        if (preHooks.isEmpty()) {
            throw new TestExecutionException("条件块必须有前置钩子: " + block.getId());
        }
        
        // 执行条件判断
        if (executeHooks(preHooks, browser, context, page)) {
            // 条件成立，执行块中的步骤
            for (ExecutionStepDTO step : block.getSteps()) {
                StepExecutionResult stepResult = executeStep(step, browser, context, page);
                caseResult.addStepResult(stepResult);
                
                if (!context.isContinueOnStepFailure() && TestStatus.FAILED.equals(stepResult.getStatus())) {
                    break;
                }
            }
            
            // 执行后置钩子
            List<UiTestHook> postHooks = block.getPostHooks();
            executeHooks(postHooks, browser, context, page);
        }
    }
    
    private int getMaxLoopCount(StepBlockDTO block) {
        // 默认最大循环次数为100，防止无限循环
        return 100;
    }

    private boolean executeHooks(List<UiTestHook> hooks, Browser browser, TestExecutionContext context, Page page) {
        if (hooks == null || hooks.isEmpty()) {
            return true;
        }
        
        boolean allSuccess = true;
        for (UiTestHook hook : hooks) {
            try {
                // 执行钩子
                 HookExecutionResult hookResult = executeHook(hook, browser, context, page);
                
                // 如果钩子执行失败，则标记失败
                if (!hookResult.isSuccess()) {
                    allSuccess = false;
                    log.error("钩子执行失败: {}, 错误: {}", hook.getId(), hookResult.getMessage());
                }
                
                // 如果钩子执行失败且不继续执行，则退出
                if (!allSuccess && !context.isContinueOnStepFailure()) {
                    break;
                }
                
                // 将钩子中提取的变量添加到上下文
                if (hookResult.getVariables() != null) {
                    context.getVariables().putAll(hookResult.getVariables());
                }
                
            } catch (Exception e) {
                log.error("钩子执行异常: {}", hook.getId(), e);
                allSuccess = false;
                if (!context.isContinueOnStepFailure()) {
                    break;
                }
            }
        }
        
        return allSuccess;
    }

    private  HookExecutionResult executeHook(UiTestHook hook, Browser browser, TestExecutionContext context, Page page) {
        if (hook == null) {
            return  HookExecutionResult.success();
        }
        
        try {
            // 根据钩子类型执行不同的操作
            HookActionType actionType = HookActionType.valueOf(hook.getActionType());

            return switch (actionType) {
                // 断言相关
                case ASSERT_ELEMENT, ASSERT_TEXT, ASSERT_URL, ASSERT_TITLE, ASSERT_ATTRIBUTE, ASSERT_VISIBLE,
                     ASSERT_ENABLED, ASSERT_SELECTED -> executeAssertHook(hook, browser, context, page);

                // 数据库相关
                case DB_QUERY -> executeSqlHook(hook, browser, context);
                case DB_ASSERT -> executeDbAssertHook(hook, browser, context);
                case DB_EXTRACT -> executeDbExtractHook(hook, browser, context);

                // 接口相关
                case API_LISTEN -> executeApiListenHook(hook, browser, context, page);
                case API_ASSERT -> executeApiAssertHook(hook, browser, context, page);
                case API_EXTRACT -> executeApiExtractHook(hook, browser, context, page);

                // 变量相关
                case SET_VARIABLE -> executeSetVariableHook(hook, browser, context);
                case GET_VARIABLE -> executeGetVariableHook(hook, browser, context);
                
                // 提取相关
                case EXTRACT_ELEMENT, EXTRACT_RESPONSE, EXTRACT_DB -> 
                    executeExtractHook(hook, browser, context, page);

                // 等待相关
                case WAIT_ELEMENT, WAIT_NETWORK_IDLE, WAIT_LOAD_STATE, WAIT_TIME ->
                        executeWaitHook(hook, browser, context, page);

                // 脚本相关
                case EXECUTE_JS -> executeJsHook(hook, browser, context, page);
                case EXECUTE_PYTHON -> executePythonHook(hook, browser, context, page);

                // 用例级别钩子
                case RUN_CASE -> executeRunCaseHook(hook, browser, context);
                case SET_ENVIRONMENT -> executeSetEnvironmentHook(hook, browser, context);
                case CLEAN_ENVIRONMENT -> executeCleanEnvironmentHook(hook, browser, context);
                case INITIALIZE_DATA -> executeInitializeDataHook(hook, browser, context);
                case CLEAN_DATA -> executeCleanDataHook(hook, browser, context);
            };
        } catch (Exception e) {
            log.error("执行钩子失败: {}", hook.getId(), e);
            return  HookExecutionResult.failure(e.getMessage());
        }
    }

    private StepExecutionResult executeStep(ExecutionStepDTO step, Browser browser, TestExecutionContext context, Page page) {
        StepExecutionResult stepResult = StepExecutionResult.builder()
            .stepId(step.getId())
            .startTime(LocalDateTime.now())
            .build();
        
        try {
            // 1. 执行步骤前置钩子
            List<UiTestHook> preHooks = step.getPreHooks();
            if (!executeHooks(preHooks, browser, context, page)) {
                stepResult.setStatus(StepStatus.FAILED);
                stepResult.setErrorMessage("步骤前置钩子执行失败");
                return stepResult;
            }
            
            // 2. 执行步骤
            try {
                executeStepAction(step.getConfig(), browser, context, page);
                stepResult.setStatus(StepStatus.SUCCESS);
            } catch (Exception e) {
                log.error("步骤执行失败: {}", step.getId(), e);
                stepResult.setStatus(StepStatus.FAILED);
                stepResult.setErrorMessage(e.getMessage());
                
                // 检查是否需要重试
                if (context.canRetryStepExecution(step.getId())) {
                    context.incrementStepRetryCount(step.getId());
                    log.info("重试步骤执行: {}, 尝试次数: {}", step.getId(), 
                        context.getStepRetryCount().get(step.getId()));
                    return executeStep(step, browser, context, page);
                }
            }
            
            // 3. 执行步骤后置钩子
            List<UiTestHook> postHooks = step.getPostHooks();
            executeHooks(postHooks, browser, context, page);
            
        } finally {
            stepResult.setEndTime(LocalDateTime.now());
            stepResult.setVariables(new HashMap<>(context.getVariables()));
        }
        
        return stepResult;
    }

    private void executeStepAction(StepConfigDTO config, Browser browser, TestExecutionContext context, Page page) {
        // 使用当前页面，不创建新页面
        if (page == null) {
            throw new TestExecutionException("当前没有可用的页面");
        }
        
        try {
            // 等待页面加载完成
            page.waitForLoadState();
            
            // 执行动作
            switch (config.getAction()) {
                case CLICK_ELEMENT:
                    executeClick(page, config);
                    break;
                    
                case INPUT_ELEMENT:
                    executeInput(page, config, context);
                    break;
                    
                case SELECT_DROPDOWN:
                    executeSelect(page, config, context);
                    break;
                    
                case VISIT_URL:
                    executeNavigate(page, config, context);
                    break;
                    
                case WAIT_ELEMENT_VALUE:
                case FORCE_WAIT_ELEMENT:
                    executeWait(page, config);
                    break;
                    
                case TAKE_SCREENSHOT:
                    executeScreenshot(page, config, context);
                    break;
                    
                case OPEN_NEW_TAB:
                    // 只有在明确要求打开新标签页时才创建新页面
                    browser.newPage();
                    break;
                    
                default:
                    throw new TestExecutionException("不支持的动作类型: " + config.getAction());
            }
        } catch (Exception e) {
            throw new TestExecutionException("执行步骤动作失败: " + e.getMessage(), e);
        }
    }

    private void executeClick(Page page, StepConfigDTO config) {
        page.click(config.getLocator(), new Page.ClickOptions()
            .setTimeout(config.getTimeout() * 1000.0));
    }

    private void executeInput(Page page, StepConfigDTO config, TestExecutionContext context) {
        String value = replaceVariables(config.getValue(), context.getVariables());
        page.fill(config.getLocator(), value, new Page.FillOptions()
            .setTimeout(config.getTimeout() * 1000.0));
    }

    private void executeSelect(Page page, StepConfigDTO config, TestExecutionContext context) {
        String value = replaceVariables(config.getValue(), context.getVariables());
        page.selectOption(config.getLocator(), value, new Page.SelectOptionOptions()
            .setTimeout(config.getTimeout() * 1000.0));
    }

    private void executeNavigate(Page page, StepConfigDTO config, TestExecutionContext context) {
        String url = replaceVariables(config.getValue(), context.getVariables());
        Response response = page.navigate(url, new Page.NavigateOptions()
            .setTimeout(config.getTimeout() * 1000.0)
            .setWaitUntil(WaitUntilState.LOAD));
            
        if (response == null || !response.ok()) {
            throw new TestExecutionException("导航失败: " + url);
        }
    }

    private void executeWait(Page page, StepConfigDTO config) {
        if (config.getLocator() != null) {
            // 等待元素
            page.waitForSelector(config.getLocator(), new Page.WaitForSelectorOptions()
                .setTimeout(config.getTimeout() * 1000.0));
        } else {
            // 固定等待时间
            page.waitForTimeout(config.getTimeout() * 1000.0);
        }
    }

    private void executeScreenshot(Page page, StepConfigDTO config, TestExecutionContext context) {
        String path = replaceVariables(config.getValue(), context.getVariables());
        page.screenshot(new Page.ScreenshotOptions()
            .setPath(Paths.get(path))
            .setFullPage(true));
    }

    private boolean compareValues(Object actual, String expected, AssertionOperator operator) {
        if (actual == null) {
            return operator == AssertionOperator.IS_NULL;
        }
        
        String actualStr = String.valueOf(actual);
        
        switch (operator) {
            case EQUALS:
                return actualStr.equals(expected);
            case NOT_EQUALS:
                return !actualStr.equals(expected);
            case CONTAINS:
                return actualStr.contains(expected);
            case NOT_CONTAINS:
                return !actualStr.contains(expected);
            case STARTS_WITH:
                return actualStr.startsWith(expected);
            case ENDS_WITH:
                return actualStr.endsWith(expected);
            case MATCHES:
                return actualStr.matches(expected);
            case NOT_MATCHES:
                return !actualStr.matches(expected);
            case IS_NULL:
                return actual == null;
            case IS_NOT_NULL:
                return actual != null;
            case IS_EMPTY:
                return actualStr.isEmpty();
            case IS_NOT_EMPTY:
                return !actualStr.isEmpty();
            default:
                throw new TestExecutionException("不支持的操作符: " + operator);
        }
    }
    
    private String replaceVariables(String input, Map<String, Object> variables) {
        if (StringUtils.isEmpty(input) || variables == null || variables.isEmpty()) {
            return input;
        }
        
        String result = input;
        
        // 匹配 ${variable} 格式的变量
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)}");
        Matcher matcher = pattern.matcher(input);
        
        while (matcher.find()) {
            String varName = matcher.group(1);
            Object varValue = variables.get(varName);
            
            if (varValue != null) {
                result = result.replace("${" + varName + "}", String.valueOf(varValue));
            }
        }
        
        return result;
    }

    private  HookExecutionResult executeAssertHook(UiTestHook hook, Browser browser, TestExecutionContext context, Page page) {
        try {
            // 解析断言配置
            String assertConfig = hook.getInputValue();
            if (StringUtils.isEmpty(assertConfig)) {
                return  HookExecutionResult.failure("断言配置为空");
            }
            
            // 解析JSON配置
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> config = mapper.readValue(assertConfig, new TypeReference<Map<String, Object>>() {});
            
            // 获取断言类型和参数
            String assertType = (String) config.get("type");
            String selector = (String) config.get("selector");
            String expectedValue = (String) config.get("expectedValue");
            String operator = (String) config.getOrDefault("operator", "EQUALS");
            
            // 替换变量
            if (expectedValue != null) {
                expectedValue = replaceVariables(expectedValue, context.getVariables());
            }
            
            // 使用当前页面执行断言
            if (page == null) {
                return  HookExecutionResult.failure("当前没有可用的页面");
            }
            
            // 根据断言类型执行不同的断言
            boolean assertResult = false;
            
            switch (assertType) {
                case "ELEMENT_PRESENT":
                    ElementHandle element = page.querySelector(selector);
                    assertResult = element != null;
                    break;
                    
                case "ELEMENT_TEXT":
                    String actualText = page.textContent(selector);
                    assertResult = compareValues(actualText, expectedValue, AssertionOperator.valueOf(operator));
                    break;
                    
                case "ELEMENT_ATTRIBUTE":
                    String attributeName = (String) config.get("attributeName");
                    String actualAttr = page.getAttribute(selector, attributeName);
                    assertResult = compareValues(actualAttr, expectedValue, AssertionOperator.valueOf(operator));
                    break;
                    
                case "PAGE_TITLE":
                    String actualTitle = page.title();
                    assertResult = compareValues(actualTitle, expectedValue, AssertionOperator.valueOf(operator));
                    break;
                    
                case "PAGE_URL":
                    String actualUrl = page.url();
                    assertResult = compareValues(actualUrl, expectedValue, AssertionOperator.valueOf(operator));
                    break;
                    
                default:
                    return  HookExecutionResult.failure("不支持的断言类型: " + assertType);
            }
            
            if (assertResult) {
                return  HookExecutionResult.success();
            } else {
                return  HookExecutionResult.failure("断言失败: " + assertType);
            }
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行断言钩子失败: " + e.getMessage());
        }
    }
    
    private  HookExecutionResult executeExtractHook(UiTestHook hook, Browser browser, TestExecutionContext context, Page page) {
        try {
            HookActionType actionType = HookActionType.valueOf(hook.getActionType());
            
            // 根据钩子类型执行不同的提取操作
            switch (actionType) {
                case EXTRACT_ELEMENT:
                    if (page == null) {
                        return  HookExecutionResult.failure("当前没有可用的页面");
                    }
                    
                    String selector = hook.getSelector();
                    // 解析parameters JSON字符串
                    ObjectMapper mapper = new ObjectMapper();
                    Map<String, Object> parameters = new HashMap<>();
                    if (!StringUtils.isEmpty(hook.getParameters())) {
                        parameters = mapper.readValue(hook.getParameters(), new TypeReference<Map<String, Object>>() {});
                    }
                    
                    String extractSource = (String) parameters.get("extractSource");
                    
                    if (StringUtils.isEmpty(extractSource)) {
                        return  HookExecutionResult.failure("提取来源为空");
                    }
                    
                    Object extractedValue;
                    switch (extractSource.toLowerCase()) {
                        case "text":
                            extractedValue = page.textContent(selector);
                            break;
                        case "value":
                            extractedValue = page.inputValue(selector);
                            break;
                        case "attribute":
                            String attributeName = (String) parameters.get("attributeName");
                            if (StringUtils.isEmpty(attributeName)) {
                                return  HookExecutionResult.failure("属性名为空");
                            }
                            extractedValue = page.getAttribute(selector, attributeName);
                            break;
                        default:
                            return  HookExecutionResult.failure("不支持的提取来源: " + extractSource);
                    }
                    
                    context.setVariable(hook.getHookName(), extractedValue);
                    
                    return  HookExecutionResult.success()
                            .addTestData("selector", selector)
                            .addTestData("extractSource", extractSource)
                            .addTestData("extractedValue", extractedValue)
                            .addVariable(hook.getHookName(), extractedValue);
                    
                case EXTRACT_RESPONSE:
                    // 获取响应对象
                    Response response = (Response) context.getVariable("lastResponse");
                    if (response == null) {
                        return  HookExecutionResult.failure("未找到响应对象");
                    }
                    
                    // 获取JSON路径
                    String jsonPathExpr = hook.getInputValue();
                    if (StringUtils.isEmpty(jsonPathExpr)) {
                        return  HookExecutionResult.failure("JSON路径为空");
                    }
                    
                    // 提取值
                    Object responseExtractedValue = JsonPath.read(response.text(), jsonPathExpr);
                    context.setVariable(hook.getHookName(), responseExtractedValue);
                    
                    return  HookExecutionResult.success()
                            .addTestData("jsonPath", jsonPathExpr)
                            .addTestData("extractedValue", responseExtractedValue)
                            .addVariable(hook.getHookName(), responseExtractedValue);
                    
                case EXTRACT_DB:
                    // 解析parameters JSON字符串
                    mapper = new ObjectMapper();
                    parameters = new HashMap<>();
                    if (!StringUtils.isEmpty(hook.getParameters())) {
                        parameters = mapper.readValue(hook.getParameters(), new TypeReference<Map<String, Object>>() {});
                    }
                    
                    // 获取SQL和数据库源
                    String dbSql = (String) parameters.get("sql");
                    String dbSourceName = (String) parameters.get("dbSource");
                    
                    if (StringUtils.isEmpty(dbSql)) {
                        return  HookExecutionResult.failure("SQL语句为空");
                    }
                    
                    if (StringUtils.isEmpty(dbSourceName)) {
                        return  HookExecutionResult.failure("数据库源为空");
                    }
                    
                    // 提取值
                    Object dbValue = executeDbExtract(dbSql, dbSourceName, parameters, context);
                    context.setVariable(hook.getHookName(), dbValue);
                    
                    return  HookExecutionResult.success()
                            .addTestData("sql", dbSql)
                            .addTestData("extractedValue", dbValue)
                            .addVariable(hook.getHookName(), dbValue);
                    
                default:
                    return  HookExecutionResult.failure("不支持的提取钩子类型: " + actionType);
            }
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行提取钩子失败: " + e.getMessage());
        }
    }
    
    private Object executeDbExtract(String sql, String dbSource, Map<String, Object> params, TestExecutionContext context) {
        try {
            // 替换SQL中的变量
            sql = replaceVariables(sql, context.getVariables());
            
            List<Map<String, Object>> results = uiTestDataSource.executeQuery(dbSource, sql, params);

            if (results.isEmpty()) {
                return null;
            } else if (results.size() == 1 && results.get(0).size() == 1) {
                return results.get(0).values().iterator().next();
            } else {
                return results;
            }
        } catch (Exception e) {
            log.error("数据库提取失败: {}", e.getMessage());
            return null;
        }
    }
    
    private  HookExecutionResult executeJsHook(UiTestHook hook, Browser browser, TestExecutionContext context, Page page) {
        try {
            String script = hook.getInputValue();
            if (StringUtils.isEmpty(script)) {
                return  HookExecutionResult.failure("脚本为空");
            }
            
            // 替换脚本中的变量
            script = replaceVariables(script, context.getVariables());
            
            // 使用当前页面执行脚本
            if (page == null) {
                return  HookExecutionResult.failure("当前没有可用的页面");
            }
            
            // 执行JavaScript脚本
            Object result = page.evaluate(script);
            
            // 如果脚本返回了变量，则添加到结果中
             HookExecutionResult hookResult =  HookExecutionResult.success();
            
            if (result instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> resultMap = (Map<String, Object>) result;
                hookResult.addVariables(resultMap);
            }
            
            return hookResult;
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行JavaScript钩子失败: " + e.getMessage());
        }
    }
    
    private  HookExecutionResult executePythonHook(UiTestHook hook, Browser browser, TestExecutionContext context, Page page) {
        try {
            String script = hook.getInputValue();
            if (StringUtils.isEmpty(script)) {
                return  HookExecutionResult.failure("脚本为空");
            }
            
            // 替换脚本中的变量
            script = replaceVariables(script, context.getVariables());
            
            // 创建Python解释器
            try (PythonInterpreter interpreter = new PythonInterpreter()) {
                // 将上下文变量传递给Python
                for (Map.Entry<String, Object> entry : context.getVariables().entrySet()) {
                    interpreter.set(entry.getKey(), entry.getValue());
                }
                
                // 执行Python脚本
                interpreter.exec(script);
                
                // 获取Python脚本中设置的变量
                 HookExecutionResult result =  HookExecutionResult.success();
                
                // 获取名为"result"的变量
                if (interpreter.get("result") != null) {
                    PyObject pyResult = interpreter.get("result");
                    result.addVariable("result", pyResult.__tojava__(Object.class));
                }
                
                // 获取名为"variables"的字典
                if (interpreter.get("variables") != null) {
                    PyObject pyVars = interpreter.get("variables");
                    @SuppressWarnings("unchecked")
                    Map<String, Object> vars = (Map<String, Object>) pyVars.__tojava__(Map.class);
                    result.addVariables(vars);
                }
                
                return result;
            }
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行Python钩子失败: " + e.getMessage());
        }
    }
    
    private HookExecutionResult executeSqlHook(UiTestHook hook, Browser browser, TestExecutionContext context) {
        try {
            // 解析SQL配置
            String sqlConfig = hook.getInputValue();
            if (StringUtils.isEmpty(sqlConfig)) {
                return  HookExecutionResult.failure("SQL配置为空");
            }
            
            // 解析JSON配置
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> config = mapper.readValue(sqlConfig, new TypeReference<Map<String, Object>>() {});
            
            // 获取数据库连接信息
            String url = (String) config.get("url");
            String username = (String) config.get("username");
            String password = (String) config.get("password");
            String sql = (String) config.get("sql");
            String variableName = (String) config.get("variableName");
            
            // 替换SQL中的变量
            sql = replaceVariables(sql, context.getVariables());
            
            // 创建数据源
            DataSource dataSource = DataSourceBuilder.create()
                .url(url)
                .username(username)
                .password(password)
                .build();
            
            // 执行SQL查询
            try (Connection conn = dataSource.getConnection();
                 Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                
                // 处理查询结果
                List<Map<String, Object>> resultList = new ArrayList<>();
                
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();
                
                while (rs.next()) {
                    Map<String, Object> row = new HashMap<>();
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        Object value = rs.getObject(i);
                        row.put(columnName, value);
                    }
                    resultList.add(row);
                }
                
                // 创建结果
                 HookExecutionResult result =  HookExecutionResult.success();
                
                // 如果指定了变量名，则将结果存储到变量中
                if (!StringUtils.isEmpty(variableName)) {
                    result.addVariable(variableName, resultList);
                }
                
                return result;
            }
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行SQL钩子失败: " + e.getMessage());
        }
    }
    
    private HookExecutionResult executeWaitHook(UiTestHook hook, Browser browser, TestExecutionContext context, Page page) {
        try {
            // 解析等待配置
            String waitConfig = hook.getInputValue();
            if (StringUtils.isEmpty(waitConfig)) {
                return HookExecutionResult.failure("等待配置为空");
            }
            
            // 解析JSON配置
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> config = mapper.readValue(waitConfig, new TypeReference<Map<String, Object>>() {});
            
            // 获取等待类型
            String waitType = (String) config.get("type");
            Integer timeout = (Integer) config.getOrDefault("timeout", 30000);
            
            // 使用当前页面执行等待
            if (page == null) {
                return  HookExecutionResult.failure("当前没有可用的页面");
            }
            
            switch (waitType) {
                case "TIMEOUT":
                    // 固定时间等待
                    page.waitForTimeout(timeout);
                    break;
                case "ELEMENT":
                    // 等待元素出现
                    String selector = (String) config.get("selector");
                    page.waitForSelector(selector, new Page.WaitForSelectorOptions().setTimeout(timeout.doubleValue()));
                    break;
                case "NAVIGATION":
                    // 等待页面导航完成
                    page.waitForNavigation(new Page.WaitForNavigationOptions().setTimeout(timeout.doubleValue()));
                    break;
                case "LOAD_STATE":
                    // 等待页面加载状态
//                    String state = (String) config.getOrDefault("state", "load");
                    page.waitForLoadState(LoadState.LOAD);
                    break;
                default:
                    return  HookExecutionResult.failure("不支持的等待类型: " + waitType);
            }
            
            return  HookExecutionResult.success();
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行等待钩子失败: " + e.getMessage());
        }
    }

    // 数据库断言钩子
    private  HookExecutionResult executeDbAssertHook(UiTestHook hook, Browser browser, TestExecutionContext context) {
        try {
            // 解析数据库断言配置
            String dbAssertConfig = hook.getInputValue();
            if (StringUtils.isEmpty(dbAssertConfig)) {
                return  HookExecutionResult.failure("数据库断言配置为空");
            }
            
            // 解析JSON配置
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> config = mapper.readValue(dbAssertConfig, new TypeReference<Map<String, Object>>() {});
            
            // 获取数据库连接信息
            String url = (String) config.get("url");
            String username = (String) config.get("username");
            String password = (String) config.get("password");
            String sql = (String) config.get("sql");
            String expectedResult = (String) config.get("expectedResult");
            String operator = (String) config.getOrDefault("operator", "EQUALS");
            
            // 替换SQL中的变量
            sql = replaceVariables(sql, context.getVariables());
            
            // 创建数据源
            DataSource dataSource = DataSourceBuilder.create()
                .url(url)
                .username(username)
                .password(password)
                .build();
            
            // 执行SQL查询
            try (Connection conn = dataSource.getConnection();
                 Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                
                // 处理查询结果
                if (rs.next()) {
                    Object actualValue = rs.getObject(1);
                    
                    // 执行断言比较
                    boolean result = compareValues(actualValue, expectedResult, AssertionOperator.valueOf(operator));
                    
                    if (result) {
                        return  HookExecutionResult.success();
                    } else {
                        String message = String.format("数据库断言失败: 预期[%s %s], 实际值[%s]", 
                            operator, expectedResult, actualValue);
                        return  HookExecutionResult.failure(message);
                    }
                } else {
                    return  HookExecutionResult.failure("数据库查询无结果");
                }
            }
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行数据库断言钩子失败: " + e.getMessage());
        }
    }
    
    // API监听钩子
    private  HookExecutionResult executeApiListenHook(UiTestHook hook, Browser browser, TestExecutionContext context, Page page) {
        try {
            // 解析API监听配置
            String apiListenConfig = hook.getInputValue();
            if (StringUtils.isEmpty(apiListenConfig)) {
                return  HookExecutionResult.failure("API监听配置为空");
            }
            
            // 解析JSON配置
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> config = mapper.readValue(apiListenConfig, new TypeReference<Map<String, Object>>() {});
            
            // 获取API监听配置
            String url = (String) config.get("url");
            String method = (String) config.getOrDefault("method", "GET");
            Integer timeout = (Integer) config.getOrDefault("timeout", 30000);
            String variableName = (String) config.get("variableName");
            
            // 替换变量
            url = replaceVariables(url, context.getVariables());
            
            // 使用当前页面执行API监听
            if (page == null) {
                return  HookExecutionResult.failure("当前没有可用的页面");
            }
            
            // 设置请求监听器
            String finalUrl1 = url;
            page.onRequest(request -> {
                if (request.url().contains(finalUrl1) && request.method().equalsIgnoreCase(method)) {
                    log.info("监听到API请求: {}", request.url());
                    
                    // 如果指定了变量名，则将请求存储到变量中
                    if (variableName != null) {
                        context.setVariable(variableName, request);
                    }
                }
            });
            
            // 设置响应监听器
            String finalUrl = url;
            page.onResponse(response -> {
                if (response.url().contains(finalUrl)) {
                    log.info("监听到API响应: {} [status={}]", response.url(), response.status());
                    
                    // 将响应存储到lastResponse变量中
                    context.setVariable("lastResponse", response);
                    
                    // 如果指定了变量名，则将响应存储到变量中
                    if (variableName != null) {
                        context.setVariable(variableName, response);
                    }
                }
            });
            
            // 等待指定的超时时间
            page.waitForTimeout(timeout);
            
            return  HookExecutionResult.success();
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行API监听钩子失败: " + e.getMessage());
        }
    }
    
    // API断言钩子
    private  HookExecutionResult executeApiAssertHook(UiTestHook hook, Browser browser, TestExecutionContext context, Page page) {
        try {
            // 解析API断言配置
            String apiAssertConfig = hook.getInputValue();
            if (StringUtils.isEmpty(apiAssertConfig)) {
                return  HookExecutionResult.failure("API断言配置为空");
            }
            
            // 解析JSON配置
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> config = mapper.readValue(apiAssertConfig, new TypeReference<Map<String, Object>>() {});
            
            // 获取API断言配置
            String url = (String) config.get("url");
            String method = (String) config.getOrDefault("method", "GET");
            String body = (String) config.get("body");
            String expectedStatus = (String) config.get("expectedStatus");
            String expectedBody = (String) config.get("expectedBody");
            String jsonPath = (String) config.get("jsonPath");
            String operator = (String) config.getOrDefault("operator", "EQUALS");
            
            // 替换变量
            url = replaceVariables(url, context.getVariables());
            if (body != null) {
                body = replaceVariables(body, context.getVariables());
            }
            if (expectedBody != null) {
                expectedBody = replaceVariables(expectedBody, context.getVariables());
            }
            
            // 使用当前页面执行API请求
            if (page == null) {
                return  HookExecutionResult.failure("当前没有可用的页面");
            }
            
            // 执行API请求
            Response response = page.request().fetch(url, new RequestOptions()
                .setMethod(method)
                .setData(body));
            
            // 检查状态码
            if (expectedStatus != null && !String.valueOf(response.status()).equals(expectedStatus)) {
                return  HookExecutionResult.failure(String.format("API状态码断言失败: 预期[%s], 实际[%s]", 
                    expectedStatus, response.status()));
            }
            
            // 检查响应体
            if (expectedBody != null) {
                String responseBody = response.text();
                
                if (jsonPath != null) {
                    // 使用JsonPath提取值进行比较
                    Object actualValue = JsonPath.read(responseBody, jsonPath);
                    
                    // 执行断言比较
                    boolean result = compareValues(actualValue, expectedBody, AssertionOperator.valueOf(operator));
                    
                    if (!result) {
                        return  HookExecutionResult.failure(String.format("API响应断言失败: 预期[%s %s], 实际值[%s]", 
                            operator, expectedBody, actualValue));
                    }
                } else if (!responseBody.equals(expectedBody)) {
                    return  HookExecutionResult.failure(String.format("API响应断言失败: 预期[%s], 实际[%s]", 
                        expectedBody, responseBody));
                }
            }
            
            return  HookExecutionResult.success();
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行API断言钩子失败: " + e.getMessage());
        }
    }
    
    // API提取钩子
    private  HookExecutionResult executeApiExtractHook(UiTestHook hook, Browser browser, TestExecutionContext context, Page page) {
        try {
            // 解析API提取配置
            String apiExtractConfig = hook.getInputValue();
            if (StringUtils.isEmpty(apiExtractConfig)) {
                return  HookExecutionResult.failure("API提取配置为空");
            }
            
            // 解析JSON配置
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> config = mapper.readValue(apiExtractConfig, new TypeReference<Map<String, Object>>() {});
            
            // 获取API提取配置
            String url = (String) config.get("url");
            String method = (String) config.getOrDefault("method", "GET");
            String body = (String) config.get("body");
            String jsonPath = (String) config.get("jsonPath");
            String variableName = (String) config.get("variableName");
            
            if (StringUtils.isEmpty(jsonPath)) {
                return  HookExecutionResult.failure("JsonPath为空");
            }
            
            if (StringUtils.isEmpty(variableName)) {
                return  HookExecutionResult.failure("变量名为空");
            }
            
            // 替换变量
            url = replaceVariables(url, context.getVariables());
            if (body != null) {
                body = replaceVariables(body, context.getVariables());
            }
            
            // 使用当前页面执行API请求
            if (page == null) {
                return  HookExecutionResult.failure("当前没有可用的页面");
            }
            
            // 执行API请求
            Response response = page.request().fetch(url, new RequestOptions()
                .setMethod(method)
                .setData(body));
            
            // 提取值
            String responseBody = response.text();
            Object extractedValue = JsonPath.read(responseBody, jsonPath);
            
            // 创建结果并添加提取的变量
             HookExecutionResult result =  HookExecutionResult.success();
            result.addVariable(variableName, extractedValue);
            
            return result;
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行API提取钩子失败: " + e.getMessage());
        }
    }
    
    // 设置变量钩子
    private  HookExecutionResult executeSetVariableHook(UiTestHook hook, Browser browser, TestExecutionContext context) {
        try {
            // 解析设置变量配置
            String setVariableConfig = hook.getInputValue();
            if (StringUtils.isEmpty(setVariableConfig)) {
                return  HookExecutionResult.failure("设置变量配置为空");
            }
            
            // 解析JSON配置
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> config = mapper.readValue(setVariableConfig, new TypeReference<Map<String, Object>>() {});
            
            // 获取变量名和值
            String variableName = (String) config.get("name");
            String variableValue = (String) config.get("value");
            
            // 替换值中的变量
            variableValue = replaceVariables(variableValue, context.getVariables());
            
            // 创建结果并添加变量
             HookExecutionResult result =  HookExecutionResult.success();
            result.addVariable(variableName, variableValue);
            
            return result;
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行设置变量钩子失败: " + e.getMessage());
        }
    }
    
    // 获取变量钩子
    private  HookExecutionResult executeGetVariableHook(UiTestHook hook, Browser browser, TestExecutionContext context) {
        try {
            // 解析获取变量配置
            String getVariableConfig = hook.getInputValue();
            if (StringUtils.isEmpty(getVariableConfig)) {
                return  HookExecutionResult.failure("获取变量配置为空");
            }
            
            // 解析JSON配置
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> config = mapper.readValue(getVariableConfig, new TypeReference<Map<String, Object>>() {});
            
            // 获取源变量名和目标变量名
            String sourceVariableName = (String) config.get("sourceVariable");
            String targetVariableName = (String) config.get("targetVariable");
            
            // 获取源变量值
            Object sourceValue = context.getVariables().get(sourceVariableName);
            
            if (sourceValue == null) {
                return  HookExecutionResult.failure("源变量不存在: " + sourceVariableName);
            }
            
            // 创建结果并添加变量
             HookExecutionResult result =  HookExecutionResult.success();
            result.addVariable(targetVariableName, sourceValue);
            
            return result;
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行获取变量钩子失败: " + e.getMessage());
        }
    }
    
    // 运行用例钩子
    private  HookExecutionResult executeRunCaseHook(UiTestHook hook, Browser browser, TestExecutionContext context) {
        try {
            // 解析运行用例配置
            String runCaseConfig = hook.getInputValue();
            if (StringUtils.isEmpty(runCaseConfig)) {
                return  HookExecutionResult.failure("运行用例配置为空");
            }
            
            // 解析JSON配置
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> config = mapper.readValue(runCaseConfig, new TypeReference<Map<String, Object>>() {});
            
            // 获取用例ID
            String caseId = (String) config.get("caseId");
            
            // TODO: 实现运行用例的逻辑
            
            return  HookExecutionResult.success();
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行运行用例钩子失败: " + e.getMessage());
        }
    }
    
    // 设置环境变量钩子
    private  HookExecutionResult executeSetEnvironmentHook(UiTestHook hook, Browser browser, TestExecutionContext context) {
        try {
            // 解析设置环境变量配置
            String setEnvironmentConfig = hook.getInputValue();
            if (StringUtils.isEmpty(setEnvironmentConfig)) {
                return  HookExecutionResult.failure("设置环境变量配置为空");
            }
            
            // 解析JSON配置
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> config = mapper.readValue(setEnvironmentConfig, new TypeReference<Map<String, Object>>() {});
            
            // 获取环境变量名和值
            String environmentName = (String) config.get("name");
            String environmentValue = (String) config.get("value");
            
            // 替换值中的变量
            environmentValue = replaceVariables(environmentValue, context.getVariables());
            
            // 设置环境变量
            variableService.updateVariable(context.getEnvironmentId(), environmentName, environmentValue);
            
            // 同时更新上下文中的变量
             HookExecutionResult result =  HookExecutionResult.success();
            result.addVariable(environmentName, environmentValue);
            
            return result;
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行设置环境变量钩子失败: " + e.getMessage());
        }
    }
    
    // 清理环境钩子
    private  HookExecutionResult executeCleanEnvironmentHook(UiTestHook hook, Browser browser, TestExecutionContext context) {
        try {
            // 解析清理环境配置
            String cleanEnvironmentConfig = hook.getInputValue();
            if (StringUtils.isEmpty(cleanEnvironmentConfig)) {
                return  HookExecutionResult.failure("清理环境配置为空");
            }
            
            // 解析JSON配置
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> config = mapper.readValue(cleanEnvironmentConfig, new TypeReference<Map<String, Object>>() {});
            
            // 获取要清理的环境变量名
            List<String> variableNames = mapper.convertValue(config.get("variableNames"), new TypeReference<List<String>>() {});
            
            // 清理环境变量
            for (String variableName : variableNames) {
//                variableService.deleteVariable(context.getEnvironmentId(), variableName);
                
                // 同时从上下文中移除变量
                context.getVariables().remove(variableName);
            }
            
            return  HookExecutionResult.success();
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行清理环境钩子失败: " + e.getMessage());
        }
    }
    
    // 初始化数据钩子
    private  HookExecutionResult executeInitializeDataHook(UiTestHook hook, Browser browser, TestExecutionContext context) {
        try {
            // 解析初始化数据配置
            String initializeDataConfig = hook.getInputValue();
            if (StringUtils.isEmpty(initializeDataConfig)) {
                return  HookExecutionResult.failure("初始化数据配置为空");
            }
            
            // 解析JSON配置
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> config = mapper.readValue(initializeDataConfig, new TypeReference<Map<String, Object>>() {});
            
            // 获取数据库连接信息
            String url = (String) config.get("url");
            String username = (String) config.get("username");
            String password = (String) config.get("password");
            List<String> sqlStatements = mapper.convertValue(config.get("sqlStatements"), new TypeReference<List<String>>() {});
            
            // 创建数据源
            DataSource dataSource = DataSourceBuilder.create()
                .url(url)
                .username(username)
                .password(password)
                .build();
            
            // 执行SQL语句
            try (Connection conn = dataSource.getConnection();
                 Statement stmt = conn.createStatement()) {
                
                for (String sql : sqlStatements) {
                    // 替换SQL中的变量
                    sql = replaceVariables(sql, context.getVariables());
                    
                    // 执行SQL语句
                    stmt.executeUpdate(sql);
                }
                
                return  HookExecutionResult.success();
            }
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行初始化数据钩子失败: " + e.getMessage());
        }
    }
    
    // 清理数据钩子
    private  HookExecutionResult executeCleanDataHook(UiTestHook hook, Browser browser, TestExecutionContext context) {
        try {
            // 解析清理数据配置
            String cleanDataConfig = hook.getInputValue();
            if (StringUtils.isEmpty(cleanDataConfig)) {
                return  HookExecutionResult.failure("清理数据配置为空");
            }
            
            // 解析JSON配置
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> config = mapper.readValue(cleanDataConfig, new TypeReference<Map<String, Object>>() {});
            
            // 获取数据库连接信息
            String url = (String) config.get("url");
            String username = (String) config.get("username");
            String password = (String) config.get("password");
            List<String> sqlStatements = mapper.convertValue(config.get("sqlStatements"), new TypeReference<List<String>>() {});
            
            // 创建数据源
            DataSource dataSource = DataSourceBuilder.create()
                .url(url)
                .username(username)
                .password(password)
                .build();
            
            // 执行SQL语句
            try (Connection conn = dataSource.getConnection();
                 Statement stmt = conn.createStatement()) {
                
                for (String sql : sqlStatements) {
                    // 替换SQL中的变量
                    sql = replaceVariables(sql, context.getVariables());
                    
                    // 执行SQL语句
                    stmt.executeUpdate(sql);
                }
                
                return  HookExecutionResult.success();
            }
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行清理数据钩子失败: " + e.getMessage());
        }
    }

    private  HookExecutionResult executeDbExtractHook(UiTestHook hook, Browser browser, TestExecutionContext context) {
        try {
            // 解析数据库提取配置
            String dbExtractConfig = hook.getInputValue();
            if (StringUtils.isEmpty(dbExtractConfig)) {
                return  HookExecutionResult.failure("数据库提取配置为空");
            }
            
            // 解析JSON配置
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> config = mapper.readValue(dbExtractConfig, new TypeReference<Map<String, Object>>() {});
            
            // 获取数据库提取配置
            String url = (String) config.get("url");
            String username = (String) config.get("username");
            String password = (String) config.get("password");
            String sql = (String) config.get("sql");
            String variableName = (String) config.get("variableName");
            
            if (StringUtils.isEmpty(sql)) {
                return  HookExecutionResult.failure("SQL语句为空");
            }
            
            if (StringUtils.isEmpty(variableName)) {
                return  HookExecutionResult.failure("变量名为空");
            }
            
            // 替换SQL中的变量
            sql = replaceVariables(sql, context.getVariables());
            
            // 创建数据源
            DataSource dataSource = DataSourceBuilder.create()
                .url(url)
                .username(username)
                .password(password)
                .build();
            
            // 执行SQL查询
            try (Connection conn = dataSource.getConnection();
                 Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                
                // 处理查询结果
                if (rs.next()) {
                    Object extractedValue = rs.getObject(1);
                    
                    // 创建结果并添加提取的变量
                     HookExecutionResult result =  HookExecutionResult.success();
                    result.addVariable(variableName, extractedValue);
                    
                    return result;
                } else {
                    return  HookExecutionResult.failure("数据库查询无结果");
                }
            }
        } catch (Exception e) {
            return  HookExecutionResult.failure("执行数据库提取钩子失败: " + e.getMessage());
        }
    }

}


