spring:
  profiles:
    active: test

# API配置
api:
  preview:
    # 预览API路径，可根据需要自定义
    path: /pv-manus-front/api/fs/preview

# AI评估配置
ai:
  evaluation:
    # 单次AI调用的最大token限制
    max-tokens: 8000
    # 提示词模板目录
    prompt-template-path: classpath:prompts/
    # 是否启用详细的控制台打印
    enable-console-print: true
    # 是否默认禁用数据分片（全局设置）
    disable-chunking-by-default: false
  # 火山引擎ARK API配置
  ark:
    # API端点
    api-url: https://ark.cn-beijing.volces.com/api/v3/chat/completions
    # API密钥
    api-key: c854450c-60c6-451c-8bd7-d0ada82a7519
    # 模型ID
    model: ep-20250723134959-88zhw
    # 请求超时时间（毫秒）- 增加到2分钟避免超时
    timeout: 120000
    # 最大重试次数
    max-retries: 3