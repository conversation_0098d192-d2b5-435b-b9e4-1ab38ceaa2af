# AI输出格式示例

## 📋 概述

本文档展示了修改后的AI输出格式示例。新格式的核心特点是：
- **只输出不一致的内容**，一致的内容不再输出
- **明确的扣分原因**，每个问题都有具体的扣分说明
- **结构化的Markdown格式**，便于阅读和解析

## 🔍 Recognize阶段输出示例

### 示例1：发现差异的情况
```markdown
# 临床试验报告识别阶段评估结果

## 数据差异识别
**注意：只列出发现的不一致内容，一致的内容不需要输出**

### 差异1: 受试者编号不一致
- **差异类型**: 受试者编号不一致
- **UAT环境内容**: SUB-001
- **TEST环境内容**: SUB-002
- **影响程度**: 高
- **是否影响评分**: 影响评分
- **扣分原因**: 受试者编号是关键识别信息，不一致会影响数据追溯，扣10分

### 差异2: 研究者签名差异
- **差异类型**: 研究者签名图片差异
- **UAT环境内容**: [签名图片A]
- **TEST环境内容**: [签名图片B]
- **影响程度**: 低
- **是否影响评分**: 不影响评分
- **扣分原因**: 研究者签名差异不影响评分，仅作记录

## 扣分汇总
[汇总所有影响评分的差异及其扣分情况]
- 总扣分项数: 1
- 主要扣分原因: 受试者编号不一致
- 扣分合计: 10分

## 评分
**最终评分**: 90
**评分说明**: 发现1个影响评分的差异（受试者编号不一致），扣10分，最终得分90分
```

### 示例2：无差异的情况
```markdown
# 临床试验报告识别阶段评估结果

## 数据差异识别
**注意：只列出发现的不一致内容，一致的内容不需要输出**

### 无差异发现
两个环境的数据完全一致，无需列出具体内容。

## 扣分汇总
[汇总所有影响评分的差异及其扣分情况]
- 总扣分项数: 0
- 主要扣分原因: 无
- 扣分合计: 0分

## 评分
**最终评分**: 100
**评分说明**: 两个环境的数据完全一致，无任何差异，满分100分
```

## 📊 Extraction阶段输出示例

### 示例1：发现问题的情况
```markdown
# 临床试验报告信息提取阶段评估结果

## 数据结构差异
**注意：只列出不一致的结构，一致的结构不需要输出**

### 无结构差异
两个环境的数据结构完全一致。

## 缺失字段问题
### 缺失字段1: 访问日期
- **缺失字段**: visitDate
- **UAT环境内容**: "2024-01-15"
- **TEST环境状态**: 字段缺失
- **重要性级别**: 关键
- **扣分原因**: 访问日期是关键临床数据，缺失会影响试验记录完整性，扣15分

## 多余字段问题
### 无多余字段
没有发现不应该提取的多余信息。

## 字段映射错误
### 无映射错误
所有字段映射都正确无误。

## 扣分汇总
- 总扣分项数: 1
- 主要扣分原因: 关键字段缺失
- 扣分合计: 15分

## 评分
**最终评分**: 85
**评分说明**: 发现1个关键字段缺失（访问日期），扣15分，最终得分85分
```

## 🏗️ Structured阶段输出示例

### 示例1：发现差异的情况
```markdown
# 临床试验报告结构化阶段评估结果

## 字段内容差异
**注意：只列出有差异的字段，一致的字段不需要输出**

### 字段差异1: quantitativeCompare
- **字段名称**: quantitativeCompare
- **UAT环境内容**: 85.6
- **TEST环境内容**: 85.7
- **差异类型**: 数值差异
- **影响程度**: 低
- **扣分原因**: 数值精度差异，可能影响数据一致性，扣5分

## 数据类型差异
**注意：只列出类型不一致的字段，类型一致的字段不需要输出**

### 无类型差异
所有字段的数据类型完全一致。

## 数据缺失问题
**注意：只列出有缺失的字段，完整的字段不需要输出**

### 无数据缺失
所有字段的数据都完整无缺失。

## 格式规范问题
**注意：只列出格式有问题的字段，符合规范的字段不需要输出**

### 无格式问题
所有字段的格式都符合规范要求。

## 扣分汇总
- 总扣分项数: 1
- 主要扣分原因: 数值精度差异
- 扣分合计: 5分

## 评分
**最终评分**: 95
**评分说明**: 发现1个轻微的数值差异，扣5分，最终得分95分
```

## 🔄 Transformer阶段输出示例

### 示例1：发现问题的情况
```markdown
# 临床试验报告转换阶段评估结果

## 数据结构差异
**注意：只列出有差异的结构，一致的结构不需要输出**

### 无结构差异
两个环境的数据结构和层次关系完全一致。

## 内容缺失问题
**注意：只列出缺失的内容，完整的内容不需要输出**

### 无内容缺失
所有内容都已完整转换，无缺失发现。

## 内容错误问题
**注意：只列出错误的内容，正确的内容不需要输出**

### 无内容错误
所有内容转换都正确无误。

## 相似度问题
**注意：只列出相似度低于90%的字段，高相似度字段不需要输出**

### 低相似度字段1: patientDescription
- **字段名称**: patientDescription
- **UAT环境内容**: "患者为65岁男性，有高血压病史"
- **TEST环境内容**: "65岁男性患者，既往有高血压"
- **相似度百分比**: 85%
- **差异原因**: 表述方式略有不同，但含义基本一致
- **扣分原因**: 相似度低于90%，可能影响信息一致性，扣8分

## 转换质量问题
**注意：只列出不符合标准的问题，符合标准的不需要输出**

### 无质量问题
数据转换质量完全符合标准要求。

## 扣分汇总
- 总扣分项数: 1
- 主要扣分原因: 字段相似度偏低
- 扣分合计: 8分

## 评分
**最终评分**: 92
**评分说明**: 发现1个字段相似度偏低，扣8分，最终得分92分
```

## 🎯 新格式的优势

### 1. **信息精简**
- 只显示有问题的内容，避免冗余信息
- 报告更加简洁，重点突出

### 2. **扣分透明**
- 每个问题都有明确的扣分原因
- 扣分汇总清晰展示总体情况

### 3. **结构统一**
- 所有阶段都采用相同的输出结构
- 便于自动化处理和解析

### 4. **无问题处理**
- 当某个方面无问题时，有明确的"无XX问题"输出
- 避免空白或模糊的表述

## 📝 使用说明

1. **开发人员**：可以快速定位问题所在，专注于修复有差异的部分
2. **测试人员**：能够清楚了解每个问题的严重程度和扣分情况
3. **项目管理**：通过扣分汇总快速评估整体质量状况

---

**更新时间**: 2025-07-25  
**版本**: v2.0 - 只输出差异版本
