import { defineStore } from 'pinia'

export interface JiraUser{
    self: string
    key: string
    name: string
    emailAddress: string
    avatarUrls: {
        '48x48': string
        '24x24': string
        '16x16': string
        '32x32': string
    }
    displayName: string
    displayNamePinyin: string
}

export const useDefectAssigneeStore = defineStore('defectAssignee', {
    state: () => ({
        cachedDefectAssignees: [] as <PERSON><PERSON><PERSON><PERSON>[]
    }),
    actions: {
        appendDefectAssignees(assignees: <PERSON><PERSON><PERSON><PERSON>[]) {
            this.cachedDefectAssignees.push(...assignees)
        }
    }
})