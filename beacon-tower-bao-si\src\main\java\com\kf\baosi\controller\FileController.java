package com.kf.baosi.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.kf.baosi.common.RequireHeader;
import com.kf.baosi.common.ResponseDoMain;
import com.kf.baosi.dto.XMindToExcelListDTO;
import com.kf.baosi.entity.TFile;
import com.kf.baosi.service.FileService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/file")
public class FileController {

    @Resource
    private FileService fileService;

    /**
     * 单文件上传
     */
    @RequireHeader("userId")
    @PostMapping("/upload")
    public ResponseDoMain upload(HttpServletRequest request, @RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return ResponseDoMain.custom("文件是空的!", false, "", 400);
        }
        return ResponseDoMain.custom("上传成功", true, fileService.uploadFile(file, request.getHeader("userId")), 200);
    }

    /**
     * 预览图片
     *
     * @param fileId 文件ID
     * @return 图片流
     */
    @GetMapping("/preview/{fileId}")
    public ResponseEntity<byte[]> previewImage(@PathVariable String fileId) {
        return fileService.readFileAsBytes(fileId);
    }

    /**
     * 单文件上传,上传文件类型为xml
     */
    @RequireHeader("userId")
    @PostMapping("/xmlUpload")
    public ResponseDoMain xmlUpload(HttpServletRequest request, @RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return ResponseDoMain.custom("文件是空的!", false, "", 400);
        }
        String fileName = file.getOriginalFilename();
        String suffixName = fileName.substring(fileName.lastIndexOf("."));
        //判断文件后缀名是否为.xml，如果不是则不允许上传
        if (!suffixName.endsWith(".xml")) {
            return ResponseDoMain.custom("文件类型错误!", false, "", 400);
        }
        fileService.xmlInsert(file, request.getHeader("userId"));
        return ResponseDoMain.custom("上传成功", true, "", 200);
    }

    /**
     * 查询setting.xml文件列表
     */
    @RequireHeader("userId")
    @GetMapping("/getXmlFileList")
    public ResponseDoMain queryXmlByUserId(HttpServletRequest request) {
        List<TFile> fileList = fileService.queryXmlByUserId(request.getHeader("userId"), ".xml");
        return ResponseDoMain.success(fileList);
    }

    /**
     * 通过ID删除maven编译用到的settings文件（逻辑删除）
     */
    @RequireHeader("userId")
    @PostMapping("/deleteFile")
    public ResponseDoMain deleteFile(HttpServletRequest request, @RequestBody String fileId) {
        String fileId1 = JSONUtil.parseObj(fileId).getStr("fileId");
        log.info("用户{}，删除文件ID：{}", request.getHeader("userId"), fileId1);
        int done = fileService.update(fileId1);
        if (done > 0) {
            return ResponseDoMain.custom("删除成功", true, "", 200);
        } else {
            return ResponseDoMain.custom("删除失败", false, "", 400);
        }
    }

    /**
     * 通过ID删除单条数据（物理删除）
     */
    @RequireHeader("userId")
    @PostMapping("/deleteFileById")
    public ResponseDoMain deleteFileById(HttpServletRequest request, @RequestParam("fileId") String fileId) {
        log.info("用户{}，删除文件ID：{}", request.getHeader("userId"), fileId);
        boolean done = fileService.deleteFileById(fileId);
        return ResponseDoMain.custom(done ? "删除成功" : "删除失败", done, "", 200);
    }

    /**
     * 获得xMind模板文件
     */
    @GetMapping("/getXMindTemplateFile")
    public void getXMindTemplateFile(HttpServletRequest req, HttpServletResponse response) {
        fileService.downloadXMindTemplateFile(req, response);
    }

    /**
     * 获得页面配置的模板文件
     */
    @GetMapping("/getInterfacePageTemplateFile")
    public void getInterfacePageTemplateFile(HttpServletRequest req, HttpServletResponse response) {
        fileService.downloadInterfacePageTemplateFile(req, response);
    }
}

