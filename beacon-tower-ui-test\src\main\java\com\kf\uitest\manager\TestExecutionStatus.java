package com.kf.uitest.manager;

import com.microsoft.playwright.Browser;
import lombok.Data;

import java.util.concurrent.atomic.AtomicBoolean;

@Data
public class TestExecutionStatus {
    private Long executionId;
    private AtomicBoolean stopFlag;    // 是否停止的标志
    private Thread executionThread;    // 执行该任务的线程
    private Browser browser;           // 用于后续停止时关闭浏览器

    public TestExecutionStatus(Long executionId) {
        this.executionId = executionId;
        this.stopFlag = new AtomicBoolean(false);
    }
}
