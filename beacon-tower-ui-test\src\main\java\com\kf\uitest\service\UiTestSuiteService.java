package com.kf.uitest.service;

import com.kf.uitest.entity.UiTestSuite;

import java.util.List;

public interface UiTestSuiteService {
    /**
     * 根据ID获取测试场景
     */
    UiTestSuite getById(String id);

    /**
     * 获取所有测试场景
     */
    List<UiTestSuite> findAll();

    /**
     * 保存测试场景
     */
    UiTestSuite save(UiTestSuite suite);

    /**
     * 删除测试场景
     */
    void delete(Long id);

    /**
     * 根据状态查找测试场景
     */
    List<UiTestSuite> findByStatus(String status);

    /**
     * 更新测试场景状态
     */
    boolean updateStatus(String id, Integer status);
}