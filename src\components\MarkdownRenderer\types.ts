/**
 * Markdown渲染组件类型定义
 */

export interface MarkdownRendererProps {
  /** Markdown内容 */
  content: string
  /** 主题样式 */
  theme?: 'light' | 'dark' | 'auto'
  /** 加载状态 */
  loading?: boolean
  /** 是否启用代码高亮 */
  highlight?: boolean
  /** 自定义CSS类名 */
  class?: string
  /** 最大高度（超出显示滚动条） */
  maxHeight?: string | number
}

export interface MarkdownRendererEmits {
  /** 内容渲染完成事件 */
  (e: 'rendered', html: string): void
  /** 渲染错误事件 */
  (e: 'error', error: Error): void
}

export interface HighlightLanguage {
  name: string
  aliases: string[]
  loaded: boolean
}

export interface MarkdownRendererInstance {
  /** 重新渲染内容 */
  render: () => void
  /** 获取渲染后的HTML */
  getHtml: () => string
  /** 清空内容 */
  clear: () => void
}
