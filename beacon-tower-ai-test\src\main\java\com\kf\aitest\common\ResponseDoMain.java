package com.kf.aitest.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 统一响应结果封装类
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResponseDoMain<T> {
    
    /**
     * 响应码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    public ResponseDoMain() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public ResponseDoMain(Integer code, String message) {
        this();
        this.code = code;
        this.message = message;
    }
    
    public ResponseDoMain(Integer code, String message, T data) {
        this(code, message);
        this.data = data;
    }
    
    /**
     * 成功响应
     */
    public static <T> ResponseDoMain<T> success() {
        return new ResponseDoMain<>(200, "操作成功");
    }
    
    /**
     * 成功响应带数据
     */
    public static <T> ResponseDoMain<T> success(T data) {
        return new ResponseDoMain<>(200, "操作成功", data);
    }
    
    /**
     * 成功响应带消息和数据
     */
    public static <T> ResponseDoMain<T> success(String message, T data) {
        return new ResponseDoMain<>(200, message, data);
    }
    
    /**
     * 失败响应
     */
    public static <T> ResponseDoMain<T> error() {
        return new ResponseDoMain<>(500, "操作失败");
    }
    
    /**
     * 失败响应带消息
     */
    public static <T> ResponseDoMain<T> error(String message) {
        return new ResponseDoMain<>(500, message);
    }
    
    /**
     * 失败响应带码和消息
     */
    public static <T> ResponseDoMain<T> error(Integer code, String message) {
        return new ResponseDoMain<>(code, message);
    }
}
