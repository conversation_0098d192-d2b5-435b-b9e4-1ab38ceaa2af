package com.kf.uitest.enums;

import lombok.Getter;

/**
 * 值提取器类型枚举
 */
@Getter
public enum ExtractorType {
    // 元素相关提取
    ELEMENT_TEXT("元素文本"),
    ELEMENT_VALUE("元素值"),
    ELEMENT_ATTRIBUTE("元素属性"),
    ELEMENT_CSS("元素CSS属性"),
    ELEMENT_PRESENT("元素存在性"),
    
    // 页面相关提取
    PAGE_TITLE("页面标题"),
    PAGE_URL("页面URL"),
    PAGE_SOURCE("页面源码"),
    
    // 变量相关提取
    VARIABLE("变量值"),
    
    // 数据库相关提取
    DB_FIELD("数据库字段"),
    DB_COUNT("数据库记录数"),
    
    // JSON相关提取
    JSON_PATH("JSON路径"),
    
    // 文件相关提取
    FILE_CONTENT("文件内容"),
    FILE_SIZE("文件大小"),
    
    // 性能相关提取
    PERFORMANCE_METRIC("性能指标"),
    
    // 网络相关提取
    NETWORK_RESPONSE("网络响应"),
    NETWORK_STATUS("网络状态");

    private final String description;
    
    ExtractorType(String description) {
        this.description = description;
    }
} 