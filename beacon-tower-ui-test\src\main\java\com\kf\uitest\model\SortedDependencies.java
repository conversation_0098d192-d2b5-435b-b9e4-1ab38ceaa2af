package com.kf.uitest.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashMap;
import java.util.stream.Collectors;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SortedDependencies {
    /**
     * 排序后的节点列表
     */
    private List<DependencyNode> sortedNodes;

    /**
     * 节点ID到其依赖节点ID集合的映射
     */
    private Map<Long, Set<Long>> dependencyMap;

    /**
     * 获取节点依赖关系映射
     */
    public Map<DependencyNode, Set<DependencyNode>> getDependencyNodeMap() {
        Map<DependencyNode, Set<DependencyNode>> nodeMap = new HashMap<>();
        Map<Long, DependencyNode> idToNodeMap = sortedNodes.stream()
                .collect(Collectors.toMap(DependencyNode::getId, node -> node));

        for (DependencyNode node : sortedNodes) {
            Set<Long> depIds = dependencyMap.get(node.getId());
            if (depIds != null) {
                Set<DependencyNode> depNodes = depIds.stream()
                        .map(idToNodeMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                nodeMap.put(node, depNodes);
            }
        }

        return nodeMap;
    }
}