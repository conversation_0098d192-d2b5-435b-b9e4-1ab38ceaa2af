package com.kf.aitest.controller;

import com.kf.aitest.common.ResponseDoMain;
import com.kf.aitest.service.ComparisonProgressManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

import static com.kf.aitest.common.ResponseDoMain.success;

/**
 * SSE监控控制器
 */
@Slf4j
@RestController
@RequestMapping("/sse-monitor")
public class SseMonitorController {
    
    @Autowired
    private ComparisonProgressManager progressManager;
    
    /**
     * 获取SSE连接统计信息
     * 
     * @return 连接统计
     */
    @GetMapping("/stats")
    public ResponseDoMain<Map<String, Object>> getStats() {
        try {
            String stats = progressManager.getConnectionStats();
            
            Map<String, Object> result = new HashMap<>();
            result.put("stats", stats);
            result.put("timestamp", System.currentTimeMillis());
            
            log.info("获取SSE统计信息: {}", stats);
            return success("获取统计信息成功", result);
            
        } catch (Exception e) {
            log.error("获取SSE统计信息失败: {}", e.getMessage(), e);
            return ResponseDoMain.error("获取统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * SSE健康检查
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseDoMain<Map<String, Object>> health() {
        try {
            Map<String, Object> health = new HashMap<>();
            health.put("status", "UP");
            health.put("service", "SSE Progress Manager");
            health.put("timestamp", System.currentTimeMillis());
            health.put("stats", progressManager.getConnectionStats());
            
            return success("SSE服务运行正常", health);
            
        } catch (Exception e) {
            log.error("SSE健康检查失败: {}", e.getMessage(), e);
            
            Map<String, Object> health = new HashMap<>();
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", System.currentTimeMillis());
            
            ResponseDoMain<Map<String, Object>> errorResponse = ResponseDoMain.error("SSE服务异常");
            errorResponse.setData(health);
            return errorResponse;
        }
    }
}
