package com.kf.baosi.utils;

import com.kf.baosi.dto.CompressResult;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.zip.Deflater;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

@Slf4j
public class compressorUtil {

    /**
     * 压缩指定目录下的所有图片文件
     *
     * @param directoryPath 图片文件所在目录
     * @param outputQuality 输出图片的质量（0.0 到 1.0 之间）
     * @throws IOException 如果文件操作失败
     */
    public static void compressImagesInDirectory(String directoryPath, double outputQuality) throws IOException {
        File directory = new File(directoryPath);

        if (!directory.isDirectory()) {
            throw new IllegalArgumentException("指定的路径不是目录：" + directoryPath);
        }

        // 获取目录下的所有文件
        File[] files = directory.listFiles();

        if (files == null) {
            throw new IOException("无法读取目录中的文件：" + directoryPath);
        }

        // 遍历文件进行压缩
        for (File file : files) {
            if (file.isFile()) {
                compressImage(file, outputQuality);
            }
        }
    }

    /**
     * 压缩单个图片文件
     *
     * @param imageFile     图片文件
     * @param outputQuality 输出图片的质量（0.0 到 1.0 之间）
     * @throws IOException 如果文件操作失败
     */
    private static void compressImage(File imageFile, double outputQuality) throws IOException {
        // 生成输出文件路径
        String outputFilePath = imageFile.getParent() + File.separator + "compressed_" + imageFile.getName();
        Thumbnails.of(imageFile)
                .outputFormat("jpeg")  // 输出格式
                .scale(0.6)  // 保持原尺寸的 50%
                .outputQuality(outputQuality)  // 设定图片质量
                .toFile(outputFilePath);  // 输出到新的文件中
        System.out.println("压缩完成: " + outputFilePath);
    }

    /**
     * 压缩图片
     *
     * @param pictureMap 图片map
     */
    public static CompressResult compressImage(Map<String, String> pictureMap) {
        log.info("原始图片map: {}", pictureMap);
        Map<String, String> resultMap = new ConcurrentHashMap<>();
        AtomicLong totalSize = new AtomicLong(0);

        pictureMap.entrySet().parallelStream().forEach(entry -> {
            String originalPath = entry.getValue();
            File originalFile = new File(originalPath);
            // 获取originalFile的文件后缀
            String suffix = originalFile.getName().substring(originalFile.getName().lastIndexOf('.'));
            String newFileName = originalFile.getName().substring(0, originalFile.getName().lastIndexOf('.')) + ".jpeg";
            String newFilePath = originalFile.getParent() + File.separator + newFileName;
            // 根据文件后缀选择压缩方式，jpg/jpeg 使用 0.75f，其他使用 0.5f
            if (suffix.equalsIgnoreCase(".jpg") || suffix.equalsIgnoreCase(".jpeg")) {
                compressImage(originalPath, newFilePath, 0.75f, 0.5);
            } else {
                compressImage(originalPath, newFilePath, 0.6f, 0.5);
            }
            // 记录压缩后的文件大小
            File compressedFile = new File(newFilePath);
            totalSize.addAndGet(compressedFile.length());
            resultMap.put(newFileName, newFilePath); // 使用压缩后的文件名作为 resultMap 的 key
        });

        // 使用原始pictureMap的顺序构建返回的Map
        Map<String, String> orderedResultMap = new LinkedHashMap<>();
        pictureMap.keySet().forEach(key -> {
            String newFileName = key.substring(0, key.lastIndexOf('.')) + ".jpeg";
            orderedResultMap.put(newFileName, resultMap.get(newFileName));
        });

        long totalSizeKB = totalSize.get() / 1024;
        log.info("压缩后的图片map: {}", orderedResultMap);
        return new CompressResult(orderedResultMap, totalSizeKB);
    }

//    public static CompressResult compressImage(Map<String, String> pictureMap) {
//        log.info("原始图片map: {}", pictureMap);
//        Map<String, String> resultMap = new ConcurrentHashMap<>();
//        AtomicLong totalSize = new AtomicLong(0);
//
//        pictureMap.entrySet().parallelStream().forEach(entry -> {
//            String originalPath = entry.getValue();
//            File originalFile = new File(originalPath);
//            // 获取图片文件的大小（单位：字节）
//            long fileSizeInBytes = originalFile.length();
//            float fileSizeInMB = fileSizeInBytes / (1024f * 1024f);
//            float fileSizeInKB = fileSizeInBytes / 1024f;
//
//            String suffix = originalFile.getName().substring(originalFile.getName().lastIndexOf('.'));
//            String newFileName = originalFile.getName().substring(0, originalFile.getName().lastIndexOf('.')) + ".jpeg";
//            String newFilePath = originalFile.getParent() + File.separator + newFileName;
//
//            // 根据图片文件的大小来调整压缩质量
//            float compressionQuality;
//            if (fileSizeInKB < 200) {
//                // 低于200KB，不压缩
//                compressionQuality = 1.0f;
//            } else if (fileSizeInMB < 2) {
//                // 200KB ~ 2MB之间，线性从1.0f降到0.8f
//                float minMB = 0.2f; // 200KB约等于0.2MB
//                float maxMB = 2.0f;
//                float startQuality = 1.0f;
//                float endQuality = 0.8f;
//                // 防止因为浮点精度导致的轻微越界，先进行clamp
//                float clampedMB = Math.max(minMB, Math.min(fileSizeInMB, maxMB));
//                compressionQuality = startQuality - (startQuality - endQuality) * ((clampedMB - minMB) / (maxMB - minMB));
//            } else if (fileSizeInMB < 10) {
//                // 2MB ~ 10MB之间，线性从0.8f降到0.6f
//                float minMB = 2.0f;
//                float maxMB = 10.0f;
//                float startQuality = 0.8f;
//                float endQuality = 0.6f;
//                float clampedMB = Math.max(minMB, Math.min(fileSizeInMB, maxMB));
//                compressionQuality = startQuality - (startQuality - endQuality) * ((clampedMB - minMB) / (maxMB - minMB));
//            } else {
//                // 大于10MB使用0.6f
//                compressionQuality = 0.6f;
//            }
//
//            // 确保compressionQuality在[0.0f, 1.0f]内
//            compressionQuality = Math.max(0.0f, Math.min(compressionQuality, 1.0f));
//
//            // 压缩图片
//            compressImage(originalPath, newFilePath, compressionQuality, 0.6f);
//            // 记录压缩后的文件大小
//            File compressedFile = new File(newFilePath);
//            totalSize.addAndGet(compressedFile.length());
//            resultMap.put(newFileName, newFilePath); // 使用压缩后的文件名作为 resultMap 的 key
//        });
//
//        // 使用原始pictureMap的顺序构建返回的Map
//        Map<String, String> orderedResultMap = new LinkedHashMap<>();
//        pictureMap.keySet().forEach(key -> {
//            String newFileName = key.substring(0, key.lastIndexOf('.')) + ".jpeg";
//            orderedResultMap.put(newFileName, resultMap.get(newFileName));
//        });
//
//        long totalSizeKB = totalSize.get() / 1024;
//        log.info("压缩后的图片map: {}", orderedResultMap);
//        return new CompressResult(orderedResultMap, totalSizeKB);
//    }


    /**
     * 压缩图片
     *
     * @param inputPath  输入文件路径
     * @param outputPath 输出文件路径
     * @param outputQuality 输出图片的质量（0.0 到 1.0 之间）
     * @param scale 图片尺寸
     */
    public static void compressImage(String inputPath, String outputPath, double outputQuality, double scale) {
        try {
            Thumbnails.of(inputPath)
                    .outputFormat("jpeg")   // 输出格式写死为 jpeg
                    .scale(scale)
                    .outputQuality(outputQuality)
                    .toFile(outputPath);
        } catch (IOException e) {
            log.error("压缩图片失败: {}", e.getMessage());
        }
    }

    public static void compressDocx(String inputPath, String outputPath, int CompressionLevel) {
        log.info("开始压缩文件: {}", inputPath);
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(inputPath));
             ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(outputPath));) {
            // 设置压缩级别
            zos.setLevel(Deflater.BEST_COMPRESSION);
            zos.setLevel(CompressionLevel);
            ZipEntry entry;
            byte[] buffer = new byte[1024];
            int len;
            while ((entry = zis.getNextEntry()) != null) {
                zos.putNextEntry(new ZipEntry(entry.getName()));
                while ((len = zis.read(buffer)) > 0) {
                    zos.write(buffer, 0, len);
                }
                zos.closeEntry();
                zis.closeEntry();
            }
            log.info("压缩文件成功: {}", outputPath);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static boolean isSupportedZipFile(MultipartFile file) {
        try (ZipInputStream zis = new ZipInputStream(file.getInputStream())) {
            // 尝试读取 ZIP 文件的第一个条目，如果可以读取说明文件格式正确
            if (zis.getNextEntry() != null) {
                // 是可以压缩的 ZIP 结构文件
                return true;
            }
        } catch (IOException e) {
            log.error("文件不能被压缩处理: {}", e.getMessage());
        }
        return false;
    }

}
