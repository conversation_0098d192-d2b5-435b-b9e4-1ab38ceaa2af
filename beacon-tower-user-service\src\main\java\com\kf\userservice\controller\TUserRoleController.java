package com.kf.userservice.controller;

import com.kf.userservice.entity.TUserRole;
import com.kf.userservice.service.UserRoleService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("userRole")
public class TUserRoleController {

    @Resource
    private UserRoleService userRoleService;

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("selectOne")
    public TUserRole selectOne(Long id) {
        return this.userRoleService.queryById(id);
    }

}