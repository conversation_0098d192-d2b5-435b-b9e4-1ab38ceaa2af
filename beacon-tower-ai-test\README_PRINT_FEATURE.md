# AI评估结果打印功能说明

## 功能概述

本功能为beacon-tower-ai-test模块添加了详细的阶段评分和AI回答结果打印功能，帮助开发者和用户实时查看AI评估过程和结果。

## 主要特性

### 🎯 详细的阶段打印
- **阶段开始提示**: 显示当前处理的阶段和数据ID
- **AI评估过程**: 实时显示AI调用状态和进度
- **阶段结果展示**: 完整显示评分、AI回答和处理信息

### 🤖 AI调用过程监控
- **API调用状态**: 显示请求构建、发送和响应状态
- **Token使用统计**: 详细的输入/输出/总计Token统计
- **多模态支持**: 区分文本和图文混合请求类型

### 📊 整体评估汇总
- **各阶段评分汇总**: 一目了然的评分对比
- **整体AI评估**: 综合评估结果和建议
- **任务执行统计**: 耗时、成功率等关键指标

## 配置选项

### 启用/禁用打印功能

在 `application.yml` 中配置：

```yaml
ai:
  evaluation:
    # 是否启用详细的控制台打印
    enable-console-print: true  # 默认为true
```

- `true`: 启用详细打印（推荐用于开发和测试环境）
- `false`: 禁用控制台打印（推荐用于生产环境）

## 打印内容示例

### 1. 任务开始
```
================================================================================
🎬 数据对比任务开始
📋 任务ID: TASK-123456
📊 待处理ID数量: 3
⏰ 开始时间: 2025-07-23 14:30:15
================================================================================
```

### 2. 阶段处理
```
🚀 开始处理阶段: 文档识别阶段 (ID: TEST001)
----------------------------------------

🤖 开始AI评估阶段: recognize
📊 数据分析: 是否需要分片处理 = 否
🔗 正在调用ARK AI服务...
   📝 提示词长度: 1250 字符
   📦 请求构建完成，模型: ep-20250723134959-88zhw
   📝 构建纯文本消息
   🚀 发送API请求到: https://ark.cn-beijing.volces.com/api/v3/chat/completions
   ✅ 收到API响应，状态码: 200 OK
   📊 Token使用统计:
      📥 输入Token: 320
      📤 输出Token: 85
      📈 总计Token: 405
✅ AI评估完成: 阶段=recognize, 评分=85, 分片处理=否
```

### 3. 阶段结果详情
```
------------------------------------------------------------
📊 阶段评估结果 - 文档识别阶段 (ID: TEST001)
------------------------------------------------------------
🔹 阶段名称: recognize
🔹 数据类型: json
🔹 处理状态: ✅ 成功
🔹 处理时间: 2025-07-23 14:30:18
🔹 处理耗时: 1500 ms

🤖 AI评估结果:
   📈 评分: 85 🟡/100
   💬 AI回答:
   ----------------------------------------
   数据识别阶段表现良好，文档结构清晰，字段识别准确率较高。
   建议优化部分边缘字段的识别精度。
   ----------------------------------------
------------------------------------------------------------
```

### 4. 整体评估结果
```
================================================================================
🎯 整体评估结果 (ID: TEST001)
================================================================================
📊 处理统计:
   🔹 总阶段数: 4
   🔹 成功阶段数: 4
   🔹 整体状态: ✅ 成功
   🔹 总耗时: 5000 ms (5.00 秒)
   🔹 开始时间: 2025-07-23 14:30:15
   🔹 结束时间: 2025-07-23 14:30:20

📈 各阶段评分汇总:
   🔸 文档识别阶段: ✅ 成功 (评分: 85 🟡/100)
   🔸 信息提取阶段: ✅ 成功 (评分: 90 🟢/100)
   🔸 结构化处理阶段: ✅ 成功 (评分: 87 🟡/100)
   🔸 数据转换阶段: ✅ 成功 (评分: 92 🟢/100)

🤖 整体AI评估:
   📈 综合评分: 88 🟡/100
   💬 整体评估:
   ------------------------------------------------------------
   整体数据处理质量优秀，各阶段表现稳定。数据一致性良好，
   符合临床试验标准。建议继续保持当前处理流程。
   ------------------------------------------------------------
================================================================================
```

## 评分颜色标识

- 🟢 **90-100分**: 优秀
- 🟡 **80-89分**: 良好  
- 🟠 **60-79分**: 一般
- 🔴 **0-59分**: 较差

## 测试接口

为了方便测试打印功能，提供了以下测试接口：

### 1. 测试阶段结果打印
```
GET /test/print/stage
```

### 2. 测试整体结果打印
```
GET /test/print/overall
```

### 3. 测试任务流程打印
```
GET /test/print/task-flow
```

### 4. 测试完整流程演示
```
GET /test/print/full-demo
```

## 使用建议

### 开发环境
- 建议启用详细打印 (`enable-console-print: true`)
- 便于调试和监控AI评估过程
- 可以实时查看评分和AI回答

### 生产环境
- 建议禁用详细打印 (`enable-console-print: false`)
- 减少控制台输出，提高性能
- 仍保留日志记录功能

### 调试技巧
1. 通过评分颜色快速识别问题阶段
2. 查看Token使用统计优化提示词长度
3. 观察AI回答内容调整评估策略

## 注意事项

1. **性能影响**: 详细打印会增加少量性能开销，生产环境建议关闭
2. **日志记录**: 即使关闭控制台打印，日志文件仍会记录关键信息
3. **多线程安全**: 打印功能在多线程环境下是安全的
4. **配置热更新**: 修改配置后需要重启应用才能生效

## 相关文件

- `ResultPrintService.java` - 打印服务接口
- `ResultPrintServiceImpl.java` - 打印服务实现
- `ResultPrintTestController.java` - 测试控制器
- `DataComparisonServiceImpl.java` - 集成打印功能的主服务
- `AiEvaluationServiceImpl.java` - 增强了AI调用过程打印
