package com.kf.baosi.enums;

import lombok.Getter;

@Getter
public enum CycleStatusEnum {

    Start("Start", "开始"),
    Complete("Complete", "完成"),
    Abort("Abort", "中止"),
    Resume("Resume", "恢复");

    private final String value;
    private final String name;

    CycleStatusEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    //获取枚举
    public static String getValue(String name) {
        for (CycleStatusEnum cycleStatusEnum : CycleStatusEnum.values()) {
            if (cycleStatusEnum.getName().equals(name)) {
                return cycleStatusEnum.value;
            }
        }
        throw new IllegalArgumentException("未找到对应的类型");
    }
}
