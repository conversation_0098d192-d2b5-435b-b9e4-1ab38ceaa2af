package com.kf.uitest.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.kf.uitest.dao.UiTestCaseMapper;
import com.kf.uitest.dao.UiTestSuiteMapper;
import com.kf.uitest.dto.CreateTestCaseRequest;
import com.kf.uitest.dto.CreateTestSuiteRequest;
import com.kf.uitest.dto.TreeNodeDTO;
import com.kf.uitest.entity.UiTestCase;
import com.kf.uitest.entity.UiTestSuite;
import com.kf.uitest.service.UiTestTreeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UiTestTreeServiceImpl implements UiTestTreeService {

    @Resource
    private UiTestSuiteMapper suiteMapper;

    @Resource
    private UiTestCaseMapper caseMapper;

    /**
     * 获取当前用户某个系统下的所有测试套件及其关联的UI测试用例
     *
     * @param userId    当前用户的ID
     * @param projectId 系统ID
     * @return 树结构的数据列表
     */
    @Override
    public List<TreeNodeDTO> getUserTestTree(Long userId, String projectId) {
        // 获取当前用户的所有测试套件
        List<UiTestSuite> suites = suiteMapper.selectList(
                new QueryWrapper<UiTestSuite>()
                        .lambda()
                        .eq(UiTestSuite::getUserId, userId)
                        .eq(UiTestSuite::getProjectId, projectId)
                        .orderByAsc(UiTestSuite::getCreateTime)
        );

        if (suites.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取所有测试套件的ID
        List<String> suiteIds = suites.stream().map(UiTestSuite::getId).collect(Collectors.toList());

        // 获取所有关联的测试用例
        List<UiTestCase> cases = caseMapper.selectList(
                new QueryWrapper<UiTestCase>()
                        .lambda()
                        .in(UiTestCase::getSuiteId, suiteIds)
                        .orderByAsc(UiTestCase::getCreateTime)
        );

        // 将测试用例按suite_id分组
        Map<String, List<UiTestCase>> suiteToCasesMap = cases.stream()
                .collect(Collectors.groupingBy(UiTestCase::getSuiteId));

        // 构建树结构
        List<TreeNodeDTO> tree = new ArrayList<>();
        for (UiTestSuite suite : suites) {
            TreeNodeDTO suiteNode = new TreeNodeDTO();
            suiteNode.setId(suite.getId());
            suiteNode.setLabel(suite.getSuiteName());

            List<UiTestCase> suiteCases = suiteToCasesMap.getOrDefault(suite.getId(), Collections.emptyList());
            if (!suiteCases.isEmpty()) {
                List<TreeNodeDTO> caseNodes = suiteCases.stream().map(testCase -> {
                    TreeNodeDTO caseNode = new TreeNodeDTO();
                    caseNode.setId(testCase.getId());
                    caseNode.setLabel(testCase.getCaseName());
                    return caseNode;
                }).collect(Collectors.toList());
                suiteNode.setChildren(caseNodes);
            }

            tree.add(suiteNode);
        }

        return tree;
    }

    /**
     * 创建测试套件
     *
     * @param userId  当前用户的ID
     * @param request 创建参数
     * @return 树结构的数据列表
     */
    @Override
    public TreeNodeDTO createTestSuite(Long userId, CreateTestSuiteRequest request) {
        // 创建新的测试套件实体
        UiTestSuite suite = new UiTestSuite();
        suite.setId(IdUtil.simpleUUID());
        suite.setProjectId(request.getProjectId());
        suite.setUserId(userId);
        suite.setSuiteName(request.getSuiteName());
        suite.setDescription(request.getDescription());
        suite.setStatus(1); // 默认启用
        suite.setCreateTime(LocalDateTime.now());
        suite.setUpdateTime(LocalDateTime.now());
        // 插入到数据库
        suiteMapper.insert(suite);

        // 转换为 TreeNodeDTO
        TreeNodeDTO suiteNode = new TreeNodeDTO();
        suiteNode.setId(suite.getId());
        suiteNode.setLabel(suite.getSuiteName());
        suiteNode.setChildren(new ArrayList<>()); // 新创建的套件暂时没有用例

        return suiteNode;
    }

    @Override
    public TreeNodeDTO createTestCase(Long userId, CreateTestCaseRequest request) {
        // 创建新的测试用例实体
        UiTestCase testCase = new UiTestCase();
        testCase.setId(IdUtil.simpleUUID());
        testCase.setSuiteId(request.getSuiteId());
        testCase.setUserId(userId);
        testCase.setCaseName(request.getCaseName());
        testCase.setDescription(request.getDescription());
        testCase.setCreateTime(LocalDateTime.now());
        testCase.setUpdateTime(LocalDateTime.now());
        // 插入到数据库
        caseMapper.insert(testCase);

        // 转换为 TreeNodeDTO
        TreeNodeDTO caseNode = new TreeNodeDTO();
        caseNode.setId(testCase.getId());
        caseNode.setLabel(testCase.getCaseName());
        caseNode.setChildren(new ArrayList<>()); // 新创建的用例没有子节点

        return caseNode;
    }
}
