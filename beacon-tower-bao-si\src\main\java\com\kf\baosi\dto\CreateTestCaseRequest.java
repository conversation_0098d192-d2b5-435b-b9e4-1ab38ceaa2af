package com.kf.baosi.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class CreateTestCaseRequest {
    @NotNull(message = "项目key不能为空")
    private String projectKey;
    private String testPlanKey;
    private String cycleId;
    private String cycleName;
    private String requirementKey;
    @NotNull(message = "用例版本号不能为空")
    private List<String> fixVersions;
    // 测试用例集
    private String testSuitePath;
    @NotNull(message = "用例名称不能为空")
    private String summary;
    @NotNull(message = "用例等级不能为空")
    private String level;
    private String precondition;
    private String assignee;
    @NotNull(message = "用例步骤不能为空")
    private List<TestCaseStep> testCaseSteps;

}
