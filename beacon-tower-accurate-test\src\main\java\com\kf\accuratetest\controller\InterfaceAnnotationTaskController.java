package com.kf.accuratetest.controller;

import com.kf.accuratetest.common.RequireHeader;
import com.kf.accuratetest.common.ResponseDoMain;
import com.kf.accuratetest.entity.UserInterfaceAnnotationTask;
import com.kf.accuratetest.service.UserInterfaceAnnotationTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@Tag(name = "接口注解任务相关接口", description = "接口注解任务相关接口")
public class InterfaceAnnotationTaskController {

    @Resource
    private UserInterfaceAnnotationTaskService userInterfaceAnnotationTaskService;

    /**
     * 通过userId查询任务列表
     */
    @RequireHeader("userId")
    @GetMapping("/getTaskList")
    @Operation(summary = "获取静态分析的历史任务列表", description = "获取静态分析的历史任务列表，返回9条数据")
    public ResponseDoMain queryByUserId(HttpServletRequest request) {
        return ResponseDoMain.custom("", true, this.userInterfaceAnnotationTaskService.queryByUserId(request.getHeader("userId")), 200);
    }

    /**
     * 通过userId查询已有的仓库列表
     */
    @RequireHeader("userId")
    @GetMapping("/getRepositoryList")
    @Operation(summary = "获取历史使用的仓库列表", description = "获取历史使用的仓库列表")
    public ResponseDoMain queryRepositoryListByUserId(HttpServletRequest request) {
        List<UserInterfaceAnnotationTask> userInterfaceAnnotationTaskList = userInterfaceAnnotationTaskService.queryRepositoryListByUserId(request.getHeader("userId"));
        //列表去重之后得到新集合
        List<String> repositoryList = userInterfaceAnnotationTaskList.stream().map(UserInterfaceAnnotationTask::getRepositoryUrl).distinct().toList();
        //将新集合转为map,仓库地址截取仓库名为key,将仓库地址作为value,方便前端展示
        Map<String, String> repositoryMap = repositoryList.stream().collect(Collectors.toMap(repository -> repository.substring(repository.lastIndexOf("/") + 1, repository.lastIndexOf(".")), repository -> repository));
        return ResponseDoMain.custom("", true, repositoryMap, 200);
    }

}

