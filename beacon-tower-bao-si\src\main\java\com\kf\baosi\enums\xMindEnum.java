package com.kf.baosi.enums;

import lombok.Getter;

@Getter
public enum xMindEnum {
    PRIORITY1("P0", "priority-1"),
    PRIORITY2("P1", "priority-2"),
    PRIORITY3("P2", "priority-3"),
    PRIORITY4("P3", "priority-4");

    private final String value;
    private final String name;

    xMindEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    //获取枚举
    public static String getEnum(String name) {
        for (xMindEnum xMindEnum : xMindEnum.values()) {
            if (xMindEnum.getName().equals(name)) {
                return xMindEnum.value;
            }
        }
        //默认返回用例等级P2
        return xMindEnum.PRIORITY3.value;
    }

    //默认返回用例等级P2
    public static String getDefaultPriorityEnum() {
        return xMindEnum.PRIORITY3.value;
    }
}
