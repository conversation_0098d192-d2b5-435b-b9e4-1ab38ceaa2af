package com.kf.uitest.dto;

import com.kf.uitest.enums.BrowserEngine;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

@Builder
@Data
public class TestExecutionRequest {

    /**
     * 要执行的测试用例ID集合
     */
    @NotEmpty(message = "测试用例ID不能为空")
    private List<String> testCaseIds;

    /**
     * 是否并发执行
     * 默认为false
     */
    private boolean concurrent = false;

    /**
     * 是否共享浏览器实例
     * 默认为false
     */
    private boolean shareBrowser = false;

    /**
     * 执行环境ID
     */
    @NotNull(message = "执行环境ID不能为空")
    private Long environmentId;

    /**
     * 浏览器类型
     */
    @NotNull(message = "浏览器类型不能为空")
    private BrowserEngine browserEngine = BrowserEngine.CHROME;

    /**
     * 执行速度（毫秒）
     * 每个操作之间的等待时间
     */
    @Min(value = 0, message = "执行速度不能小于0")
    @Max(value = 10000, message = "执行速度不能大于10000")
    private int executionSpeed;

    /**
     * 用例失败是否继续执行其他用例
     * 默认为true，表示一个用例失败不影响其他用例执行
     */
    private boolean continueOnCaseFailure = true;

    /**
     * 用例是否启用失败重试
     */
    private boolean retryOnCaseFailure = false;

    /**
     * 用例失败重试次数
     */
    @Min(value = 0, message = "用例重试次数不能小于0")
    @Max(value = 3, message = "用例重试次数不能大于3")
    private Integer maxCaseRetries = 0;

    /**
     * 步骤失败是否继续执行用例内其他步骤
     * 默认为false，表示步骤失败则整个用例失败
     */
    private boolean continueOnStepFailure = false;

    /**
     * 步骤是否启用失败重试
     */
    private boolean retryOnStepFailure = false;

    /**
     * 步骤失败重试次数
     */
    @Min(value = 0, message = "步骤重试次数不能小于0")
    @Max(value = 3, message = "步骤重试次数不能大于3")
    private Integer maxStepRetries = 0;

    /**
     * 循环控制配置
     */
    @Builder.Default
    private Map<String, LoopConfig> loopConfigs = new HashMap<>();

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LoopConfig {
        /**
         * 最大迭代次数
         */
        @Min(value = 1, message = "最大迭代次数不能小于1")
        @Max(value = 100, message = "最大迭代次数不能大于100")
        private Integer maxIterations = 10;

        /**
         * 是否在循环失败时继续执行
         */
        private boolean continueOnLoopFailure = false;
    }
}