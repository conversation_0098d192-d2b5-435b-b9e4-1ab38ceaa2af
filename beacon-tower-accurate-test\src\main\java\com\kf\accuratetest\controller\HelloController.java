package com.kf.accuratetest.controller;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson2.JSON;
import jdk.jfr.DataAmount;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class HelloController {
    @PostMapping("/hello")
    public void hello(@RequestBody JSONObject json) {
        log.info("json:{}", json);
    }

    @GetMapping("/hello2")
    public String hello() {
        return test();
    }
    private String test() {
        System.out.println("test");
        return "test";
    }

    @GetMapping("/hello6")
    public String hello6() {
        return test6();
    }
    private String test6() {
        System.out.println("test");
        return "test";
    }

}
