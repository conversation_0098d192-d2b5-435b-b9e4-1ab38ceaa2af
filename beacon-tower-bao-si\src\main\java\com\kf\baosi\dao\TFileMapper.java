package com.kf.baosi.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kf.baosi.entity.DataToJiraParam;
import com.kf.baosi.entity.TFile;

import java.util.List;

public interface TFileMapper extends BaseMapper<TFile> {

    /**
     * 查询Xml数据
     *
     * @param userId 主键
     * @return 实例对象
     */
    List<TFile> queryBySuffixUserId(String userId, String fileSuffix);

    /**
     * 查询CSV数据
     *
     * @param userId 用户ID
     * @param fileSuffix 文件后缀
     * @return 实例对象
     */
    List<DataToJiraParam> queryJoinedByBySuffixUserId(String userId, String fileSuffix);


    /**
     * 新增数据
     *
     * @param tFile 实例对象
     * @return 影响行数
     */
    int insert(TFile tFile);


    /**
     * 修改数据
     *
     * @param tFile 实例对象
     * @return 影响行数
     */
    int update(TFile tFile);

    /**
     * 通过主键查询单条数据
     */
    TFile queryById(String id);

}

