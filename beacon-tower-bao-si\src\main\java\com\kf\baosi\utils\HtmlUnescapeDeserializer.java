package com.kf.baosi.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;

public class HtmlUnescapeDeserializer extends JsonDeserializer<String> {

    @Override
    public String deserialize(JsonParser p, DeserializationContext ctxt) {
        String original = null;
        try {
            original = p.getText();
        } catch (IOException e) {
            return original;
        }
        return original
            .replace("&ldquo;", "“")
            .replace("&rdquo;", "”")
            .replace("&amp;", "&")
            .replace("&lt;", "<")
            .replace("&gt;", ">")
            .replace("&nbsp;", " ")
            .replace("&quot;", "\"")
            .replace("&apos;", "'")
            .replace("&times;", "×")
            .replace("&divide;", "÷")
            .replace("&brvbar;", "¦")
            .replace("&hellip;", "…")
            .replace("&mdash;", "—")
            .replace("&ndash;", "–")
            .replace("&middot;", "·")
            .replace("&laquo;", "«")
            .replace("&raquo;", "»")
            .replace("&lsaquo;", "‹")
            .replace("&rsaquo;", "›")
            .replace("&sbquo;", "‚")
            .replace("&bdquo;", "„")
            .replace("&permil;", "‰")
            .replace("&prime;", "′")
            .replace("&Prime;", "″")
            .replace("&oline;", "‾")
            .replace("&frasl;", "⁄");
    }
}
