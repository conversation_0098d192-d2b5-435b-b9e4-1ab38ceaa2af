<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kf.accuratetest.dao.RepositoryInfoDao">
    <resultMap id="BaseResultMap" type="com.kf.accuratetest.entity.RepositoryInfo">
        <!--@mbg.generated-->
        <!--@Table t_repository_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="repository_url" jdbcType="VARCHAR" property="repositoryUrl"/>
        <result column="branch" jdbcType="VARCHAR" property="branch"/>
        <result column="object_id" jdbcType="VARCHAR" property="objectId"/>
        <result column="directory_path" jdbcType="VARCHAR" property="directoryPath"/>
        <result column="is_done" jdbcType="INTEGER" property="isDone"/>
        <result column="is_compile" jdbcType="INTEGER" property="isCompile"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        repository_url,
        branch,
        object_id,
        directory_path,
        is_done,
        is_compile,
        create_time,
        update_time,
        is_deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from t_repository_info
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByRepositoryAndBranch" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from t_repository_info
        <!--    where repository_url = #{repositoryUrl} and branch = #{branch} and is_deleted = 0 order by update_time desc limit 1-->
        <where>
            <if test="repositoryUrl != null">
                and repository_url = #{repositoryUrl}
            </if>
            <if test="branch != null">
                and branch = #{branch}
            </if>
            <if test="directoryPath != null">
                and directory_path = #{directoryPath}
            </if>
            <if test="objectId != null">
                and object_id = #{objectId}
            </if>
            and is_deleted = 0
        </where>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from t_repository_info
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into t_repository_info (repository_url, branch, object_id,
                                       directory_path, create_time, update_time,
                                       is_deleted)
        values (#{repositoryUrl,jdbcType=VARCHAR}, #{branch,jdbcType=VARCHAR}, #{objectId,jdbcType=VARCHAR},
                #{directoryPath,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
                #{isDeleted,jdbcType=BOOLEAN})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into t_repository_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="repositoryUrl != null">
                repository_url,
            </if>
            <if test="branch != null">
                branch,
            </if>
            <if test="objectId != null">
                object_id,
            </if>
            <if test="directoryPath != null">
                directory_path,
            </if>
            <if test="isDone != null">
                is_done,
            </if>
            <if test="isCompile != null">
                is_compile,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="repositoryUrl != null">
                #{repositoryUrl,jdbcType=VARCHAR},
            </if>
            <if test="branch != null">
                #{branch,jdbcType=VARCHAR},
            </if>
            <if test="objectId != null">
                #{objectId,jdbcType=VARCHAR},
            </if>
            <if test="directoryPath != null">
                #{directoryPath,jdbcType=VARCHAR},
            </if>
            <if test="isDone != null">
                #{isDone,jdbcType=INTEGER},
            </if>
            <if test="isCompile != null">
                #{isCompile,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=BOOLEAN},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective">
        <!--@mbg.generated-->
        update t_repository_info
        <set>
            <if test="repositoryUrl != null">
                repository_url = #{repositoryUrl,jdbcType=VARCHAR},
            </if>
            <if test="branch != null">
                branch = #{branch,jdbcType=VARCHAR},
            </if>
            <if test="objectId != null">
                object_id = #{objectId,jdbcType=VARCHAR},
            </if>
            <if test="directoryPath != null">
                directory_path = #{directoryPath,jdbcType=VARCHAR},
            </if>
            <if test="isDone != null">
                is_done = #{isDone,jdbcType=INTEGER},
            </if>
            <if test="isCompile != null">
                is_compile = #{isCompile,jdbcType=BOOLEAN},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=BOOLEAN},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey">
        <!--@mbg.generated-->
        update t_repository_info
        set repository_url = #{repositoryUrl,jdbcType=VARCHAR},
            branch         = #{branch,jdbcType=VARCHAR},
            object_id      = #{objectId,jdbcType=VARCHAR},
            directory_path = #{directoryPath,jdbcType=VARCHAR},
            create_time    = #{createTime,jdbcType=TIMESTAMP},
            update_time    = #{updateTime,jdbcType=TIMESTAMP},
            is_deleted     = #{isDeleted,jdbcType=BOOLEAN}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByRepositoryAndBranch">
        <!--@mbg.generated-->
        update t_repository_info
        <set>
            <if test="objectId != null">
                object_id = #{objectId,jdbcType=VARCHAR},
            </if>
            <if test="directoryPath != null">
                directory_path = #{directoryPath,jdbcType=VARCHAR},
            </if>
            <if test="isDone != null">
                is_done = #{isDone,jdbcType=BOOLEAN},
            </if>
            <if test="isCompile != null">
                is_compile = #{isCompile,jdbcType=BOOLEAN},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=BOOLEAN},
            </if>
        </set>
        where repository_url = #{repositoryUrl,jdbcType=VARCHAR}
          and branch = #{branch,jdbcType=VARCHAR}
    </update>
    <update id="updateCompileByRepositoryPath">
        <!--@mbg.generated-->
        update t_repository_info
        <set>
            <if test="isCompile != null">
                is_compile = #{isCompile,jdbcType=BOOLEAN},
            </if>
        </set>
        where directory_path = #{directoryPath,jdbcType=VARCHAR}
    </update>
</mapper>