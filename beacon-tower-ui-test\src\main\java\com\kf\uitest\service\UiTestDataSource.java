package com.kf.uitest.service;

import java.util.List;
import java.util.Map;

public interface UiTestDataSource {
    
    /**
     * 执行查询SQL
     *
     * @param dbSource 数据源名称
     * @param sql SQL语句
     * @param params SQL参数
     * @return 查询结果列表
     */
    List<Map<String, Object>> executeQuery(String dbSource, String sql, Map<String, Object> params);
    
    /**
     * 执行更新SQL
     *
     * @param dbSource 数据源名称
     * @param sql SQL语句
     * @param params SQL参数
     * @return 影响的行数
     */
    int executeUpdate(String dbSource, String sql, Map<String, Object> params);
}