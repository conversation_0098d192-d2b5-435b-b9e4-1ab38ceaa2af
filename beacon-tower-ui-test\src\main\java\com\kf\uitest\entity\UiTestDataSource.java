package com.kf.uitest.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ui_test_datasource")
public class UiTestDataSource {
    
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    
    @TableField("name")
    private String name;
    
    @TableField("url")
    private String url;
    
    @TableField("username")
    private String username;
    
    @TableField("password")
    private String password;
    
    @TableField("driver_class")
    private String driverClass;
    
    @TableField("description")
    private String description;
    
    @TableField("project_id")
    private Long projectId;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}