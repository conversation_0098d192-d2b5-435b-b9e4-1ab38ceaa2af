<template>
    <div class="code-diff-container">
        <div class="diff-header">
            <div class="diff-title">
                <span class="title-text">数据对比</span>
                <div class="diff-controls">
                    <el-button-group size="small">
                        <el-button
                            :type="viewMode === 'split' ? 'primary' : 'default'"
                            @click="viewMode = 'split'"
                        >
                            并排对比
                        </el-button>
                        <el-button
                            :type="viewMode === 'unified' ? 'primary' : 'default'"
                            @click="viewMode = 'unified'"
                        >
                            统一视图
                        </el-button>
                    </el-button-group>
                </div>
            </div>
        </div>

        <div class="diff-content" :class="{ 'unified-view': viewMode === 'unified' }">
            <!-- 并排对比视图 -->
            <div v-if="viewMode === 'split'" class="split-view">
                <div class="diff-panel left-panel">
                    <div class="panel-header">
                        <span class="env-label uat">UAT环境</span>
                        <span class="line-count">{{ leftLines.length }} 行</span>
                    </div>
                    <div class="panel-content">
                        <div class="line-numbers">
                            <div
                                v-for="(line, index) in leftLines"
                                :key="index"
                                class="line-number"
                                :class="getLineClass(line, 'left')"
                            >
                                {{ index + 1 }}
                            </div>
                        </div>
                        <div class="code-content">
                            <div
                                v-for="(line, index) in leftLines"
                                :key="index"
                                class="code-line"
                                :class="getLineClass(line, 'left')"
                            >
                                <span v-html="highlightDifferences(line.content, line.type)"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="diff-panel right-panel">
                    <div class="panel-header">
                        <span class="env-label test">TEST环境</span>
                        <span class="line-count">{{ rightLines.length }} 行</span>
                    </div>
                    <div class="panel-content">
                        <div class="line-numbers">
                            <div
                                v-for="(line, index) in rightLines"
                                :key="index"
                                class="line-number"
                                :class="getLineClass(line, 'right')"
                            >
                                {{ index + 1 }}
                            </div>
                        </div>
                        <div class="code-content">
                            <div
                                v-for="(line, index) in rightLines"
                                :key="index"
                                class="code-line"
                                :class="getLineClass(line, 'right')"
                            >
                                <span v-html="highlightDifferences(line.content, line.type)"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统一视图 -->
            <div v-else class="unified-view">
                <div class="diff-panel">
                    <div class="panel-header">
                        <span class="env-label">数据对比</span>
                        <span class="line-count">{{ unifiedLines.length }} 行</span>
                    </div>
                    <div class="panel-content">
                        <div class="line-numbers">
                            <div
                                v-for="(line, index) in unifiedLines"
                                :key="index"
                                class="line-number"
                                :class="getLineClass(line, 'unified')"
                            >
                                <span class="left-num">{{ line.leftNum || '' }}</span>
                                <span class="right-num">{{ line.rightNum || '' }}</span>
                            </div>
                        </div>
                        <div class="code-content">
                            <div
                                v-for="(line, index) in unifiedLines"
                                :key="index"
                                class="code-line"
                                :class="getLineClass(line, 'unified')"
                            >
                                <span class="line-prefix">{{ line.prefix }}</span>
                                <span v-html="highlightDifferences(line.content, line.type)"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 差异统计 -->
        <div class="diff-stats">
            <div class="stats-item added">
                <span class="stats-label">新增:</span>
                <span class="stats-value">{{ diffStats.added }}</span>
            </div>
            <div class="stats-item removed">
                <span class="stats-label">删除:</span>
                <span class="stats-value">{{ diffStats.removed }}</span>
            </div>
            <div class="stats-item modified">
                <span class="stats-label">修改:</span>
                <span class="stats-value">{{ diffStats.modified }}</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import * as Diff from 'diff'

// Props
interface Props {
    leftData: string
    rightData: string
    leftLabel?: string
    rightLabel?: string
}

const props = withDefaults(defineProps<Props>(), {
    leftData: '',
    rightData: '',
    leftLabel: 'UAT环境',
    rightLabel: 'TEST环境'
})

// 响应式数据
const viewMode = ref<'split' | 'unified'>('split')

// 数据处理
const formatData = (data: string) => {
    try {
        const parsed = typeof data === 'string' ? JSON.parse(data) : data
        return JSON.stringify(parsed, null, 2)
    } catch {
        return data || ''
    }
}

const leftFormatted = computed(() => formatData(props.leftData))
const rightFormatted = computed(() => formatData(props.rightData))

// 计算差异
const diffResult = computed(() => {
    return Diff.diffLines(leftFormatted.value, rightFormatted.value)
})

// 并排视图的行数据
const leftLines = computed(() => {
    const lines: Array<{ content: string, type: string, lineNum: number }> = []
    let lineNum = 1

    diffResult.value.forEach(part => {
        if (!part.removed) {
            const partLines = part.value.split('\n').slice(0, -1) // 移除最后的空行
            partLines.forEach(line => {
                lines.push({
                    content: line,
                    type: part.added ? 'added' : part.removed ? 'removed' : 'normal',
                    lineNum: lineNum++
                })
            })
        }
    })

    return lines
})

const rightLines = computed(() => {
    const lines: Array<{ content: string, type: string, lineNum: number }> = []
    let lineNum = 1

    diffResult.value.forEach(part => {
        if (!part.added) {
            const partLines = part.value.split('\n').slice(0, -1)
            partLines.forEach(line => {
                lines.push({
                    content: line,
                    type: part.added ? 'added' : part.removed ? 'removed' : 'normal',
                    lineNum: lineNum++
                })
            })
        }
    })

    return lines
})

// 统一视图的行数据
const unifiedLines = computed(() => {
    const lines: Array<{
        content: string
        type: string
        prefix: string
        leftNum?: number
        rightNum?: number
    }> = []
    let leftNum = 1
    let rightNum = 1

    diffResult.value.forEach(part => {
        const partLines = part.value.split('\n').slice(0, -1)
        partLines.forEach(line => {
            if (part.added) {
                lines.push({
                    content: line,
                    type: 'added',
                    prefix: '+',
                    rightNum: rightNum++
                })
            } else if (part.removed) {
                lines.push({
                    content: line,
                    type: 'removed',
                    prefix: '-',
                    leftNum: leftNum++
                })
            } else {
                lines.push({
                    content: line,
                    type: 'normal',
                    prefix: ' ',
                    leftNum: leftNum++,
                    rightNum: rightNum++
                })
            }
        })
    })

    return lines
})

// 差异统计
const diffStats = computed(() => {
    let added = 0
    let removed = 0
    let modified = 0

    diffResult.value.forEach(part => {
        const lineCount = part.value.split('\n').length - 1
        if (part.added) {
            added += lineCount
        } else if (part.removed) {
            removed += lineCount
        }
    })

    modified = Math.min(added, removed)
    added -= modified
    removed -= modified

    return { added, removed, modified }
})

// 工具函数
const getLineClass = (line: any, side: string) => {
    const classes = []
    if (line.type === 'added') classes.push('line-added')
    if (line.type === 'removed') classes.push('line-removed')
    if (line.type === 'normal') classes.push('line-normal')
    return classes
}

const highlightDifferences = (content: string, type: string) => {
    // 简单的HTML转义
    const escaped = content
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;')

    return escaped
}
</script>

<style scoped>
.code-diff-container {
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden;
    background: #fff;
}

.diff-header {
    background: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 16px;
}

.diff-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.title-text {
    font-weight: 600;
    color: #303133;
}

.diff-content {
    min-height: 400px;
    max-height: 600px;
    overflow: auto;
}

.split-view {
    display: grid;
    grid-template-columns: 1fr 1fr;
}

.diff-panel {
    border-right: 1px solid #e4e7ed;
}

.diff-panel:last-child {
    border-right: none;
}

.panel-header {
    background: #fafafa;
    padding: 8px 12px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.env-label {
    font-size: 12px;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 3px;
}

.env-label.uat {
    background: #e6f7ff;
    color: #1890ff;
}

.env-label.test {
    background: #f6ffed;
    color: #52c41a;
}

.line-count {
    font-size: 12px;
    color: #8c8c8c;
}

.panel-content {
    display: flex;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.line-numbers {
    background: #fafafa;
    border-right: 1px solid #e4e7ed;
    padding: 0;
    min-width: 50px;
    text-align: right;
    user-select: none;
}

.line-number {
    padding: 0 8px;
    color: #8c8c8c;
    height: 18.2px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.code-content {
    flex: 1;
    padding: 0;
}

.code-line {
    padding: 0 12px;
    height: 18.2px;
    display: flex;
    align-items: center;
    white-space: pre;
}

.line-added {
    background: #f6ffed !important;
    border-left: 3px solid #52c41a;
}

.line-removed {
    background: #fff2f0 !important;
    border-left: 3px solid #ff4d4f;
}

.line-normal {
    background: transparent;
}

.line-prefix {
    width: 20px;
    text-align: center;
    margin-right: 8px;
    font-weight: bold;
}

.diff-stats {
    background: #fafafa;
    border-top: 1px solid #e4e7ed;
    padding: 8px 16px;
    display: flex;
    gap: 16px;
    font-size: 12px;
}

.stats-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.stats-label {
    color: #8c8c8c;
}

.stats-value {
    font-weight: 600;
}

.stats-item.added .stats-value {
    color: #52c41a;
}

.stats-item.removed .stats-value {
    color: #ff4d4f;
}

.stats-item.modified .stats-value {
    color: #faad14;
}

.unified-view .line-number {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4px;
    min-width: 80px;
}

.left-num, .right-num {
    text-align: center;
}
</style>
