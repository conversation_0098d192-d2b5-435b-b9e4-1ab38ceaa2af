package com.kf.uitest.service;

import com.kf.uitest.dto.execution.ExecutionGroupDTO;
import com.kf.uitest.model.TestExecutionContext;
import com.kf.uitest.model.TestExecutionStep;
import com.kf.uitest.model.TestResult;

import java.util.List;
import java.util.Map;

public interface UiTestExecutionService {
    /**
     * 执行测试计划
     * @param executionGroups 执行组列表，每个执行组包含一系列有依赖关系的步骤
     * @param context 执行上下文
     * @return 测试结果
     */
    TestResult executeTestPlan(List<ExecutionGroupDTO> executionGroups, TestExecutionContext context);

}