/**
 * 用户服务类型定义
 */

import type { AxiosResponse } from 'axios'

// ==================== 用户服务类型定义 ====================

export interface LoginParam {
    username: string
    password: string
}

export interface IGetUser {
    userName: string
    userId: string
    roles: Array<string>
    token: string
}

export interface RegisterParam {
    username: string
    password: string
    email: string
    code: string
}

export interface EmailParam {
    email: string
}

export interface CheckVerificationCode {
    email: string
    code: string
}

export interface ResetPassWord {
    userName: string
    passWord: string
    sign: string
}

export interface ResetUserPassword {
    oldPassWord: string
    newPassWord: string
}

// ==================== API响应类型 ====================

export type LoginApiResponse = AxiosResponse<IResponse<IGetUser>>
export type RegisterApiResponse = AxiosResponse<IResponse<string>>
export type VerificationCodeApiResponse = AxiosResponse<IResponse>
