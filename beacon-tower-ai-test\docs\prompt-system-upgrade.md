# AI测试提示词系统升级说明

## 概述

本次升级将AI测试系统的提示词逻辑从入参控制改为基于文件的模板系统，专门针对临床试验报告反馈数据对比场景进行了优化。

## 主要改进

### 1. 提示词模板化
- **之前**: 通过`customPrompt`参数传递提示词，所有阶段使用相同的通用提示词
- **现在**: 每个阶段都有专门的MD模板文件，针对不同阶段的特点进行了定制

### 2. 数据分片处理
- **问题**: 大数据量会超过AI模型的token限制
- **解决**: 自动检测数据大小，超过限制时进行智能分片处理，最后合并结果

### 3. 临床试验专业化
- 所有提示词都针对临床试验报告场景进行了专业化设计
- 重点关注临床数据的准确性、完整性和合规性

## 文件结构

```
beacon-tower-ai-test/
├── src/main/resources/prompts/
│   ├── recognize.md      # 识别阶段提示词
│   ├── extraction.md     # 提取阶段提示词
│   ├── structured.md     # 结构化阶段提示词
│   └── transformer.md    # 转换阶段提示词
├── src/main/java/com/kf/aitest/service/
│   ├── PromptTemplateService.java        # 提示词模板服务接口
│   ├── DataChunkService.java             # 数据分片服务接口
│   └── impl/
│       ├── PromptTemplateServiceImpl.java # 提示词模板服务实现
│       ├── DataChunkServiceImpl.java      # 数据分片服务实现
│       └── AiEvaluationServiceImpl.java   # 重构后的AI评估服务
└── docs/
    └── prompt-system-upgrade.md          # 本文档
```

## 各阶段提示词特点

### 1. Recognize阶段 (recognize.md)
- **重点**: 页眉页脚处理和核心内容识别
- **评估维度**: 页眉页脚处理质量(30%)、核心内容识别准确性(40%)、格式保持一致性(20%)、不一致内容提取(10%)
- **特别关注**: 临床试验特有元素、数据完整性、表格结构、时间信息、医学术语

### 2. Extraction阶段 (extraction.md)
- **重点**: 数据结构一致性和信息完整性检查
- **评估维度**: 数据结构一致性(35%)、信息完整性检查(30%)、多余信息识别(20%)、字段映射准确性(15%)
- **特别关注**: 受试者信息、访问记录、用药信息、检查结果、不良事件、疗效评估、合规性数据

### 3. Structured阶段 (structured.md)
- **重点**: 数据内容一致性检查（允许JSON字段顺序不同）
- **评估维度**: 数据内容一致性(40%)、数据类型一致性(25%)、数据完整性(20%)、数据格式规范性(15%)
- **特别关注**: 时间数据格式、数值数据精度、编码数据标准化、枚举值一致性

### 4. Transformer阶段 (transformer.md)
- **重点**: 全面的数据转换质量评估
- **评估维度**: 数据结构一致性(25%)、内容完整性与准确性(30%)、字段内容相似度(25%)、数据转换质量(20%)
- **特别关注**: 标准化转换、数据关联性、业务规则验证、数据质量指标

## 数据分片机制

### 自动分片条件
- 当数据大小超过配置的token限制时（默认8000 tokens）
- 系统会自动将数据分片处理

### 分片策略
1. **数组数据**: 按元素数量分片
2. **对象数据**: 按字段分组分片
3. **字符串数据**: 按字符长度分片

### 结果合并
- 各分片独立评估后，系统会自动合并结果
- 提供分片统计信息和综合质量评估
- 计算加权平均分作为最终评分

## 配置说明

在`application.yml`中添加了新的配置项：

```yaml
ai:
  evaluation:
    # 单次AI调用的最大token限制
    max-tokens: 8000
    # 提示词模板目录
    prompt-template-path: classpath:prompts/
```

## API变更

### 接口变更
- `AiEvaluationService.evaluateStageData()` 移除了`customPrompt`参数
- `AiEvaluationService.evaluateOverallResult()` 移除了`customPrompt`参数
- `AiEvaluationService.buildStagePrompt()` 移除了`customPrompt`参数

### DTO变更
- `DataComparisonRequestDTO.customPrompt` 字段标记为`@Deprecated`

## 使用方式

### 1. 修改提示词
直接编辑`src/main/resources/prompts/`目录下的对应MD文件即可，系统支持热重载。

### 2. 调用热重载
```java
@Autowired
private PromptTemplateService promptTemplateService;

// 重新加载所有提示词模板
promptTemplateService.reloadTemplates();
```

### 3. 检查模板是否存在
```java
boolean hasTemplate = promptTemplateService.hasTemplate("recognize");
```

## 向后兼容性

- `customPrompt`字段仍然存在但已标记为废弃，不会影响现有API调用
- 系统会忽略`customPrompt`参数，使用新的模板系统
- 建议在下个版本中完全移除`customPrompt`相关代码

## 测试

运行测试验证功能：
```bash
mvn test -Dtest=PromptTemplateServiceTest
```

## 注意事项

1. **提示词文件编码**: 确保MD文件使用UTF-8编码
2. **文件路径**: 提示词文件必须放在`src/main/resources/prompts/`目录下
3. **文件命名**: 文件名必须与阶段名称一致（如`recognize.md`）
4. **内容格式**: 建议保持现有的Markdown格式，便于维护和阅读
5. **临床专业性**: 修改提示词时请确保符合临床试验数据质量要求

## 后续优化建议

1. 添加提示词版本管理
2. 支持多语言提示词模板
3. 添加提示词效果统计和优化建议
4. 集成更多临床试验标准（如CDISC）
5. 添加提示词A/B测试功能
