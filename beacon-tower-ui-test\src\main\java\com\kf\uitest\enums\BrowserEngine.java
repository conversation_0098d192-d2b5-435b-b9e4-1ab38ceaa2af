package com.kf.uitest.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@JsonFormat(shape = JsonFormat.Shape.STRING)
@Getter
public enum BrowserEngine {
    CHROME("chrome", "chromium", "Google Chrome"),
    CHROMIUM("chromium", "chromium", "Chromium"),
    FIREFOX("firefox", "firefox", "Mozilla Firefox"),
    WEBKIT("webkit", "webkit", "WebKit"),
    EDGE("edge", "chromium", "Microsoft Edge"),
    SAFARI("safari", "webkit", "Apple Safari");

    @JsonValue
    private final String value;

    private final String playwrightEngine;

    private final String displayName;

    BrowserEngine(String value, String playwrightEngine, String displayName) {
        this.value = value;
        this.playwrightEngine = playwrightEngine;
        this.displayName = displayName;
    }

    @JsonCreator
    public static BrowserEngine fromString(String value) {
        if (value == null) {
            return null;
        }

        for (BrowserEngine type : BrowserEngine.values()) {
            if (type.value.equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown browser type: " + value);
    }
}
