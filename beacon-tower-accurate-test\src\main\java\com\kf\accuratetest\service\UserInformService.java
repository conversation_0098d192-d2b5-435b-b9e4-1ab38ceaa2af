package com.kf.accuratetest.service;

import com.kf.accuratetest.dto.InFromDTO;
import com.kf.accuratetest.entity.UserInform;

import java.util.List;

public interface UserInformService {

    /**
     * 查询当前用户所有钉钉消息
     *
     * @return 实例对象list
     */
    List<UserInform> queryDingDingById(String userId);

    /**
     * 查询多条数据
     *
     * @return 实例对象
     */
    List<UserInform> queryAllByLimit(String userId);

    /**
     * 新增数据
     *
     * @param fromList 实例对象list
     * @return 实例对象
     */
    int insert(List<InFromDTO> fromList,String userId);


}
