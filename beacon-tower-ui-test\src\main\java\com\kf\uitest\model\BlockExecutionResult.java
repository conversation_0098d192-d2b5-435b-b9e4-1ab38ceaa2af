package com.kf.uitest.model;

import com.kf.uitest.enums.TestStatus;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class BlockExecutionResult {
    private String blockId;
    private TestStatus status;
    private String errorMessage;
    private List<StepExecutionResult> stepResults;
    private Map<String, Object> variables;
    private int iterationCount;  // 循环块的迭代次数
} 