package com.kf.uitest.service;

import com.kf.uitest.entity.UiTestEnvironmentVariable;

import java.util.List;
import java.util.Map;

public interface UiTestEnvironmentVariableService {
    /**
     * 根据环境ID查找环境变量
     */
    List<UiTestEnvironmentVariable> findByEnvironmentId(Long environmentId);

    /**
     * 保存环境变量
     */
    void save(UiTestEnvironmentVariable variable);

    /**
     * 批量保存环境变量
     */
    void saveBatch(List<UiTestEnvironmentVariable> variables);

    /**
     * 更新环境变量
     */
    void update(UiTestEnvironmentVariable variable);

    /**
     * 删除环境变量
     */
    void delete(Long id);

    /**
     * 根据ID获取环境变量
     */
    UiTestEnvironmentVariable getById(Long id);

    void updateVariables(String environmentId, Map<String, Object> variables);
}