server:
  port: 8001
  tomcat:
    max-swallow-size: -1
kf:
  file:
    upload:
      dir: d:/file
spring:
  application:
    name: beacon-tower-user-service
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      database: 1
  mail:
    host: smtp.163.com
    username: <EMAIL>
    password: JEZQOXATWSWKQGUL
    default-encoding: utf-8
  servlet:
    multipart:
      max-request-size: 5GB
      max-file-size: 20MB
  datasource:
    #   数据源基本配置
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      validation-query: SELECT 1
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml