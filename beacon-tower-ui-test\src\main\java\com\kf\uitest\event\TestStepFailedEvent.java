package com.kf.uitest.event;

import com.kf.uitest.model.TestExecutionStep;
import com.kf.uitest.model.TestResult;
import lombok.Getter;

@Getter
public class TestStepFailedEvent extends BaseTestEvent {
    private final TestExecutionStep step;
    private final TestResult.StepExecutionResult result;

    public TestStepFailedEvent(String executionId, TestExecutionStep step, TestResult.StepExecutionResult result) {
        super(executionId);
        this.step = step;
        this.result = result;
    }

    /**
     * 获取错误信息
     */
    public String getErrorMessage() {
        return result != null ? result.getErrorMessage() : null;
    }
}