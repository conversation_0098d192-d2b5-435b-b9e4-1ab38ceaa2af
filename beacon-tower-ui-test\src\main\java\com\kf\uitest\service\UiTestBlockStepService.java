package com.kf.uitest.service;

import com.kf.uitest.entity.UiTestBlockStep;

import java.util.List;

public interface UiTestBlockStepService {
    /**
     * 根据块ID查找所有步骤关联
     */
    List<UiTestBlockStep> findByBlockId(String blockId);
    
    /**
     * 创建块步骤关联
     */
    UiTestBlockStep create(UiTestBlockStep blockStep);
    
    /**
     * 批量创建块步骤关联
     */
    List<UiTestBlockStep> batchCreate(List<UiTestBlockStep> blockSteps);
    
    /**
     * 更新块步骤关联
     */
    boolean update(UiTestBlockStep blockStep);
    
    /**
     * 删除块步骤关联
     */
    boolean delete(String id);
    
    /**
     * 删除块的所有步骤关联
     */
    boolean deleteByBlockId(String blockId);
    
    /**
     * 调整步骤顺序
     */
    boolean updateStepOrder(String blockId, String stepId, Integer newOrder);
} 