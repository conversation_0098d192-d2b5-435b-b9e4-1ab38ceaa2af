package com.kf.uitest.enums;

import lombok.Getter;

/**
 * UI自动化可能用到的操作类型枚举
 */
@Getter
public enum ActionType {

    CLICK_ELEMENT("点击"),
    INPUT_ELEMENT("输入"),
    UPLOAD_ELEMENT("上传元素"),
    DRAG_ELEMENT("拖拽"),
    WAIT_ELEMENT_VALUE("等待元素"),
    REQUEST_API("请求接口"),
    LISTEN_API("监听接口"),
    FORCE_WAIT_ELEMENT("强制等待"),
    VISIT_URL("访问URL地址"),
    DOUBLE_CLICK_ELEMENT("双击元素"),
    HOVER_ELEMENT("悬停"),
    RIGHT_CLICK_ELEMENT("右键点击元素"),
    SELECT_DROPDOWN("选择下拉框选项"),
    SCROLL_ELEMENT("滚动元素"),
    NAVIGATE_BACK("导航返回"),
    REFRESH_PAGE("刷新页面"),
    CLEAR_FIELD("清空输入框"),

    // 常见动作类型
    CHECK_CHECKBOX("勾选复选框"),
    UNCHECK_CHECKBOX("取消勾选复选框"),
    SELECT_RADIO("选择单选框"),
    TAKE_SCREENSHOT("截图"),
    WAIT_FOR_NAVIGATION("等待页面跳转"),
    EXECUTE_SCRIPT("执行脚本"),
    SWITCH_IFRAME("切换至 IFrame"),
    SWITCH_WINDOW_OR_TAB("切换窗口/标签页"),
    ACCEPT_DIALOG("接受弹窗"),
    DISMISS_DIALOG("拒绝弹窗"),
    SEND_KEYSTROKE("发送按键"),
    SELECT_DATE("选择日期"),
    SCROLL_TO_POSITION("滚动到指定位置"),
    WAIT_FOR_RESPONSE("等待指定接口响应"),
    ASSERT_ELEMENT("断言元素状态"),
    EXECUTE_ASSERT("执行通用断言"),
    OPEN_NEW_TAB("打开新标签页"),

    // 针对Cookies或Storage等额外操作
    SET_COOKIE("设置Cookie"),
    CLEAR_COOKIE("清除Cookie"),
    GET_COOKIE("获取Cookie"),
    MANAGE_STORAGE("操作浏览器Storage");

    private final String description;

    ActionType(String description) {
        this.description = description;
    }
}
