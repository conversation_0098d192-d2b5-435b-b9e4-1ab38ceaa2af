package com.kf.accuratetest.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@Schema(description = "接口和页面参数")
public class TInterfaceAnnotationDTO {
    @Schema(description = "接口名称")
    String interfaceName;
    @Schema(description = "接口描述")
    String interfaceDesc;
    @Schema(description = "接口详细描述")
    String interfaceNotes;
    @Schema(description = "页面路径列表")
    List<String> pageUrl = new ArrayList<>();
}
