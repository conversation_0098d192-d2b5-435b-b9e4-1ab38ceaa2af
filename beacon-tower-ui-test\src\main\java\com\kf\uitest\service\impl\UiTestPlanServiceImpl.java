package com.kf.uitest.service.impl;

import com.kf.uitest.dto.execution.*;
import com.kf.uitest.entity.*;
import com.kf.uitest.enums.*;
import com.kf.uitest.exception.TestPlanBuildException;
import com.kf.uitest.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UiTestPlanServiceImpl implements UiTestPlanService {

    @Resource
    private UiTestCaseService caseService;
    @Resource
    private UiTestStepService stepService;
    @Resource
    private UiTestBlockService blockService;
    @Resource
    private UiTestHookService hookService;
    @Resource
    private UiTestAssertionService assertionService;

    @Override
    public List<ExecutionGroupDTO> buildExecutionPlan(List<String> testCaseIds) {
        try {
            // 用于存储已处理的用例ID，避免重复处理
            Set<String> processedCases = new HashSet<>();
            // 存储最终的执行组列表
            List<ExecutionGroupDTO> executionGroups = new ArrayList<>();

            // 遍历每个用例ID，构建执行组
            for (String caseId : testCaseIds) {
                if (!processedCases.contains(caseId)) {
                    // 构建一个新的执行组
                    ExecutionGroupDTO group = buildExecutionGroup(caseId, processedCases);
                    executionGroups.add(group);
                }
            }

            return executionGroups;

        } catch (Exception e) {
            log.error("构建执行计划时发生错误", e);
            throw new TestPlanBuildException("构建执行计划失败", e);
        }
    }

    private ExecutionGroupDTO buildExecutionGroup(String rootCaseId, Set<String> processedCases) {
        // 用于存储当前执行组中的所有用例
        List<ExecutionCaseDTO> groupCases = new ArrayList<>();
        // 用于检测循环依赖
        Set<String> visitingCases = new HashSet<>();

        // 递归处理用例及其依赖
        processCaseWithDependencies(rootCaseId, groupCases, processedCases, visitingCases);

        // 构建执行组
        return ExecutionGroupDTO.builder()
                .id(UUID.randomUUID().toString())
                .name(generateGroupName(groupCases))
                .cases(groupCases)
                .build();
    }

    private void processCaseWithDependencies(String caseId, List<ExecutionCaseDTO> groupCases, Set<String> processedCases, Set<String> visitingCases) {
        // 检查循环依赖
        if (visitingCases.contains(caseId)) {
            throw new TestPlanBuildException("检测到循环依赖: " + caseId);
        }

        // 如果已经处理过，直接返回
        if (processedCases.contains(caseId)) {
            return;
        }

        visitingCases.add(caseId);

        try {
            // 获取用例的前置钩子
            List<UiTestHook> preHooks = hookService.findHooks(HookOwnerType.CASE, caseId, HookTiming.PRE);

            // 先处理前置钩子中的用例依赖
            for (UiTestHook hook : preHooks) {
                if (HookActionType.RUN_CASE.name().equals(hook.getActionType())) {
                    String depCaseId = hook.getInputValue();
                    processCaseWithDependencies(depCaseId, groupCases, processedCases, visitingCases);
                }
            }

            // 构建当前用例
            ExecutionCaseDTO caseDTO = buildExecutionCase(caseId);
            groupCases.add(caseDTO);
            processedCases.add(caseId);

            // 处理后置钩子中的用例依赖
            List<UiTestHook> postHooks = hookService.findHooks(HookOwnerType.CASE, caseId, HookTiming.POST);
            for (UiTestHook hook : postHooks) {
                if (HookActionType.RUN_CASE.name().equals(hook.getActionType())) {
                    String depCaseId = hook.getInputValue();
                    processCaseWithDependencies(depCaseId, groupCases, processedCases, visitingCases);
                }
            }

        } finally {
            visitingCases.remove(caseId);
        }
    }

    private ExecutionCaseDTO buildExecutionCase(String caseId) {
        UiTestCase testCase = caseService.getById(caseId);
        if (testCase == null) {
            throw new TestPlanBuildException("未找到测试用例: " + caseId);
        }

        // 获取用例级别的钩子
        List<UiTestHook> preHooks = hookService.findHooks(HookOwnerType.CASE, caseId, HookTiming.PRE);
        List<UiTestHook> postHooks = hookService.findHooks(HookOwnerType.CASE, caseId, HookTiming.POST);

        // 构建步骤组
        List<StepBlockDTO> stepBlocks = buildStepBlocks(caseId);

        return ExecutionCaseDTO.builder()
                .id(caseId)
                .name(testCase.getCaseName())
                .preHooks(preHooks)
                .postHooks(postHooks)
                .stepBlocks(stepBlocks)
                .build();
    }

    private List<StepBlockDTO> buildStepBlocks(String caseId) {
        List<UiTestBlock> blocks = blockService.findByCaseId(caseId);
        return blocks.stream()
                .sorted(Comparator.comparing(UiTestBlock::getBlockOrder))
                .map(this::buildStepBlock)
                .collect(Collectors.toList());
    }

    private StepBlockDTO buildStepBlock(UiTestBlock block) {
        // 获取块级别的钩子
        List<UiTestHook> preHooks = hookService.findHooks(HookOwnerType.BLOCK, block.getId(), HookTiming.PRE);
        List<UiTestHook> postHooks = hookService.findHooks(HookOwnerType.BLOCK, block.getId(), HookTiming.POST);

        // 构建步骤列表
        List<ExecutionStepDTO> steps = buildSteps(block.getId());

        // 构建子步骤组（处理嵌套）
        List<StepBlockDTO> children = buildNestedBlocks(block.getId());

        return StepBlockDTO.builder()
                .id(block.getId())
                .name(block.getBlockName())
                .type(BlockType.valueOf(block.getBlockType()))
                .order(block.getBlockOrder())
                .preHooks(preHooks)
                .postHooks(postHooks)
                .steps(steps)
                .children(children)
                .build();
    }

    private List<StepBlockDTO> buildNestedBlocks(String parentBlockId) {
        List<UiTestBlock> nestedBlocks = blockService.findChildBlocks(parentBlockId);
        return nestedBlocks.stream()
                .sorted(Comparator.comparing(UiTestBlock::getBlockOrder))
                .map(this::buildStepBlock)
                .collect(Collectors.toList());
    }

    private List<ExecutionStepDTO> buildSteps(String blockId) {
        List<UiTestStep> steps = stepService.findByBlockId(blockId);
        return steps.stream()
                .sorted(Comparator.comparing(UiTestStep::getStepOrder))
                .map(this::buildStep)
                .collect(Collectors.toList());
    }

    private ExecutionStepDTO buildStep(UiTestStep step) {
        // 获取步骤级别的钩子
        List<UiTestHook> preHooks = hookService.findHooks(HookOwnerType.STEP, step.getId(), HookTiming.PRE);
        List<UiTestHook> postHooks = hookService.findHooks(HookOwnerType.STEP, step.getId(), HookTiming.POST);

        return ExecutionStepDTO.builder()
                .id(step.getId())
                .order(step.getStepOrder())
                .preHooks(preHooks)
                .postHooks(postHooks)
                .config(buildStepConfig(step))
                .build();
    }

    private StepConfigDTO buildStepConfig(UiTestStep step) {
        return StepConfigDTO.builder()
                .action(ActionType.valueOf(step.getActionType()))
                .locator(step.getSelector())
                .value(step.getInputData())
                .timeout(step.getWaitTime())
                .retryTimes(3) // 默认重试3次
                .retryInterval(1000L) // 默认重试间隔1秒
                .assertions(buildAssertions(step))
                .build();
    }

    private List<AssertionConfigDTO> buildAssertions(UiTestStep step) {
        // 获取步骤相关的所有断言
        List<UiTestAssertion> assertions = assertionService.findByStepId(step.getId());

        return assertions.stream()
                .map(this::buildAssertion)
                .collect(Collectors.toList());
    }

    private AssertionConfigDTO buildAssertion(UiTestAssertion assertion) {
        return AssertionConfigDTO.builder()
                .type(AssertionType.valueOf(assertion.getAssertionType()))
                .valueExtractor(buildValueExtractor(assertion))
                .operator(AssertionOperator.valueOf(assertion.getAssertionOperator()))
                .expected(assertion.getExpectedValue())
                .message(assertion.getDescription())
                .soft(assertion.getSoftAssert())
                .timeout(assertion.getTimeout())
                .retryInterval(assertion.getPollingInterval().longValue())
                .build();
    }

    private ValueExtractorDTO buildValueExtractor(UiTestAssertion assertion) {
        AssertionType type = AssertionType.valueOf(assertion.getAssertionType());
        String target = assertion.getAssertionTarget();

        // 根据断言类型选择对应的提取器类型
        ExtractorType extractorType = determineExtractorType(type);
        if (extractorType == null) {
            return null;
        }

        // 根据提取器类型构建提取器
        return switch (extractorType) {
            // 元素相关提取
            case ELEMENT_TEXT, ELEMENT_VALUE, ELEMENT_PRESENT -> buildElementExtractor(target, extractorType);
            case ELEMENT_ATTRIBUTE, ELEMENT_CSS -> buildElementPropertyExtractor(target, extractorType);

            // 页面相关提取
            case PAGE_TITLE, PAGE_URL, PAGE_SOURCE -> buildSimpleExtractor(extractorType);

            // 变量提取
            case VARIABLE -> buildVariableExtractor(target);

            // 数据库相关提取
            case DB_FIELD, DB_COUNT -> buildDatabaseExtractor(target, extractorType);

            // JSON相关提取
            case JSON_PATH -> buildJsonPathExtractor(target);

            // 文件相关提取
            case FILE_CONTENT, FILE_SIZE -> buildFileExtractor(target, extractorType);

            // 性能相关提取
            case PERFORMANCE_METRIC -> buildPerformanceExtractor(target);

            // 网络相关提取
            case NETWORK_RESPONSE, NETWORK_STATUS -> buildNetworkExtractor(target, extractorType);
            default -> {
                log.warn("Unsupported extractor type: {}", extractorType);
                yield null;
            }
        };
    }

    private ExtractorType determineExtractorType(AssertionType assertionType) {
        return switch (assertionType) {
            case ELEMENT_TEXT -> ExtractorType.ELEMENT_TEXT;
            case ELEMENT_VALUE -> ExtractorType.ELEMENT_VALUE;
            case ELEMENT_ATTRIBUTE -> ExtractorType.ELEMENT_ATTRIBUTE;
            case ELEMENT_CSS -> ExtractorType.ELEMENT_CSS;
            case ELEMENT_PRESENT, ELEMENT_NOT_PRESENT, ELEMENT_VISIBLE, ELEMENT_NOT_VISIBLE ->
                    ExtractorType.ELEMENT_PRESENT;
            case PAGE_TITLE -> ExtractorType.PAGE_TITLE;
            case PAGE_URL -> ExtractorType.PAGE_URL;
            case PAGE_SOURCE -> ExtractorType.PAGE_SOURCE;
            case VARIABLE_VALUE -> ExtractorType.VARIABLE;
            case DB_FIELD_VALUE -> ExtractorType.DB_FIELD;
            case DB_RECORD_COUNT -> ExtractorType.DB_COUNT;
            case JSON_PATH -> ExtractorType.JSON_PATH;
            case FILE_CONTENT -> ExtractorType.FILE_CONTENT;
            case FILE_SIZE -> ExtractorType.FILE_SIZE;
            case PERFORMANCE_TIMING -> ExtractorType.PERFORMANCE_METRIC;
            case NETWORK_RESPONSE -> ExtractorType.NETWORK_RESPONSE;
            case NETWORK_STATUS -> ExtractorType.NETWORK_STATUS;
            default -> null;
        };
    }

    private ValueExtractorDTO buildElementExtractor(String target, ExtractorType extractorType) {
        return ValueExtractorDTO.builder()
                .type(extractorType)
                .locator(buildElementLocatorFromTarget(target))
                .build();
    }

    private ValueExtractorDTO buildElementPropertyExtractor(String target, ExtractorType extractorType) {
        // 格式：selector@attributeName
        String[] parts = target.split("@", 2);
        if (parts.length != 2) {
            log.warn("Invalid attribute target format: {}", target);
            return null;
        }

        return ValueExtractorDTO.builder()
                .type(extractorType)
                .locator(buildElementLocatorFromTarget(parts[0]))
                .attributeName(parts[1])
                .build();
    }

    private ValueExtractorDTO buildSimpleExtractor(ExtractorType type) {
        return ValueExtractorDTO.builder()
                .type(type)
                .build();
    }

    private ValueExtractorDTO buildVariableExtractor(String variableName) {
        return ValueExtractorDTO.builder()
                .type(ExtractorType.VARIABLE)
                .variableName(variableName)
                .build();
    }

    private ValueExtractorDTO buildJsonPathExtractor(String jsonPath) {
        return ValueExtractorDTO.builder()
                .type(ExtractorType.JSON_PATH)
                .expression(jsonPath)
                .build();
    }

    private ValueExtractorDTO buildDatabaseExtractor(String target, ExtractorType extractorType) {

        return null;
    }

    private ValueExtractorDTO buildFileExtractor(String target, ExtractorType extractorType) {

        return null;
    }

    private ValueExtractorDTO buildPerformanceExtractor(String target) {

        return null;
    }

    private ValueExtractorDTO buildNetworkExtractor(String target, ExtractorType extractorType) {

        return null;
    }

    private ElementLocatorDTO buildElementLocatorFromTarget(String target) {
        // 格式：locatorType:value
        String[] parts = target.split(":", 2);
        if (parts.length != 2) {
            log.warn("Invalid element locator format: {}", target);
            return null;
        }

        return ElementLocatorDTO.builder()
                .type(LocatorType.valueOf(parts[0]))
                .value(parts[1])
                .timeout(10)  // 默认超时时间
                .build();
    }

    private String generateGroupName(List<ExecutionCaseDTO> cases) {
        return cases.stream()
                .map(ExecutionCaseDTO::getName)
                .collect(Collectors.joining(" -> "));
    }

}