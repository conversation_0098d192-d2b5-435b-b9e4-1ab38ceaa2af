<template>
    <div>
        <div class="acc_container">
            <div>
                <el-form ref="codeForm" :rules="codeRule" :model="codeInfo" label-width="120px">
                    <el-form-item label="Git仓库地址" prop="gitPath">
                        <el-autocomplete v-model="codeInfo.gitPath" :fetch-suggestions="querySearch" teleported
                            placeholder="选择或输入仓库地址，请填写http://xxxx.git" @select="handleSelect" style="width: 100%;">
                        </el-autocomplete>
                    </el-form-item>
                    <el-form-item label="账号" prop="userName">
                        <el-input v-model="codeInfo.userName" placeholder="账号"></el-input>
                    </el-form-item>
                    <el-form-item label="密码/令牌" prop="passWord">
                        <el-input v-model="codeInfo.passWord" placeholder="密码/令牌" show-password>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="基准分支名称" prop="masterBranch" class="dual-inputs">
                        <el-input v-model="codeInfo.masterBranch" placeholder="基准分支名称，不填则会读取默认分支"
                            class="half-input"></el-input>
                        <el-form-item prop="masterBranchCommitId" class="commit-input half-input">
                            <el-input v-model="codeInfo.masterBranchCommitId" placeholder="基准分支提交记录号"></el-input>
                        </el-form-item>
                    </el-form-item>
                    <el-form-item label="对比分支名称" prop="devBranch" class="dual-inputs">
                        <el-input v-model="codeInfo.devBranch" placeholder="对比分支名称，不填则会读取默认分支号"
                            class=" half-input"></el-input>
                        <el-form-item prop="devBranchCommitId" class="commit-input half-input">
                            <el-input v-model="codeInfo.devBranchCommitId" placeholder="对比分支提交记录号"></el-input>
                        </el-form-item>
                    </el-form-item>
                    <el-form-item label="项目的JDK版本" prop="jdkVersion">
                        <el-select v-model="codeInfo.jdkVersion" placeholder="不选默认为JDK8">
                            <el-option v-for="item in jdkOptions" :key="item.value" :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSubmit" :disabled="isAnalysis" v-prevent-default>{{ btnText }}</el-button>
                        <el-button @click="onClear" v-prevent-default>清空</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="acc_front">
                <p>精准测试的目标：</p>
                <p>找出受影响的接口，协助进行风险评估，降低漏测率提高交付质量</p><br />
                <p>举例：我想检查开发分支dev_test修改/新增了哪些方法，这些方法最终被哪些接口调用了？</p>
                <p>1.输入Git地址和账密，目标分支填写“dev_test”，如果想对比master分支，那么基准分支名称就填master</p>
                <p>2.如果想分析同一个分支的最后一次提交的影响范围，则将源分支与目标分支设置相同即可</p><br />
                <p>静态分析使用注意事项：</p>
                <p>1.目前只针对Spring工程进行字节码分析</p>
                <p>2.执行过程会下载编译工程，耗时较长，请耐心等待分析结束</p>
                <p>3.静态分析有局限性，无法判断AOP、多态的场景</p>
                <p>4.可以在右上角个人中心配置settings.xml和钉钉推送</p>
                <p>5.可以在页面配置中手动维护接口与页面的关系，通过接口找到页面入口</p>
            </div>
        </div>
        <div class="acc_logInput">
            <div id="log-container" class="textLog">
                <el-scrollbar ref="scrollbarRef">
                    <div class="log" v-for="(item, index) in listData.data" :key="index">{{ item }}</div>
                </el-scrollbar>
            </div>
        </div>
    </div>
</template>
<script setup lang='ts'>
import { ref, reactive } from 'vue'
import { validate } from '@/utils/formExtend'
import { useLayoutStore } from '@/stores/modules/layout'
import { codeAnalysis, getRepositoryList } from '@/api/layout'
let codeInfo = reactive({
    gitPath: '',
    userName: '',
    passWord: '',
    masterBranch: '',
    masterBranchCommitId: '',
    devBranch: '',
    devBranchCommitId: '',
    jdkVersion: ''
})
const codeRule = reactive({
    gitPath: [{ required: true, message: '请输入仓库地址' }],
    userName: [{ required: true, message: '请输入登录账号', trigger: 'blur' }],
    passWord: [{ required: true, message: '请输入密码或token', trigger: 'blur' }]
})

let btnText = ref('分析')
let isAnalysis = ref(false)
const codeForm = ref()

const jdkOptions = [
    {
        value: '8'
    },
    {
        value: '11'
    },
    {
        value: '17'
    }
]
let webSocketTaskId = ''
const onSubmit = async () => {
    let { gitPath, userName, passWord, masterBranch, masterBranchCommitId, devBranch, devBranchCommitId, jdkVersion } = codeInfo
    if (!await validate(codeForm)) return
    listData.data = []
    const res = await codeAnalysis({ gitPath, userName, passWord, masterBranch, masterBranchCommitId, devBranch, devBranchCommitId, jdkVersion })
    if (res.data.isSuccess === true) {
        webSocketTaskId = res.data.data
        socket()
    }
}

const onClear = () => {
    codeForm.value.resetFields()
}
let listData = reactive({
    data: [] as any[]
})
// onUpdated(() => {
//     scrollbarRef.value.setScrollTop(listData.data.length * 15)
// });
let ws: WebSocket
const socket = () => {
    const { getStatus } = useLayoutStore()
    const token = getStatus.ACCESS_TOKEN
    const wsProtocol = window.location.protocol.includes('s') ? 'wss://' : 'ws://'
    const webPath = `${window.location.host}/api/beacon-tower/accurate-test/analysisLog/`
    const localPath = 'localhost:8080/accurate-test/analysisLog/'
    const isLocal = window.location.host.indexOf('localhost') !== -1
    ws = new WebSocket(`${wsProtocol}${isLocal ? localPath : webPath}` + webSocketTaskId + '?' + [token])
    ws.onopen = () => {
        console.log('连接成功')
        btnText.value = '分析中'
        isAnalysis.value = true
    }
    ws.onclose = () => {
        console.log('连接断开')
        ws.close()
        btnText.value = '分析'
        isAnalysis.value = false
    }
    ws.onmessage = (msg: any) => {
        listData.data.push(msg.data)
    }
    ws.onerror = () => {
        btnText.value = '分析'
        isAnalysis.value = false
        console.log('连接错误')
    }
}

let repositoryMap = [] as any[]
const getRepositoryLists = async () => {
    const res = await getRepositoryList()
    if (res.data.isSuccess) {
        repositoryMap = Object.entries(res.data.data).map(([name, value]) => ({ name, value }))
    }
}
getRepositoryLists()

const querySearch = (queryString: string, cb: (string: any[]) => void) => {
    const results = queryString
        ? repositoryMap.filter(item => item.name.toLowerCase().includes(queryString.toLowerCase()))
        : repositoryMap
    cb(results)
}
const handleSelect = (item: any) => {
    codeInfo.gitPath = item.value

}

</script>
<style scoped lang='postcss'>
.dual-inputs {
    display: flex;
    justify-content: space-between;
}

.half-input {
    flex: 1;
    margin-right: 1px;
}

.commit-input {
    flex: 1;
}

.acc_logInput {
    flex-grow: 1;
    width: 100%;
    height: 100%;
    padding: 10px;
    /* box-sizing: border-box; */
}

.acc_container {
    display: flex;
    margin: 12px;

    >div:first-child {
        width: 40%;
    }

    >div:nth-of-type(2) {
        flex: 1;
    }
}

.acc_front {
    margin-left: 50px;
    font-size: 15px;
    font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
}

.textLog {
    margin-top: 1px;
    white-space: pre;
    margin-bottom: 15px;
    overflow-y: auto;
    height: 450px;
    cursor: text;
    background-color: #eeeeee;
    font-size: 14px;
    /* padding-bottom: 15px; */
}

.log {
    margin-top: 1px;
}
</style>