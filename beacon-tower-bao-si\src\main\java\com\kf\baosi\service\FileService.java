package com.kf.baosi.service;

import com.kf.baosi.dto.FSFileParamsDTO;
import com.kf.baosi.entity.DataToJiraParam;
import com.kf.baosi.entity.TFile;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface FileService {

    /**
     * 上传文件
     */
    TFile uploadFile(MultipartFile file, String userId);

    /**
     * 读取文件内容为字节数组
     *
     * @param fileId 文件ID
     * @return 文件内容
     */
    ResponseEntity<byte[]> readFileAsBytes(String fileId);

    /**
     * 通过userId查询单条数据
     *
     * @param UserId 用户ID
     * @return 实例对象
     */
    List<TFile> queryXmlByUserId(String UserId, String fileSuffix);

    List<DataToJiraParam> queryJoinedByBySuffixUserId(String userId, String fileSuffix);

    /**
     * 新增数据
     *
     * @param tFile 实例对象
     * @return 实例对象
     */
    int xmlInsert(MultipartFile tFile, String userId);

    /**
     * 修改数据
     *
     * @return 实例对象
     */
    int update(String fileId);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteFileById(String id);

    /**
     * 下载文件
     *
     * @param fileId 文件ID
     */
    void downloadFile(String fileId, HttpServletResponse response);

    /**
     * 下载xMind模板文件
     */
    void downloadXMindTemplateFile(HttpServletRequest req, HttpServletResponse response);

    /**
     * 下载页面配置的模板文件
     */
    void downloadInterfacePageTemplateFile(HttpServletRequest req, HttpServletResponse response);

    /**
     * 根据文件ID查询文件信息
     */
    TFile queryFileById(String fileId);

    /**
     * FS服务
     * 申请一个token，可以进行一次文件上传
     *
     * @return fs_token
     */
    String getTokenForFSUpload();

    /**
     * FS服务
     * 免登录上传base64文件
     *
     * @param fsFileParams 文件参数
     * return 文件ID
     */
    String uploadFileToFS(FSFileParamsDTO fsFileParams);

    /**
     * FS服务 批量上传文件
     *
     * @param fsFileParamsMap key:文件名 value:文件路径
     * @return key:文件名 value:文件ID
     */
    Map<String, String> batchUploadFileToFS(Map<String,String> fsFileParamsMap);


    /**
     * 下载FS的文件
     */
    void getFsFile(String fileId, String fileName,HttpServletResponse response);
}
