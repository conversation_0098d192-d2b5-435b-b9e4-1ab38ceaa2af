import { useLayoutStore } from '@/stores/modules/layout'
import axios, { type AxiosError, type AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// let loading: { close(): void }
// 创建 axios 实例
const request = axios.create({
    // API 请求的默认前缀
    baseURL: import.meta.env.VUE_APP_API_BASE_URL as string | undefined,
    timeout: 60000 // 请求超时时间
})

// 异常拦截处理器
const errorHandler = (error: AxiosError) => {
    if (error.response) {
        if (error.response.status === 401) {
            const { logout } = useLayoutStore()
            logout()
        } else {
            console.log('errorHandler:', error.message)
            // ElMessage.error(error.message)
        }
    } else {
        ElMessage.error('Network Error')
    }
    return Promise.reject(error)
}

// request interceptor
request.interceptors.request.use(config => {
    const { getStatus } = useLayoutStore()
    const token = getStatus.ACCESS_TOKEN
    // 携带token
    if (token) {
        config.headers['Authorization'] = 'Bearer ' + token
    }
    return config
}, errorHandler)

// response 拦截器
request.interceptors.response.use(async (response: AxiosResponse) => {
    const { getStatus, logout } = useLayoutStore()
    // loading.close()
    // 判断 response 是不是一个 blob 对象
    if (response.data instanceof Blob) {
        const newData = await response.data.text()
        if (newData.includes('message')) {
            const msg = JSON.parse(newData).message != '' ? JSON.parse(newData).message : JSON.parse(newData).errors[0].message
            ElMessage.error(msg)
            throw new Error(msg)
        }
    } else if (response.status === 401) {
        if (getStatus.ACCESS_TOKEN) {
            logout()
        }
        return Promise.reject(new Error('Unauthorized'))
    } else if (response.status !== 200) {
        return Promise.reject(new Error(response.statusText || 'Error'))
    }
    return response
}, errorHandler)

export default request