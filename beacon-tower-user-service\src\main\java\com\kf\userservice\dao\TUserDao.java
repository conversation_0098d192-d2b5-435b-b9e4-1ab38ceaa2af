package com.kf.userservice.dao;

import com.kf.userservice.entity.TUser;

import java.util.List;

public interface TUserDao {

    /**
     * 通过ID查询单条数据
     *
     * @param userId 主键
     * @return 实例对象
     */
    TUser queryById(String userId);

    /**
     * 通过ID查询单条数据
     *
     * @param username 用户名
     * @return 实例对象
     */
    TUser queryByUserName(String username);

    /**
     * 通过email查询单条数据
     *
     * @param email 邮箱
     * @return 实例对象
     */
    TUser queryByEmail(String email);

    /**
     * 通过实体作为筛选条件查询
     *
     * @param tUser 实例对象
     * @return 对象列表
     */
    List<TUser> queryAll(TUser tUser);

    /**
     * 新增数据
     *
     * @param tUser 实例对象
     * @return 影响行数
     */
    int insert(TUser tUser);

    /**
     * 修改数据
     *
     * @param tUser 实例对象
     * @return 影响行数
     */
    int update(TUser tUser);

    /**
     * 通过主键删除数据
     *
     * @param userId 主键
     * @return 影响行数
     */
    int deleteById(Long userId);

    String getRoleById(Long userId);

    int deleteByIds(String ids);

}