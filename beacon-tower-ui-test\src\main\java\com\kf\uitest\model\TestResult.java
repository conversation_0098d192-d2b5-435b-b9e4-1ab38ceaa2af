package com.kf.uitest.model;

import com.kf.uitest.enums.StepStatus;
import com.kf.uitest.enums.TestStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestResult {
    /**
     * 执行ID
     */
    private String executionId;

    /**
     * 执行状态
     */
    private TestStatus status;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行时长（毫秒）
     */
    private Long duration;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 用例执行结果列表
     */
    @Builder.Default
    private List<CaseExecutionResult> caseResults = new ArrayList<>();

    /**
     * 步骤执行结果列表
     */
    @Builder.Default
    private List<StepExecutionResult> stepResults = new ArrayList<>();

    @Builder.Default
    private List<GroupResult> groupResults = new ArrayList<>();

    public TestResult(String executionId) {
        this.executionId = executionId;
        this.status = TestStatus.RUNNING;
        this.stepResults = new ArrayList<>();
        this.startTime = LocalDateTime.now();
    }

    /**
     * 添加用例执行结果
     */
    public void addCaseResult(CaseExecutionResult caseResult) {
        this.caseResults.add(caseResult);
        updateStatus();
    }

    /**
     * 添加步骤执行结果
     */
    public void addStepResult(StepExecutionResult stepResult) {
        this.stepResults.add(stepResult);
        if (!stepResult.isSuccess() && status != TestStatus.FAILED) {
            this.status = TestStatus.FAILED;
            this.errorMessage = stepResult.getErrorMessage();
        }
        updateStatus();
    }

    /**
     * 完成执行
     */
    public void complete() {
        this.endTime = LocalDateTime.now();
        this.duration = ChronoUnit.MILLIS.between(startTime, endTime);
        if (status == TestStatus.RUNNING) {
            this.status = TestStatus.PASSED;
        }
        updateStatus();
    }

    /**
     * 检查是否执行成功
     */
    public boolean isSuccessful() {
        return this.status == TestStatus.SUCCESS;
    }

    /**
     * 获取执行摘要
     */
    public String getSummary() {
        return String.format(
                "Execution[id=%s, status=%s, cases=%d, passed=%d, failed=%d, duration=%dms]",
                executionId,
                status,
                caseResults.size(),
                getPassedCases(),
                getFailedCases(),
                duration
        );
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CaseExecutionResult {
        /**
         * 用例ID
         */
        private Long caseId;

        /**
         * 执行状态
         */
        private TestStatus status;

        /**
         * 开始时间
         */
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        private LocalDateTime endTime;

        /**
         * 执行时长（毫秒）
         */
        private Long duration;

        /**
         * 错误信息
         */
        private String errorMessage;

        /**
         * 重试次数
         */
        private Integer retryCount;

        /**
         * 元数据
         */
        @Builder.Default
        private Map<String, Object> metadata = new HashMap<>();

        /**
         * 检查是否执行成功
         */
        public boolean isSuccess() {
            return this.status == TestStatus.SUCCESS;
        }

        /**
         * 获取用例执行摘要
         */
        public String getSummary() {
            StringBuilder summary = new StringBuilder();
            summary.append(String.format("Case[id=%d, status=%s", caseId, status));

            if (duration != null) {
                summary.append(String.format(", duration=%dms", duration));
            }

            if (retryCount != null && retryCount > 0) {
                summary.append(String.format(", retries=%d", retryCount));
            }

            if (errorMessage != null) {
                summary.append(String.format(", error=%s", errorMessage));
            }

            summary.append("]");
            return summary.toString();
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StepExecutionResult {
        /**
         * 步骤引用
         */
        private TestExecutionStep step;

        /**
         * 步骤ID
         */
        private String stepId;

        /**
         * 用例ID
         */
        private String caseId;

        /**
         * 执行状态
         */
        private StepStatus status;

        /**
         * 开始时间
         */
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        private LocalDateTime endTime;

        /**
         * 执行时长（毫秒）
         */
        private Long duration;

        /**
         * 错误信息
         */
        private String errorMessage;

        /**
         * 重试次数
         */
        private Integer retryCount;

        /**
         * 步骤类型
         */
        private String stepType;

        /**
         * 步骤名称
         */
        private String stepName;

        /**
         * 步骤参数
         */
        @Builder.Default
        private Map<String, Object> parameters = new HashMap<>();

        /**
         * 元数据
         */
        @Builder.Default
        private Map<String, Object> metadata = new HashMap<>();

        /**
         * 测试数据
         */
        @Builder.Default
        private Map<String, Object> testData = new HashMap<>();

        /**
         * 检查是否执行成功
         */
        public boolean isSuccess() {
            return this.status == StepStatus.SUCCESS;
        }

        /**
         * 获取步骤执行摘要
         */
        public String getSummary() {
            StringBuilder summary = new StringBuilder();
            summary.append(String.format("Step[id=%s, caseId=%s, type=%s, name=%s, status=%s",
                    stepId, caseId, stepType, stepName, status));

            if (duration != null) {
                summary.append(String.format(", duration=%dms", duration));
            }

            if (retryCount != null && retryCount > 0) {
                summary.append(String.format(", retries=%d", retryCount));
            }

            if (errorMessage != null) {
                summary.append(String.format(", error=%s", errorMessage));
            }

            summary.append("]");
            return summary.toString();
        }

        /**
         * 开始执行
         */
        public void start() {
            this.startTime = LocalDateTime.now();
            this.status = StepStatus.RUNNING;
        }

        /**
         * 完成执行
         */
        public void complete(StepStatus status, String message) {
            this.endTime = LocalDateTime.now();
            this.status = status;
            this.duration = ChronoUnit.MILLIS.between(startTime, endTime);
            if (message != null) {
                this.errorMessage = message;
            }
        }

        /**
         * 完成执行（无消息）
         */
        public void complete(StepStatus status) {
            complete(status, null);
        }

        /**
         * 标记为失败
         */
        public void markAsFailed(String errorMessage) {
            complete(StepStatus.FAILED, errorMessage);
        }

        /**
         * 标记为成功
         */
        public void markAsSuccess() {
            complete(StepStatus.SUCCESS);
        }

        /**
         * 标记为跳过
         */
        public void markAsSkipped(String reason) {
            complete(StepStatus.SKIPPED, reason);
        }

        /**
         * 从步骤创建结果
         */
        public static StepExecutionResult fromStep(TestExecutionStep step) {
            return StepExecutionResult.builder()
                    .step(step)
                    .stepId(step.getId())
                    .caseId(step.getNodeId())
                    .stepType(step.getType().name())
                    .stepName(step.getName())
                    .status(StepStatus.PENDING)  // 初始状态为PENDING
                    .startTime(LocalDateTime.now())
                    .build();
        }

        /**
         * 添加单个测试数据
         */
        public void addTestData(String key, Object value) {
            if (testData == null) {
                testData = new HashMap<>();
            }
            testData.put(key, value);
        }

        /**
         * 添加多个测试数据
         */
        public void addTestData(Map<String, Object> data) {
            if (testData == null) {
                testData = new HashMap<>();
            }
            if (data != null) {
                testData.putAll(data);
            }
        }
    }

    /**
     * 获取失败的用例ID列表
     */
    public List<Long> getFailedCaseIds() {
        return caseResults.stream()
                .filter(result -> result.getStatus() == TestStatus.FAILED)
                .map(CaseExecutionResult::getCaseId)
                .collect(Collectors.toList());
    }

    /**
     * 获取成功的用例数
     */
    public int getPassedCases() {
        return (int) caseResults.stream()
                .filter(result -> result.getStatus() == TestStatus.SUCCESS)
                .count();
    }

    /**
     * 获取失败的用例数
     */
    public int getFailedCases() {
        return (int) caseResults.stream()
                .filter(result -> result.getStatus() == TestStatus.FAILED)
                .count();
    }

    /**
     * 更新执行状态
     */
    private void updateStatus() {
        if (caseResults.isEmpty()) {
            return;
        }

        int failedCases = getFailedCases();
        int totalCases = caseResults.size();

        if (failedCases > 0) {
            this.status = TestStatus.FAILED;
        } else if (totalCases == 0) {
            this.status = TestStatus.SKIPPED;
        } else if (getPassedCases() == totalCases) {
            this.status = TestStatus.SUCCESS;
        } else {
            this.status = TestStatus.RUNNING;
        }
    }

    /**
     * 获取用例的所有步骤结果
     */
    public List<StepExecutionResult> getCaseStepResults(Long caseId) {
        return stepResults.stream()
                .filter(step -> step.getCaseId().equals(caseId))
                .collect(Collectors.toList());
    }

    public void addGroupResult(GroupResult groupResult) {
        this.groupResults.add(groupResult);
        groupResult.getCaseResults().forEach(this::addCaseResult);
        updateStatus();
    }
}