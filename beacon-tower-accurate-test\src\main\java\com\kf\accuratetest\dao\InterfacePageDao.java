package com.kf.accuratetest.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kf.accuratetest.entity.InterfacePage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InterfacePageDao extends BaseMapper<InterfacePage> {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    InterfacePage queryById(Long id);

    /**
     * 通过ID查询单条数据
     *
     * @param userId 用户id
     * @return 实例对象
     */
    List<InterfacePage> queryAllByUserId(String userId);

    /**
     * 统计总行数
     *
     * @param InterfacePage 查询条件
     * @return 总行数
     */
    long count(InterfacePage InterfacePage);

    /**
     * 新增数据
     *
     * @param InterfacePage 实例对象
     * @return 影响行数
     */
    int insert(InterfacePage InterfacePage);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<TInterfacePage> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<InterfacePage> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<TInterfacePage> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<InterfacePage> entities);

    /**
     * 修改数据
     *
     * @param InterfacePage 实例对象
     * @return 影响行数
     */
    int update(InterfacePage InterfacePage);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

}

