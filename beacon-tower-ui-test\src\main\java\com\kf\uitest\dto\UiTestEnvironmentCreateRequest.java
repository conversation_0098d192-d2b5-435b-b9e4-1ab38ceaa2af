package com.kf.uitest.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.List;

@Data
public class UiTestEnvironmentCreateRequest {

    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    @NotBlank(message = "环境名称不能为空")
    private String environmentName;
    
    private String browserType = "chromium";
    private Integer isHeadless = 1;
    private String description;
    
    @Valid
    private List<VariableCreateRequest> variables;
    
    @Data
    public static class VariableCreateRequest {
        private String variableName;
        private String variableValue;
        private String description;
    }
}