<template>
    <el-dialog
        v-model="dialogVisible"
        title="环境配置"
        width="1200px"
        :before-close="handleClose"
        destroy-on-close
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
        <div class="env-config-container">
            <!-- 左侧环境列表 -->
            <div class="env-list-section">
                <div class="section-header">
                    <h3>环境列表</h3>
                    <el-button type="primary" size="small" @click="handleAddEnvironment">
                        新增环境
                    </el-button>
                </div>
                <el-scrollbar>
                    <div class="env-list">
                        <template v-if="localEnvironments.length > 0">
                            <div
                                v-for="(env, index) in localEnvironments"
                                :key="index"
                                class="env-item"
                                :class="{ active: currentIndex === index }"
                                @click="selectEnvironment(index)"
                            >
                                <span class="env-name">{{ env.environment.environmentName }}</span>
                                <div class="env-actions">
                                    <el-button
                                        type="danger"
                                        size="small"
                                        link
                                        :disabled="isDeleteDisabled"
                                        @click.stop="handleDeleteEnvironment(index)"
                                    >
                                        删除
                                    </el-button>
                                </div>
                            </div>
                        </template>
                        <div v-else class="empty-tip">
                            暂无环境配置，请点击"新增环境"按钮创建
                        </div>
                    </div>
                </el-scrollbar>
            </div>

            <!-- 右侧配置区域 -->
            <div class="env-config-section" v-if="currentEnvironment">
                <el-scrollbar>
                    <!-- 环境基本配置 -->
                    <div class="config-section basic-config">
                        <div class="section-header">
                            <h3>环境配置</h3>
                        </div>
                        <el-form
                            ref="envFormRef"
                            :model="currentEnvironment.environment"
                            :rules="envRules"
                        >
                            <div class="form-row">
                                <el-form-item label="环境名称" prop="environmentName">
                                    <el-input v-model="currentEnvironment.environment.environmentName" />
                                </el-form-item>
                                <el-form-item label="浏览器类型" prop="browserType">
                                    <el-select v-model="currentEnvironment.environment.browserType">
                                        <el-option
                                            v-for="option in browserOptions"
                                            :key="option.value"
                                            :label="option.label"
                                            :value="option.value"
                                        />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="无头模式" prop="isHeadless">
                                    <el-checkbox 
                                        v-model="currentEnvironment.environment.isHeadless"
                                        :true-label="1"
                                        :false-label="0"
                                    />
                                </el-form-item>
                            </div>
                        </el-form>
                    </div>

                    <!-- 环境变量配置 -->
                    <div class="config-section variables-config">
                        <div class="section-header">
                            <h3>环境变量</h3>
                            <el-button type="primary" size="small" @click="handleAddVariable">
                                添加变量
                            </el-button>
                        </div>
                        <el-form ref="variablesFormRef" :model="currentEnvironment">
                            <el-table :data="currentEnvironment.variables">
                                <el-table-column prop="variableName" label="变量名" min-width="30%">
                                    <template #default="{ row, $index }">
                                        <el-form-item 
                                            :prop="`variables.${$index}.variableName`"
                                            :rules="variableRules.variableName"
                                        >
                                            <el-input v-model="row.variableName" size="small" />
                                        </el-form-item>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="variableValue" label="变量值" min-width="30%">
                                    <template #default="{ row }">
                                        <el-input v-model="row.variableValue" size="small" />
                                    </template>
                                </el-table-column>
                                <el-table-column prop="description" label="描述" min-width="40%">
                                    <template #default="{ row }">
                                        <el-input v-model="row.description" size="small" />
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="80">
                                    <template #default="{ $index }">
                                        <el-button
                                            type="danger"
                                            size="small"
                                            link
                                            @click="handleDeleteVariable($index)"
                                        >
                                            删除
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-form>
                    </div>
                </el-scrollbar>
            </div>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose" :disabled="isSaving">关闭</el-button>
                <el-button type="primary" @click="handleSave" :loading="isSaving">保存</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { createEnvironment, getBrowsersOptions, updateEnvironment } from '@/api/layout'

interface EnvironmentVariableDTO {
    id?: number
    environmentId?: number
    variableName: string
    variableValue: string
    description: string
}

interface EnvironmentDTO {
    id?: number
    userId?: number
    environmentName: string
    browserType: string
    isHeadless: number
}

interface EnvironmentWithVariablesDTO {
    environment: EnvironmentDTO
    variables: EnvironmentVariableDTO[]
}

interface CreateEnvironmentDTO {
    projectId: number
    environmentName: string
    browserType: string
    isHeadless: number
    variables?: Array<{
        variableName: string
        variableValue: string
        description: string
    }>
}

interface UpdateEnvironmentDTO {
    id: number
    environmentName?: string
    browserType?: string
    isHeadless?: number
    delete?: number[]
    variables?: {
        create?: Array<{
            variableName: string
            variableValue: string
            description: string
        }>
        update?: Array<{
            id: number
            variableName?: string
            variableValue?: string
            description?: string
        }>
        delete?: number[]
    }
}

const props = defineProps<{
    visible: boolean
    projectId: number
    environments: EnvironmentWithVariablesDTO[]
}>()

const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void
    (e: 'update:environments', value: EnvironmentWithVariablesDTO[]): void
}>()
const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
})

// 本地环境数据
const localEnvironments = ref<EnvironmentWithVariablesDTO[]>([])
const currentIndex = ref<number>(-1)
const currentEnvironmentData = ref<EnvironmentWithVariablesDTO | null>(null)
const envFormRef = ref<FormInstance>()

// 表单校验规则
const envRules: FormRules = {
    environmentName: [{ required: true, message: '请输入环境名称', trigger: 'blur' }, { max: 10, message: '环境名称不能超过10个字符', trigger: 'blur' }]
}

// 计算属性用于显示当前环境
const currentEnvironment = computed(() => {
    if (currentIndex.value === -1) {
        return currentEnvironmentData.value
    }
    return currentIndex.value >= 0 ? localEnvironments.value[currentIndex.value] : null
})

// 添加浏览器选项的状态
const browserOptions = ref<Array<{label: string, value: string}>>([])

// 添加获取浏览器选项的方法
const fetchBrowserOptions = async () => {
    try {
        const response = await getBrowsersOptions()
        if (response.data.isSuccess) {
            browserOptions.value = response.data.data
        }
    } catch (error) {
        console.error('获取浏览器选项失败:', error)
        ElMessage.error('获取浏览器选项失败')
    }
}

// 初始化数据
const initData = () => {
    localEnvironments.value = JSON.parse(JSON.stringify(props.environments))
    currentEnvironmentData.value = null
    if (localEnvironments.value.length > 0) {
        currentIndex.value = 0
    } else {
        currentIndex.value = -1
    }
}

// 选择环境
const selectEnvironment = (index: number) => {
    // 保存当前环境的更改到本地数组
    if (currentIndex.value !== -1) {
        localEnvironments.value[currentIndex.value] = JSON.parse(JSON.stringify(currentEnvironment.value))
    }
    
    currentIndex.value = index
    currentEnvironmentData.value = null
    // 重置表单校验
    envFormRef.value?.resetFields()
    variablesFormRef.value?.resetFields()
}

// 新增环境
const handleAddEnvironment = () => {
    currentEnvironmentData.value = {
        environment: {
            environmentName: '',
            browserType: 'chromium',
            isHeadless: 1
        },
        variables: []
    }
    currentIndex.value = -1
}

// 添加一个ref来存储要删除的环境ID
const environmentsToDelete = ref<number[]>([])

// 修改删除环境的方法
const handleDeleteEnvironment = async (index: number) => {
    try {
        await ElMessageBox.confirm('确定要删除该环境配置吗？', '提示', {
            type: 'warning'
        })
        
        // 如果环境有ID，添加到待删除列表中
        const envToDelete = localEnvironments.value[index]
        if (envToDelete.environment.id) {
            environmentsToDelete.value.push(envToDelete.environment.id)
        }
        
        localEnvironments.value.splice(index, 1)
        if (currentIndex.value === index) {
            currentIndex.value = localEnvironments.value.length > 0 ? 0 : -1
        } else if (currentIndex.value > index) {
            currentIndex.value--
        }
    } catch {
        // 用户取消删除
    }
}

// 新增变量
const handleAddVariable = () => {
    if (!currentEnvironment.value) return

    currentEnvironment.value.variables.push({
        variableName: '',
        variableValue: '',
        description: ''
    })
}

// 删除变量
const handleDeleteVariable = (index: number) => {
    if (!currentEnvironment.value) return
    currentEnvironment.value.variables.splice(index, 1)
}

// 添加变量表单的引用
const variablesFormRef = ref<FormInstance>()

// 添加变量校验规则
const variableRules = {
    variableName: [
        { required: true, message: '请输入变量名', trigger: 'blur' }
    ]
}

// 添加 loading 状态
const isSaving = ref(false)

// 修改保存方法，移除不必要的错误提示
const handleSave = async () => {
    if (!envFormRef.value || !currentEnvironment.value || !variablesFormRef.value) return

    try {
        isSaving.value = true  // 开始保存，设置 loading 状态

        // 先验证当前环境
        await Promise.all([
            envFormRef.value.validate(),
            variablesFormRef.value.validate()
        ])
        
        // 检查所有环境的必填项和变量名重复
        for (const env of localEnvironments.value) {
            // 检查环境名称
            if (!env.environment.environmentName?.trim()) {
                ElMessage.error('请检查环境名称')
                return
            }

            // 检查环境变量名称重复
            const variableNames = new Set<string>()
            for (const variable of env.variables) {
                const variableName = variable.variableName?.trim()
                if (variableName) {
                    if (variableNames.has(variableName)) {
                        ElMessage.error(`环境 "${env.environment.environmentName}" 中存在重复的变量名称: ${variableName}`)
                        return
                    }
                    variableNames.add(variableName)
                }
            }
        }

        // 处理待删除的环境
        if (environmentsToDelete.value.length > 0) {
            const deleteData: UpdateEnvironmentDTO = {
                id: environmentsToDelete.value[0],
                delete: environmentsToDelete.value
            }
            const res = await updateEnvironment(deleteData)
            if (res.data.isSuccess) {
                // 从本地环境列表中移除已删除的环境
                localEnvironments.value = localEnvironments.value.filter(
                    env => !environmentsToDelete.value.includes(env.environment.id!)
                )
            }
        }

        // 如果当前是新建环境，先创建它
        if (currentIndex.value === -1 && currentEnvironment.value) {
            const createData: CreateEnvironmentDTO = {
                projectId: props.projectId,
                environmentName: currentEnvironment.value.environment.environmentName,
                browserType: currentEnvironment.value.environment.browserType,
                isHeadless: currentEnvironment.value.environment.isHeadless,
                variables: currentEnvironment.value.variables.map(v => ({
                    variableName: v.variableName,
                    variableValue: v.variableValue,
                    description: v.description
                }))
            }
            
            const res = await createEnvironment(createData)
            if (res.data.isSuccess) {
                localEnvironments.value.push(res.data.data)
                currentIndex.value = localEnvironments.value.length - 1
                currentEnvironmentData.value = null
            }
        }

        // 收集所有需要更新的环境
        const updatePromises = localEnvironments.value.map(async (env) => {
            if (env.environment.id) {
                // 找到原始环境数据进行比较
                const originalEnv = props.environments.find(e => e.environment.id === env.environment.id)
                if (originalEnv) {
                    const variableChanges = computeVariableChanges(originalEnv.variables, env.variables)
                    const hasEnvironmentChanges = 
                        originalEnv.environment.environmentName !== env.environment.environmentName ||
                        originalEnv.environment.browserType !== env.environment.browserType ||
                        originalEnv.environment.isHeadless !== env.environment.isHeadless

                    const hasVariableChanges = 
                        variableChanges.create.length > 0 || 
                        variableChanges.update.length > 0 || 
                        variableChanges.delete.length > 0

                    // 只有当有实际变更时才发送更新请求
                    if (hasEnvironmentChanges || hasVariableChanges) {
                        const updateData: UpdateEnvironmentDTO = {
                            id: env.environment.id,
                            environmentName: env.environment.environmentName,
                            browserType: env.environment.browserType,
                            isHeadless: env.environment.isHeadless,
                            variables: {
                                create: variableChanges.create,
                                update: variableChanges.update,
                                delete: variableChanges.delete
                            }
                        }
                        const res = await updateEnvironment(updateData)
                        // 如果更新成功，本地数据保持不变（因为已经是最新的了）
                        if (!res.data.isSuccess) {
                            throw new Error('更新失败')
                        }
                    }
                }
            }
        })

        // 等待所有更新完成
        await Promise.all(updatePromises)
        
        // 清空待删除列表
        environmentsToDelete.value = []
        ElMessage.success('保存成功')
    } catch {
        return
    } finally {
        isSaving.value = false  // 无论成功失败，都结束 loading 状态
    }
}

// 计算变量变更
const computeVariableChanges = (
    originalVars: EnvironmentVariableDTO[],
    currentVars: EnvironmentVariableDTO[]
) => {
    const result = {
        create: [] as Array<Omit<EnvironmentVariableDTO, 'id' | 'environmentId'>>,
        update: [] as Array<Partial<EnvironmentVariableDTO> & { id: number }>,
        delete: [] as number[]
    }
    
    // 找出要删除的变量
    originalVars.forEach(originalVar => {
        if (!currentVars.find(v => v.id === originalVar.id)) {
            result.delete.push(originalVar.id!)
        }
    })
    
    // 找出要新建和更新的变量
    currentVars.forEach(currentVar => {
        if (!currentVar.id) {
            // 新建的变量
            result.create.push({
                variableName: currentVar.variableName,
                variableValue: currentVar.variableValue,
                description: currentVar.description
            })
        } else {
            // 更新的变量
            const originalVar = originalVars.find(v => v.id === currentVar.id)
            if (originalVar && (
                originalVar.variableName !== currentVar.variableName ||
                originalVar.variableValue !== currentVar.variableValue ||
                originalVar.description !== currentVar.description
            )) {
                result.update.push({
                    id: currentVar.id,
                    variableName: currentVar.variableName,
                    variableValue: currentVar.variableValue,
                    description: currentVar.description
                })
            }
        }
    })
    
    return result
}

// 监听 props.environments 的变化
watch(
    () => props.environments,
    (newValue) => {
        if (dialogVisible.value) {  // 只在弹窗打开时更新数据
            localEnvironments.value = JSON.parse(JSON.stringify(newValue))
            if (localEnvironments.value.length > 0) {
                currentIndex.value = 0
            } else {
                currentIndex.value = -1
            }
        }
    },
    { immediate: true }  // 立即执行一次
)

// 监听 dialogVisible 的变化
watch(
    () => dialogVisible.value,
    (newValue) => {
        if (newValue) {  // 当弹窗打开时
            localEnvironments.value = JSON.parse(JSON.stringify(props.environments))
            if (localEnvironments.value.length > 0) {
                currentIndex.value = 0
            } else {
                currentIndex.value = -1
            }
        }
    }
)

// handleClose 可以简化为
const handleClose = () => {
    emit('update:environments', localEnvironments.value)
    dialogVisible.value = false
}

// 在组件挂载时获取浏览器选项
onMounted(() => {
    fetchBrowserOptions()
})

// 组件挂载时初始化数据
initData()

// 添加计算属性判断是否允许删除
const isDeleteDisabled = computed(() => {
    return localEnvironments.value.length <= 1
})
</script>

<style lang="postcss" scoped>
.env-config-container {
    display: flex;
    height: 600px;
}

.env-list-section {
    width: 250px;
    border-right: 1px solid var(--el-border-color-light);
    display: flex;
    flex-direction: column;
    padding-right: 8px;

    :deep(.el-scrollbar__view) {
        display: flex;
        flex-direction: column;
        gap: 20px;
        height: 100%;
    }
}

.env-config-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-left: -1px;

    :deep(.el-scrollbar) {
        flex: 1;
        overflow: hidden;
    }

    :deep(.el-scrollbar__wrap) {
        height: 100%;
    }

    :deep(.el-scrollbar__view) {
        padding: 12px 12px 0;
        display: flex;
        flex-direction: column;
        gap: 20px;
        height: 100%;
    }
}

.variables-config {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .el-form {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    :deep(.el-table) {
        flex: 1;
        height: 100%;

        .el-table__body-wrapper {
            overflow-x: auto;
        }
    }

    :deep(.el-scrollbar__view) {
        padding: 0;
    }
}

.form-row {
    display: flex;
    gap: 20px;
    align-items: flex-start;

    :deep(.el-form-item) {
        margin-bottom: 0;
        flex: 1;

        &:last-child {
            flex: 0 0 120px;
        }
    }
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px; /* 统一标题间距 */

    h3 {
        margin: 0;
        font-size: 15px;
        font-weight: 600;
        color: var(--el-text-color-primary);
    }
}

.env-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: var(--el-border-radius-base);
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid var(--el-border-color-light);
    background: var(--el-fill-color-blank);
    padding: 8px 12px; /* 调整内边距使高度更合适 */
    margin-bottom: 8px; /* 添加项目间距 */

    &:last-child {
        margin-bottom: 0;
    }

    &:hover {
        background-color: var(--el-fill-color-light);
        border-color: var(--el-border-color);
    }

    &.active {
        background-color: var(--el-color-primary-light-9);
        border-color: var(--el-color-primary-light-7);
    }
}

.env-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; /* 确保文本不换行 */
    font-size: 14px;
    line-height: 1.5; /* 优化行高 */
    padding-right: 8px; /* 与删除按钮保持间距 */
}

.env-actions {
    opacity: 0;
    transition: opacity 0.2s;
    margin-left: 8px;
}

.env-item:hover .env-actions {
    opacity: 1;
}

.config-section {
    background: var(--el-bg-color);
    padding: 20px;
    border-radius: var(--el-border-radius-base);
    border: 1px solid var(--el-border-color-light);

    + .config-section {
        margin-top: -8px; /* 减少区块间距 */
    }
}

/* 优化表格样式 */
:deep(.el-table) {
    --el-table-border-color: var(--el-border-color-light);
    --el-table-header-bg-color: var(--el-fill-color-light);

    .el-table__body-wrapper {
        overflow-x: hidden;
    }

    .el-form-item {
        margin-bottom: 0;
    }

    .el-form-item__error {
        position: absolute;
        top: 100%;
        left: 0;
    }
}

/* 统一表单元素尺寸 */
:deep(.el-form-item__label) {
    font-size: 13px;
}
:deep(.el-input),
:deep(.el-select),
:deep(.el-textarea) {
    font-size: 13px;
}

.empty-tip {
    color: var(--el-text-color-secondary);
    padding: 16px;
    text-align: center;
    font-size: 13px;
}

/* 优化滚动条样式 */
:deep(.el-scrollbar) {
    --el-scrollbar-opacity: 0.3;
}

</style>