<template>
    <div>
        <div style="width: 40%">
            <el-form ref="userInfoRef" :model="userInfo" :rules="userInfoRules">
                <div class="login-form-container">
                    <el-form-item label="JIRA账号" prop="userName">
                        <el-input v-model="userInfo.userName" :disabled="!isDisabled"
                                  placeholder="填写JIRA账号"></el-input>
                    </el-form-item>
                    <el-form-item label="JIRA密码" prop="passWord">
                        <el-input v-model="userInfo.passWord" :disabled="!isDisabled"
                                  placeholder="填写JIRA密码"
                                  type="password"></el-input>
                    </el-form-item>
                    <div v-if="showOverlay" class="overlay">
                        <div class="overlay-content">
                            <div class="icon-and-message">
                                <el-icon class="login-icon">
                                    <el-icon-circle-check/>
                                </el-icon>
                                <p class="login-message">【{{ loginUserName }}】已登录</p>
                            </div>
                            <el-button v-prevent-default class="login-button" text
                                       type="primary" @click="handleOverlayButton">重新登录
                            </el-button>
                        </div>
                    </div>
                </div>
            </el-form>
            <el-button v-prevent-default :disabled="!isDisabled" :loading="loading"
                       type="primary" @click="submit">登录
            </el-button>
        </div>

    </div>
</template>

<script lang='ts' setup>
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { loginCheck, loginJira } from '@/api/layout'
import { validateField } from '@/utils/formExtend'

const userInfoRef = ref(null)
const userInfoRules = reactive({
    userName: [{ required: true, message: '请输入账号', trigger: 'blur' }],
    passWord: [{ required: true, message: '请输入密码', trigger: 'blur' }]
})

const isDisabled = ref(true)
const loading = ref(false)

// 初始化遮罩层不显示
const showOverlay = ref(false)
// 初始化遮罩层的提示信息
const loginUserName = ref('')
const userInfo = reactive({
    userName: '',
    passWord: ''
})
const check = async () => {
    const res = await loginCheck()
    if (!res.data.isSuccess) {
        return
    }
    loginUserName.value = res.data.data.jiraUserName
    userInfo.userName = res.data.data.jiraUserName
    showOverlay.value = true
    isDisabled.value = false
}
check()

// 点击取消遮罩层
const handleOverlayButton = () => {
    showOverlay.value = false
    isDisabled.value = true
}
// 登录jira
const submit = async () => {
    loading.value = true
    try {
        const { userName, passWord } = userInfo
        if (!await validateField(userInfoRef, ['userName', 'passWord'])) return
        const res = await loginJira({ userName: userName, passWord: passWord })
        if (!res.data.isSuccess) {
            ElMessage.error(res.data.message)
            return
        }
        ElMessage.success('登录成功')
        isDisabled.value = false
        showOverlay.value = true
    } catch (error) {
        console.log(error)
    } finally {
        loading.value = false
    }
}

</script>
<style lang='postcss' scoped>
.login-form-container {
    position: relative;
}

.overlay-content {
    text-align: center;
    flex-direction: column;
}

.icon-and-message {
    display: flex;
    align-items: center;
}

.login-icon {
    color: green;
    font-size: 25px;
}

.login-message {
    font-size: 18px;
    padding-left: 0;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid #e0dfdf;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
}

.login-button:hover {
    background-color: transparent !important;
    text-decoration: underline;
}
</style>

