package com.kf.aitest.controller;

import com.kf.aitest.common.ResponseDoMain;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.kf.aitest.common.ResponseDoMain.success;

/**
 * 健康检查控制器
 */
@RestController
@RequestMapping("/api/health")
public class HealthController {

    /**
     * 健康检查接口
     */
    @GetMapping("/check")
    public ResponseDoMain<Map<String, Object>> healthCheck() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("service", "beacon-tower-ai-test");
        
        return success("服务运行正常", data);
    }
}
