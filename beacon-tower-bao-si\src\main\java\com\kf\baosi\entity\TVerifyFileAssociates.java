package com.kf.baosi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_verify_file_associates")
public class TVerifyFileAssociates extends Model<TVerifyFileAssociates> {
    @Serial
    private static final long serialVersionUID = 1L;
    // 主键
    @TableId(type = IdType.AUTO)
    private Long id;

    private String tVerifyFileId;

    // taskId wordTemplatePlus服务的任务id
    private String taskId;

    // 文件id
    private String fileId;

    // 创建时间
    private Date createTime;

    // 修改时间
    private Date updateTime;

    // 是否是最后一个文档 0是 1不是
    private Integer completeTag;

    // 禁用 0正常 1删除
    private Integer mergeTag;

    // 状态 0正常 1禁用
    private Integer status;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
