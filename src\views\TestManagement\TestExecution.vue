<template>
    <div>
        <div ref="searchEl" class="table-search-form">
            <el-row :gutter="15" class="clear-both">
                <el-col :span="24">
                    <card-list :show-header="true" title="搜索" type="keyvalue">
                        <template #btn>
                            <el-button-group>
                                <el-button v-prevent-default icon="el-icon-search" size="small" @click="selectData">
                                    搜索
                                </el-button>
                            </el-button-group>
                        </template>
                        <template #keyvalue>
                            <el-form ref="refForm" :model="form" class="card-list-form" size="small">
                                <el-row :gutter="15">
                                    <card-list-item width="100px">
                                        <template #key>项目名称</template>
                                        <template #value>
                                            <el-select
                                                v-model="form.projectKey"
                                                clearable
                                                filterable
                                                placeholder="选择项目"
                                                @change="projectChange"
                                                :filter-method="filterProjects"
                                            >
                                                <el-option
                                                    v-for="item in filteredProjects"
                                                    :key="item.value"
                                                    :label="item.name"
                                                    :value="item.value"
                                                />
                                            </el-select>
                                        </template>
                                    </card-list-item>
                                    <card-list-item width="100px">
                                        <template #key>测试计划</template>
                                        <template #value>
                                            <el-select
                                                v-model="form.testPlanKey"
                                                clearable
                                                filterable
                                                placeholder="选择测试计划"
                                                @change="planChange"
                                            >
                                                <el-option
                                                    v-for="item in plans"
                                                    :key="item.value"
                                                    :label="item.name"
                                                    :value="item.value"
                                                />
                                            </el-select>
                                        </template>
                                    </card-list-item>
                                    <card-list-item width="100px">
                                        <template #key>测试周期</template>
                                        <template #value>
                                            <el-select
                                                v-model="form.testCycleId"
                                                clearable
                                                filterable
                                                placeholder="选择测试周期"
                                                @change="cycleChange"
                                            >
                                                <el-option
                                                    v-for="item in cycles"
                                                    :key="item.value"
                                                    :label="item.name"
                                                    :value="item.value"
                                                />
                                            </el-select>
                                        </template>
                                    </card-list-item>
                                </el-row>
                            </el-form>
                        </template>
                    </card-list>
                </el-col>
            </el-row>
        </div>

        <div class='flex justify-between items-center mb-2 '>
            <div style="display: flex; align-items: center;">
                <el-button-group>
                    <el-button v-prevent-default type="primary" @click="toggleCapture">
                        {{ isCaptureEnabled ? '关闭截图' : '开启截图' }}
                    </el-button>
                    <el-button v-prevent-default type="primary" @click="reLoadClick">重载用例</el-button>
                </el-button-group>
            </div>
            <el-button v-prevent-default type='text' @click='toggleSearch'>
                搜索
                <el-icon>
                    <el-icon-arrow-up v-if='isShow'/>
                    <el-icon-arrow-down v-else/>
                </el-icon>
            </el-button>
        </div>
        <el-table
            v-loading="loading"
            :data="requirementList"
            row-key="key"
            style="width: 100%"
            @expand-change="handleExpandChange"
        >
            <!-- 行展开 -->
            <el-table-column type="expand">
                <template #default="{ row: requirementRow  }">
                    <!-- 懒加载的测试用例表格 -->
                    <el-table  v-loading="loadingStatus[requirementRow.key]" :data="testCaseListCache[requirementRow.key]" :span-method="mergeCells"  @cell-mouse-enter="handleCellMouseEnter"
                               @cell-mouse-leave="handleCellMouseLeave" @row-click="handleRowClick"
                               :cell-class-name="cellClassName" style="width: 100%">
                        <el-table-column prop="testCaseKey" label="用例编号" min-width="8%">
                            <template #default="{ row }">
                                <a :href="getLink(row.testCaseKey)" target="_blank" rel="noopener noreferrer" class="link">
                                    {{ row.testCaseKey }}
                                </a>
                            </template>
                        </el-table-column>
                        <el-table-column prop="level" label="等级" min-width="4%"></el-table-column>
                        <el-table-column prop="summary" label="标题" min-width="15%">
                            <template #default="{ row }">
                                <div>
                                    <a
                                        @click="openTestCaseModal(row, requirementRow.key)"
                                        rel="noopener noreferrer"
                                        class="link"
                                        style="white-space: normal; word-break: break-word;"
                                    >
                                        {{ row.summary }}
                                    </a>
                                    <!-- 如果存在前置条件，则显示在标题下方 -->
                                    <div v-if="row.precondition" class="precondition">
                                        前置条件：{{ row.precondition }}
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="step" label="步骤" min-width="21%">
                            <template #default="{ row }">
                                <el-input
                                    v-model="row.step"
                                    @focus="handleFocus(row, 'step')"
                                    @input="handleInput(row,'step')"
                                    @blur="saveStep(row,'step', requirementRow.key)"
                                    type="textarea"
                                    autosize
                                    element-loading-custom-class="small-loading"
                                    v-loading="loadingStates[row.testCaseKey]"
                                    :disabled="loadingStates[row.testCaseKey]"
                                    :class="{'no-border': true}"
                                ></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column prop="expectedResult" label="预期结果" min-width="21%">
                            <template #default="{ row }">
                                <el-input
                                    v-model="row.expectedResult"
                                    @focus="handleFocus(row, 'expectedResult')"
                                    @input="handleInput(row,'expectedResult')"
                                    @blur="saveStep(row,'expectedResult', requirementRow.key)"
                                    type="textarea"
                                    autosize
                                    element-loading-custom-class="small-loading"
                                    v-loading="loadingStates[row.testCaseKey]"
                                    :disabled="loadingStates[row.testCaseKey]"
                                    :class="{'no-border': true}"
                                ></el-input>
                            </template>
                        </el-table-column>
                        <!-- 步骤结果 -->
                        <el-table-column prop="status" label="步骤结果" min-width="7%">
                            <template v-slot:default="{ row }">
                                <TestRunStatus :initialText="row.status" :isLoading="stepLoadingMap[row.id] || false" @selectionChange="(command: string) => handleStepSelectionChange(command, row)"></TestRunStatus>
                            </template>
                        </el-table-column>
                        <el-table-column label="步骤操作"  min-width="8%">
                            <template #default="{ row: testCaseRow }">
                                <el-tooltip content="创建缺陷" placement="top" :show-after="500">
                                    <el-button
                                        v-prevent-default
                                        type="text"
                                        @click="createBug(testCaseRow, requirementRow)"
                                        :icon="Plus"
                                    ></el-button>
                                </el-tooltip>
                                <el-tooltip content="关联缺陷" placement="top" :show-after="500">
                                    <el-button
                                        v-prevent-default
                                        type="text"
                                        @click="linkBug(requirementRow.key, testCaseRow)"
                                        :icon="Link"
                                    ></el-button>
                                </el-tooltip>
                                <el-button v-prevent-default v-if="isCaptureEnabled" type="text" @click="captureScreen(testCaseRow, requirementRow.key)" :icon="Camera"></el-button>
                                <!--                                <el-popconfirm cancel-button-text="取消" confirm-button-text="确定" title="从JIRA中彻底删除该用例，确定删除吗？"-->
                                <!--                                               width="220"-->
                                <!--                                               @confirm="() => deleteCase(testCaseRow, requirementRow.key)">-->
                                <!--                                    <template #reference>-->
                                <!--                                            <el-button v-prevent-default type="text" :icon="Delete"></el-button>-->
                                <!--                                    </template>-->
                                <!--                                </el-popconfirm>-->
                            </template>
                        </el-table-column>
                        <el-table-column label="缺陷" min-width="4%">
                            <template #default="{ row: testCaseRow }">
                                <DefectCell
                                    :testCaseKey="testCaseRow.testCaseKey"
                                    :testCaseRow="testCaseRow"
                                    :requirementKey="requirementRow.key"
                                    :getTotalDefects="getTotalDefects"
                                    :openDefectDialog="openDefectDialog"
                                >
                                </DefectCell>
                            </template>
                        </el-table-column>
                        <!-- 附件 -->
                        <el-table-column label="附件" min-width="5%">
                            <template #default="scope">
                                <el-tag type="info" class="elegant-tag" v-if="scope.row.testRunAttachments.length > 0" @click="openAttachmentDialog(scope.row, requirementRow.key)"> + {{ scope.row.testRunAttachments.length }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="parentStatus" label="执行总结果" min-width="7%">
                            <template v-slot:default="{ row }">
                                <TestRunStatus :initialText="row.parentStatus" :isLoading="runLoadingMap[row.id] || false" @selectionChange="(command: string) => handleRunSelectionChange(command, row)"></TestRunStatus>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </el-table-column>

            <!-- 需求编号列 -->
            <el-table-column prop="key" label="需求编号" min-width="8%">
                <template #default="{ row }">
                    <a :href="getLink(row.key)" target="_blank" rel="noopener noreferrer" class="link">
                        {{ row.key }}
                    </a>
                </template>
            </el-table-column>

            <!-- 摘要列 -->
            <el-table-column prop="summary" label="需求名称" min-width="48%"></el-table-column>

            <!-- 模块列 -->
            <el-table-column prop="modules" label="模块" min-width="10%">
                <template #default="{ row }">
                    <div class="tags-container">
                        <el-tag
                            v-for="module in row.modules"
                            :key="module"
                            type="info"
                            effect="plain"
                            class="tag-item"
                        >
                            {{ module }}
                        </el-tag>
                    </div>
                </template>
            </el-table-column>

            <!-- 修复版本 -->
            <el-table-column prop="fixVersions" label="修复版本" min-width="8%">
                <template #default="{ row }">
                    <div class="tags-container">
                        <el-tag
                            v-for="version in row.fixVersions"
                            :key="version"
                            type="info"
                            effect="plain"
                            class="tag-item"
                        >
                            {{ version }}
                        </el-tag>
                    </div>
                </template>
            </el-table-column>

            <!-- 状态列 -->
            <el-table-column prop="status" label="状态" min-width="8%"></el-table-column>

            <!-- 测试负责人列 -->
            <el-table-column prop="tester" label="测试负责人" min-width="6%"></el-table-column>

            <!-- 开发负责人列 -->
            <el-table-column prop="assignee" label="经办人" min-width="6%"></el-table-column>

            <el-table-column label="操作" min-width="6%">
                <template #default="{ row }">
                    <el-tooltip content="创建用例" placement="top" :show-after="500">
                        <el-button
                            v-prevent-default
                            type="text"
                            @click="createTestCaseClick(row.key)"
                            :icon="Plus"
                        ></el-button>
                    </el-tooltip>
                </template>
            </el-table-column>
        </el-table>
        <!-- 附件列表 -->
        <el-drawer v-model="attachmentDialogVisible" :title="`${attachmentDialog.testCaseKey} 附件列表`" :destroy-on-close="true" @close="resetAttachmentDialog">
            <el-table :data="attachments" v-loading="attachmentDialogLoading">
                <el-table-column prop="fileName" label="文件名">
                    <template #default="{ row }">
                        <template v-if="isImage(row.fileName)">
                            <el-button
                                v-prevent-default
                                type="text"
                                @click="handleImagePreview(row)">
                                {{ row.fileName }}
                            </el-button>
                            <el-image
                                v-if="imageBlobUrls[row.filePath]"
                                :src="imageBlobUrls[row.filePath]"
                                :preview-src-list="[imageBlobUrls[row.filePath]]"
                                preview-teleported
                                close-on-press-escape
                                hide-on-click-modal
                            >
                            </el-image>
                        </template>
                        <template v-else>
                            <a :href="row.filePath" target="_blank" class="link">{{ row.fileName }}</a>
                        </template>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                    <template #default="{ row }">
                        <el-button v-prevent-default type="text" size="small" @click="downloadJiraFile(row.filePath, row.fileName)">下载</el-button>
                        <el-popconfirm cancel-button-text="取消" confirm-button-text="确定" title="从JIRA中删除该附件，确定删除吗？"
                                       width="220"
                                       @confirm="() => deleteAttachment(row)">
                            <template #reference>
                                <el-button v-prevent-default type="text" size="small">删除</el-button>
                            </template>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
        </el-drawer>
        <BugList
            v-if="defectDialogVisible"
            :visible = "defectDialogVisible"
            :run-id="bugListRunId"
            :requirement-key="bngListRequirementKey"
            :test-case-key="bugListTestCaseKey"
            :jiraToken="jiraToken"
            @close="bugListClose"
        ></BugList>
        <el-dialog
            v-model="isTestCaseModalVisible"
            :title="`${testCaseModalData[0]?.testCaseKey} 测试用例详情`"
            width="60%"
            @closed="handleModalClosed"
        >
            <p>{{testCaseModalData[0]?.summary}}</p>
            <el-table :data="displayedTestCaseModalData" style="width: 100%">
                <el-table-column prop="step" label="步骤" min-width="48%">
                    <template #default="{ row }">
                        <el-input
                            v-model="row.step"
                            type="textarea"
                            autosize
                        ></el-input>
                    </template>
                </el-table-column>
                <el-table-column prop="expectedResult" label="预期结果" min-width="48%">
                    <template #default="{ row }">
                        <el-input
                            v-model="row.expectedResult"
                            type="textarea"
                            autosize
                        ></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="操作" min-width="6%">
                    <template #default="{ row }">
                        <el-button type="text" @click="deleteRow(row)" :icon="Delete"></el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-footer class="testCaseModal_footer">
                <el-button v-prevent-default type="text" size="small" @click="addRow" :icon="Plus">添加一行</el-button>
                <el-button v-prevent-default type="primary" @click="saveChanges" :loading="testCaseModalSaveLoading">保存</el-button>
            </el-footer>
        </el-dialog>
        <CreateTestCase
            v-if="isCreateTestCaseModalVisible"
            :requirementKey="requirementKey"
            :version="testPlanFixVersionList"
            :jiraToken="jiraToken"
            :projectKey="form.projectKey"
            :testPlanKey="form.testPlanKey"
            :cycleId="form.testCycleId"
            :cycleName="form.testCycleName"
            :displayName="JiraUserInfo?.displayName || ''"
            :jiraName="JiraUserInfo?.name || ''"
            @save="createTestCaseSave"
            @close="isCreateTestCaseModalVisible = false"
        ></CreateTestCase>
        <LinkBugsToTestRunStep
            v-if="isLinkBugsToTestRunStepModalVisible"
            :visible="isLinkBugsToTestRunStepModalVisible"
            :test-case-key="testCaseKey"
            :requirement-key="requirementKey"
            :step-id="stepId"
            :jira-token="jiraToken"
            :run-id="runId"
            @save="linkBugsToTestRunStepSave"
            @close="isLinkBugsToTestRunStepModalVisible = false"
        >
        </LinkBugsToTestRunStep>
        <CreateBug
            v-if="isCreateBugModalVisible"
            :visible="isCreateBugModalVisible"
            :requirement-key="createBugRequirementKey"
            :projectKey="form.projectKey"
            :test-case-key="createBugTestCaseKey"
            :run-id="createBugRunId"
            :step-id="createBugStepId"
            :fix-versions="createBugFixVersions"
            :components="createBugComponents"
            :jira-users="createBugJiraUsers"
            :jira-token="jiraToken"
            @save="createBugSave"
            @close="isCreateBugModalVisible = false"
        >
        </CreateBug>
        <jiraLoginDialog
            v-if="jiraLoginDialogModalVisible"
            :visible="jiraLoginDialogModalVisible"
            @close="jiraLoginDialogModalVisible = false"
        >
        </jiraLoginDialog>
        <ReLoadTestCase
            v-if="reLoadCaseVisible"
            :visible="reLoadCaseVisible"
            :jira-token="jiraToken"
            @close="reLoadCaseVisible = false"
        >
        </ReLoadTestCase>
    </div>
</template>

<script lang='ts' setup>
import CardList from '@/components/CardList/CardList.vue'
import CardListItem from '@/components/CardList/CardListItem.vue'
import { computed, onBeforeUnmount, onMounted, reactive, ref, toRefs } from 'vue'
import { Camera, Delete, Link, Plus } from '@element-plus/icons-vue'
import {
    deleteRunAttachment,
    deleteTestCase,
    type deleteTestCaseRequest,
    downloadAttachment,
    getAllCycleForPlan,
    getAllPlanForProject,
    getAllProject,
    getAllUsers,
    getJiraUserInfo,
    getTestCaseList,
    getTestPlanFixVersion,
    getTestRequirementList,
    getTestRunAttachments,
    type JiraTestCaseRunStep,
    loginCheck,
    type UpdateTestRunResultRequest,
    updateTestRunStatus,
    type UpdateTestStepResultRequest,
    updateTestSteps,
    updateTestStepStatus, uploadTestRunAttachment
} from '@/api/layout'
import { slide } from '@/utils/animate'
import { ElMessage, ElMessageBox } from 'element-plus'
import TestRunStatus from '@/components/TestRun/TestRunStatus.vue'
import { useJiraRunStore } from '@/stores/jiraRunStore'
import CreateTestCase from '@/components/TestRun/CreateTestCase.vue'
import BugList from '@/components/TestRun/BugList.vue'
import LinkBugsToTestRunStep from '@/components/TestRun/LinkBugsToTestRunStep.vue'
import DefectCell from '@/components/TestRun/DefectCell.vue'
import CreateBug from '@/components/TestRun/CreateBug.vue'
import ReLoadTestCase from '@/components/TestRun/ReLoadTestCase.vue'
import JiraLoginDialog from '@/components/TestRun/JiraLoginDialog.vue'

const searchEl = ref(null)
const isShow = ref(false)
// 默认不打开登录提示框
const jiraLoginDialogModalVisible = ref(false)
// jira token
const jiraToken = ref('')

// 点击搜索按钮
const toggleSearch = () => {
    isShow.value = !isShow.value
    slide(searchEl, isShow.value)
}

const jiraLoginClick = async (): Promise<boolean> => {
    if (jiraToken.value) {
        return true
    }
    const res = await loginCheck()
    if (!res.data.isSuccess) {
        // 如果登录失败，就打开登录框提示
        jiraLoginDialogModalVisible.value = true
        return false
    }
    jiraToken.value = res.headers['jiratoken']
    // 获取jira中所有的用户信息
    getJiraUsers()
    return true
}
// 新增一个标志变量，用来标志是否正在初始化
let isInitializing = false

// 使用 Pinia 存储
const jiraRunStore = useJiraRunStore()

// 将存储中的状态解构出来
const { projectKey, projectName, testPlanKey, testPlanName, testCycleId, testCycleName } = toRefs(jiraRunStore)
const form = reactive({
    projectKey,
    projectName,
    testPlanKey,
    testPlanName,
    testCycleId,
    testCycleName
})

// 定义查询，用于过滤项目列表
const query = ref('')

// onMounted 中按顺序回显
onMounted(async () => {
    isInitializing = true
    const res = await jiraLoginClick()
    if (!res){
        return
    }
    await getProjects()

    // 回显 projectKey 和 projectName
    if (projectKey.value) {
        // 确保项目数据已加载
        const project = projects.value.find(p => p.value === projectKey.value)
        if (project) {
            form.projectKey = project.value  // 确保绑定正确
            form.projectName = project.name
            await projectChange() // 加载测试计划数据
        }
    }

    // 回显 testPlanKey 和 testPlanName
    if (testPlanKey.value) {
        // 等待 plans 数据加载完成
        if (!plans.value.length) {
            await projectChange()
        }
        const plan = plans.value.find(p => p.value === testPlanKey.value)
        if (plan) {
            form.testPlanKey = plan.value
            form.testPlanName = plan.name
            await planChange() // 加载测试周期数据
        }
    }

    // 回显 testCycleId 和 testCycleName
    if (testCycleId.value) {
        // 等待 cycles 数据加载完成
        if (!cycles.value.length) {
            await planChange()
        }
        const cycle = cycles.value.find(c => c.value === testCycleId.value)
        if (cycle) {
            form.testCycleId = cycle.value
            form.testCycleName = cycle.name
        }
    }
    isInitializing = false
})

interface select {
    name: string
    value: string
}

// 项目列表
const projects = ref<select[]>([])

const getProjects = async () => {
    const res = await getAllProject(jiraToken.value)
    if (res.data.isSuccess) {
        projects.value = res.data.data.map((project: any) => ({
            name: project.name,
            value: project.key
        }))
    }
}

// 过滤后的项目列表
const filteredProjects = computed(() => {
    if (!query.value) {
        return projects.value
    } else {
        const lowerCaseQuery = query.value.toLowerCase()
        return projects.value.filter((project) => {
            return (
                project.name.toLowerCase().includes(lowerCaseQuery) ||
                project.value.toLowerCase().includes(lowerCaseQuery)
            )
        })
    }
})

// 自定义的过滤方法，更新查询条件
const filterProjects = (val: string) => {
    query.value = val
}

// 计划列表
const plans = ref<select[]>([])

// 选择项目后，获取测试计划
const projectChange = async () => {
    // 如果不是初始化，才清空测试计划和周期的数据
    if (!isInitializing) {
        // 清空测试计划
        form.testPlanKey = ''
        form.testPlanName = ''
        // 清空测试计划列表
        plans.value = []
        form.testCycleId = ''
        form.testCycleName = ''
        // 清空测试周期列表
        cycles.value = []
    }
    // 检查是否选择了项目
    if (!form.projectKey) {
        form.projectName = ''
        return
    }

    // 更新项目名称
    const project = projects.value.find(p => p.value === form.projectKey)
    if (project) {
        form.projectName = project.name
    }

    // 获取测试计划列表
    const params = { projectKey: form.projectKey }
    const res = await getAllPlanForProject(params, jiraToken.value)
    if (res.data.isSuccess) {
        plans.value = Object.entries(res.data.data).map(([name, value]) => ({
            name,
            value: String(value)
        }))

        // 回显测试计划名称
        if (form.testPlanKey) {
            const plan = plans.value.find(p => p.value === form.testPlanKey)
            if (plan) {
                form.testPlanName = plan.name
            }
        }
    }
}

// 周期列表
const cycles = ref<select[]>([])
// 选择测试计划后，获取测试周期
const planChange = async () => {
    if (!isInitializing) {
        // 清空测试周期
        form.testCycleId = ''
        form.testCycleName = ''
        // 清空测试周期列表
        cycles.value = []
    }
    // 检查是否选择了测试计划
    if (!form.testPlanKey) {
        form.testPlanName = ''
        return
    }

    // 更新测试计划名称
    const plan = plans.value.find(p => p.value === form.testPlanKey)
    if (plan) {
        form.testPlanName = plan.name
    }

    // 获取测试周期列表
    const res = await getAllCycleForPlan(form.testPlanKey, jiraToken.value)
    if (res.data.isSuccess) {
        cycles.value = Object.entries(res.data.data).map(([name, value]) => ({
            name,
            value: String(value)
        }))

        // 回显测试周期名称
        if (form.testCycleId) {
            const cycle = cycles.value.find(c => c.value === form.testCycleId)
            if (cycle) {
                form.testCycleName = cycle.name
            }
        }
    }
}

const cycleChange = () => {
    // 检查是否选择了测试周期
    if (!form.testCycleId) {
        // 如果 testCycleId 被清空，清空 testCycleName
        form.testCycleName = ''
        return
    }

    // 如果选择了测试周期，根据 testCycleId 更新 testCycleName
    const cycle = cycles.value.find(c => c.value === form.testCycleId)
    if (cycle) {
        form.testCycleName = cycle.name
    }
}
interface Requirement {
    key: string
    summary: string
    status: string
    tester: string
    assignee: string
    fixVersions: string[]
    modules: string[]
    // 执行进度
    progress: string
    // 未关闭BUG数量
    bugCount: number
}
const requirementList = ref<Requirement[]>([])

const loading = ref(false)
const selectData = async () => {
    const res = await jiraLoginClick()
    if (!res) return
    if (!form.testPlanKey) {
        ElMessage.warning('请选择测试计划')
        return
    }
    if (!form.testCycleId) {
        ElMessage.warning('请选择测试周期')
        return
    }
    loading.value = true
    try {
        const res = await getTestRequirementList(form.testPlanKey, form.testCycleId, jiraToken.value)
        if (res.data.isSuccess) {
            requirementList.value = res.data.data
        } else {
            ElMessage.error('获取测试需求列表失败')
        }
        getFixVersionList(form.testPlanKey)
        getJiraUser()
    } finally {
        loading.value = false
    }
}

const loadingStatus = ref<Record<string, boolean>>({})
// 缓存测试用例数据的对象 key:需求编号 value:测试用例列表
const testCaseListCache = ref<{ [key: string]: JiraTestCaseRunStep[] }>({})
// 行展开时懒加载数据
const handleExpandChange = async (row: Requirement, expanded: boolean, isCacheEmpty = !testCaseListCache.value[row.key]) => {
    if (expanded && isCacheEmpty) {
        try {
            loadingStatus.value[row.key] = true // 开始加载，显示 v-loading
            const res = await getTestCaseList(form.testPlanKey, form.testCycleId, row.key, jiraToken.value)
            if (res.data.isSuccess) {
                const testCaseList = res.data.data as JiraTestCaseRunStep[]

                // 对数据进行分组
                const groupedData: { [key: string]: JiraTestCaseRunStep[] } = {}
                testCaseList.forEach((item) => {
                    if (!groupedData[item.testCaseKey]) {
                        groupedData[item.testCaseKey] = []
                    }
                    groupedData[item.testCaseKey].push(item)
                })

                // 设置 rowspan 和 mergeGroupId
                const processedData: JiraTestCaseRunStep[] = []
                for (const testCaseKey in groupedData) {
                    const group = groupedData[testCaseKey]
                    const groupId = `group-${testCaseKey}` // 使用 testCaseKey 作为 mergeGroupId
                    group.forEach((item, index) => {
                        item.mergeGroupId = groupId
                        if (index === 0) {
                            // 第一行
                            item.rowspan = group.length
                        } else {
                            // 其余行
                            item.rowspan = 0
                        }
                        processedData.push(item)
                    })
                }

                // 将处理后的数据赋值
                testCaseListCache.value[row.key] = processedData
            } else {
                ElMessage.error('获取测试用例列表失败')
            }
        } finally {
            loadingStatus.value[row.key] = false // 加载结束，隐藏 v-loading
        }
    } else {
        // 如果不展开，清空缓存
        delete testCaseListCache.value[row.key]
    }
}
interface RowType {
    rowspan?: number
    mergeGroupId?: string
    [key: string]: any
}

interface ColumnType {
    property?: string
    label?: string
    [key: string]: any
}

const mergeCells = ({
    row,
    column,
    rowIndex,
    columnIndex
}: {
    row: RowType
    column: ColumnType
    rowIndex: number
    columnIndex: number
}): [number, number] => {
    const columnsToMergeByProp = ['testCaseKey', 'level', 'summary', 'precondition', 'parentStatus']
    const columnsToMergeByLabel = ['缺陷', '附件']

    if (column.property && columnsToMergeByProp.includes(column.property)) {
        const { rowspan } = row
        if (rowspan && rowspan > 0) {
            return [rowspan, 1]
        } else {
            return [0, 0]
        }
    }

    if (column.label && columnsToMergeByLabel.includes(column.label)) {
        const { rowspan } = row
        if (rowspan && rowspan > 0) {
            return [rowspan, 1]
        } else {
            return [0, 0]
        }
    }

    return [1, 1]
}


const cellClassName = ({
    row,
    column,
    rowIndex,
    columnIndex
}: {
    row: RowType
    column: ColumnType
    rowIndex: number
    columnIndex: number
}): string => {
    const classes: string[] = []
    if (row.mergeGroupId) {
        classes.push(`merge-group-${row.mergeGroupId}`)
        const [rowspan, colspan] = mergeCells({ row, column, rowIndex, columnIndex })
        if (rowspan > 0 && colspan > 0) {
            classes.push(`merge-group-main-${row.mergeGroupId}`)
        }
        if (selectedMergeGroups.value.has(row.mergeGroupId)) {
            classes.push('merged-cell-selected')
        }
    }
    return classes.join(' ')
}

const handleCellMouseEnter = (
    row: RowType,
    column: ColumnType,
    cell: HTMLElement,
    event: MouseEvent
): void => {
    const { mergeGroupId } = row
    if (mergeGroupId) {
        const tableBody = cell.closest('.el-table__body')
        if (tableBody) {
            const cells = tableBody.querySelectorAll(`.merge-group-${mergeGroupId}`)
            cells.forEach((cellElement) => {
                cellElement.classList.add('merged-cell-hover')
            })
        }
    }
}

const handleCellMouseLeave = (
    row: RowType,
    column: ColumnType,
    cell: HTMLElement,
    event: MouseEvent
): void => {
    const { mergeGroupId } = row
    if (mergeGroupId) {
        const tableBody = cell.closest('.el-table__body')
        if (tableBody) {
            const cells = tableBody.querySelectorAll(`.merge-group-${mergeGroupId}`)
            cells.forEach((cellElement) => {
                cellElement.classList.remove('merged-cell-hover')
            })
        }
    }
}
const selectedMergeGroups = ref<Set<string>>(new Set())
const handleRowClick = (
    row: RowType,
    column: ColumnType,
    event: Event
): void => {
    const { mergeGroupId } = row
    if (mergeGroupId) {
        if (selectedMergeGroups.value.has(mergeGroupId)) {
            selectedMergeGroups.value.delete(mergeGroupId)
        } else {
            selectedMergeGroups.value.add(mergeGroupId)
        }
    }
}
// 生成链接的方法
const getLink = (key: string) => {
    return `http://jira.taimei.com/browse/${key}`
}

interface testRun {
    // 测试计划
    testPlanKey: string
    testCaseKey: string
    // 测试周期
    testCycleId: string
    testCaseId: string
    // 运行的状态
    status: string
    id: string
    // 执行结果的背景色
    selectedColor?: string
    testRunDetails:{
        testRunSteps:[{
            // 步骤关联的BUG
            testRunStepBugsWrapper:[]
            // 预期结果
            expectedResult: string
            // 步骤
            step: string
            // 步骤ID
            id: string
            // 步骤状态
            status: string
            // 附件
            testRunStepAttachments:[]
            // 步骤结果的背景色
            selectedColor?: string
        }]
    }
}
const stepLoadingMap = ref<Record<string | number, boolean>>({})
const handleStepSelectionChange = async (command: string, row: JiraTestCaseRunStep) => {
    console.log('command', command)
    const key = row.id
    stepLoadingMap.value[key] = true
    try {
        const requestData: UpdateTestStepResultRequest = {
            runId: row.parentId,
            runStepId: row.id,
            result: command
        }
        const res = await updateTestStepStatus(jiraToken.value, requestData) // 模拟请求
        if (res.data.isSuccess) {
            const testRun = res.data.data as testRun
            // 需要同步更新父级的状态
            // 遍历所有 testCaseListCache，更新相关行的 parentStatus
            Object.keys(testCaseListCache.value).forEach(requirementKey => {
                testCaseListCache.value[requirementKey].forEach(testCaseRow => {
                    if (testCaseRow.parentId === row.parentId) {
                        testCaseRow.parentStatus = testRun.status
                    }
                })
            })
            // 查找匹配的步骤
            const matchingStep = testRun.testRunDetails.testRunSteps.find(step => step.id === row.id)
            if (matchingStep) {
                // 更新步骤的状态
                row.status = matchingStep.status
            } else {
                // 如果未找到匹配的步骤，可以在此处理错误或日志
                console.error(`未找到ID为 ${row.id} 的步骤`)
            }
        } else {
            ElMessage.error(res.data.message)
        }
    } catch (error) {
        console.error('请求失败', error)
    } finally {
        stepLoadingMap.value[key] = false
    }
}

const runLoadingMap = ref<Record<string | number, boolean>>({})
const handleRunSelectionChange = async (command: string, row: JiraTestCaseRunStep) => {
    console.log('command', command)
    const key = row.id
    runLoadingMap.value[key] = true
    try {
        const requestData: UpdateTestRunResultRequest = {
            runId: row.parentId,
            result: command
        }
        const res = await updateTestRunStatus(jiraToken.value, requestData)
        if (res.data.isSuccess) {
            const testRun = res.data.data as testRun
            row.parentStatus = testRun.status
        } else {
            ElMessage.error(res.data.message)
        }
    } catch (error) {
        console.error('请求失败', error)
    } finally {
        runLoadingMap.value[key] = false
    }
}

// 用于跟踪哪些字段被修改过
const dirtyFields = reactive<Record<string, boolean>>({})
// 处理输入事件，设置修改标志
const handleInput = (row: JiraTestCaseRunStep, field: keyof JiraTestCaseRunStep) => {
    dirtyFields[`${row.id}_${field}`] = true
}
// 保存原始值
const originalValues = reactive<Record<string, { step: string, expectedResult: string }>>({})
const handleFocus = (row: JiraTestCaseRunStep, field: keyof JiraTestCaseRunStep) => {
    const key = row.id
    // 如果尚未保存原始数据，则保存
    if (!originalValues[key]) {
        originalValues[key] = {
            step: row.step,
            expectedResult: row.expectedResult
        }
    }
}
// 用于标志加载状态
const loadingStates = reactive<Record<string, boolean>>({})

const saveStep = async (
    row: JiraTestCaseRunStep,
    field: 'step' | 'expectedResult',
    requirementKey: string
) => {
    const key = `${row.id}_${field}`
    if (dirtyFields[key]) {
        // 初始化 loadingStates[row.id]，如果不存在
        loadingStates[row.testCaseKey] = true

        try {
            if (row.testRunAttachments.length > 0) {
                // 检查是否有附件，并询问用户是否确认保存
                await ElMessageBox.confirm(
                    '该用例包含附件，保存后会丢失附件信息，确认保存吗？',
                    '注意',
                    {
                        confirmButtonText: '确认',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                )

                await performSave(row, requirementKey)

                // 保存成功后，重置脏标志和原始数据
                delete dirtyFields[key]
                delete originalValues[row.id]
            } else {
                await performSave(row, requirementKey)

                // 保存成功后，重置脏标志和原始数据
                delete dirtyFields[key]
                delete originalValues[row.id]
            }
        } catch {
            // 用户取消或保存失败，恢复原始数据
            const originalData = originalValues[row.id]
            if (originalData) {
                row.step = originalData.step
                row.expectedResult = originalData.expectedResult
            }

            // 取消脏标志的设置，以防止重复提示
            delete dirtyFields[key]
        } finally {
            loadingStates[row.testCaseKey] = false // 结束加载
        }
    } else {
        console.log('内容未更改，不需要保存')
    }
}


const performSave = async (row: JiraTestCaseRunStep, requirementKey: string) => {
    // 获取当前testCaseKey对应的所有步骤
    const { testCaseKey } = row
    const originalData = originalValues[row.id]
    const steps = testCaseListCache.value[requirementKey].filter(
        (item) => item.testCaseKey === testCaseKey
    )

    try {
        // 调用保存接口，发送所有步骤的数据
        const res = await updateTestSteps(jiraToken.value, steps)

        if (!res.data.isSuccess) {
            ElMessage.error(res.data.message)

            if (originalData) {
                // 恢复原始数据
                row.step = originalData.step
                row.expectedResult = originalData.expectedResult
            }
            return
        }
        ElMessage.success('步骤更新成功')

        // 获取返回的步骤数据
        const updatedSteps = res.data.data as JiraTestCaseRunStep[]

        // 调用更新函数，直接使用requirementKey
        updateTestCaseListCache(updatedSteps, requirementKey, testCaseKey)

    } catch (error) {
        if (originalData) {
            // 恢复原始数据
            row.step = originalData.step
            row.expectedResult = originalData.expectedResult
        }
        console.error('保存步骤失败:', error)
    }
}
const updateTestCaseListCache = (
    updatedSteps: JiraTestCaseRunStep[],
    requirementKey: string,
    testCaseKey: string
) => {
    const testCaseList = testCaseListCache.value[requirementKey]

    if (testCaseList) {
        // 找到需要更新的索引范围
        const startIndex = testCaseList.findIndex(
            (item) => item.testCaseKey === testCaseKey
        )

        if (startIndex !== -1) {
            // 计算需要替换的条目数量
            const oldStepsCount = testCaseList.filter(
                (item) => item.testCaseKey === testCaseKey
            ).length

            // 处理rowspan和mergeGroupId
            const groupId = `group-${testCaseKey}`
            updatedSteps.forEach((item, index) => {
                item.mergeGroupId = groupId
                if (index === 0) {
                    item.rowspan = updatedSteps.length
                } else {
                    item.rowspan = 0
                }
            })

            // 替换旧的步骤数据
            testCaseList.splice(startIndex, oldStepsCount, ...updatedSteps)
        }
    }
}
// 删除测试用例
const deleteCase = async (testCaseRow: JiraTestCaseRunStep, requirementKey: string) => {
    const { testCaseKey } = testCaseRow
    console.log('删除测试用例', testCaseKey)
    const requestData: deleteTestCaseRequest = {
        testPlanKey: testCaseRow.testPlanKey,
        testCycleId: testCaseRow.testCycleId,
        testCycleName: form.testCycleName,
        removeTestCaseKeys: [testCaseKey]
    }
    const res = await deleteTestCase(jiraToken.value, requestData)
    if (res.data.isSuccess) {
        ElMessage.success('删除成功')
        // 找到对应的 rowKey 并从 testCaseListCache 中移除匹配的 testCaseKey
        const testCaseList = testCaseListCache.value[requirementKey]
        if (testCaseList) {
            testCaseListCache.value[requirementKey] = testCaseList.filter(item => item.testCaseKey !== testCaseKey)

        }
    } else {
        ElMessage.error('删除失败')
    }
}

// 附件弹窗
const attachmentDialogVisible = ref(false)
interface Attachment {
    id: string
    mimeType: string
    fileName: string
    filePath: string
}
// 存储获取到的附件
const attachments = ref<Attachment[]>([])
// 附件抽屉中的列表加载状态
const attachmentDialogLoading = ref(false)

// 引用 JiraTestCaseRunStep 中的 testRunAttachments
const attachmentDialog = ref<Pick<JiraTestCaseRunStep, 'parentId' | 'testCaseKey'>>({
    parentId: '', // 运行ID
    testCaseKey: ''
})

const attachmentRequirementRowKey = ref('')
const attachmentTestCaseKey = ref('')
// 打开附件弹窗
const openAttachmentDialog = async (row: JiraTestCaseRunStep, requirementRowKey: string) => {
    attachmentDialog.value = {
        parentId: row.parentId,
        testCaseKey: row.testCaseKey
    }
    attachmentRequirementRowKey.value = requirementRowKey
    attachmentTestCaseKey.value = row.testCaseKey
    attachmentDialogVisible.value = true
    try {
        attachmentDialogLoading.value = true
        const res = await getTestRunAttachments(jiraToken.value, row.parentId)
        if (!res.data.isSuccess) {
            ElMessage.error('获取附件列表失败')
        }
        attachments.value = res.data.data

    } catch (error) {
        console.error('获取附件列表失败', error)
        ElMessage.error('获取附件列表失败')
    } finally {
        attachmentDialogLoading.value = false
    }
}

// 重置抽屉数据
const resetAttachmentDialog = () => {
    attachmentDialog.value = {
        parentId: '',
        testCaseKey: ''
    }
    // 释放所有 Blob URL
    Object.values(imageBlobUrls.value).forEach((url) => {
        window.URL.revokeObjectURL(url)
    })
    attachments.value = [] // 清空附件列表
    imageBlobUrls.value = {}
}

// 下载文件
const downloadJiraFile = async (filePath: string, defaultFileName: string) => {
    try {
        const res: any = await downloadAttachment(filePath, jiraToken.value)

        // 获取Content-Disposition
        const contentDisposition = res.headers['content-disposition']
        let fileName = defaultFileName

        // 优先处理 UTF-8 编码的 filename*= 格式
        const utf8FilenameMatch = contentDisposition.match(/filename\*=UTF-8''(.+)/)
        if (utf8FilenameMatch) {
            fileName = decodeURIComponent(utf8FilenameMatch[1])
        } else {
            // 使用解构赋值提取 filename
            const [, extractedFileName] = contentDisposition.match(/filename="(.+?)"/) || []
            if (extractedFileName) {
                fileName = extractedFileName
            }
        }

        // 创建Blob对象并触发文件下载
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
    } catch (err) {
        console.error('文件下载失败', err)
    }
}
// 存储每个图片的 Blob URL
const imageBlobUrls = ref<Record<string, string>>({})
// 判断文件是否是图片
const isImage = (fileName: string) => {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
    const extension = fileName.split('.').pop()?.toLowerCase()
    return extension && imageExtensions.includes(extension)
}

// 获取图片预览 URL（只用于预览，不影响下载）
const getImagePreviewUrl = async (row: any) => {
    // 检查图片 URL 是否已经缓存
    if (!imageBlobUrls.value[row.filePath]) {
        await fetchImageForPreview(row.filePath) // 如果未缓存，才去下载
    }
    return imageBlobUrls.value[row.filePath] // 返回缓存的 URL
}

// 下载图片用于预览
const fetchImageForPreview = async (filePath: string) => {
    try {
        console.log(`Fetching image for preview: ${filePath}`)
        const res: any = await downloadAttachment(filePath, jiraToken.value)
        const blob = new Blob([res.data])
        imageBlobUrls.value[filePath] = window.URL.createObjectURL(blob)
    } catch (err) {
        console.error('图片获取失败', err)
    }
}
const handleImagePreview = async (row: any) => {
    if (!imageBlobUrls.value[row.filePath]) {
        await getImagePreviewUrl(row)
    }
}
// 删除附件的方法
const deleteAttachment = async (row: any) => {
    // 获取与当前需求编号对应的测试用例列表
    const originalData = testCaseListCache.value[attachmentRequirementRowKey.value].filter(item => item.testCaseKey === attachmentTestCaseKey.value)
    if (originalData.length === 0) {
        console.error('未找到对应的测试用例数据')
        return
    }
    // 获取第一条数据的 testRunAttachments
    const [firstTestCase] = originalData
    const testCaseListCacheAttachment = firstTestCase.testRunAttachments
    try {
        // 调用删除接口，传递 jiraToken、parentId 和 attachmentId
        const res: any = await deleteRunAttachment(jiraToken.value, attachmentDialog.value.parentId, row.id)

        if (res.data.isSuccess) {
            ElMessage.success('删除成功')
            // 从 attachments 中移除附件
            attachments.value = attachments.value.filter(att => att.id !== row.id)
            testCaseListCacheAttachment.splice(testCaseListCacheAttachment.findIndex(att => att.fileName === row.fileName), 1)
        } else {
            ElMessage.error(res.data.message)
        }
    } catch (error) {
        console.error('删除附件失败', error)
        ElMessage.error('删除附件失败，请稍后再试')
    }
}

// 定义状态
const defectDialogVisible = ref(false)

// 缺陷结束的状态列表
const allowedStatuses = ['非需求', '非缺陷', 'CLOSED', '作为后续版本需求']

// 计算缺陷总数和显示状态
const getTotalDefects = (testCaseKey: string, requirementKey: string): { count: number, isRed: boolean } => {
    const steps = testCaseListCache.value[requirementKey]?.filter(
        step => step.testCaseKey === testCaseKey
    ) || []

    const allDefectIds = new Set<string>()
    let isRed = false
    const testRunBugsWrapper = steps[0].testRunBugsWrapper || []
    for (const bug of [...testRunBugsWrapper]) {
        if (!allDefectIds.has(bug.key)) {
            allDefectIds.add(bug.key)
            if (!allowedStatuses.includes(bug.status)) {
                isRed = true
            }
        }
    }

    return {
        count: allDefectIds.size,
        isRed
    }
}


const bugListRunId = ref('')
const bngListRequirementKey = ref('')
const bugListTestCaseKey = ref('')
// 打开缺陷列表弹窗
const openDefectDialog = (row: JiraTestCaseRunStep, requirementKey: string) => {
    // 获取当前测试用例的所有步骤
    bugListRunId.value = row.parentId
    bngListRequirementKey.value = requirementKey
    bugListTestCaseKey.value = row.testCaseKey
    defectDialogVisible.value = true
}

const isTestCaseModalVisible = ref(false)
const testCaseModalData = ref<JiraTestCaseRunStep[]>([])

// 定义一个变量用于存储当前testCaseModal的需求编号
const testCaseModalRequirementKey = ref('')
const handleModalClosed = () => {
    testCaseModalData.value = []
}

// 打开测试用例弹窗
const openTestCaseModal = (row: JiraTestCaseRunStep, requirementKey: string) => {
    // 根据 testCaseKey 获取对应的数据
    const originalData = testCaseListCache.value[requirementKey].filter(item => item.testCaseKey === row.testCaseKey)
    testCaseModalData.value = JSON.parse(JSON.stringify(originalData))

    isTestCaseModalVisible.value = true
    testCaseModalRequirementKey.value = requirementKey
}
const deleteRow = (row: JiraTestCaseRunStep) => {
    if (row.stepProperty === 'add') {
        testCaseModalData.value = testCaseModalData.value.filter(item => item !== row)
        return
    }
    row.stepProperty = 'delete'
}
const displayedTestCaseModalData = computed(() => {
    return testCaseModalData.value.filter(row => row.stepProperty !== 'delete')
})
const testCaseModalSaveLoading = ref(false)
const addRow = () => {
    const newRow: JiraTestCaseRunStep = {
        testCaseKey: testCaseModalData.value[0].testCaseKey,
        level: testCaseModalData.value[0].level,
        summary: testCaseModalData.value[0].summary,
        precondition: testCaseModalData.value[0].precondition,
        testPlanKey: testCaseModalData.value[0].testPlanKey,
        testCycleId: testCaseModalData.value[0].testCycleId,
        testCaseId: testCaseModalData.value[0].testCaseId,
        parentId: testCaseModalData.value[0].parentId,
        parentStatus: testCaseModalData.value[0].parentStatus,
        testRunBugsWrapper: [],
        testRunStepBugsWrapper: [],
        expectedResult: '',
        stepData: '',
        testRunAttachments: testCaseModalData.value[0].testRunAttachments,
        actualResult: '',
        step: '',
        id: 'id_' + Date.now() + '_' + Math.random().toString(36).slice(2, 11),
        status: '',
        rowspan: 0,
        stepProperty: 'add',
        mergeGroupId: testCaseModalData.value[0].mergeGroupId
    }
    testCaseModalData.value.push(newRow)
}

const saveChanges = async () => {
    // 校验步骤和预期结果不能为空
    for (const row of testCaseModalData.value) {
        if (!row.step || !row.expectedResult) {
            ElMessage.warning('步骤和预期结果不能为空')
            return
        }
    }
    testCaseModalSaveLoading.value = true
    try {
        // 调用保存接口，发送所有步骤的数据
        const res = await updateTestSteps(jiraToken.value, testCaseModalData.value)
        if (res.data.isSuccess) {
            ElMessage.success('保存成功')

            // 遍历删除originalValues中的数据
            testCaseModalData.value.forEach((item) => {
                delete originalValues[item.id]
            })
            // 获取返回的步骤数据
            const updatedSteps = res.data.data as JiraTestCaseRunStep[]
            // 调用更新函数，直接使用requirementKey
            updateTestCaseListCache(updatedSteps, testCaseModalRequirementKey.value, testCaseModalData.value[0].testCaseKey)
            // 关闭弹窗
            isTestCaseModalVisible.value = false
        } else {
            ElMessage.error(res.data.message)
        }
    } catch (error) {
        console.error('保存失败', error)
        ElMessage.error('保存失败，请稍后再试')
    } finally {
        testCaseModalSaveLoading.value = false
    }
    isTestCaseModalVisible.value = false
}
const isCreateTestCaseModalVisible = ref(false)
const createTestCaseSave = (data: any) => {
    const { requirementKey } = data
    // 找到对应的 row 数据
    const row = requirementList.value.find(item => item.key === requirementKey)
    if (row) {
        // 强制调用 handleExpandChange，跳过缓存检查
        handleExpandChange(row, true, true)
    }
}
const requirementKey = ref('')
const createTestCaseClick = (key: string) => {
    requirementKey.value = key
    isCreateTestCaseModalVisible.value = true
}

const testPlanFixVersionList = ref<string[]>([])
// 获取测试计划的fixVersion列表
const getFixVersionList = async (testPlanKey: string) => {
    const res = await getTestPlanFixVersion(jiraToken.value, testPlanKey)
    if (res.data.isSuccess) {
        testPlanFixVersionList.value = res.data.data
    } else {
        ElMessage.error('获取修复版本列表失败')
    }
}
interface JiraUser{
    self: string
    key: string
    name: string
    emailAddress: string
    avatarUrls: {
        '48x48': string
        '24x24': string
        '16x16': string
        '32x32': string
    }
    displayName: string
    displayNamePinyin: string
}
const JiraUserInfo = ref<JiraUser | null>(null)

const getJiraUser = async () => {
    const res = await getJiraUserInfo(jiraToken.value)
    if (res.data.isSuccess) {
        JiraUserInfo.value = res.data.data
    } else {
        ElMessage.error('获取用户信息失败')
    }
}

const testCaseKey = ref('')
const stepId = ref('')
const runId = ref('')
const isLinkBugsToTestRunStepModalVisible = ref(false)
const linkBug = ( requirementRowKey: string, row: JiraTestCaseRunStep) => {
    requirementKey.value = requirementRowKey
    testCaseKey.value = row.testCaseKey
    stepId.value = row.id
    runId.value = row.parentId
    isLinkBugsToTestRunStepModalVisible.value = true
}
const linkBugsToTestRunStepSave = (data: any) => {
    console.log('data', data)
    const { requirementKey, testCaseKey, selectedIssues } = data
    // 获取所有匹配的 testRunSteps
    const matchedTestRunSteps = testCaseListCache.value[requirementKey].filter(
        (step: any) => step.testCaseKey === testCaseKey
    )
    if (matchedTestRunSteps.length === 1) {
        // 只有一个匹配的 testRunStep，直接追加
        const [testRunStep] = matchedTestRunSteps
        if (!testRunStep.testRunBugsWrapper) {
            testRunStep.testRunBugsWrapper = []
        }
        appendIssueIfNotExists(testRunStep, selectedIssues)
    } else if (matchedTestRunSteps.length > 1) {
        // 多个匹配的 testRunSteps，找到 rowspan 不为 0 的那个并追加
        const targetTestRunStep = matchedTestRunSteps.find(
            (step: any) => step.rowspan !== 0
        )
        if (targetTestRunStep) {
            if (!targetTestRunStep.testRunBugsWrapper) {
                targetTestRunStep.testRunBugsWrapper = []
            }
            appendIssueIfNotExists(targetTestRunStep, selectedIssues)
        } else {
            console.warn('没有找到 rowspan 不为 0 的 testRunStep')
        }
    } else {
        console.warn('没有找到匹配的 testRunStep')
    }
}
interface BugInfo {
    key: string
    summary: string
    status: string
    assignee: string
}
const appendIssueIfNotExists = (testRunStep: any, selectedIssue: BugInfo) => {
    if (!testRunStep.testRunBugsWrapper) {
        testRunStep.testRunBugsWrapper = []
    }
    const exists = testRunStep.testRunBugsWrapper.some(
        (issue: BugInfo) => issue.key === selectedIssue.key
    )
    if (!exists) {
        testRunStep.testRunBugsWrapper.push(selectedIssue)
    }
}
const JiraUserInfos = ref<JiraUser[]>([])
const getJiraUsers = async () => {
    const res = await getAllUsers(jiraToken.value)
    if (res.data.isSuccess) {
        JiraUserInfos.value = res.data.data
    } else {
        ElMessage.error('获取用户信息失败')
    }
}
const isCreateBugModalVisible = ref(false)
const createBugRequirementKey = ref('')
const createBugTestCaseKey = ref('')
const createBugRunId = ref('')
const createBugStepId = ref('')
const createBugFixVersions = ref<string[]>([])
const createBugComponents = ref<string[]>([])
const createBugJiraUsers = ref<JiraUser[]>([])

const createBug = (row: JiraTestCaseRunStep, requirementRow: Requirement) => {
    createBugRequirementKey.value = requirementRow.key
    createBugTestCaseKey.value = row.testCaseKey
    createBugRunId.value = row.parentId
    createBugStepId.value = row.id
    createBugFixVersions.value = requirementRow.fixVersions
    createBugComponents.value = requirementRow.modules
    createBugJiraUsers.value = JiraUserInfos.value
    isCreateBugModalVisible.value = true
}
const createBugSave = (data: any) => {
    console.log('data', data)
    const { requirementKey, testCaseKey, bugInfo } = data

    // 获取所有匹配的 testRunSteps
    const matchedTestRunSteps = testCaseListCache.value[requirementKey].filter(
        (step: any) => step.testCaseKey === testCaseKey
    )
    if (matchedTestRunSteps.length === 1) {
        // 只有一个匹配的 testRunStep，直接追加
        const [testRunStep] = matchedTestRunSteps
        if (!testRunStep.testRunBugsWrapper) {
            testRunStep.testRunBugsWrapper = []
        }
        testRunStep.testRunBugsWrapper.push(bugInfo)
        console.log('Appended to single testRunStep', testRunStep)
    } else if (matchedTestRunSteps.length > 1) {
        // 多个匹配的 testRunSteps，找到 rowspan 不为 0 的那个并追加
        const targetTestRunStep = matchedTestRunSteps.find(
            (step: any) => step.rowspan !== 0
        )

        if (targetTestRunStep) {
            if (!targetTestRunStep.testRunBugsWrapper) {
                targetTestRunStep.testRunBugsWrapper = []
            }
            targetTestRunStep.testRunBugsWrapper.push(bugInfo)
            console.log('Appended to target testRunStep with rowspan !== 0', targetTestRunStep)
        } else {
            console.warn('没有找到 rowspan 不为 0 的 testRunStep')
        }
    } else {
        console.warn('没有找到匹配的 testRunStep')
    }

    // 如果需要，可以选择性地打印整个缓存以供调试
    console.log('Updated testCaseListCache', testCaseListCache.value)
}

declare var ImageCapture: any
const captureScreen = async (row: JiraTestCaseRunStep, requirementRowKey: string) => {
    // 检查是否授权了屏幕共享
    if (!stream.value || stream.value.getTracks().length === 0) {
        ElMessage.error('未授权屏幕共享 请刷新页面后重试')
        console.error('未授权屏幕共享')
        return
    }
    // 获取与当前需求编号对应的测试用例列表
    const originalData = testCaseListCache.value[requirementRowKey].filter(item => item.testCaseKey === row.testCaseKey)

    if (originalData.length === 0) {
        console.error('未找到对应的测试用例数据')
        return
    }

    // 获取第一条数据的 testRunAttachments
    const [firstTestCase] = originalData
    const attachments = firstTestCase.testRunAttachments

    // 过滤出与当前 row.sortNumber 相同的附件名称，忽略文件后缀
    const sortNumber = row.sortNumber ?? 1 // 确保 sortNumber 有值
    const regex = new RegExp(`^OQ-${row.testCaseKey}-${sortNumber}-(\\d+)\\.\\w+$`)
    const relevantAttachments = attachments.filter(attachment => {
        return regex.test(attachment.fileName)
    })

    // 提取现有序号并找到最大值
    let maxSeqNumber = 0
    relevantAttachments.forEach(attachment => {
        const match = attachment.fileName.match(regex)
        if (match && match[1]) {
            const seq = parseInt(match[1], 10)
            if (seq > maxSeqNumber) {
                maxSeqNumber = seq
            }
        }
    })
    const newSeqNumber = maxSeqNumber + 1

    // 生成新的文件名
    const fileName = `OQ-${row.testCaseKey}-${sortNumber}-${newSeqNumber}.png`
    try {
        console.log('开始截图')
        // 获取视频轨道
        const [track] = stream.value.getVideoTracks()
        // 检查轨道是否有效
        if (!track || track.readyState !== 'live') {
            ElMessage.error('截图失败，请稍后再试')
            console.log('截图失败：无效的视频轨道', track)
        }
        const imageCapture = new ImageCapture(track)

        // 捕获一帧图像
        const bitmap = await imageCapture.grabFrame()

        // 将位图绘制到 Canvas
        const canvas = document.createElement('canvas')
        canvas.width = bitmap.width
        canvas.height = bitmap.height
        const context = canvas.getContext('2d')
        context?.drawImage(bitmap, 0, 0)

        // 将 Canvas 转换为 Blob
        canvas.toBlob(async (blob) => {
            if (blob) {
                // const fileName = `screenshot_${Date.now()}.png`
                const file = new File([blob], fileName, { type: 'image/png' })
                const formData = new FormData()
                formData.append('file', file)
                const response = await uploadTestRunAttachment(jiraToken.value, row.parentId, formData)
                console.log('文件上传成功', response)
                // 创建新的 testRunAttachment 对象
                const newAttachment = {
                    id: '',
                    mimeType: 'image/png',
                    fileName: fileName,
                    filePath: ''
                }
                // 将新的附件添加到第一条测试用例的 testRunAttachments 中
                firstTestCase.testRunAttachments.push(newAttachment)

            }
        }, 'image/png')
    } catch (error) {
        console.error('屏幕截图失败：', error)
        ElMessage.error('截图失败，请稍后再试')
    }
}
const isCaptureEnabled = ref(false) // 是否开启截图功能
const hasPermission = ref(false)    // 记录用户是否已经授权过屏幕共享
const stream = ref<MediaStream | null>(null) // 存储获取的屏幕共享流
// 切换截图功能的开启/关闭
const toggleCapture = async () => {
    if (isCaptureEnabled.value) {
        // 如果已经开启，关闭截图功能并停止流
        if (stream.value) {  // 只有在关闭时停止
            const tracks = stream.value.getTracks()
            tracks.forEach(track => track.stop())
            stream.value = null
        }
        hasPermission.value = false
    } else {
        // 开启截图功能，进行用户授权
        try {
            stream.value = await navigator.mediaDevices.getDisplayMedia({ video: true })
            hasPermission.value = true
        } catch (error) {
            console.error('屏幕共享授权失败:', error)
            // 检查错误类型，提示用户切换为 HTTPS
            if (error instanceof TypeError && error.message.includes('getDisplayMedia')) {
                ElMessage.warning('请切换为HTTPS协议 以获得屏幕共享权限')
            }
            hasPermission.value = false
            return
        }
    }
    isCaptureEnabled.value = !isCaptureEnabled.value
}
// 卸载时清理媒体流、关闭截图
onBeforeUnmount(() => {
    isCaptureEnabled.value = false
    if (stream.value) {
        const tracks = stream.value.getTracks()
        tracks.forEach(track => track.stop())
        stream.value = null
    }
})
const bugListClose = (data: any) => {
    defectDialogVisible.value = false
    // 根据返回的数据更新缺陷列表
    const { requirementKey, testCaseKey, bugList } = data
    const matchedTestRunSteps = testCaseListCache.value[requirementKey].filter(
        (step) => step.testCaseKey === testCaseKey
    )
    if (matchedTestRunSteps.length === 1) {
        matchedTestRunSteps[0].testRunBugsWrapper = bugList
    } else if (matchedTestRunSteps.length > 1) {
        // 多个匹配的 testRunSteps，找到 rowspan 不为 0 的那个
        const targetTestRunStep = matchedTestRunSteps.find(
            (step) => step.rowspan !== 0
        )
        if (targetTestRunStep) {
            targetTestRunStep.testRunBugsWrapper = bugList
        }
    }
}
const reLoadCaseVisible = ref(false)
const reLoadClick = async () => {
    const res = await jiraLoginClick()
    if (!res) {
        jiraLoginDialogModalVisible.value = true
        return
    }
    reLoadCaseVisible.value = true
}
</script>
<style lang='postcss' scoped>
.table-search-form {
    overflow: hidden;
    height: 0;
}

::v-deep(.el-card__header) {
    padding: 7px 15px;
}

::v-deep(.el-button) {
    padding: 4px 6px;
    border-radius: 3px;
}

.link {
    color: #3e9dfc;
    text-decoration: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.link:hover {
    cursor: pointer;
    text-decoration: underline;
}

::v-deep(.el-collapse-item__content) {
    padding: 0;
}

::v-deep(.merged-cell-hover) {
    background-color: #f5f7fa !important;
}
::v-deep(.merged-cell-selected) {
    background-color: #f5f7fa !important;
}
::v-deep(.cell) {
    padding: 0 12px !important;
}
.elegant-tag {
    cursor: pointer;
    transition: background-color 0.3s, border-color 0.3s, color 0.3s;
    background-color: #f5f7fa; /* 默认背景颜色更淡 */
}

.elegant-tag:hover {
    background-color: #e0e6ed; /* 悬停时背景色稍稍加深 */
}
.red-bold {
    color: red;
    font-weight: bold;
}

::v-deep(.el-textarea .el-loading-mask .el-loading-spinner) {
    transform: scale(0.8) !important;
}

.testCaseModal_footer{
    display: flex;
    justify-content: space-between;
    padding: 0 20px 0 0;
    align-items: center;
}
.testCaseModal_footer .el-button{
    font-size: 14px;
}
.el-button+.el-button {
    margin-left: 0;
}

.precondition {
    margin-top: 4px;
    font-size: 12px;
    color: #606266;
    line-height: 1.4;
    white-space: normal;
    word-break: break-word;
    padding: 4px 8px;
    border-left: 2px solid #3e9dfc;
    border-radius: 2px;
}

.tags-container {
    display: flex;
    flex-wrap: wrap; /* 允许换行 */
    gap: 4px 4px; /* 设置行间距和列间距 */
}

.tag-item {
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>