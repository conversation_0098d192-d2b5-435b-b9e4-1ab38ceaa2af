package com.kf.accuratetest.dao;

import com.kf.accuratetest.entity.InterfaceAnnotation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InterfaceAnnotationDao {

    /**
     * 通过ID查询单条数据
     *
     * @param  taskId 任务ID
     * @return 实例对象
     */
    List<InterfaceAnnotation> queryById(String taskId);


    /**
     * 统计总行数
     *
     * @param interfaceAnnotation 查询条件
     * @return 总行数
     */
    long count(InterfaceAnnotation interfaceAnnotation);


    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<TInterfaceAnnotation> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<InterfaceAnnotation> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<TInterfaceAnnotation> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<InterfaceAnnotation> entities);

    /**
     * 修改数据
     *
     * @param interfaceAnnotation 实例对象
     * @return 影响行数
     */
    int update(InterfaceAnnotation interfaceAnnotation);


}

