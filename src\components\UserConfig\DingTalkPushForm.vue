<template>
    <div>
        <el-form ref="formRef" :model="dynamicValidateForm">
            <div class="profile-input" v-for="(domain, index) in dynamicValidateForm.domains" :key="index">
                <el-form-item>
                    <el-input v-model="domain.gitPath" :disabled="isDisabled" placeholder="git地址" />
                </el-form-item>
                <el-form-item :prop="'domains.' + index + '.webHook'" :rules="dynamicValidateRules.Webhook">
                    <el-input v-model="domain.webHook" :disabled="isDisabled" placeholder="Webhook地址" />
                </el-form-item>
                <el-switch v-model="domain.status" style="margin-right: 10px;" />
                <el-button v-prevent-default type="danger" @click.prevent="removeDomain(domain)" plain>删除</el-button>
            </div>
            <el-form-item>
                <el-button v-prevent-default type="primary" @click="submitForm">保存</el-button>
                <el-button v-prevent-default type="primary" @click="editFrom">编辑</el-button>
                <el-button v-prevent-default @click="addDomain">新增一项</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang='ts'>
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { validate } from '@/utils/formExtend'
import { useLayoutStore } from '@/stores/modules/layout'
import { inFromData } from '@/api/layout'

const validatePass = (rule: any, value: string, callback: any) => {
    if (!value.startsWith('https://')) {
        callback(new Error('请输入正确的Webhook地址'))
    } else {
        callback()
    }
}
const dynamicValidateRules = reactive({
    Webhook: [{ required: true, message: 'Webhook地址不能为空', trigger: 'blur' }, { validator: validatePass, trigger: 'blur' }]
})

const show = ref(false)
const formRef = ref<any>()
const dynamicValidateForm = reactive<{
    domains: DomainItem[]
}>({
    domains: [
        {
            gitPath: '',
            webHook: '',
            status: false
        }
    ]
})

interface DomainItem {
    gitPath: string
    webHook: string
    status: boolean
}

const removeDomain = (item: DomainItem) => {
    if (dynamicValidateForm.domains.length > 1) {
        dynamicValidateForm.domains.splice(dynamicValidateForm.domains.indexOf(item), 1)
    }
}

const addDomain = () => {
    if (dynamicValidateForm.domains.length < 5) {
        dynamicValidateForm.domains.push({
            gitPath: '',
            webHook: '',
            status: true
        })
    } else {
        ElMessage.warning('最多只能添加5个webhook地址')
    }
}

const isDisabled = ref(true)
const editFrom = () => {
    isDisabled.value = false
}
const { saveInFrom } = useLayoutStore()
const submitForm = async () => {
    if (!await validate(formRef)) return
    // 解构参数
    const { domains } = dynamicValidateForm
    const result = await saveInFrom(domains)
    if (result.data.isSuccess) {
        ElMessage.success(result.data.message)
        isDisabled.value = true
    } else {
        ElMessage.error(result.data.message)
    }
}
const fromData = async () => {
    dynamicValidateForm.domains = await (await inFromData()).data.data
}
fromData()
</script>

<style scoped>

.el-form-item{
    margin-bottom: 0;
}

.profile-input {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .el-input {
        margin-right: 10px;
        margin-bottom: 0;
        width: 350px;
    }
}
</style>
