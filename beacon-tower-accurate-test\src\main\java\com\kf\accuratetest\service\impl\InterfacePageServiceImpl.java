package com.kf.accuratetest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kf.accuratetest.dao.InterfacePageDao;
import com.kf.accuratetest.dto.InterfacePageBatchImportDTO;
import com.kf.accuratetest.entity.InterfacePage;
import com.kf.accuratetest.service.InterfacePageService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InterfacePageServiceImpl implements InterfacePageService {
    @Resource
    private InterfacePageDao interfacePageDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public InterfacePage queryById(Long id) {
        return this.interfacePageDao.queryById(id);
    }

    /**
     * 分页查询
     *
     * @param interfacePage 筛选条件
     * @param current        当前页数
     * @param size           每页记录数
     * @return 查询结果
     */
    @Override
    public IPage<InterfacePage> listInterfacePages(InterfacePage interfacePage, int current, int size) {
        QueryWrapper<InterfacePage> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", interfacePage.getUserId())
                .like(StringUtils.isNotBlank(interfacePage.getProjectName()), "project_name", interfacePage.getProjectName())
                .like(StringUtils.isNotBlank(interfacePage.getInterfaceName()), "interface_name", interfacePage.getInterfaceName())
                .like(StringUtils.isNotBlank(interfacePage.getPageUrl()), "page_url", interfacePage.getPageUrl())
                .eq("is_deleted", 0)
                .orderByDesc("create_time");
        return interfacePageDao.selectPage(new Page<>(current, size), wrapper);
    }

    @Override
    public int incrementalAdd(List<InterfacePageBatchImportDTO> TInterfacePageList, String userId) {
        List<InterfacePage> pageList = new ArrayList<>();
        for (InterfacePageBatchImportDTO dto : TInterfacePageList) {
            InterfacePage page = new InterfacePage();
            page.setUserId(userId);
            page.setProjectName(dto.getProjectName());
            page.setInterfaceName(dto.getInterfaceName());
            page.setPageUrl(dto.getPageUrl());
            page.setCreateTime(new Date());
            page.setUpdateTime(new Date());
            page.setStatus(0);
            page.setIsDeleted(0);
            pageList.add(page);
        }
        return interfacePageDao.insertBatch(pageList);
    }

    /**
     * 新增数据
     *
     * @param file 实例对象
     * @return 受影响行数
     */
    @Override
    public int pageUpload(MultipartFile file, String userId) {
        InputStream inputStream = null;
        Workbook workbook = null;
        List<InterfacePage> pageList = new ArrayList<>();
        try {
            // 将 MultipartFile 对象转换为 InputStream 对象
            inputStream = file.getInputStream();
            workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表

            // 遍历行
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);

                // 获取第一列单元格数据,页面入口
                Cell pageEntryCell = row.getCell(0);
                String pageEntry = pageEntryCell.getStringCellValue();

                // 获取第二列单元格数据，接口名称
                Cell apiNameCell = row.getCell(1);
                String apiName = apiNameCell.getStringCellValue();
                //判断第二列数据是否没有中文
//                if (apiName.matches("^[a-zA-Z0-9_]*$")) {
//                    apiName = apiName;
//                }
                // 获取第三列单元格数据，项目名称
                Cell projectCell = row.getCell(2);
                String projectName = projectCell.getStringCellValue();

                InterfacePage interfacePage = new InterfacePage();
                interfacePage.setUserId(userId);
                interfacePage.setProjectName(projectName);
                interfacePage.setInterfaceName(apiName);
                interfacePage.setPageUrl(pageEntry);
                interfacePage.setCreateTime(new Date());
                interfacePage.setUpdateTime(new Date());
                interfacePage.setStatus(0);
                interfacePage.setIsDeleted(0);
                pageList.add(interfacePage);
            }
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException(e);

        } finally {
            // 关闭工作簿和文件输入流
            try {
                if (workbook != null) {
                    workbook.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }

        }
        return interfacePageDao.insertBatch(pageList);
    }

    /**
     * 新增数据
     *
     * @param InterfacePage 实例对象
     * @return 实例对象
     */
    @Override
    public InterfacePage insert(InterfacePage InterfacePage) {
        this.interfacePageDao.insert(InterfacePage);
        return InterfacePage;
    }

    /**
     * 校验数据
     *
     * @param file 文件
     * @return 实例对象
     */
    @Override
    public List<InterfacePageBatchImportDTO> checkFileData(MultipartFile file, String userId) {
        List<InterfacePage> pageList = analysis(file, userId);

        Set<InterfacePageBatchImportDTO> uniqueDTOSet = pageList.stream()
                .map(tInterfacePage -> {
                    InterfacePageBatchImportDTO interfacePageBatchImportDTO = new InterfacePageBatchImportDTO();
                    interfacePageBatchImportDTO.setPageUrl(tInterfacePage.getPageUrl());
                    interfacePageBatchImportDTO.setInterfaceName(tInterfacePage.getInterfaceName());
                    interfacePageBatchImportDTO.setProjectName(tInterfacePage.getProjectName());
                    return interfacePageBatchImportDTO;
                })
                .collect(Collectors.toCollection(LinkedHashSet::new));

        List<InterfacePage> existingPages = interfacePageDao.queryAllByUserId(userId);
        Set<InterfacePageBatchImportDTO> existingDTOSet = existingPages.stream()
                .map(tInterfacePage -> {
                    InterfacePageBatchImportDTO interfacePageBatchImportDTO = new InterfacePageBatchImportDTO();
                    interfacePageBatchImportDTO.setPageUrl(tInterfacePage.getPageUrl());
                    interfacePageBatchImportDTO.setInterfaceName(tInterfacePage.getInterfaceName());
                    interfacePageBatchImportDTO.setProjectName(tInterfacePage.getProjectName());
                    return interfacePageBatchImportDTO;
                })
                .collect(Collectors.toSet());

        uniqueDTOSet.removeAll(existingDTOSet);

        uniqueDTOSet.forEach(dto -> {
            StringBuilder sb = new StringBuilder();
            if (StringUtils.isBlank(dto.getPageUrl())) {
                sb.append("页面入口不能为空");
            }
            if (StringUtils.isBlank(dto.getInterfaceName())) {
                if (!sb.isEmpty()) {
                    sb.append(",");
                }
                sb.append("接口名称不能为空");
            }
            dto.setMsg(sb.toString());
        });

        return new ArrayList<>(uniqueDTOSet);
    }


    /**
     * 修改数据
     *
     * @param InterfacePage 实例对象
     * @return 实例对象
     */
    @Override
    public InterfacePage update(InterfacePage InterfacePage) {
        this.interfacePageDao.update(InterfacePage);
        return this.queryById(InterfacePage.getId().longValue());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(int id) {
        return this.interfacePageDao.deleteById(id) > 0;
    }

    private List<InterfacePage> analysis(MultipartFile file, String userId) {
        InputStream inputStream = null;
        Workbook workbook = null;
        List<InterfacePage> pageList = new ArrayList<>();
        try {
            // 将 MultipartFile 对象转换为 InputStream 对象
            inputStream = file.getInputStream();
            workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表

            // 遍历行
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }
                // 获取第一列单元格数据,页面入口
                Cell pageEntryCell = row.getCell(0);
                String pageEntry = (pageEntryCell != null) ? pageEntryCell.getStringCellValue() : "";

                // 获取第二列单元格数据，接口名称
                Cell apiNameCell = row.getCell(1);
                String apiName = (apiNameCell != null) ? apiNameCell.getStringCellValue() : "";

                // 获取第三列单元格数据，项目名称
                Cell projectCell = row.getCell(2);
                String projectName = (projectCell != null) ? projectCell.getStringCellValue() : "";
                InterfacePage interfacePage = new InterfacePage();
//                tInterfacePage.setUserId(userId);
                interfacePage.setProjectName(projectName);
                interfacePage.setInterfaceName(apiName);
                interfacePage.setPageUrl(pageEntry);
//                tInterfacePage.setCreateTime(new Date());
//                tInterfacePage.setUpdateTime(new Date());
//                tInterfacePage.setStatus(0);
//                tInterfacePage.setIsDeleted(0);
                pageList.add(interfacePage);
            }
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());

        } finally {
            // 关闭工作簿和文件输入流
            try {
                if (workbook != null) {
                    workbook.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        return pageList;
    }
}
