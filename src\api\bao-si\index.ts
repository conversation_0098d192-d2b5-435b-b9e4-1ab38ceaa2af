/**
 * 宝思服务 API (工具服务)
 */

import request from '@/utils/request'
import type { AxiosResponse } from 'axios'
import type {
    XMindToExcelFileList,
    FileIdParam,
    LoginJiraData,
    CsvToJiraData,
    JiraParams,
    ReloadTestRuns,
    UpdateTestRunResultRequest,
    UpdateTestStepResultRequest,
    DeleteTestCaseRequest,
    JiraTestCaseRunStep,
    LinkTestRunStepBugsRequest,
    VerifyFileList,
    FileTypeData
} from './types'

const serviceName = '/api/beacon-tower'
const baoSiName = 'bao-si'

// 宝思服务API端点
const baoSiApi = {
    // XMind相关
    downloadCSVFile: `${serviceName}/${baoSiName}/xmind/downloadCSVFile`,
    xMindToExcelFileUpload: `${serviceName}/${baoSiName}/xmind/xMindToExcelFileUpload`,
    getImportStatus: `${serviceName}/${baoSiName}/xmind/getImportStatus`,
    getXMindToExcelFileList: `${serviceName}/${baoSiName}/xmind/getFileList`,
    deleteFileById: `${serviceName}/${baoSiName}/xmind/deleteFile`,
    deleteTestCaseByFileId: `${serviceName}/${baoSiName}/xmind/deleteTestCaseByFileId`,
    csvFileToJira: `${serviceName}/${baoSiName}/xmind/csvFileToJira`,
    getXMindTemplateFile: `${serviceName}/${baoSiName}/xmind/getXMindTemplateFile`,
    
    // 文件相关
    uploadFile: `${serviceName}/${baoSiName}/file/upload`,
    previewFile: `${serviceName}/${baoSiName}/file/preview`,
    
    // Jira相关
    loginCheck: `${serviceName}/${baoSiName}/jira/loginCheck`,
    loginJira: `${serviceName}/${baoSiName}/jira/login`,
    getXMindFileInfo: `${serviceName}/${baoSiName}/jira/getXMindFileInfo`,
    jiraProgress: `${serviceName}/${baoSiName}/jira/progress`,
    getAllProject: `${serviceName}/${baoSiName}/jira/getAllProject`,
    getAllVersionForProject: `${serviceName}/${baoSiName}/jira/getAllVersionForProject`,
    getAllPlanForProject: `${serviceName}/${baoSiName}/jira/getAllPlanForProject`,
    getAllCycleForPlan: `${serviceName}/${baoSiName}/jira/getAllCycleForPlan`,
    reloadTestRuns: `${serviceName}/${baoSiName}/jira/reloadTestRuns`,
    getTestRequirementList: `${serviceName}/${baoSiName}/jira/getTestRequirementList`,
    getTestCaseList: `${serviceName}/${baoSiName}/jira/getTestCaseList`,
    updateTestRunStatus: `${serviceName}/${baoSiName}/jira/updateTestRunStatus`,
    updateTestStepStatus: `${serviceName}/${baoSiName}/jira/updateTestStepStatus`,
    deleteTestCase: `${serviceName}/${baoSiName}/jira/deleteTestCase`,
    deleteRunAttachment: `${serviceName}/${baoSiName}/jira/deleteRunAttachment`,
    downloadRunAttachment: `${serviceName}/${baoSiName}/jira/downloadRunAttachment`,
    updateTestStep: `${serviceName}/${baoSiName}/jira/updateTestStep`,
    getTestRunAttachments: `${serviceName}/${baoSiName}/jira/getTestRunAttachments`,
    createTestCase: `${serviceName}/${baoSiName}/jira/createTestCase`,
    getAllTestSuiteForProject: `${serviceName}/${baoSiName}/jira/getAllTestSuiteForProject`,
    getTestPlanFixVersion: `${serviceName}/${baoSiName}/jira/getTestPlanFixVersion`,
    getJiraUserInfo: `${serviceName}/${baoSiName}/jira/getJiraUserInfo`,
    getJiraBugsInfo: `${serviceName}/${baoSiName}/jira/getJiraBugsInfo`,
    linkTestRunStepBugs: `${serviceName}/${baoSiName}/jira/linkTestRunStepBugs`,
    getComponentsForProject: `${serviceName}/${baoSiName}/jira/getComponentsForProject`,
    getAllUsers: `${serviceName}/${baoSiName}/jira/getAllUsers`,
    createBug: `${serviceName}/${baoSiName}/jira/createBug`,
    uploadTestRunAttachment: `${serviceName}/${baoSiName}/jira/uploadTestRunAttachment`,
    deleteBug: `${serviceName}/${baoSiName}/jira/deleteBug`,
    getTestRunBugsInfo: `${serviceName}/${baoSiName}/jira/getTestRunBugsInfo`,
    getTestPlanSummary: `${serviceName}/${baoSiName}/jira/getTestPlanSummary`,
    
    // 验证文档相关
    getVerifyFileList: `${serviceName}/${baoSiName}/verifyDocument/getVerifyFileList`,
    getVerifyFileTypeList: `${serviceName}/${baoSiName}/verifyDocument/getVerifyFileTypeList`,
    createVerifyFile: `${serviceName}/${baoSiName}/verifyDocument/createVerifyFile`,
    downloadVerifyFile: `${serviceName}/${baoSiName}/verifyDocument/downloadVerifyFile`,
    deleteVerifyFile: `${serviceName}/${baoSiName}/verifyDocument/deleteVerifyFile`,
    compressDocument: `${serviceName}/${baoSiName}/verifyDocument/compressFile`,
    getPresetFields: `${serviceName}/${baoSiName}/verifyDocument/getPresetFields`
}

// ==================== XMind相关API ====================

/**
 * XMind文件转Excel上传
 */
export function xMindToExcelFileUpload(data: FormData): Promise<AxiosResponse<IResponse>> {
    return request({
        url: baoSiApi.xMindToExcelFileUpload,
        method: 'post',
        data: data
    })
}

/**
 * 获取导入状态
 */
export function getImportStatus(): Promise<AxiosResponse<IResponse>> {
    return request({
        url: baoSiApi.getImportStatus,
        method: 'get'
    })
}

/**
 * 获取XMind转Excel文件列表
 */
export function getXMindToExcelFileList(params: XMindToExcelFileList): Promise<AxiosResponse<IResponse>> {
    return request({
        url: baoSiApi.getXMindToExcelFileList,
        method: 'get',
        params: params
    })
}

/**
 * 下载CSV文件
 */
export function downloadCSVFile(param: FileIdParam): Promise<AxiosResponse<IResponse>> {
    return request({
        url: baoSiApi.downloadCSVFile,
        method: 'get',
        responseType: 'blob',
        params: param
    })
}

/**
 * 根据文件ID删除文件
 */
export function deleteFileById(fileId: string): Promise<AxiosResponse<IResponse>> {
    return request({
        url: baoSiApi.deleteFileById,
        method: 'post',
        params: { fileId: fileId }
    })
}

/**
 * 根据文件ID删除测试用例
 */
export function deleteTestCaseByFileId(fileId: string, jiraToken: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.deleteTestCaseByFileId,
        method: 'delete',
        params: { fileId: fileId }
    })
}

/**
 * 获取XMind模板文件
 */
export function getXMindTemplateFile(): Promise<AxiosResponse<IResponse>> {
    return request({
        url: baoSiApi.getXMindTemplateFile,
        method: 'get',
        responseType: 'blob'
    })
}

// ==================== 文件相关API ====================

/**
 * 上传文件
 */
export function uploadFile(data: FormData): Promise<AxiosResponse<IResponse>> {
    return request({
        url: baoSiApi.uploadFile,
        method: 'post',
        data: data
    })
}

/**
 * 预览文件
 */
export function previewFile(fileId: string): Promise<AxiosResponse<IResponse>> {
    return request({
        url: `${baoSiApi.previewFile}/${encodeURIComponent(fileId)}`,
        method: 'get'
    })
}

/**
 * 预览图片
 */
export function previewImage(fileId: string): string {
    return `${baoSiApi.previewFile}/${encodeURIComponent(fileId)}`
}

/**
 * 获取上传文件URL
 */
export function getUploadFileUrl(): string {
    return baoSiApi.uploadFile
}

// ==================== Jira相关API ====================

/**
 * 登录检查
 */
export function loginCheck(): Promise<AxiosResponse<IResponse>> {
    return request({
        url: baoSiApi.loginCheck,
        method: 'get'
    })
}

/**
 * 登录Jira
 */
export function loginJira(data: LoginJiraData): Promise<AxiosResponse<IResponse>> {
    return request({
        url: baoSiApi.loginJira,
        method: 'post',
        data: data
    })
}

/**
 * 获取XMind文件信息
 */
export function getXMindFileInfo(param: FileIdParam): Promise<AxiosResponse<IResponse>> {
    return request({
        url: baoSiApi.getXMindFileInfo,
        method: 'get',
        params: param
    })
}

/**
 * CSV转Jira
 */
export function csvToJira(data: CsvToJiraData, jiraToken: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.csvFileToJira,
        method: 'post',
        data: data
    })
}

/**
 * 获取Jira进度URL
 */
export function getJiraProgress(): string {
    return baoSiApi.jiraProgress
}

/**
 * 获取所有项目
 */
export function getAllProject(jiraToken: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.getAllProject,
        method: 'get'
    })
}

/**
 * 获取项目的所有版本
 */
export function getAllVersionForProject(projectKey: string, jiraToken: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.getAllVersionForProject,
        method: 'get',
        params: { projectKey: projectKey }
    })
}

/**
 * 获取项目的所有测试计划
 */
export function getAllPlanForProject(params: JiraParams, jiraToken: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.getAllPlanForProject,
        method: 'get',
        params: params
    })
}

/**
 * 获取计划下的所有测试周期
 */
export function getAllCycleForPlan(planKey: string, jiraToken: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.getAllCycleForPlan,
        method: 'get',
        params: { planKey: planKey }
    })
}

/**
 * 刷新测试用例
 */
export function reloadTestRuns(data: ReloadTestRuns, jiraToken: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.reloadTestRuns,
        method: 'post',
        data: data
    })
}

/**
 * 获取需求列表
 */
export function getTestRequirementList(planKey: string, cycleId: string, jiraToken: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.getTestRequirementList,
        method: 'get',
        params: { planKey: planKey, cycleId: cycleId }
    })
}

/**
 * 获取需求的测试用例列表
 */
export function getTestCaseList(planKey: string, cycleId: string, requirementKey: string, jiraToken: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.getTestCaseList,
        method: 'get',
        params: { planKey: planKey, cycleId: cycleId, requirementKey: requirementKey }
    })
}

/**
 * 更新测试运行状态
 */
export function updateTestRunStatus(jiraToken: string, data: UpdateTestRunResultRequest): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.updateTestRunStatus,
        method: 'post',
        data: data
    })
}

/**
 * 更新测试步骤状态
 */
export function updateTestStepStatus(jiraToken: string, data: UpdateTestStepResultRequest): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.updateTestStepStatus,
        method: 'post',
        data: data
    })
}

/**
 * 删除测试用例
 */
export function deleteTestCase(jiraToken: string, deleteTestCase: DeleteTestCaseRequest): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.deleteTestCase,
        method: 'delete',
        data: deleteTestCase
    })
}

/**
 * 删除运行附件
 */
export function deleteRunAttachment(jiraToken: string, runId: string, attachmentId: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.deleteRunAttachment,
        method: 'delete',
        params: { runId: runId, attachmentId: attachmentId }
    })
}

/**
 * 下载附件
 */
export function downloadAttachment(jiraUrl: string, jiraToken: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        method: 'get',
        url: baoSiApi.downloadRunAttachment,
        params: { jiraUrl: jiraUrl },
        responseType: 'blob'
    })
}

/**
 * 更新测试步骤
 */
export function updateTestSteps(jiraToken: string, data: JiraTestCaseRunStep[]): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.updateTestStep,
        method: 'post',
        data: data
    })
}

/**
 * 获取测试运行附件
 */
export function getTestRunAttachments(jiraToken: string, testRunId: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.getTestRunAttachments,
        method: 'get',
        params: { testRunId: testRunId }
    })
}

/**
 * 创建测试用例
 */
export function createTestCase(jiraToken: string, data: any): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.createTestCase,
        method: 'post',
        data: data
    })
}

/**
 * 获取项目下所有的测试用例集
 */
export function getAllTestSuiteForProject(jiraToken: string, projectKey: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.getAllTestSuiteForProject,
        method: 'get',
        params: { projectKey: projectKey }
    })
}

/**
 * 获取测试计划的版本
 */
export function getTestPlanFixVersion(jiraToken: string, planKey: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.getTestPlanFixVersion,
        method: 'get',
        params: { planKey: planKey }
    })
}

/**
 * 获取Jira用户信息
 */
export function getJiraUserInfo(jiraToken: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.getJiraUserInfo,
        method: 'get'
    })
}

/**
 * 批量获取bug的详细信息
 */
export function getJiraBugsInfo(jiraToken: string, issueKey: string[]): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.getJiraBugsInfo,
        method: 'post',
        data: issueKey
    })
}

/**
 * 关联bug到测试运行步骤
 */
export function linkTestRunStepBugs(jiraToken: string, data: LinkTestRunStepBugsRequest): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.linkTestRunStepBugs,
        method: 'post',
        data: data
    })
}

/**
 * 获取项目下的所有模块
 */
export function getComponentsForProject(jiraToken: string, projectKey: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.getComponentsForProject,
        method: 'get',
        params: { projectKey: projectKey }
    })
}

/**
 * 获取Jira的所有用户
 */
export function getAllUsers(jiraToken: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.getAllUsers,
        method: 'get'
    })
}

/**
 * 创建bug
 */
export function createBug(jiraToken: string, data: any): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.createBug,
        method: 'post',
        data: data
    })
}

/**
 * 上传运行附件
 */
export function uploadTestRunAttachment(jiraToken: string, runId: string, data: FormData): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.uploadTestRunAttachment,
        method: 'post',
        params: { runId: runId },
        data: data
    })
}

/**
 * 删除缺陷
 */
export function deleteBug(jiraToken: string, runId: string, issueKey: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.deleteBug,
        method: 'delete',
        params: {
            runId: runId,
            issueKey: issueKey
        }
    })
}

/**
 * 获取测试运行的所有缺陷信息
 */
export function getTestRunBugsInfo(jiraToken: string, testRunId: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.getTestRunBugsInfo,
        method: 'get',
        params: { testRunId: testRunId }
    })
}

/**
 * 获取Issue的标题
 */
export function getIssueSummary(jiraToken: string, issueKey: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.getTestPlanSummary,
        method: 'get',
        params: { issueKey: issueKey }
    })
}

// ==================== 验证文档相关API ====================

/**
 * 获得验证文档列表
 */
export function getVerifyFileList(params: VerifyFileList): Promise<AxiosResponse<IResponse>> {
    return request({
        url: baoSiApi.getVerifyFileList,
        method: 'get',
        params: params
    })
}

/**
 * 获得验证文档类型列表
 */
export function getVerifyFileTypeList(): Promise<AxiosResponse<IResponse<FileTypeData>>> {
    return request({
        url: baoSiApi.getVerifyFileTypeList,
        method: 'get'
    })
}

/**
 * 创建验证文档
 */
export function createVerifyFile(data: any, jiraToken: string): Promise<AxiosResponse<IResponse>> {
    return request({
        headers: { 'jiraToken': jiraToken },
        url: baoSiApi.createVerifyFile,
        method: 'post',
        data: data
    })
}

/**
 * 下载验证文档
 */
export function downloadVerifyFile(params: number): Promise<AxiosResponse<IResponse>> {
    return request({
        url: baoSiApi.downloadVerifyFile,
        method: 'get',
        params: { id: params },
        responseType: 'blob'
    })
}

/**
 * 删除验证文档
 */
export function deleteVerifyFile(params: number): Promise<AxiosResponse<IResponse>> {
    return request({
        url: baoSiApi.deleteVerifyFile,
        method: 'delete',
        params: { id: params }
    })
}

/**
 * 压缩文档
 */
export function compressFile(data: any): Promise<AxiosResponse<Blob>> {
    return request({
        url: baoSiApi.compressDocument,
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}

/**
 * 获取预设字段
 */
export function getPresetFields(objectType: string, verifyDocType: string): Promise<AxiosResponse<IResponse>> {
    return request({
        url: baoSiApi.getPresetFields,
        method: 'get',
        params: { objectType: objectType, verifyDocType: verifyDocType }
    })
}
