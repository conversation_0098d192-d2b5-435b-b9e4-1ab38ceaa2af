package com.kf.uitest.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("ui_test_assertion")
public class UiTestAssertion {
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    
    @TableField("hook_id")
    private String hookId;
    
    @TableField("assertion_type")
    private String assertionType;
    
    @TableField("assertion_target")
    private String assertionTarget;
    
    @TableField("assertion_operator")
    private String assertionOperator;
    
    @TableField("expected_value")
    private String expectedValue;
    
    @TableField("timeout")
    private Integer timeout;
    
    @TableField("polling_interval")
    private Integer pollingInterval;
    
    @TableField("soft_assert")
    private Boolean softAssert;
    
    @TableField("description")
    private String description;
    
    @TableField("create_time")
    private LocalDateTime createTime;
    
    @TableField("update_time")
    private LocalDateTime updateTime;
} 