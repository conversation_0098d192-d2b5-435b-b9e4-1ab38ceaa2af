package com.kf.baosi.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

@Data
public class CSVFileToJiraDTO {

    @NotBlank(message = "fileId不能为空")
    private String fileId;

    @NotBlank(message = "项目不能为空")
    private String projectKey;

    private List<String> fixVersion;

    // 测试计划
    private String testPlan;

    // 测试周期
    private String testCycle;

    // 是否合并多个步骤与多个结果
    private boolean mergeStepAndResult = true;

}
