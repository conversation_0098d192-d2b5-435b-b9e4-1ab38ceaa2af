package com.kf.uitest.dto.execution;

import com.kf.uitest.enums.LocatorType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ElementLocatorDTO {
    /**
     * 定位方式
     * @see com.kf.uitest.enums.LocatorType
     */
    private LocatorType type;
    
    /**
     * 定位表达式
     * 示例：
     * - ID: "login-button"
     * - NAME: "username"
     * - CLASS_NAME: "submit-btn"
     * - CSS_SELECTOR: ".login-form input[type='password']"
     * - XPATH: "//div[@class='login-form']//button"
     * - LINK_TEXT: "忘记密码"
     * - PARTIAL_LINK_TEXT: "忘记"
     * - TAG_NAME: "button"
     */
    private String value;
    
    /**
     * 索引
     * 当定位到多个元素时，使用索引选择特定元素
     * 从0开始计数，-1表示不使用索引
     */
    private Integer index;
    
    /**
     * 超时时间（秒）
     * 查找元素的最大等待时间
     * 默认：10
     */
    private Integer timeout;
    
    /**
     * 元素描述
     * 用于日志输出和错误提示
     * 示例：登录按钮、用户名输入框
     */
    private String description;
} 