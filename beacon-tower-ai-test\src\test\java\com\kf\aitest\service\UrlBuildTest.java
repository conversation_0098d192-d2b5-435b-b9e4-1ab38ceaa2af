package com.kf.aitest.service;

import com.kf.aitest.service.impl.DataFetchServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * URL构建测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class UrlBuildTest {

    @Test
    public void testBuildUrlWithCorrectFormat() {
        DataFetchServiceImpl service = new DataFetchServiceImpl();

        String baseUrl = "https://copilot-uat.pharmaronclinical.com";
        String id = "0fabd500fbb7cc3230808eb5bdaebc59";

        // 测试recognize阶段
        String recognizeUrl = service.buildUrl(baseUrl, id, "recognize");
        String expectedRecognizeUrl = "https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=0fabd500fbb7cc3230808eb5bdaebc59/recognize/content/content.md";
        System.out.println("recognize URL: " + recognizeUrl);
        assertEquals(expectedRecognizeUrl, recognizeUrl);
        
        // 测试extraction阶段
        String extractionUrl = service.buildUrl(baseUrl, id, "extraction");
        String expectedExtractionUrl = "https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=0fabd500fbb7cc3230808eb5bdaebc59/extraction/extract_result.json";
        System.out.println("extraction URL: " + extractionUrl);
        assertEquals(expectedExtractionUrl, extractionUrl);

        // 测试structured阶段
        String structuredUrl = service.buildUrl(baseUrl, id, "structured");
        String expectedStructuredUrl = "https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=0fabd500fbb7cc3230808eb5bdaebc59/structured/structured.json";
        System.out.println("structured URL: " + structuredUrl);
        assertEquals(expectedStructuredUrl, structuredUrl);

        // 测试transformer阶段
        String transformerUrl = service.buildUrl(baseUrl, id, "transformer");
        String expectedTransformerUrl = "https://copilot-uat.pharmaronclinical.com/pv-manus-front/api/fs/preview?type=document&file_path=0fabd500fbb7cc3230808eb5bdaebc59/transformer/transformer.json";
        System.out.println("transformer URL: " + transformerUrl);
        assertEquals(expectedTransformerUrl, transformerUrl);
    }
    
    @Test
    public void testUrlEncoding() {
        DataFetchServiceImpl service = new DataFetchServiceImpl();

        String baseUrl = "https://copilot-uat.pharmaronclinical.com";
        String id = "test-id-with-special-chars";
        String stageName = "extraction";

        String url = service.buildUrl(baseUrl, id, stageName);
        System.out.println("URL with special chars: " + url);

        // 验证URL包含正确编码的参数
        assertTrue(url.contains("file_path=test-id-with-special-chars/extraction/extract_result.json"));
        assertTrue(url.contains("type=document"));
    }
    
    @Test
    public void testStageFileMapping() {
        DataFetchServiceImpl service = new DataFetchServiceImpl();
        
        assertEquals("content/content.md", service.getStageFileName("recognize"));
        assertEquals("extract_result.json", service.getStageFileName("extraction"));
        assertEquals("structured.json", service.getStageFileName("structured"));
        assertEquals("transformer.json", service.getStageFileName("transformer"));
        assertEquals("unknown.json", service.getStageFileName("unknown"));
    }
}
