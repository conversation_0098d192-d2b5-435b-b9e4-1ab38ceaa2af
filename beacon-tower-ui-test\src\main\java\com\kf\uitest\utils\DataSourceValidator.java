package com.kf.uitest.utils;

import com.kf.uitest.entity.UiTestDataSource;
import com.kf.uitest.exception.TestExecutionException;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.net.URI;
import java.util.regex.Pattern;

@Component
public class DataSourceValidator {

    private static final Pattern URL_PATTERN = Pattern.compile(
            "^jdbc:(mysql|postgresql|oracle|sqlserver)://[\\w\\-\\.]+:\\d+/\\w+.*$");

    private static final Pattern USERNAME_PATTERN = Pattern.compile("^[\\w\\-@.]{3,50}$");

    /**
     * 验证数据源配置
     */
    public void validate(UiTestDataSource dataSource) {
        validateBasicFields(dataSource);
        validateUrl(dataSource.getUrl());
        validateUsername(dataSource.getUsername());
        validatePassword(dataSource.getPassword());
        validateDriverClass(dataSource.getDriverClass());
    }

    private void validateBasicFields(UiTestDataSource dataSource) {
        if (!StringUtils.hasText(dataSource.getName())) {
            throw new TestExecutionException("Data source name is required");
        }
        if (dataSource.getName().length() > 50) {
            throw new TestExecutionException("Data source name is too long (max 50 characters)");
        }
    }

    private void validateUrl(String url) {
        if (!StringUtils.hasText(url)) {
            throw new TestExecutionException("Database URL is required");
        }
        if (!URL_PATTERN.matcher(url).matches()) {
            throw new TestExecutionException("Invalid database URL format");
        }
        try {
            URI uri = new URI(url.substring(5)); // Remove "jdbc:"
            if (!uri.getScheme().matches("mysql|postgresql|oracle|sqlserver")) {
                throw new TestExecutionException("Unsupported database type");
            }
        } catch (Exception e) {
            throw new TestExecutionException("Invalid database URL");
        }
    }

    private void validateUsername(String username) {
        if (!StringUtils.hasText(username)) {
            throw new TestExecutionException("Username is required");
        }
        if (!USERNAME_PATTERN.matcher(username).matches()) {
            throw new TestExecutionException("Invalid username format");
        }
    }

    private void validatePassword(String password) {
        if (!StringUtils.hasText(password)) {
            throw new TestExecutionException("Password is required");
        }
        if (password.length() < 6 || password.length() > 50) {
            throw new TestExecutionException("Password length must be between 6 and 50 characters");
        }
    }

    private void validateDriverClass(String driverClass) {
        if (!StringUtils.hasText(driverClass)) {
            throw new TestExecutionException("Driver class is required");
        }
        try {
            Class.forName(driverClass);
        } catch (ClassNotFoundException e) {
            throw new TestExecutionException("Invalid driver class: " + driverClass);
        }
    }
}