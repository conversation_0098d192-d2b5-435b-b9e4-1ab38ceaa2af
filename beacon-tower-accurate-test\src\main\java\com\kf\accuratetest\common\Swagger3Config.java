package com.kf.accuratetest.common;//package com.kf.common;
//
//
//import io.swagger.annotations.ApiOperation;
//import io.swagger.v3.oas.annotations.security.SecurityScheme;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import springfox.documentation.builders.ApiInfoBuilder;
//import springfox.documentation.builders.PathSelectors;
//import springfox.documentation.builders.RequestHandlerSelectors;
//import springfox.documentation.oas.annotations.EnableOpenApi;
//import springfox.documentation.service.ApiInfo;
//import springfox.documentation.service.ApiKey;
//import springfox.documentation.service.AuthorizationScope;
//import springfox.documentation.service.SecurityReference;
//import springfox.documentation.spi.DocumentationType;
//import springfox.documentation.spi.service.contexts.SecurityContext;
//import springfox.documentation.spring.web.plugins.Docket;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Configuration
//@EnableOpenApi
//public class Swagger3Config {
//
//    Boolean swaggerEnabled = true;//ture 启用Swagger3.0 fasle 禁用（生产环境要禁用）
//
//    @Bean
//    public Docket createRestApi() {
//        return new Docket(DocumentationType.OAS_30)
//                .apiInfo(apiInfo())
//                // 是否开启
//                .enable(swaggerEnabled)
//                .select()
//                // 扫描的路径使用@Api的controller
//                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
//                // 指定路径处理PathSelectors.any()代表所有的路径
//                .paths(PathSelectors.any())
//                .build();
//    }
//
//    private ApiInfo apiInfo() {
//        return new ApiInfoBuilder()
//                .title("质效平台接口文档")
////                .description("质效平台接口文档")
//                //作者信息
//                //.contact(new Contact("name","url", "email"))
//                .version("1.0")
//                .build();
//    }
//
//    /**
//     * 认证的安全上下文
//     */
//    private List<SecurityScheme> securitySchemes() {
//        List<SecurityScheme> securitySchemes = new ArrayList<>();
//        securitySchemes.add((SecurityScheme) new ApiKey("token", "token", "header"));
//        return securitySchemes;
//    }
//
//    /**
//     * 授权信息全局应用
//     */
//    private List<SecurityContext> securityContexts() {
//        List<SecurityContext> securityContexts = new ArrayList<>();
//        securityContexts.add(SecurityContext.builder()
//                .securityReferences(defaultAuth())
//                .forPaths(PathSelectors.any()).build());
//        return securityContexts;
//    }
//
//    private List<SecurityReference> defaultAuth() {
//        AuthorizationScope authorizationScope = new AuthorizationScope("global", "accessEverything");
//        AuthorizationScope[] authorizationScopes = new AuthorizationScope[1];
//        authorizationScopes[0] = authorizationScope;
//        List<SecurityReference> securityReferences = new ArrayList<>();
//        securityReferences.add(new SecurityReference("Authorization", authorizationScopes));
//        return securityReferences;
//    }
//
//}
