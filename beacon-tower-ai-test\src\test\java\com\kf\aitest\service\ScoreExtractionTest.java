package com.kf.aitest.service;

import com.kf.aitest.service.impl.AiEvaluationServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.lang.reflect.Method;

/**
 * 评分提取测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class ScoreExtractionTest {

    @Test
    public void testScoreExtraction() throws Exception {
        AiEvaluationServiceImpl service = new AiEvaluationServiceImpl();
        
        // 使用反射访问私有方法
        Method extractScoreMethod = AiEvaluationServiceImpl.class.getDeclaredMethod("extractScore", String.class);
        extractScoreMethod.setAccessible(true);
        
        Method extractEvaluationMethod = AiEvaluationServiceImpl.class.getDeclaredMethod("extractEvaluation", String.class);
        extractEvaluationMethod.setAccessible(true);
        
        // 测试不同格式的AI响应
        String[] testResponses = {
            "**综合评估**: 各阶段数据处理整体质量存在显著缺陷，流程完整性受损，临床合规性风险高\n**综合评分**: 38",
            "**评分**: 85",
            "评分：90",
            "综合评分：75",
            "**最终评分**: 92",  // 新格式
            "**整体评估**: 数据质量良好，各阶段表现稳定\n**最终评分**: 88",  // 新格式
            "评估结果：数据处理完成，质量符合要求\n最终评分：92",  // 新格式
            "**核心内容对比分析**: 两份数据核心内容基本一致\n**综合评估**: 整体表现良好\n**评分**: 82",
            // 精简后的格式测试
            "# 临床试验报告识别阶段评估结果\n\n## 数据差异识别\n### 无差异\n如无差异则输出此项。\n\n## 扣分汇总\n- **错误类型数**: 0\n- **总扣分**: 0\n\n## 评分\n**最终评分**: 100"
        };
        
        System.out.println("=== 评分解析测试结果 ===");
        
        for (int i = 0; i < testResponses.length; i++) {
            String response = testResponses[i];
            Integer score = (Integer) extractScoreMethod.invoke(service, response);
            String evaluation = (String) extractEvaluationMethod.invoke(service, response);
            
            System.out.printf("\n测试 %d:\n", i + 1);
            System.out.printf("输入: %s\n", response);
            System.out.printf("解析评分: %s\n", score);
            System.out.printf("解析评估: %s\n", 
                evaluation.length() > 100 ? evaluation.substring(0, 100) + "..." : evaluation);
            System.out.println("---");
            
            // 验证评分不为0（除非输入为null）
            if (response != null && !response.trim().isEmpty()) {
                assert score != null && score > 0 : "评分应该大于0，但实际为: " + score;
            }
        }
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    @Test
    public void testEdgeCases() throws Exception {
        AiEvaluationServiceImpl service = new AiEvaluationServiceImpl();
        
        Method extractScoreMethod = AiEvaluationServiceImpl.class.getDeclaredMethod("extractScore", String.class);
        extractScoreMethod.setAccessible(true);
        
        // 测试边界情况
        String[] edgeCases = {
            null,
            "",
            "没有评分的响应",
            "评分: 不是数字",
            "**评分**: [85]",      // 带括号
            "综合评分: (92)",       // 带括号
            "评分：100分",          // 带"分"字
            "最终评分：0",          // 0分
            "**最终评分**: [95]",   // 新格式带括号
            "最终评分: 88"          // 新格式简单格式
        };
        
        System.out.println("=== 边界情况测试 ===");
        
        for (int i = 0; i < edgeCases.length; i++) {
            String response = edgeCases[i];
            Integer score = (Integer) extractScoreMethod.invoke(service, response);
            
            System.out.printf("测试 %d: 输入='%s' -> 评分=%s\n", i + 1, response, score);
        }
        
        System.out.println("=== 边界测试完成 ===");
    }
}
