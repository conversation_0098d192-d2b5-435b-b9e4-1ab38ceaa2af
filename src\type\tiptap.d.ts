import '@tiptap/core'

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        image: {
            /**
             * Insert an image with optional id
             */
            setImage: (options: { src: string, alt?: string, title?: string, id?: string }) => ReturnType
        }
    }

    interface ImageAttributes {
        src: string
        alt: string
        title: string
        id?: string
    }
}