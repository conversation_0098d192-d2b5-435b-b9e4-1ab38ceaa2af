# [002] AI测试模块一体化重构

**创建时间**: 2025-07-24T10:11:29+08:00  
**项目**: beacon-tower-ai-test  
**模块**: AI评估与数据对比  

## 任务目标

对AI测试模块进行一体化重构，解决提示词对比逻辑、SSE实时推送、数据库存储等核心问题。

## 核心需求

1. **提示词修改**: 修改recognize.md，忽略页眉页脚对比，专注核心内容差异
2. **其他MD检查**: 确保extraction.md、structured.md、transformer.md都是对比模式
3. **SSE实时推送**: AI返回结果需要通过SSE流式返回
4. **数据库存储**: 设计对比结果实体类，完善存储逻辑

## 技术方案：一体化重构方案

### 任务1: 重构AI评估流程
**预估时间**: 4-5小时
**状态**: [x] 已完成
**开始时间**: 2025-07-24T10:15:28+08:00
**完成时间**: 2025-07-24T10:37:12+08:00

#### 1.1 提示词系统重构
- [x] 修改recognize.md的页眉页脚评估维度
- [x] 统一四个MD文件的对比逻辑
- [x] 优化提示词模板的动态加载机制
- [ ] 添加提示词版本管理

#### 1.2 AI评估服务增强
- [x] 重构AiEvaluationServiceImpl的评估流程
- [x] 添加实时结果推送机制
- [x] 优化分片处理逻辑
- [x] 增强错误处理和重试机制

**实现要点**:
- 提示词重点关注两份数据的差异对比，而非单份数据质量检查
- AI评估过程中实时推送中间结果
- 支持评估过程的暂停和恢复
- 完善的异常处理和日志记录

### 任务2: 统一SSE事件管理
**预估时间**: 3-4小时
**状态**: [x] 已完成
**完成时间**: 2025-07-24T10:15:28+08:00

#### 2.1 SSE事件类型扩展
- [x] 新增ai-evaluation事件类型
- [x] 新增ai-result事件类型
- [x] 新增stage-complete事件类型
- [x] 优化现有progress事件

#### 2.2 实时推送机制
- [ ] 在AI评估过程中推送实时结果
- [ ] 支持分片评估结果的流式推送
- [ ] 添加评估进度的细粒度推送
- [ ] 优化消息缓存和发送策略

#### 2.3 事件管理优化
- [ ] 统一事件格式和命名规范
- [ ] 添加事件优先级管理
- [ ] 优化连接状态管理
- [ ] 增强异常处理机制

**实现要点**:
- 统一的事件格式：{type, data, timestamp, taskId}
- 支持事件的批量发送和优先级处理
- 完善的连接状态监控和自动恢复
- 详细的事件日志和监控指标

### 任务3: 完整数据库设计
**预估时间**: 3-4小时
**状态**: [x] 已完成
**完成时间**: 2025-07-24T10:15:28+08:00

#### 3.1 实体类设计
- [x] 设计TDataComparison主表实体
- [x] 设计TDataComparisonStage阶段结果表
- [x] 设计TDataComparisonDetail详细结果表
- [x] 添加对应的Mapper接口

#### 3.2 数据库表结构
- [ ] 创建数据库迁移脚本
- [ ] 设计合理的索引策略
- [ ] 添加数据完整性约束
- [ ] 考虑数据归档和清理策略

#### 3.3 服务层实现
- [ ] 实现DataComparisonStorageService
- [ ] 添加结果查询和统计功能
- [ ] 实现数据的增量更新机制
- [ ] 添加数据导出功能

**实现要点**:
- 主表存储任务基本信息和整体结果
- 阶段表存储四个阶段的详细评估结果
- 详细表存储AI评估的原始内容和分片结果
- 支持结果的版本管理和历史查询

### 任务4: 全面集成测试
**预估时间**: 2-3小时
**状态**: [ ] 待开始

#### 4.1 单元测试
- [ ] AI评估服务测试
- [ ] SSE事件管理测试
- [ ] 数据库存储测试
- [ ] 提示词模板测试

#### 4.2 集成测试
- [ ] 端到端对比流程测试
- [ ] SSE实时推送测试
- [ ] 数据库事务测试
- [ ] 异常场景测试

#### 4.3 性能测试
- [ ] 大数据量对比测试
- [ ] 并发连接测试
- [ ] 内存使用测试
- [ ] 响应时间测试

**实现要点**:
- 覆盖所有核心功能的测试用例
- 模拟各种异常场景的处理
- 验证SSE连接的稳定性和性能
- 确保数据库操作的事务一致性

## 数据库设计方案

### 主表：t_data_comparison
```sql
CREATE TABLE t_data_comparison (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    user_id VARCHAR(64) COMMENT '用户ID',
    comparison_ids TEXT COMMENT '对比ID列表(JSON)',
    overall_status VARCHAR(32) COMMENT '整体状态',
    overall_score INT COMMENT '整体评分',
    overall_ai_evaluation TEXT COMMENT '整体AI评估',
    total_duration BIGINT COMMENT '总耗时(毫秒)',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status TINYINT DEFAULT 0 COMMENT '状态:0正常,1删除',
    INDEX idx_task_id (task_id),
    INDEX idx_user_id (user_id),
    INDEX idx_create_time (create_time)
);
```

### 阶段表：t_data_comparison_stage
```sql
CREATE TABLE t_data_comparison_stage (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    comparison_id BIGINT NOT NULL COMMENT '对比主表ID',
    stage_name VARCHAR(32) NOT NULL COMMENT '阶段名称',
    stage_status VARCHAR(32) COMMENT '阶段状态',
    ai_score INT COMMENT 'AI评分',
    ai_evaluation TEXT COMMENT 'AI评估结果',
    duration BIGINT COMMENT '耗时(毫秒)',
    fetch_time DATETIME COMMENT '获取时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (comparison_id) REFERENCES t_data_comparison(id),
    INDEX idx_comparison_id (comparison_id),
    INDEX idx_stage_name (stage_name)
);
```

## 验收标准

1. **提示词优化**: recognize.md正确对比两份数据，忽略页眉页脚差异
2. **SSE实时推送**: AI评估结果能够实时推送给前端
3. **数据库存储**: 对比结果完整存储，支持查询和统计
4. **系统稳定性**: 重构后系统功能正常，性能不降低
5. **代码质量**: 代码结构清晰，注释完整，测试覆盖率高

## 风险评估

**技术风险**: 中等
- 重构范围较大，可能影响现有功能
- SSE实时推送需要仔细处理并发和异常情况
- 数据库设计需要考虑性能和扩展性

**缓解措施**:
- 分模块实施，每个模块完成后进行测试
- 保留原有代码的备份，支持快速回滚
- 充分的单元测试和集成测试
- 渐进式部署，先在测试环境验证

## 后续优化建议

1. **监控告警**: 添加AI评估性能监控和异常告警
2. **缓存优化**: 对频繁查询的结果添加缓存机制
3. **批量处理**: 支持批量对比任务的并行处理
4. **结果分析**: 添加对比结果的统计分析功能
