package com.kf.baosi.dto.jiraTestRun;

import com.kf.baosi.dto.AttachmentDTO;
import com.kf.baosi.dto.BugDTO;
import lombok.Data;
import lombok.NonNull;

import java.util.List;

/**
 * 测试步骤
 */
@Data
public class JiraTestCaseRunStepDTO {

    // 测试用例编号
    private String testCaseKey;

    // 测试用例等级
    private String level;

    // 测试用例标题
    private String summary;

    // 前提
    private String precondition;

    // 测试计划key
    private String testPlanKey;

    // 测试周期id
    private String testCycleId;

    // 测试用例id
    private String testCaseId;

    // 测试运行id
    private String parentId;

    // 测试运行总结果
    private String parentStatus;

    // 测试运行关联的bug
    private List<BugDTO> testRunBugsWrapper;

    // 测试步骤关联的bug
    private List<BugDTO> testRunStepBugsWrapper;

    // 预期结果
    private String expectedResult;

    // 数据
    private String stepData;

    // 附件数量
    private List<AttachmentDTO> testRunAttachments;

    // 实际结果
    private String actualResult;

    // 步骤
    private String step;

    // 步骤id
    private String id;

    // 步骤结果
    private String status;

    // 跨越多少行（后端计算给前端使用）
    private int rowspan;

    // 步骤属性: update更新 add新增 delete删除 后端初始化为update 之后由前端向后端传递
    private String stepProperty;

    // 排序号
    private int sortNumber;
}
