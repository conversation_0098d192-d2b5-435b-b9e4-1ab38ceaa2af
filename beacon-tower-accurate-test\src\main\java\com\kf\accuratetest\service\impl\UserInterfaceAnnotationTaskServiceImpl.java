package com.kf.accuratetest.service.impl;

import cn.hutool.core.date.DateUtil;
import com.kf.accuratetest.dao.UserInterfaceAnnotationTaskDao;
import com.kf.accuratetest.dto.CardListDTO;
import com.kf.accuratetest.dto.CodeParamDTO;
import com.kf.accuratetest.entity.UserInterfaceAnnotationTask;
import com.kf.accuratetest.service.InterfaceAnnotationService;
import com.kf.accuratetest.service.UserInterfaceAnnotationTaskService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-11-18 17:23:02
 */
@Slf4j
@Service
public class UserInterfaceAnnotationTaskServiceImpl implements UserInterfaceAnnotationTaskService {
    @Resource
    private UserInterfaceAnnotationTaskDao userInterfaceAnnotationTaskDao;

    @Resource
    private InterfaceAnnotationService tInterfaceAnnotationService;

    /**
     * 通过ID查询单条数据
     *
     * @param userId 用户ID
     * @return 实例对象
     */
    @Override
    public List<CardListDTO> queryByUserId(String userId) {
        List<UserInterfaceAnnotationTask> userInterfaceAnnotationTasks = this.userInterfaceAnnotationTaskDao.queryByUserId(userId);
        //保留最近的10条记录
        if (userInterfaceAnnotationTasks.size() > 9) {
            userInterfaceAnnotationTasks = userInterfaceAnnotationTasks.subList(0, 9);
        }
        List<CardListDTO> cardListDTOList = new ArrayList<>();
        for (UserInterfaceAnnotationTask userInterfaceAnnotationTask : userInterfaceAnnotationTasks) {
            CardListDTO cardListDTO = new CardListDTO();
            String subObj = userInterfaceAnnotationTask.getRepositoryUrl().substring(userInterfaceAnnotationTask.getRepositoryUrl().lastIndexOf("/") + 1, userInterfaceAnnotationTask.getRepositoryUrl().lastIndexOf(".git"));
            String branch = userInterfaceAnnotationTask.getBranch();
            cardListDTO.setTitle("[" + subObj + "]项目的[" + branch + "]分支");
            cardListDTO.setSubTitle("受影响的接口总数:" + tInterfaceAnnotationService.queryCount(userInterfaceAnnotationTask.getTaskId()));
            cardListDTO.setTime("分析时间：" + DateUtil.format(userInterfaceAnnotationTask.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            cardListDTOList.add(cardListDTO);
        }
        return cardListDTOList;
    }

    @Override
    public List<UserInterfaceAnnotationTask> queryRepositoryListByUserId(String userId) {
        return this.userInterfaceAnnotationTaskDao.queryByUserId(userId);
    }

    /**
     * 新增数据
     *
     * @param param  实例
     * @param userId 用户ID
     * @return taskID
     */
    @Override
    public String insert(CodeParamDTO param, String userId, String taskId) {
        UserInterfaceAnnotationTask userInterfaceAnnotationTask = new UserInterfaceAnnotationTask();
        userInterfaceAnnotationTask.setUserId(userId);
        //taskID与webSocketId保持一致
        userInterfaceAnnotationTask.setTaskId(taskId);
        userInterfaceAnnotationTask.setRepositoryUrl(param.getGitPath());
        userInterfaceAnnotationTask.setBranch(param.getDevBranch());
        userInterfaceAnnotationTask.setCreateTime(new Date());
        userInterfaceAnnotationTask.setUpdateTime(new Date());
        userInterfaceAnnotationTask.setIsDeleted(0);
        if (this.userInterfaceAnnotationTaskDao.insert(userInterfaceAnnotationTask) > 0) {
            return taskId;
        }
        return null;
    }

    /**
     * 通过主键删除数据
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String userId) {
        return this.userInterfaceAnnotationTaskDao.deleteById(userId) > 0;
    }
}
