package com.kf.uitest.controller;

import com.kf.uitest.common.RequireHeader;
import com.kf.uitest.common.ResponseDoMain;
import com.kf.uitest.dto.CreateTestCaseRequest;
import com.kf.uitest.dto.CreateTestSuiteRequest;
import com.kf.uitest.service.UiTestTreeService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/test-tree")
public class TestTreeController {

    @Resource
    private UiTestTreeService treeService;

    /**
     * 获取当前用户的测试套件和测试用例树结构
     *
     * @return 树结构的数据列表
     */
    @RequireHeader("userId")
    @GetMapping("/getTestTree")
    public ResponseDoMain getTestTree(HttpServletRequest request, @RequestParam("projectId") String projectId) {
        Long userId = Long.parseLong(request.getHeader("userId"));
        return ResponseDoMain.custom("", true, treeService.getUserTestTree(userId, projectId), 200);
    }

    @RequireHeader("userId")
    @PostMapping("/createTestSuite")
    public ResponseDoMain createTestSuite(HttpServletRequest request, @Valid @RequestBody CreateTestSuiteRequest createTestSuiteRequest) {
        Long userId = Long.parseLong(request.getHeader("userId"));
        return ResponseDoMain.custom("创建成功", true, treeService.createTestSuite(userId, createTestSuiteRequest), 200);
    }

    /**
     * 创建UI测试用例
     *
     * @return 树结构的数据列表
     */
    @RequireHeader("userId")
    @PostMapping("/createTestCase")
    public ResponseDoMain createTestCase(HttpServletRequest request, @Valid @RequestBody CreateTestCaseRequest createTestCaseRequest) {
        Long userId = Long.parseLong(request.getHeader("userId"));
        return ResponseDoMain.custom("创建成功", true, treeService.createTestCase(userId, createTestCaseRequest), 200);
    }
}
