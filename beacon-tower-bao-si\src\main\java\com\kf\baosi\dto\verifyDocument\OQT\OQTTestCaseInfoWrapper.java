package com.kf.baosi.dto.verifyDocument.OQT;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class OQTTestCaseInfoWrapper {

    // wordTemplatePlus规定的行循环的type为2
    @JsonProperty("wordTemplateType")
    private String wordTemplateType = "2";

    // 该list的key值（填充时的key的名称）
    private List<OQTTestCaseParams> value = new ArrayList<>();
}
