html,
body {
    overflow: hidden;
    margin: 0;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

button:focus {
    outline: none;
}
.rtl {
    transform: none !important;
}
@layer components {
    .transition-width {
        transition-property: width;
    }

    .flex-center {
        justify-content: center;
        align-items: center;
    }

    .min-height-10 {
        min-height: 2.5rem;
    }

    .min-width-32 {
        min-width: 8rem;
    }

    .leading-12 {
        line-height: 3rem;
    }

}
