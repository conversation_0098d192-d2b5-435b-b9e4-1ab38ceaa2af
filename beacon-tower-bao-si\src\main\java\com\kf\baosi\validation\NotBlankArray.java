package com.kf.baosi.validation;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = NotBlankArrayValidator.class)
@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface NotBlankArray {
    String message() default "数组中的元素不能为空";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}

class NotBlankArrayValidator implements ConstraintValidator<NotBlankArray, String[]> {

    @Override
    public boolean isValid(String[] value, ConstraintValidatorContext context) {
        if (value == null || value.length == 0) {
            return false; // 数组为空或null
        }
        for (String s : value) {
            if (s == null || s.trim().isEmpty()) {
                return false; // 任一元素为空或仅为空格
            }
        }
        return true;
    }
}
