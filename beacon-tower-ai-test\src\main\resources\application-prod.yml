server:
  port: 8005
  tomcat:
    max-swallow-size: -1
spring:
  application:
    name: beacon-tower-ai-test
  datasource:
    # 主数据库 - 标准Spring Boot数据源配置
    url: ***************************************************************************************************************************************
    username: root
    password: Mysql#r2368
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 30
      minimum-idle: 10
      idle-timeout: 600000
      connection-timeout: 30000
      max-lifetime: 1800000
        
    # 其他数据源配置（如果需要多数据源，需要创建对应的配置类）
    # PMP数据库
    pmp:
      jdbc-url: ********************************************************************************************************************************************
      username: root
      password: Mysql#r2368
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        maximum-pool-size: 30
        minimum-idle: 10
        idle-timeout: 600000
        connection-timeout: 30000
        max-lifetime: 1800000
        
    # 知识库数据库
    kb:
      jdbc-url: *******************************************************************************************************************************
      username: root
      password: Mysql#r2368
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        maximum-pool-size: 30
        minimum-idle: 10
        idle-timeout: 600000
        connection-timeout: 30000
        max-lifetime: 1800000
        
    # 生产数据库连接
    prod:
      jdbc-url: ***************************************************************************************************************************************
      username: root
      password: Mysql#r2368
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        maximum-pool-size: 30
        minimum-idle: 10
        idle-timeout: 600000
        connection-timeout: 30000
        max-lifetime: 1800000
        
    # PMC医学编码数据库
    pmc:
      jdbc-url: ************************************************************************************************************************************
      username: dicReader
      password: reader@123!
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        maximum-pool-size: 10
        minimum-idle: 2
        idle-timeout: 600000
        connection-timeout: 30000
        max-lifetime: 1800000

# AI服务配置
ai:
  azure:
    gpt:
      concurrent: 500
  claude:
    concurrent: 500
  reason-claude:
    concurrent: 500
  anthropic:
    version: bedrock-2023-05-31
  # 火山引擎ARK API配置
  ark:
    # API端点
    api-url: https://ark.cn-beijing.volces.com/api/v3/chat/completions
    # API密钥
    api-key: c854450c-60c6-451c-8bd7-d0ada82a7519
    # 模型ID
    model: ep-20250723134959-88zhw
    # 请求超时时间（毫秒）
    timeout: 30000
    # 最大重试次数
    max-retries: 3

# Langfuse配置（生产环境）
langfuse:
  secret-key: sk-lf-3fc076f0-5545-491e-b225-8bbb458ae8cc
  public-key: pk-lf-57367e6c-f87e-4e43-a4e2-4606b49a60ad
  host: http://**********:3000

# 文件上传配置
upload:
  file:
    path: /home/<USER>/uploadFile/pmp

# Python相关配置
python:
  malloc: malloc
  malloc-trim-threshold: 100000

# 环境标识
environment: prod

# 日志配置
logging:
  level:
    com.kf.aitest: INFO
    org.springframework: WARN
    root: WARN
