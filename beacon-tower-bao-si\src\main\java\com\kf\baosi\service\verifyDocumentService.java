package com.kf.baosi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kf.baosi.dto.VerifyDocumentListDTO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

public interface verifyDocumentService {

    /**
     * 查询验证文档列表
     */
    IPage<VerifyDocumentListDTO> getVerifyDocumentList(String userId, String fileName, String fileType, String status, int current, int size);


    /**
     * 进行文档填充，获得taskId
     *
     * @param params 参数
     * return taskId 任务id
     */
    String wordTemplatePlusHandle(Map<String, Object> params);


    /**
     * 使用taskId查询文件信息
     *
     * @param params 参数
     * @return taskId 任务id
     */
    String wordTemplatePlusTaskInfo(Map<String, Object> params);

    /**
     * 下载文件
     *
     * @param fileId 文件id
     */
    void getFile(String fileId, String fileName,HttpServletResponse response);

    /**
     * 压缩并下载文件
     *
     * @param file 文件
     * response
     */
    void fileCompress(MultipartFile file,HttpServletResponse response);


    /**
     * 获取预设字段
     */
    Map<String, String> getVerifyField(String objectType, String verifyDocType);

}
