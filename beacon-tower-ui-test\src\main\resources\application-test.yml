server:
  port: 8003
  tomcat:
    max-swallow-size: -1
file:
  upload:
    fs-url: http://inner.test.com/api/fs-service
    dir: d:/file
test:
  executor:
    core-pool-size: 5
    max-pool-size: 10
    queue-capacity: 25
spring:
  application:
    name: beacon-tower-ui-test
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
  datasource:
    #   数据源基本配置
    username: root
    password: zc1234
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://************:3306/k_base?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false&allowPublicKeyRetrieval=true
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      validation-query: SELECT 1
  servlet:
    multipart:
      max-file-size: 2GB
      max-request-size: 2GB
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  configuration:
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
rabbit:
  connections:
    - name: test-mq-001
      host: test-mq-pub-01-sh2b.taimei.com
      port: 5672
      username: MjpyYWJiaXRtcS1zZXJ2ZXJsZXNzLWNuLWxtcjN4YWNlazAxOkxUQUk1dEdRcXh0dVhlVmtLNVNtSm9NNA==
      password: MzMxNERENDFDOTU5RDE4NzFGMzhDQzAxREU3QkJENDUyNjBENkM2OToxNzI3MjQ5MDAxNjA3
      virtual-host: /

