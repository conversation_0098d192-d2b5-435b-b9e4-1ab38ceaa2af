package com.kf.baosi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.kf.baosi.dao.TXMindMapper;
import com.kf.baosi.dto.*;
import com.kf.baosi.dto.JiraSynapseRT.*;
import com.kf.baosi.dto.jiraTestRun.*;
import com.kf.baosi.dto.response.CreateBugResponse;
import com.kf.baosi.dto.response.CreateTestCaseResponse;
import com.kf.baosi.dto.response.GetTestSuitesResponse;
import com.kf.baosi.dto.testCaseRuns.TestCaseForReLoad;
import com.kf.baosi.dto.testCaseRuns.TestCycleForReLoad;
import com.kf.baosi.dto.testCaseRuns.TestPlanForReLoad;
import com.kf.baosi.dto.verifyDocument.OQP.OQPTestCaseListParams;
import com.kf.baosi.dto.verifyDocument.OQP.OQPTestCaseListWrapper;
import com.kf.baosi.dto.verifyDocument.OQP.OQPWrapper;
import com.kf.baosi.dto.verifyDocument.OQR.*;
import com.kf.baosi.dto.verifyDocument.OQT.*;
import com.kf.baosi.entity.*;
import com.kf.baosi.enums.JiraIssueFieldEnum;
import com.kf.baosi.enums.StepProperty;
import com.kf.baosi.enums.VerifyDocument;
import com.kf.baosi.service.FileService;
import com.kf.baosi.service.JiraService;
import com.kf.baosi.service.verifyDocumentService;
import com.kf.baosi.utils.ExclUtil;
import com.kf.baosi.utils.JsonUtil;
import com.kf.baosi.utils.WordTemplatePlusParamsUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.kf.baosi.enums.VerifyDocument.getVerifyDocument;
import static com.kf.baosi.utils.JsonUtil.*;
import static com.kf.baosi.utils.ImageCompressor.compressImages;

@Slf4j
@Service
public class JiraServiceImpl implements JiraService {

    private static final String JIRA_URL = "http://jira.taimei.com";

    // 登录接口
    private static final String LOGIN_URL = JIRA_URL + "/rest/gadget/1.0/login";

    // 令牌接口
    private static final String TOKEN_URL = JIRA_URL + "/rest/pat/latest/tokens";

    // 用户信息接口(通过token查询)
    private static final String USER_INFO_URL = JIRA_URL + "/rest/api/2/myself";

    // 用户信息接口（通过用户名查询）
    private static final String USER_INFO_BY_NAME_URL = JIRA_URL + "/rest/api/2/user?username=%s";

    // 获取所有项目
    private static final String GET_ALL_PROJECT = JIRA_URL + "/rest/api/2/project";

    // 获取项目下的所有版本 projectIdOrKey
    private static final String GET_ALL_VERSION = JIRA_URL + "/rest/api/2/project/%s/versions";

    // 使用jql查询issue
    private static final String URL_ISSUE = JIRA_URL + "/rest/api/2/search";

    // 测试用例集创建接口
    private static final String CREATE_SUITE_URL = JIRA_URL + "/rest/synapse/latest/public/testSuite/createTestSuite";

    // 创建issue
    private static final String CREATE_ISSUE = JIRA_URL + "/rest/api/2/issue";

    // 删除issue issueIdOrKey
    private static final String DELETE_ISSUE = JIRA_URL + "/rest/api/2/issue/%s";

    // 向测试用例添加测试步骤 testCaseIssueKey
    private static final String ADD_TEST_STEP = JIRA_URL + "/rest/synapse/latest/public/testCase/%s/addSteps";

    // 将现有测试用例链接到测试套件
    private static final String LINK_TEST_CASE_TO_SUITE = JIRA_URL + "/rest/synapse/latest/public/testSuite/linkTestCase";

    // 项目中获取测试用例集 projectKey
    private static final String GET_TEST_SUITE = JIRA_URL + "/rest/synapse/latest/public/requirements/%s/requirementsSuites";

    // 获取测试计划下的测试周期列表 testPlanIssueKey
    private static final String GET_TEST_CYCLES = JIRA_URL + "/rest/synapse/latest/public/testPlan/%s/cycles";

    // 测试用例添加到测试计划 testPlanIssueKey
    private static final String ADD_TEST_CASE_TO_PLAN = JIRA_URL + "/rest/synapse/latest/public/testPlan/%s/addMembers";

    // 创建测试周期 testPlanIssueKey
    private static final String CREATE_TEST_CYCLE = JIRA_URL + "/rest/synapse/latest/public/testPlan/%s/addCycle";

    // 在测试周期中添加/删除测试用例（基于测试计划中的测试用例）testPlanIssueKey
    private static final String ADD_TEST_CASE_TO_CYCLE = JIRA_URL + "/rest/synapse/latest/public/testPlan/%s/addMembersToTestCycle";

    // 获取测试用例关联的测试周期
    private static final String GET_TEST_CASE_CYCLE = JIRA_URL + "/rest/synapse/latest/public/testCase/%s/getTestCycle";

    // 向一个需求关联测试用例 requirementIssueKey
    private static final String LINK_TEST_CASE_TO_REQUIREMENT = JIRA_URL + "/rest/synapse/latest/public/requirement/%s/linkTestCase";

    // 更新一个测试运行的总结果 testPlanIssueKey cycleName
    private static final String UPDATE_TEST_RUN = JIRA_URL + "/rest/synapse/latest/public/testPlan/%s/cycle/%s/updateTestRun";

    // 更新一个测试运行的总结果
    private static final String UPDATE_TEST_RUN_RESULT = JIRA_URL + "/rest/synapse/latest/public/testRun/update";

    // 更新每个测试运行的步骤结果
    private static final String UPDATE_TEST_RUN_STEP = JIRA_URL + "/rest/synapse/latest/public/testRun/updateStep";

    // 获取测试计划下的测试周期的所有测试运行，使用测试周期的name查询
    private static final String GET_TEST_RUNS = JIRA_URL + "/rest/synapse/latest/public/testPlan/%s/cycle/%s/testRuns";

    // 获取测试计划下的测试周期的所有测试运行，使用测试周期的ID查询
    private static final String GET_TEST_RUNS_BY_CYCLE_ID = JIRA_URL + "/rest/synapse/latest/public/testPlan/%s/cycle/%s/testRunsByCycleId";

    // 获得单个测试运行的所有数据 runID
    private static final String GET_TEST_RUN = JIRA_URL + "/rest/synapse/latest/public/testRun/%s";

    // 重新加载测试用例
    private static final String RELOAD_TEST_RUNS = JIRA_URL + "/secure/ReloadTestRuns.jspa?tpId=%s&cycleId=%s&selectedRuns=%s";

    // 使用运行id获得关联的测试缺陷
    private static final String GET_GUG = JIRA_URL + "/rest/synapse/latest/public/testRun/defects/%s";

    // 将Bug链接到测试运行
    private static final String LINK_BUG_TO_TEST_RUN = JIRA_URL + "/rest/synapse/latest/public/testPlan/%s/cycle/%s/linkBugToTestRun";

    // 从测试用例获取关联的测试计划列表
    private static final String GET_TEST_PLANS_FOR_TEST_CASE = JIRA_URL + "/rest/synapse/latest/public/testCase/%s/linkedTestPlans";

    // 从测试用例获取关联的需求 testCaseIssueKey
    private static final String GET_REQUIREMENTS_FOR_TEST_CASE = JIRA_URL + "/rest/synapse/latest/public/testCase/%s/linkedRequirements";

    private static final String JQL_TEST_PLANS_FOR_TESTER = "issue in testPlansForTester(\"%s\") AND status != closed";

    private static final String JQL_TEST_PLANS_FOR_PROJECT = "project = %s AND issuetype = 测试计划 AND status != closed ORDER BY issuekey DESC";

    // 返回测试执行中附件的详细信息 runId
    private static final String GET_TEST_RUN_ATTACHMENT_DETAILS = JIRA_URL + "/rest/synapse/latest/public/attachment/%s/getAttachmentDetails";

    // 返回Issue的指定字段 Issue key
    private static final String GET_FIELD = JIRA_URL + "/rest/api/2/issue/%s";

    // 修改测试周期的状态testPlanIssueKey cycleName action action：Start Complete Abort Resume
    private static final String UPDATE_CYCLE_STATUS = JIRA_URL + "/rest/synapse/latest/public/testPlan/%s/cycle/%s/wf/%s";

    // 从测试执行中删除测试附件 runId attachmentId
    private static final String DELETE_TEST_RUN_ATTACHMENT = JIRA_URL + "/rest/synapse/latest/public/attachment/%s/deleteAttachment/%s";

    // 通过ID更新测试用例的步骤信息 testCaseIssueKey
    private static final String UPDATE_TEST_CASE_STEP = JIRA_URL + "/rest/synapse/latest/public/testCase/%s/updateStep";

    // 根据测试用例中的序号删除步骤 testCaseIssueKey stepNo
    private static final String DELETE_TEST_STEP_BY_INDEX = JIRA_URL + "/rest/synapse/latest/public/testCase/%s/deleteStepBySequenceNo/%s";

    // 创建测试用例
    private static final String CREATE_TEST_CASE = JIRA_URL + "/rest/synapse/latest/public/testCase/create/createTestCase";

    // 获取项目中所有的测试用例集 projectKey
    private static final String GET_TEST_SUITES = JIRA_URL + "/rest/synapse/latest/public/testSuite/%s/testSuites";

    // 测试机步骤链接测试用例
    private static final String ADD_TEST_RUN_STEP_BUGS = JIRA_URL + "/rest/synapse/latest/testRunBug/addTestRunStepBugs";

    // 获得项目的所有模块 projectKey
    private static final String GET_COMPONENTS = JIRA_URL + "/rest/api/2/project/%s/components";

    // 查询jira的所有用户
    private static final String GET_ALL_USERS = JIRA_URL + "/rest/api/2/user/search?username=.&maxResults=1000";

    // 上传jira缺陷的附件
    private static final String UPLOAD_BUG_ATTACHMENT = JIRA_URL + "/rest/synapse/latest/public/attachment/uploadAttachment";

    // Issue添加附件 issueIdOrKey
    private static final String ADD_ATTACHMENT = JIRA_URL + "/rest/api/latest/issue/%s/attachments";

    private static final String ADD_TEST_RUN_ATTACHMENT = JIRA_URL + "/rest/synapse/latest/public/attachment/%s/testrun";

    // 获得测试运行的所有缺陷 runId
    private static final String GET_TEST_RUN_DEFECTS = JIRA_URL + "/rest/synapse/latest/public/testRun/defects/%s";

    @Resource
    private RestTemplate restTemplate;

    @Resource
    FileService fileService;

    @Resource
    TXMindMapper xMindDAO;

    @Resource
    verifyDocumentService verifyDocumentService;

    @Value("${file.upload.dir}")
    private String uploadFilePath;

    private final ConcurrentMap<String, SseEmitter> emitters = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, Double> taskProgress = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, Boolean> methodCompleted = new ConcurrentHashMap<>();
    private final ScheduledExecutorService executorService = Executors.newScheduledThreadPool(5);

    public SseEmitter getEmitter(String taskId) {
        return emitters.get(taskId);
    }

    public void startProgressUpdate(String taskId) {
        executorService.scheduleAtFixedRate(() -> {
            Double progress = taskProgress.get(taskId);
            if (progress != null) {
                SseEmitter emitter = emitters.get(taskId);
                if (emitter != null) {
                    try {
                        emitter.send(SseEmitter.event().name("progress").data(progress));
                    } catch (Exception e) {
                        log.error("发送SSE时发生错误: {}", e.getMessage(), e);
//                        cleanup(taskId);
                        // 通知客户端错误发生并且关闭连接
                        emitter.completeWithError(e);
                        // 主动关闭连接
                        cleanup(taskId);
                    }
                }
            }

            if (Boolean.TRUE.equals(methodCompleted.get(taskId))) {
                completeTask(taskId);
            }
        }, 0, 2, TimeUnit.SECONDS);
    }

    private void completeTask(String taskId) {
        SseEmitter emitter = emitters.get(taskId);
        if (emitter != null) {
            try {
                emitter.send(SseEmitter.event().name("completed").data("任务成功完成。"));
                emitter.complete();
            } catch (IOException e) {
                log.error("完成SSE时发生错误: {}", e.getMessage(), e);
            } finally {
                cleanup(taskId);
            }
        }

    }

    private void cleanup(String taskId) {
        emitters.remove(taskId);
        taskProgress.remove(taskId);
        methodCompleted.remove(taskId);
    }


    @Override
    public TUserToken getJiraUserToken(String userId) {
        TUserToken userToken = new TUserToken();
        userToken.setUserId(userId);
        return userToken.selectById();
    }

    private TUserToken getJiraUserInfo(String userId, String jiraUserName) {
        TUserToken userToken = new TUserToken();
        QueryWrapper<TUserToken> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId).eq("jira_user_name", jiraUserName);
        return userToken.selectOne(queryWrapper);
    }

    @Override
    public boolean checkJiraToken(TUserToken userTokenInfo) {
        if (userTokenInfo == null || userTokenInfo.getJiraToken().isEmpty()) {
            return false;
        }
        HttpHeaders headers = buildJiraHeader(userTokenInfo.getJiraToken());
        HttpEntity<String> entity = new HttpEntity<>(headers);
        try {
            ResponseEntity<String> response = restTemplate.exchange(USER_INFO_URL, HttpMethod.GET, entity, String.class);
            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            log.error("令牌验证发生错误:{}", e.getMessage());
            return false;
        }
    }

    /**
     * 根据jiraToken获取用户姓名 xuewen.wang
     */
    private String getJiraNameByJiraToken(HttpHeaders headers) {
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(USER_INFO_URL, HttpMethod.GET, entity, String.class);
        return getFieldFromJson(response.getBody(), "name");
    }

    /**
     * 根据jiraToken获取用户姓名 姓名
     */
    private String getDisplayNameByJiraToken(String jiraToken) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(USER_INFO_URL, HttpMethod.GET, entity, String.class);
        return getFieldFromJson(response.getBody(), "displayName");
    }

    /**
     * 根据jiraToken获取用户信息
     */
    private JiraUserDTO getUserInfoByJiraToken(HttpHeaders headers) {
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<JiraUserDTO> response = restTemplate.exchange(USER_INFO_URL, HttpMethod.GET, entity, JiraUserDTO.class);
        return response.getBody();
    }

    /**
     * 根据用户名获取用户姓名，获取失败就返回当前用户名
     *
     * @param jiraToken    jiraToken
     * @param jiraUserName 用户名
     * @return 用户姓名
     */
    private String getUserInfoByName(String jiraToken, String jiraUserName) {
        try {
            HttpHeaders headers = buildJiraHeader(jiraToken);
            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<String> body = restTemplate.exchange(buildUrl(USER_INFO_BY_NAME_URL, jiraUserName), HttpMethod.GET, entity, String.class);
            log.info("获取用户信息返回结果:{}", body.getBody());
            return Optional.ofNullable(body.getBody()).map(json -> {
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    JsonNode jsonObject = objectMapper.readTree(json);
                    return jsonObject.get("displayName").asText();
                } catch (Exception e) {
                    throw new RuntimeException("JSON 解析失败", e);
                }
            }).orElseThrow(() -> new RuntimeException("获取用户信息失败"));
        } catch (Exception e) {
            return jiraUserName;
        }
    }

    /**
     * 登录jira
     *
     * @param loginJiraDTO 登录信息
     * @param userId       用户id
     * @return JiraToken
     */
    @Override
    public String login(LoginJiraDTO loginJiraDTO, String userId) {
        TUserToken userToken = getJiraUserInfo(userId, loginJiraDTO.getUserName());
        if (userToken != null && checkJiraToken(userToken)) {
            return userToken.getJiraToken();
        }
        try {
            MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
            map.add("os_username", loginJiraDTO.getUserName());
            map.add("os_password", loginJiraDTO.getPassWord());

            HttpHeaders headers = new HttpHeaders();
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(LOGIN_URL, request, String.class);

            return Optional.ofNullable(response.getBody()).map(json -> {
                        try {
                            ObjectMapper objectMapper = new ObjectMapper();
                            JsonNode jsonObject = objectMapper.readTree(json);
                            return jsonObject;
                        } catch (Exception e) {
                            throw new RuntimeException("JSON 解析失败", e);
                        }
                    }).filter(jsonObject -> jsonObject.has("loginSucceeded") && jsonObject.get("loginSucceeded").asBoolean())
                    .map(jsonObject -> createJiraToken(loginJiraDTO.getUserName(), loginJiraDTO.getPassWord(), userId))
                    .orElseThrow(() -> new RuntimeException("Jira登录失败!"));

        } catch (Exception e) {
            throw new RuntimeException("Jira登录失败!", e);
        }
    }

    @Override
    public Map<String, Object> getXMindFileInfo(String fileId) {
        Map<String, Object> map = new HashMap<>();
//        List<Map<String, String>> allProjects = jiraService.getAllProject(loginJiraDTO.getUserName(), loginJiraDTO.getPassWord());
//        map.put("allProjects",allProjects);
        TFile fileInfo = fileService.queryFileById(fileId);
        File file = new File(fileInfo.getFilePath());
        //判断file是否为空
        if (!file.exists()) {
            throw new RuntimeException("文件不存在");
        }
        List<TestCase> testCaseList = csvToTestCaseEntity(file, "", false);
        //项目key
        String projectKey = testCaseList.get(0).getFields().getProject().getKey();
        //判断projectKey是英文，如果是英文转换成大写
        if (projectKey.matches("^[A-Za-z]+$")) {
            map.put("projectKey", projectKey.toUpperCase());
        }
        //获取所有版本号
        List<FixVersion> fixVersions = testCaseList.get(0).getFields().getFixVersions();
        map.put("fixVersions", fixVersions);
        return map;
    }

    private String extractCookies(ResponseEntity<String> response) {
        return Objects.requireNonNull(response.getHeaders().get("Set-Cookie")).stream().map(data -> data.substring(0, data.indexOf(";"))).collect(Collectors.joining("; "));
    }

    private String createJiraToken(String username, String password, String userId) {
        TUserToken userToken = getJiraUserInfo(userId, username);
        if (userToken != null && checkJiraToken(userToken)) {
            return userToken.getJiraToken();
        }
        String authStr = String.format("%s:%s", username, password);
        String jsonBody = """
                {"name": "token"}
                """;
        String encodedAuthStr = Base64.getEncoder().encodeToString(authStr.getBytes());
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Basic " + encodedAuthStr);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(jsonBody, headers);
        ResponseEntity<String> response = restTemplate.exchange(TOKEN_URL, HttpMethod.POST, request, String.class);
        // 获取 rawToken 字段
        Optional<JsonNode> optionalJsonObject = Optional.ofNullable(response.getBody()).map(json -> {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                return objectMapper.readTree(json);
            } catch (Exception e) {
                throw new RuntimeException("JSON 解析失败", e);
            }
        });
        String rawToken = optionalJsonObject
                .map(jsonNode -> jsonNode.has("rawToken") ? jsonNode.get("rawToken").asText() : null)
                .orElse("Default Token");
        if (userToken == null) {
            userToken = new TUserToken();
        }
        userToken.setUserId(userId);
        userToken.setJiraUserName(username);
        userToken.setJiraToken(rawToken);
        userToken.insertOrUpdate();
        return rawToken;
    }

    @Override
    public List<ProjectDTO> getAllProject(String jiraToken) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        // 使用 exchange 并直接将响应反序列化为 List<ProjectDTO>
        ParameterizedTypeReference<List<ProjectDTO>> responseType = new ParameterizedTypeReference<>() {
        };
        ResponseEntity<List<ProjectDTO>> response = restTemplate.exchange(GET_ALL_PROJECT, HttpMethod.GET, entity, responseType);
        // 获取响应体
        return response.getBody();
    }


    @Override
    public List<Map<String, String>> getAllPlanForProject(String jiraToken, String projectKey) {
        HttpHeaders headers = buildJiraHeader(jiraToken);

        // 构建请求 JSON
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode requestJson = objectMapper.createObjectNode();
        requestJson.put("jql", String.format(JQL_TEST_PLANS_FOR_PROJECT, projectKey));

        // 构建 HttpEntity
        HttpEntity<String> entity = new HttpEntity<>(requestJson.toString(), headers);

        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.postForEntity(URL_ISSUE, entity, String.class);

        // 解析响应
        List<Map<String, String>> resultList = new ArrayList<>();
        try {
            JsonNode responseObject = objectMapper.readTree(response.getBody());

            // 使用 Optional 避免 NullPointerException
            JsonNode issues = Optional.ofNullable(responseObject.get("issues")).orElse(objectMapper.createArrayNode());

            // 遍历 issues
            for (JsonNode issue : issues) {
                Map<String, String> issueMap = Map.of(
                        "key", issue.get("key").asText(),
                        "summary", issue.get("fields").get("summary").asText()
                );
                resultList.add(issueMap);
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        return resultList;
    }


    /**
     * 根据项目key获取版本列表
     *
     * @param projectKey 项目key
     * @return 版本列表
     */
    @Override
    public List<String> getAllVersionForProject(String jiraToken, String projectKey) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        try {
            // 确保泛型类型正确
            ResponseEntity<JiraProjectVersion[]> response = restTemplate.exchange(buildUrl(GET_ALL_VERSION, projectKey), HttpMethod.GET, entity, JiraProjectVersion[].class);
            JiraProjectVersion[] versionsArray = response.getBody();

            if (versionsArray == null) {
                return Collections.emptyList();
            }

            return Arrays.stream(versionsArray)
                    .sorted(Comparator.comparingInt((JiraProjectVersion v) -> {
                        try {
                            return Integer.parseInt(v.getId());
                        } catch (NumberFormatException e) {
                            return Integer.MIN_VALUE;
                        }
                    }).reversed())
                    .map(JiraProjectVersion::getName)
                    .collect(Collectors.toList());
        } catch (RestClientException e) {
            throw new RuntimeException("获取版本列表失败", e);
        }
    }


    private HttpHeaders buildJiraHeader(String jiraToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + jiraToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }

    private HttpHeaders buildJiraHeaderMultiPart(String jiraToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + jiraToken);
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        return headers;
    }

    public void setEmitter(String taskId) {
        emitters.put(taskId, new SseEmitter(600000L));
    }

    /**
     * 新建新的测试用例集
     *
     * @param jiraToken    jiraToken
     * @param testSuitPath 测试用例集路径,重名覆盖 (需要创建子集则用"\"分割路径 eg: 父用例集名\子用例集名\孙用例集名)
     * @param projectKey   项目名称
     */
    @Override
    public ResponseEntity<String> createTestSuite(String jiraToken, String testSuitPath, String projectKey) {
        HttpHeaders headersWithCookies = new HttpHeaders();
        headersWithCookies.add("Cookie", jiraToken);
        String dataJson = """
                {
                    "testSuitePath": "%s",
                    "projectKey": "%s"
                }
                """;
        String suiteData = String.format(dataJson, testSuitPath, projectKey);
        HttpEntity<String> request = new HttpEntity<>(suiteData, headersWithCookies);
        return restTemplate.postForEntity(CREATE_SUITE_URL, request, String.class);
    }

    /**
     * 创建测试用例
     * 如果测试用例集不存在，将自动创建一个新的测试用例集
     *
     * @param jiraToken        jiraToken
     * @param taskId           任务id
     * @param csvFileToJiraDTO 参数
     */
    @Override
    public void createTestCaseTask(String jiraToken, String taskId, CSVFileToJiraDTO csvFileToJiraDTO) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        TFile fileInfo = fileService.queryFileById(csvFileToJiraDTO.getFileId());
        File file = new File(fileInfo.getFilePath());
        if (!file.exists()) {
            log.error("文件不存在");
            return;
        }
        String jiraUserName = getJiraNameByJiraToken(headers);
        List<TestCase> testCaseList = csvToTestCaseEntity(file, jiraUserName, csvFileToJiraDTO.isMergeStepAndResult());
        String projectKey = csvFileToJiraDTO.getProjectKey();
        List<FixVersion> fixVersions = getFixVersionList(csvFileToJiraDTO.getFixVersion(), jiraToken, projectKey, testCaseList.get(0).getFields().getFixVersions().get(0).getName());
        if (fixVersions.isEmpty()) {
            log.error("fixVersions为空");
            return;
        }
        int total = testCaseList.size();
        AtomicInteger count = new AtomicInteger(0);
        ConcurrentLinkedQueue<Throwable> exceptions = new ConcurrentLinkedQueue<>();  // 线程安全的异常集合
        CopyOnWriteArrayList<String> testCaseKeys = new CopyOnWriteArrayList<>();  // 线程安全的集合

        // 获取测试计划
        String testPlanIssueKey = null;
        // 获取测试周期
        TestCycle testCycle = null;

        try {
            if (StrUtil.isNotBlank(csvFileToJiraDTO.getTestPlan())) {
                testPlanIssueKey = getTestPlanKey(jiraToken, csvFileToJiraDTO.getTestPlan(), jiraUserName, projectKey, fixVersions);
            }
            if (StrUtil.isNotBlank(csvFileToJiraDTO.getTestCycle())) {
                testCycle = getTestPlanCycle(jiraToken, testPlanIssueKey, csvFileToJiraDTO.getTestCycle());
            }
        } catch (Exception e) {
            log.error("获取测试计划或测试周期时发生异常: ", e);
            exceptions.add(e);
        }

        ExecutorService executorService = Executors.newFixedThreadPool(10);  // 设置线程池大小为10

        String finalTestPlanIssueKey = testPlanIssueKey;
        TestCycle finalTestCycle = testCycle;
        List<CompletableFuture<Void>> futures = testCaseList.stream().map(testCase -> CompletableFuture.runAsync(() -> {
            try {
                if (StrUtil.isNotBlank(projectKey)) {
                    testCase.getFields().getProject().setKey(projectKey);
                }
                if (CollUtil.isNotEmpty(fixVersions)) {
                    testCase.getFields().setFixVersions(fixVersions);
                } else {
                    log.info("fixVersions为空");
                    return;
                }
                if (!checkTestCase(testCase)) {
                    log.error("测试用例数据不完整:{}", testCase);
                    return;
                }

                // 创建测试用例
                String testCaseKey = createTest(headers, testCase);

                // 添加测试步骤
                addTestStep(headers, testCaseKey, testCase.getTestCaseSteps());

                LinkToTestSuite linkToTestSuite = new LinkToTestSuite();
                linkToTestSuite.setProjectKey(projectKey);
                linkToTestSuite.setTestSuitePath(testCase.getTestSuitePath());
                linkToTestSuite.setTestCaseKeys(List.of(testCaseKey));

                linkTestCaseToSuite(headers, linkToTestSuite);
                linkTestCaseToRequirement(headers, testCase.getReqNum(), testCaseKey);

                if (finalTestPlanIssueKey != null) {
                    addTestCaseToPlan(jiraToken, finalTestPlanIssueKey, testCaseKey);
                }
                if (finalTestCycle != null) {
                    addTestCaseToCycle(headers, finalTestPlanIssueKey, finalTestCycle.getId(), finalTestCycle.getName(), testCaseKey);
                }

                log.info("创建测试用例成功:{}", testCaseKey);
                testCaseKeys.add(testCaseKey);

                int progress = count.incrementAndGet();
                double rawProgress = (double) progress / total * 100;
                taskProgress.put(taskId, Math.round(rawProgress * 100.0) / 100.0);

            } catch (Exception e) {
                // 捕获并记录异常
                log.error("处理测试用例时发生异常: {}", testCase, e);
                exceptions.add(e);
            }
        }, executorService)).toList();

        // 等待所有任务完成
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allOf.join();  // 等待所有并发任务完成
        // 关闭线程池
        executorService.shutdown();
        try {
            // 等待最长10分钟
            if (!executorService.awaitTermination(10, TimeUnit.MINUTES)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
        }

        UpdateWrapper<TXMind> updateWrapper = new UpdateWrapper<>();
        // 如果存在异常,统一处理
        if (!exceptions.isEmpty()) {
            log.error("部分测试用例处理失败，失败数量: {}", exceptions.size());
            // 处理异常集合，如记录到数据库或发送通知
            exceptions.forEach(ex -> log.error("任务{}异常：", taskId, ex));
            updateWrapper.eq("task_id", taskId).set("is_complete", 4).set("test_case_id", String.join(", ", testCaseKeys)).set("update_time", new Date()).set("error_message", exceptions.stream().map(Throwable::getMessage).collect(Collectors.joining("\n")));
        } else {
            updateWrapper.eq("task_id", taskId).set("is_complete", 2).set("test_case_id", String.join(", ", testCaseKeys)).set("update_time", new Date());
        }
        xMindDAO.update(null, updateWrapper);
        methodCompleted.put(taskId, true);
        log.info("创建测试用例完成");
    }

    /**
     * 创建测试用例
     *
     * @param headers  请求头，包含jiraToken
     * @param testCase 测试用例
     * @return 测试用例key
     */
    private String createTest(HttpHeaders headers, TestCase testCase) {
        String testCaseJson = toPrettyJson(testCase);
        log.info("创建测试用例参数:{}", testCaseJson);
        HttpEntity<String> request = new HttpEntity<>(testCaseJson, headers);
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(CREATE_ISSUE, request, String.class);
            log.info("创建测试用例返回结果:{}", response.getBody());
            return Objects.requireNonNull(getFieldFromJson(response.getBody(), "key"));
        } catch (RestClientException e) {
            throw new RuntimeException("创建测试用例失败:{}", e);
        }
    }

    private boolean checkTestCase(TestCase testCase) {
        // 定义一个匹配任意汉字的正则表达式
        Pattern chineseCharacterPattern = Pattern.compile("[\\u4e00-\\u9fa5]");

        // 检查测试用例是否有效
        boolean isValid = testCase.getTestCaseSteps() != null &&
                !testCase.getTestCaseSteps().isEmpty() &&
                testCase.getFields().getProject() != null &&
                testCase.getFields().getSummary() != null &&
                testCase.getFields().getIssuetype() != null;

        // 额外检查：测试用例等级不能包含汉字
        if (isValid) {
            String caseLevelValue = testCase.getFields().getCustomfield_12851().getValue();
            isValid = !chineseCharacterPattern.matcher(caseLevelValue).find();
        }

        return isValid;
    }

    private List<FixVersion> getFixVersionList(List<String> fixVersionList, String jiraToken, String projectKey, String versionForFile) {
        List<String> fixVersionForProject = getAllVersionForProject(jiraToken, projectKey);
        List<String> versionsToProcess = (fixVersionList == null || fixVersionList.isEmpty())
                ? Collections.singletonList(versionForFile)
                : fixVersionList;
        Set<String> fixVersionForProjectSet = new HashSet<>(fixVersionForProject);
        return versionsToProcess.stream()
                .filter(fixVersionForProjectSet::contains)
                .map(version -> {
                    FixVersion fixVersion = new FixVersion();
                    fixVersion.setName(version);
                    return fixVersion;
                })
                .collect(Collectors.toList());
    }

    private TestCycle findMatchingCycle(List<TestCycle> testCycles, String target) {
        return testCycles.stream().filter(testCycle -> testCycle.getName().equals(target) || testCycle.getId().equals(target)).findFirst().orElse(null);
    }

    /**
     * 向测试用例添加测试步骤
     *
     * @param headers       jiraToken
     * @param caseKey       测试用例key
     * @param testCaseSteps 测试步骤
     */
    public void addTestStep(HttpHeaders headers, String caseKey, List<TestCaseStep> testCaseSteps) {
        HttpEntity<String> request = new HttpEntity<>(toJson(testCaseSteps), headers);
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(buildUrl(ADD_TEST_STEP, caseKey), request, String.class);
            log.info("添加测试步骤返回结果:{}", response.getBody());
        } catch (RestClientException e) {
            throw new RuntimeException("添加测试步骤失败", e);
        }
    }

    /**
     * 将现有测试用例链接到测试集
     *
     * @param headers            jiraToken
     * @param testCaseToSuiteMap 测试用例集与测试用例的映射
     * @param projectKey         项目代号
     * @return 添加成功/失败 {"message":"Success"}
     */
    public boolean linkTestCaseListToSuite(HttpHeaders headers, Map<String, List<String>> testCaseToSuiteMap, String projectKey, String taskId, int importTestSuite) {
        List<LinkToTestSuite> linkToTestSuiteList = testCaseToSuiteMap.entrySet().stream().map(entry -> {
            LinkToTestSuite link = new LinkToTestSuite();
            link.setProjectKey(projectKey);
            link.setTestSuitePath(entry.getKey());
            link.setTestCaseKeys(entry.getValue());
            return link;
        }).toList();
        int total = linkToTestSuiteList.size();
        int count = 0;
        double initProgress = taskProgress.get(taskId);
        for (LinkToTestSuite link : linkToTestSuiteList) {
            String dataJson = toJson(link);
            HttpEntity<String> request = new HttpEntity<>(dataJson, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(LINK_TEST_CASE_TO_SUITE, request, String.class);
            if (!Objects.requireNonNull(response.getBody()).contains("Success")) {
                return false;
            }
            count++;
            double rawProgress = (double) count / total * importTestSuite;
            double progress = Math.round(rawProgress * 100.0) / 100.0;
            taskProgress.put(taskId, initProgress + progress);
        }
        return true;
    }

    /**
     * 将单个测试用例添加到测试集
     *
     * @param headers         jiraToken
     * @param testCaseToSuite 测试用例集与测试用例的映射
     *                        添加成功/失败 {"message":"Success"}
     */
    public void linkTestCaseToSuite(HttpHeaders headers, LinkToTestSuite testCaseToSuite) {
        try {
            // 处理 testSuitePath，只要有 / 就使用转义符号
            testCaseToSuite.setTestSuitePath(testCaseToSuite.getTestSuitePath().replaceAll("/", "\\\\/"));
            String dataJson = toJson(testCaseToSuite);
            HttpEntity<String> request = new HttpEntity<>(dataJson, headers);
            log.info("添加测试用例到测试集请求参数:{}", dataJson);
            ResponseEntity<String> response = restTemplate.postForEntity(LINK_TEST_CASE_TO_SUITE, request, String.class);
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("添加测试用例到测试集成功，返回结果:{}", response.getBody());
            } else {
                log.error("添加测试用例到测试集失败，HTTP 状态码: {}, 返回结果: {}", response.getStatusCode(), response.getBody());
            }
        } catch (Exception ex) {
            log.error("添加测试用例到测试集 请求处理时发生异常: {}", ex.getMessage());
        }
    }


    /**
     * 获取测试计划key，如果不存在则创建一个新的测试计划
     *
     * @param projectKey 项目key
     * @return testPlanIssueKey
     */
    private String getTestPlanKey(String jiraToken, String testPlanName, String userName, String projectKey, List<FixVersion> fixVersions) {
        List<Map<String, String>> allPlanForProject = getAllPlanForProject(jiraToken, projectKey);
        Map<String, String> matchingPlan = findMatchingPlan(allPlanForProject, testPlanName);

        if (matchingPlan != null) {
            return matchingPlan.get("key");
        }

        createTestPlan(jiraToken, projectKey, testPlanName, userName, fixVersions);

        allPlanForProject = getAllPlanForProject(jiraToken, projectKey);
        matchingPlan = findMatchingPlan(allPlanForProject, testPlanName);

        return matchingPlan != null ? matchingPlan.get("key") : null;
    }

    private Map<String, String> findMatchingPlan(List<Map<String, String>> plans, String testPlan) {
        return plans.stream().filter(plan -> plan.get("summary").equals(testPlan.trim()) || plan.get("key").equals(testPlan.trim())).findFirst().orElse(null);
    }

    /**
     * 创建测试计划
     */
    public void createTestPlan(String jiraToken, String projectKey, String testPlanName, String userName, List<FixVersion> fixVersions) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        Fields fields = new Fields();
        Project project = new Project();
        project.setKey(projectKey);

        IssueType issueType = new IssueType();
        issueType.setName("测试计划");

        Assignee assignee = new Assignee();
        assignee.setName(userName);

        fields.setProject(project);
        fields.setIssuetype(issueType);
        fields.setSummary(testPlanName);
        fields.setAssignee(assignee);
        fields.setFixVersions(fixVersions);
        TestPlan testPlan = new TestPlan();
        testPlan.setFields(fields);
        HttpEntity<String> request = new HttpEntity<>(toJson(testPlan), headers);
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(CREATE_ISSUE, request, String.class);
            log.info("创建测试计划返回结果:{}", response.getBody());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 删除ISSUE
     */
    public void deleteIssue(String jiraToken, String issueKey) {
        log.info("删除测试用例:{}", issueKey);
        log.info("jiraToken-----------:{}", jiraToken);
        HttpHeaders headers = buildJiraHeader(jiraToken);
        HttpEntity<String> request = new HttpEntity<>(headers);
        try {
            restTemplate.exchange(buildUrl(DELETE_ISSUE, issueKey), HttpMethod.DELETE, request, String.class);
        } catch (Exception e) {
            throw new RuntimeException("删除失败", e);
        }
    }

    /**
     * 将测试用例添加到测试计划
     *
     * @param jiraToken        jiraToken
     * @param testPlanIssueKey 测试计划编号
     * @param testCaseKeys     测试用例key列表
     */
    public void addTestCaseListToPlan(String jiraToken, String testPlanIssueKey, List<String> testCaseKeys, String taskId, int importTestPlan) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        Map<String, List<String>> wrapMap = new HashMap<>();
        wrapMap.put("testCaseKeys", testCaseKeys);
        HttpEntity<String> request = new HttpEntity<>(toJson(wrapMap), headers);
        double initProgress = taskProgress.get(taskId);
        ResponseEntity<String> response = restTemplate.postForEntity(buildUrl(ADD_TEST_CASE_TO_PLAN, testPlanIssueKey), request, String.class);
        if (!Objects.requireNonNull(response.getBody()).contains("Success")) {
            log.error("添加测试用例到测试计划失败:{}", response.getBody());
        }
        log.info("添加测试用例到测试计划完成");
        taskProgress.put(taskId, initProgress + importTestPlan);
    }

    /**
     * 将测试用例添加到测试计划
     *
     * @param jiraToken        jiraToken
     * @param testPlanIssueKey 测试计划编号
     */
    public void addTestCaseToPlan(String jiraToken, String testPlanIssueKey, String testCaseKey) {
        if (StrUtil.isBlank(testPlanIssueKey) || StrUtil.isBlank(testCaseKey)) {
            log.error("测试计划key或测试用例key为空,不处理");
            return;
        }
        HttpHeaders headers = buildJiraHeader(jiraToken);
        Map<String, List<String>> wrapMap = new HashMap<>();
        wrapMap.put("testCaseKeys", List.of(testCaseKey));
        HttpEntity<String> request = new HttpEntity<>(toJson(wrapMap), headers);
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(buildUrl(ADD_TEST_CASE_TO_PLAN, testPlanIssueKey), request, String.class);
            log.info("添加测试用例到测试计划返回结果:{}", response.getBody());
        } catch (Exception ignored) {
        }
    }

    /**
     * 获取测试计划下的测试周期列表
     *
     * @param JiraToken        jiraToken
     * @param testPlanIssueKey 测试计划key
     * @return 测试周期列表
     */
    @Override
    public List<TestCycle> getTestPlanCycles(String JiraToken, String testPlanIssueKey) {
        try {
            HttpHeaders headers = buildJiraHeader(JiraToken);
            HttpEntity<String> request = new HttpEntity<>(headers);
            ResponseEntity<List<TestCycle>> response = restTemplate.exchange(buildUrl(GET_TEST_CYCLES, testPlanIssueKey), HttpMethod.GET, request, new ParameterizedTypeReference<>() {
            });
            return response.getBody();
        } catch (Exception e) {
            log.error("获取测试周期失败:{}", e.getMessage());
            throw new RuntimeException("测试计划[" + testPlanIssueKey + "]不存在");
        }
    }

    /**
     * 获取测试计划下的测试周期列表
     *
     * @param jiraToken        jiraToken
     * @param testPlanIssueKey 测试计划key
     * @param testCycleName    测试周期名称
     * @return 测试周期列表
     */
    public TestCycle getTestPlanCycle(String jiraToken, String testPlanIssueKey, String testCycleName) {
        TestCycle testCycleInfo = new TestCycle();
        HttpHeaders headers = buildJiraHeader(jiraToken);
        //获取测试计划下的周期列表
        List<TestCycle> testPlanCycles = getTestPlanCycles(jiraToken, testPlanIssueKey);
        //检查周期是否存在
        TestCycle matchingCycle = findMatchingCycle(testPlanCycles, testCycleName);
        //不存在则创建一个新周期
        if (matchingCycle == null) {
            createTestCycle(headers, testPlanIssueKey, testCycleName);
            // 更新测试周期列表
            testPlanCycles = getTestPlanCycles(jiraToken, testPlanIssueKey);
            // 创建之后再次检查，获得新的周期
            matchingCycle = findMatchingCycle(testPlanCycles, testCycleName);

            if (matchingCycle == null) {
                throw new RuntimeException("创建测试周期失败");
            }
            testCycleInfo.setId(matchingCycle.getId());
            testCycleInfo.setName(matchingCycle.getName());
        }
        return matchingCycle;
    }


    /**
     * 重新加载测试用例
     *
     * @param reloadTestRunsDTO 重新加载测试用例参数
     * @param jiraToken         jiraToken
     * @return 是否成功
     */
    @Override
    public boolean reloadTestRuns(ReloadTestRunsDTO reloadTestRunsDTO, String jiraToken) {
        //获取每个测试用例的信息
        for (String testCaseKey : reloadTestRunsDTO.getTestCaseIds()) {
            TestCaseForReLoad testCaseInfo = getTestPlanIdsFromTestCase(jiraToken, testCaseKey);
            // 循环获取每个测试计划下的测试运行
            for (TestPlanForReLoad testPlanInfo : testCaseInfo.getTestPlans()) {
                // 测试计划key
                String testPlanKey = testPlanInfo.getTestPlanKey();
                for (TestCycleForReLoad testCycleInfo : testPlanInfo.getTestCycles()) {
                    // 测试周期id
                    int testCycleId = testCycleInfo.getTestCycleId();
                    // 测试周期名称
                    String testCycleName = testCycleInfo.getTestCycleName();
                    List<TestRunForCycle> testRuns = getTestRuns(jiraToken, testPlanInfo.getTestPlanKey(), testCycleInfo.getTestCycleName());
                    // 从当前周期中获得测试用例的id
                    String runId = getTestCaseId(testRuns, testCaseKey);
                    log.info("获取测试运行id:{}", runId);
                    TestRun testRun = getTestRun(jiraToken, runId);
                    String[] testRunBugs = extractBugIds(testRun.getTestRunDetails().getTestRunBugs());
                    log.info("获取测试运行信息:{}", testRun);
                    // 重新载入测试运行的结果
                    reload(jiraToken, testPlanKey, String.valueOf(testCycleId), runId);
                    TestRun testRun2 = getTestRun(jiraToken, runId);
                    // 判断是否需要刷新每一个步骤的结果
                    if (reloadTestRunsDTO.isKeepStepResults()) {
                        TestRunDetails testRunDetails = testRun.getTestRunDetails();
                        for (int i = 0; i < testRunDetails.getTestRunSteps().size(); i++) {
                            // 步骤使用重载后的id
                            String runStepId;
                            List<TestRunStep> steps = testRun2.getTestRunDetails().getTestRunSteps();
                            if (i < steps.size()) {
                                runStepId = String.valueOf(steps.get(i).getId());
                            } else {
                                continue;
                            }
                            // 步骤结果
                            String status = testRunDetails.getTestRunSteps().get(i).getStatus();
                            // 步骤关联的bug
                            List<BugDTO> bugList = testRunDetails.getTestRunSteps().get(i).getTestRunStepBugsWrapper();

                            // 判断是否要关bug
                            if (reloadTestRunsDTO.isKeepDefects()) {
                                // 更新每一步的结果，并关联bug
                                updateTestStepResult(jiraToken, runStepId, status, extractBugIds(bugList));
                            } else {
                                // 更新每一步骤的结果，不关联bug
                                updateTestStepResult(jiraToken, runStepId, status, null);
                            }
                        }
                    }
                    // 判断是否需要刷新总结果
                    if (reloadTestRunsDTO.isKeepTestResults()) {
                        // 重新设置测试运行的结果
                        updateTestRun(jiraToken, testPlanKey, testCycleName, testCaseKey, testRun.getStatus());
                    }
                    // 判断是否要关联bug
                    if (reloadTestRunsDTO.isKeepDefects()) {
                        if (testRunBugs.length > 0) {
                            // 将不重复的bug关联到测试运行
                            linkBugToTestRun(jiraToken, testPlanKey, testCycleName, testCaseKey, testRunBugs);
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 通过文件ID删除jira中的测试用例
     *
     * @param fileId 文件ID
     */
    @Override
    public int deleteTestCaseByFileId(String jiraToken, String fileId) {
        QueryWrapper<TXMind> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("file_id", fileId);
        TXMind txMind = xMindDAO.selectOne(queryWrapper);
        String caseId = txMind.getTestCaseId();

        List<String> testCaseKeys = Arrays.asList(caseId.split(","));
        // 创建一个线程安全的计数器
        AtomicInteger count = new AtomicInteger(0);
        ConcurrentLinkedQueue<Throwable> exceptions = new ConcurrentLinkedQueue<>();
        CopyOnWriteArrayList<String> testCaseKeyList = new CopyOnWriteArrayList<>();
        ExecutorService executorService = Executors.newFixedThreadPool(10);


        List<CompletableFuture<Void>> futures = testCaseKeys.stream().map(testCaseKey -> CompletableFuture.runAsync(() -> {
            try {

                deleteIssue(jiraToken, testCaseKey);
                count.incrementAndGet();
            } catch (Exception e) {
                // 捕获并记录异常
                log.error("删除失败: {}", testCaseKey, e);
                exceptions.add(e);
                testCaseKeyList.add(testCaseKey);
            }
        }, executorService)).toList();

        // 等待所有任务完成
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allOf.join();  // 等待所有并发任务完成
        // 关闭线程池
        executorService.shutdown();
        try {
            // 等待最长10分钟
            if (!executorService.awaitTermination(10, TimeUnit.MINUTES)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
        }
        List<String> disjunction = new ArrayList<>();
        if (!exceptions.isEmpty()) {
            // 使用hutool获取两个集合的差集
            disjunction = CollUtil.subtractToList(testCaseKeys, testCaseKeyList);
        }
        txMind.setTestCaseId(String.join(",", disjunction));
        xMindDAO.updateById(txMind);
        return count.get();
    }

    private String getTestCaseId(List<TestRunForCycle> testRuns, String testCaseKey) {
        for (TestRunForCycle testRun : testRuns) {
            if (testCaseKey.equals(testRun.getTestCaseKey())) {
                return testRun.getId();
            }
        }
        return null;

    }

    /**
     * 获得测试周期下的所有测试运行数据
     *
     * @param jiraToken        jiraToken
     * @param testPlanIssueKey 测试计划key
     * @param cycleName        测试周期名称
     * @return 测试运行数据<测试用例key, 测试运行id>
     */
    private List<TestRunForCycle> getTestRuns(String jiraToken, String testPlanIssueKey, String cycleName) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<List<TestRunForCycle>> response = restTemplate.exchange(buildUrl(GET_TEST_RUNS, testPlanIssueKey, cycleName), HttpMethod.GET, request, new ParameterizedTypeReference<>() {
        });
        return response.getBody();
    }

    /**
     * 获得测试周期下的所有测试运行数据
     */
    private List<TestRunForCycle> getTestRunsForCycleId(String jiraToken, String testPlanIssueKey, String cycleId) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<List<TestRunForCycle>> response = restTemplate.exchange(buildUrl(GET_TEST_RUNS_BY_CYCLE_ID, testPlanIssueKey, cycleId), HttpMethod.GET, request, new ParameterizedTypeReference<>() {
        });
        return response.getBody();
    }

    /**
     * 使用运行ID获取单个测试运行的所有数据
     *
     * @param jiraToken jiraToken
     * @param testRunId 测试运行id
     */
    private TestRun getTestRun(String jiraToken, String testRunId) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<TestRun> response = restTemplate.exchange(buildUrl(GET_TEST_RUN, testRunId), HttpMethod.GET, request, TestRun.class);
        return response.getBody();
    }


//    /**
//     * 从测试运行中获取缺陷报告
//     *
//     * @param jiraToken jiraToken
//     * @param runId     测试运行id
//     * @return 缺陷列表
//     */
//    private List<String> getBugsFromTestRun(String jiraToken, String runId) {
//        HttpHeaders headers = buildJiraHeader(jiraToken);
//        HttpEntity<String> request = new HttpEntity<>(headers);
//        ResponseEntity<String> response = restTemplate.exchange(buildUrl(GET_GUG, runId), HttpMethod.GET, request, String.class);
//        return Optional.ofNullable(response.getBody()).map(JSONArray::parseArray).map(jsonArray -> jsonArray.stream().map(item -> ((JSONObject) item).getString("key")).collect(Collectors.toList())).orElseGet(Collections::emptyList);
//    }

    /**
     * 重新加载测试用例
     *
     * @param jiraToken    jiraToken
     * @param tpId         测试计划id
     * @param cycleId      测试周期id
     * @param selectedRuns 测试运行id的数组 使用,分割
     */
    private boolean reload(String jiraToken, String tpId, String cycleId, String selectedRuns) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(buildUrl(RELOAD_TEST_RUNS, tpId, cycleId, selectedRuns), HttpMethod.GET, request, String.class);
        return response.getStatusCode().is2xxSuccessful();
    }

    /**
     * 更新一个测试运行的总结果
     *
     * @param jiraToken        jiraToken
     * @param testPlanIssueKey 测试计划key
     * @param cycleName        测试周期名称
     * @param testcaseKey      测试用例key TROS-1234
     * @param result           结果
     */
    private void updateTestRun(String jiraToken, String testPlanIssueKey, String cycleName, String testcaseKey, String result) {
        log.info("更新测试运总结果:testPlanIssueKey:{},cycleName:{},testcaseKey:{},result:{}", testPlanIssueKey, cycleName, testcaseKey, result);
        HttpHeaders headers = buildJiraHeader(jiraToken);
        String url = buildUrl(UPDATE_TEST_RUN, testPlanIssueKey, cycleName);
        Map<String, Object> map = new HashMap<>();
        map.put("testcaseKey", testcaseKey);
        map.put("result", result);
        HttpEntity<String> request = new HttpEntity<>(toJson(map), headers);
        try {
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
            log.info("更新测试运行总结果返回结果:{}", response.getBody());
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new RuntimeException("周期[" + cycleName + "]不是运行状态");
            }
        } catch (Exception e) {
            log.error("更新测试运行总结果失败:{}，周期可能不是运行状态", e.getMessage());
        }
    }

    /**
     * 更新一个测试运行的总结果
     *
     * @param jiraToken jiraToken
     * @param runId     测试运行ID
     * @param result    测试运行结果
     * @param bugs      缺陷列表
     */
    private void updateTestRunResult(String jiraToken, String runId, String result, String[] bugs) {
        log.info("更新测试运行总结果:runId:{},result:{},bugs:{}", runId, result, bugs);
        HttpHeaders headers = buildJiraHeader(jiraToken);
        Map<String, Object> map = new HashMap<>();
        map.put("runId", runId);
        map.put("result", result);
        map.put("bugs", bugs);
        HttpEntity<String> request = new HttpEntity<>(toJson(map), headers);
        try {
            ResponseEntity<String> response = restTemplate.exchange(UPDATE_TEST_RUN_RESULT, HttpMethod.POST, request, String.class);
            log.info("更新测试运行总结果返回结果:{}", response.getBody());
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new RuntimeException("周期不是运行状态");
            }
        } catch (Exception e) {
            throw new RuntimeException("周期不是运行状态", e);
        }
    }

    /**
     * 更新一个步骤的测试结果
     *
     * @param jiraToken jiraToken
     * @param runStepId 测试步骤id
     * @param bugs      需要关联的bug列表,传null则不处理bug
     * @param result    结果
     */
    private void updateTestStepResult(String jiraToken, String runStepId, String result, String[] bugs) {
        log.info("更新测试步骤结果:runStepId:{},result:{},bugs:{}", runStepId, result, bugs);
        HttpHeaders headers = buildJiraHeader(jiraToken);
        Map<String, Object> map = new HashMap<>();
        map.put("runStepId", runStepId);
        map.put("result", result);
        if (bugs != null) {
            map.put("bugs", bugs);
        }
        HttpEntity<String> request = new HttpEntity<>(toJson(map), headers);
        try {
            ResponseEntity<String> response = restTemplate.exchange(UPDATE_TEST_RUN_STEP, HttpMethod.POST, request, String.class);
            log.info("更新测试步骤结果返回结果:{}", response.getBody());
        } catch (Exception e) {
            log.error("更新测试步骤结果失败:{}，周期可能不是运行状态", e.getMessage());
        }
    }

    /**
     * 将bug关联到测试运行
     *
     * @param jiraToken        jiraToken
     * @param testPlanIssueKey 测试计划key
     * @param cycleName        测试周期名称
     * @param testcaseKey      测试用例key
     * @param bugs             需要关联的bug列表
     * @return 是否成功
     */
    private boolean linkBugToTestRun(String jiraToken, String testPlanIssueKey, String cycleName, String testcaseKey, String[] bugs) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        Map<String, Object> map = new HashMap<>();
        map.put("testcaseKey", testcaseKey);
        map.put("bugs", bugs);
        HttpEntity<String> request = new HttpEntity<>(toJson(map), headers);
        try {
            ResponseEntity<String> response = restTemplate.exchange(buildUrl(LINK_BUG_TO_TEST_RUN, testPlanIssueKey, cycleName), HttpMethod.POST, request, String.class);
            log.info("关联bug到测试运行返回结果:{}", response.getBody());
            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            log.error("关联bug到测试运行失败:{}，周期可能不是运行状态", e.getMessage());
            return false;
        }
    }

    /**
     * 从测试用例获取关联的测试计划列表（包括测试周期）
     *
     * @param jiraToken   jiraToken
     * @param testCaseKey 测试用例key
     */
    public TestCaseForReLoad getTestPlanIdsFromTestCase(String jiraToken, String testCaseKey) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        HttpEntity<String> request = new HttpEntity<>(headers);
        try {
            ResponseEntity<TestCaseForReLoad> response = restTemplate.exchange(buildUrl(GET_TEST_PLANS_FOR_TEST_CASE, testCaseKey), HttpMethod.GET, request, TestCaseForReLoad.class);
            return response.getBody();
        } catch (Exception e) {
            log.error("获取测试计划失败:{}", e.getMessage());
            throw new RuntimeException("用例[" + testCaseKey + "]没有关联的测试计划");
        }
    }


    /**
     * 创建测试周期
     */
    public void createTestCycle(HttpHeaders headers, String testPlanIssueKey, String cycleName) {
        TestCycle testCycle = new TestCycle();
        testCycle.setName(cycleName);
        HttpEntity<String> request = new HttpEntity<>(toJson(testCycle), headers);
        ResponseEntity<String> response = restTemplate.postForEntity(buildUrl(CREATE_TEST_CYCLE, testPlanIssueKey), request, String.class);
        if (!Objects.requireNonNull(response.getBody()).contains("Success")) {
            log.error("创建测试周期失败:{}", response.getBody());
        }
        log.info("创建测试周期成功");
    }

    /**
     * 在测试周期中添加测试用例（基于测试计划中的测试用例）
     */
    public void addTestCaseToCycle(HttpHeaders headers, String testPlanIssueKey, String cycleId, String cycleName, String testCaseKey) {
        TestCaseToTestCycle testCaseToTestCycle = new TestCaseToTestCycle();
        testCaseToTestCycle.setTestCycleId(cycleId);
        testCaseToTestCycle.setTestCycleName(cycleName);
        testCaseToTestCycle.setAddTestCaseKeys(List.of(testCaseKey));
        actionTestCaseToCycle(headers, testPlanIssueKey, testCaseToTestCycle);
    }

    /**
     * 在测试周期中添加/删除测试用例（基于测试计划中的测试用例）
     *
     * @param headers             jiraToken
     * @param testPlanIssueKey    测试计划key
     * @param testCaseToTestCycle 测试用例与测试周期的映射
     */
    public void actionTestCaseToCycle(HttpHeaders headers, String testPlanIssueKey, TestCaseToTestCycle testCaseToTestCycle) {
        HttpEntity<String> request = new HttpEntity<>(toJson(testCaseToTestCycle), headers);
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(buildUrl(ADD_TEST_CASE_TO_CYCLE, testPlanIssueKey), request, String.class);
            log.info("添加测试用例到测试周期返回结果:{}", response.getBody());
        } catch (Exception e) {
            log.error("添加测试用例到测试周期失败", e);
        }
    }

    /**
     * 测试用例关联需求（批量）
     */
    public void linkTestCaseListToRequirement(HttpHeaders headers, Map<String, List<String>> requirementToTestCaseMap) {
        for (Map.Entry<String, List<String>> entry : requirementToTestCaseMap.entrySet()) {
            //需求为空时，跳过
            if (!entry.getKey().isEmpty()) {
                Map<String, List<String>> map = new HashMap<>();
                map.put("testCaseKeys", entry.getValue());
                String dataJson = toJson(map);
                HttpEntity<String> request = new HttpEntity<>(dataJson, headers);
                ResponseEntity<String> response = restTemplate.postForEntity(buildUrl(LINK_TEST_CASE_TO_REQUIREMENT, entry.getKey()), request, String.class);
                if (!Objects.requireNonNull(response.getBody()).contains("Success")) {
                    log.error("测试用例关联需求失败:{}", response.getBody());
                }
            }

        }
        log.info("测试用例关联需求完成");
    }

    /**
     * 单个测试用例关联需求
     *
     * @param headers             jiraToken
     * @param requirementIssueKey 需求key
     * @param testCaseKey         测试用例key
     */
    public void linkTestCaseToRequirement(HttpHeaders headers, String requirementIssueKey, String testCaseKey) {
        if (requirementIssueKey.isEmpty() || testCaseKey.isEmpty()) {
            return;
        }
        Map<String, List<String>> map = new HashMap<>();
        map.put("testCaseKeys", List.of(testCaseKey));
        String dataJson = toJson(map);
        HttpEntity<String> request = new HttpEntity<>(dataJson, headers);
        log.info("测试用例关联需求请求参数:{}", dataJson);
        ResponseEntity<String> response = null;
        try {
            response = restTemplate.postForEntity(buildUrl(LINK_TEST_CASE_TO_REQUIREMENT, requirementIssueKey), request, String.class);
            log.info("测试用例关联需求返回结果:{}", response.getBody());
        } catch (Exception ignored) {

        }
    }

    @Override
    public List<TestCase> csvToTestCaseEntity(File file, String assigneeName, boolean mergeStepAndResult) {
        List<Map<String, String>> maps = ExclUtil.readCSV(file).dataMap();
        List<TestCase> testCases = new ArrayList<>();

        for (Map<String, String> map : maps) {
            TestCase testCase = null;
            Fields fields = new Fields();

            Project project = new Project();
            List<FixVersion> fixVersions = new ArrayList<>();
            FixVersion fixVersion = new FixVersion();
            List<TestCaseStep> testCaseSteps = new ArrayList<>();

            String testSuitePath = "";
            String reqNum = "";

            IssueType issueType = new IssueType();
            issueType.setName("测试用例");
            fields.setIssuetype(issueType);

            Assignee assignee = new Assignee();
            assignee.setName(assigneeName);
            fields.setAssignee(assignee);

            CaseLevel caseLevel = new CaseLevel();

            String steps = "";
            String results = "";

            for (String key : map.keySet()) {
                String value = map.get(key);
                switch (key) {
                    case "项目" -> {
                        project.setKey(value);
                        fields.setProject(project);
                    }
                    case "测试用例集", "模块" -> testSuitePath = value.replaceAll("[\\\\\\-.]", "/");
                    case "标题", "用例标题" -> fields.setSummary(value);
                    case "前提", "前置条件" -> fields.setCustomfield_12859(value);
                    case "步骤", "操作步骤" -> steps = value;
                    case "期望结果" -> results = value;
                    case "用例等级" -> {
                        caseLevel.setValue(value);
                        fields.setCustomfield_12851(caseLevel);
                    }
                    case "修复版本", "版本" -> {
                        fixVersion.setName(value.trim());
                        fixVersions.add(fixVersion);
                        fields.setFixVersions(fixVersions);
                    }
                    case "需求编号" -> reqNum = value;
                }
            }

            if (!mergeStepAndResult) {
                String[] stepArray = steps.split("\n");
                String[] resultArray = results.split("\n");

                if (stepArray.length == resultArray.length) {
                    for (int i = 0; i < stepArray.length; i++) {
                        TestCaseStep testCaseStep = new TestCaseStep();
                        testCaseStep.setStep(stepArray[i]);
                        testCaseStep.setExpectedResult(resultArray[i]);
                        testCaseSteps.add(testCaseStep);
                    }
                } else {
                    //步骤和结果不一一对应，也合并为一个步骤
                    TestCaseStep singleStep = new TestCaseStep();
                    singleStep.setStep(steps);
                    singleStep.setExpectedResult(results);
                    testCaseSteps.add(singleStep);
                }
            } else {
                TestCaseStep singleStep = new TestCaseStep();
                singleStep.setStep(steps);
                singleStep.setExpectedResult(results);
                testCaseSteps.add(singleStep);
            }

            testCase = TestCase.builder().fields(fields).testCaseSteps(testCaseSteps).testSuitePath(testSuitePath).reqNum(reqNum).build();

            testCases.add(testCase);
        }
        return testCases;
    }

    // tVerifyFile入库操作
    private void processTVerifyFile(Long id, String taskId) {
        TVerifyFileAssociates tVerifyFileAssociates = new TVerifyFileAssociates();
        tVerifyFileAssociates.setTVerifyFileId(id.toString());
        tVerifyFileAssociates.setTaskId(taskId);
        tVerifyFileAssociates.setCreateTime(new Date());
        tVerifyFileAssociates.setUpdateTime(new Date());
        // 设置状态完成
        tVerifyFileAssociates.setCompleteTag(0);
        tVerifyFileAssociates.setStatus(0);
        // 设置合并标志为非合并数据
        tVerifyFileAssociates.setMergeTag(0);
        tVerifyFileAssociates.insert();
    }

    private String getTemplateFileId(String fileType) {
        TVerifyFileTemplate tVerifyFileTemplate = new TVerifyFileTemplate();
        QueryWrapper<TVerifyFileTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("verify_file_type", fileType);
        TVerifyFileTemplate template = tVerifyFileTemplate.selectOne(queryWrapper);
        if (template == null) {
            throw new RuntimeException("未找到" + fileType + "的模板");
        }
        log.info("获取到的模板ID:{}", template.getFileId());
        return template.getFileId();
    }

    @Override
    public void verifyDocument(Long id, String jiraToken, VerifyDocumentDTO verifyDocumentDTO) {
        switch (verifyDocumentDTO.getFileType()) {
            case "OQP", "PQP" -> {
                OQPVerifyDocument(id, jiraToken, verifyDocumentDTO);
            }
            case "OQT", "PQT" -> {
                OQTVerifyDocument(id, jiraToken, verifyDocumentDTO);
            }
            case "OQR", "PQR" -> {
                OQRVerifyDocument(id, jiraToken, verifyDocumentDTO);
            }
            default -> {
                TVerifyFile tVerifyFile = new TVerifyFile();
                tVerifyFile.setId(id);
                tVerifyFile.selectById();
                tVerifyFile.setUpdateTime(new Date());
                tVerifyFile.setErrorMsg("暂不支持" + verifyDocumentDTO.getFileType() + "文档类型");
                tVerifyFile.setIsComplete(2);
                tVerifyFile.updateById();
                throw new RuntimeException("暂不支持的文档类型");
            }
        }
    }

    private void OQPVerifyDocument(Long id, String jiraToken, VerifyDocumentDTO verifyDocumentDTO) {
        VerifyDocument document = getVerifyDocument(verifyDocumentDTO.getFileType());
        String fileId = getTemplateFileId(verifyDocumentDTO.getFileType());
        String fileName = verifyDocumentDTO.getCodeVersion() + document.getName() + verifyDocumentDTO.getVersion();
        OQPTestCaseListWrapper oqpListWrapper = new OQPTestCaseListWrapper();
        List<TestRunForCycle> testRuns = getTestRuns(jiraToken, verifyDocumentDTO);
        // 遍历测试运行，获取测试用例编号和概要
        for (TestRunForCycle testRun : testRuns) {
            OQPTestCaseListParams oqpListParams = new OQPTestCaseListParams();
            oqpListParams.setTestCaseId(testRun.getTestCaseKey());
            oqpListParams.setTestCaseName(testRun.getSummary());
            oqpListWrapper.getValue().add(oqpListParams);
        }

        OQPWrapper OQPWrapper = new OQPWrapper();

        // 设置版本号
        OQPWrapper.setVersion(verifyDocumentDTO.getVersion());
        // 设置测试者
        OQPWrapper.setTester(getDisplayNameByJiraToken(jiraToken));
        OQPWrapper.setDate(verifyDocumentDTO.getUpdateDate());
        OQPWrapper.setCode_version(verifyDocumentDTO.getCodeVersion());
        OQPWrapper.setAuditor(verifyDocumentDTO.getAuditor());
        OQPWrapper.setApprover(verifyDocumentDTO.getApprover());
        OQPWrapper.setTestCaseInfo(oqpListWrapper);
        Map<String, Object> wordTemplateParams = WordTemplatePlusParamsUtil.buildWordTemplateParams(fileId, fileName, toJson(OQPWrapper));
        String taskId = verifyDocumentService.wordTemplatePlusHandle(wordTemplateParams);
        processTVerifyFile(id, taskId);
    }

    private void OQTVerifyDocument(Long id, String jiraToken, VerifyDocumentDTO verifyDocumentDTO) {
        VerifyDocument document = getVerifyDocument(verifyDocumentDTO.getFileType());
        String fileId = getTemplateFileId(verifyDocumentDTO.getFileType());
        String fileName = verifyDocumentDTO.getCodeVersion() + document.getName() + verifyDocumentDTO.getVersion();
        OQTWrapper oqtWrapper = new OQTWrapper();

        oqtWrapper.setCode_version(verifyDocumentDTO.getCodeVersion());
        oqtWrapper.setAuditor(verifyDocumentDTO.getAuditor());
        oqtWrapper.setApprover(verifyDocumentDTO.getApprover());
        oqtWrapper.setVersion(verifyDocumentDTO.getVersion());
        oqtWrapper.setTester(getDisplayNameByJiraToken(jiraToken));

        Map<String, String> nameMap = new HashMap<>();

        List<TestRunForCycle> testRuns = getTestRuns(jiraToken, verifyDocumentDTO);
        long totalSize = 0L;
        String fileNameEnd = fileName;
        int counter = 1;
        for (TestRunForCycle testRun : testRuns) {
            // 如果图片总大小超过2G，就重新生成一个word，实际上这里是小于2G的 2000000L
            if (totalSize >= 2000000L) {
                String testers = String.join("、", nameMap.values());
                oqtWrapper.setTesters(testers);
                String taskId = processOQTWrapper(fileId, fileNameEnd, oqtWrapper);
                TVerifyFileAssociates tVerifyFileAssociates = new TVerifyFileAssociates();
                tVerifyFileAssociates.setTVerifyFileId(id.toString());
                tVerifyFileAssociates.setTaskId(taskId);
                tVerifyFileAssociates.setCreateTime(new Date());
                tVerifyFileAssociates.setUpdateTime(new Date());
                // 设置状态未完成
                tVerifyFileAssociates.setCompleteTag(1);
                tVerifyFileAssociates.setStatus(0);
                tVerifyFileAssociates.setMergeTag(0);
                tVerifyFileAssociates.insert();
                oqtWrapper = new OQTWrapper();
                totalSize = 0L;  // 重置图片计数
                // 使用新的fileId
                fileId = getTemplateFileId(verifyDocumentDTO.getFileType() + "_merge");
                fileNameEnd = fileName + "_" + counter;
                counter++;
            }

            OQTTestCaseListParamsWrapper oqtTestCaseListParamsWrapper = new OQTTestCaseListParamsWrapper();
            TestRun testRunInfo = getTestRun(jiraToken, testRun.getId());
            TestRunDetails testRunDetails = testRunInfo.getTestRunDetails();
            List<TestRunStep> testRunSteps = testRunDetails.getTestRunSteps();
            if (testRunSteps == null) {
                throw new RuntimeException(testRunInfo.getTestCaseKey() + "的测试用例步骤为空");
            }
            OQTTestCaseInfoWrapper oqtTestCaseInfoWrapper = new OQTTestCaseInfoWrapper();

            for (TestRunStep testRunStep : testRunSteps) {
                OQTTestCaseParams oqtTestCaseParams = new OQTTestCaseParams();
                oqtTestCaseParams.setId(String.valueOf(testRunSteps.indexOf(testRunStep) + 1));
                oqtTestCaseParams.setStep(testRunStep.getStep());
                oqtTestCaseParams.setResult(testRunStep.getExpectedResult());
                oqtTestCaseParams.setConclusion(testRun.getStatus());
                oqtTestCaseInfoWrapper.getValue().add(oqtTestCaseParams);
            }

            oqtTestCaseListParamsWrapper.setTestCaseInfo(oqtTestCaseInfoWrapper);
            oqtTestCaseListParamsWrapper.setTestCaseId(testRun.getTestCaseKey());
            oqtTestCaseListParamsWrapper.setTestCaseName(testRun.getSummary());

            String precondition = getPrecondition(jiraToken, testRun.getTestCaseKey());
            if (precondition != null && !precondition.equals("无")) {
                oqtTestCaseListParamsWrapper.setPrecondition(precondition);
            }

            String testCaseLevel = getCaseLevel(jiraToken, testRun.getTestCaseKey());
            oqtTestCaseListParamsWrapper.setTestCaseLevel(testCaseLevel);
            String testCaseAssignee = testRun.getExecutedBy();
            String tester = StrUtil.isEmpty(testCaseAssignee) ? "" : nameMap.computeIfAbsent(testCaseAssignee, key -> getUserInfoByName(jiraToken, key));
            oqtTestCaseListParamsWrapper.setTester(tester);
            oqtTestCaseListParamsWrapper.setConclusion(testRun.getStatus());
            oqtTestCaseListParamsWrapper.setTestDate(getDate(testRun.getExecutionTimeStamp()));

            List<AttachmentDTO> attachments = getAttachment(jiraToken, testRun.getId());
            // 附件总数
            log.info(testRun.getTestCaseKey() + "的附件总数:{}", attachments.size());
            OQTPictureWrapperResult pictureWrappers = getTestCasePicture(attachments, jiraToken);
            log.info(testRun.getTestCaseKey() + "成功上传的图片总数:{}", pictureWrappers.getPictureWrapper().getValue().size());
            totalSize += pictureWrappers.getTotalSize();
            oqtTestCaseListParamsWrapper.setImageList(pictureWrappers.getPictureWrapper());
            oqtTestCaseListParamsWrapper.setAuditor(verifyDocumentDTO.getAuditor());
            oqtTestCaseListParamsWrapper.setAuditDate(verifyDocumentDTO.getAuditDate());

            oqtWrapper.getTestCaseList().add(oqtTestCaseListParamsWrapper);
        }

        log.info("nameMap.values()" + nameMap.values());
        // 获取所有测试执行者的姓名
        String testers = String.join("、", nameMap.values());
        oqtWrapper.setTesters(testers);

        // 最后一次的oqtWrapper也需要添加
        String taskId = processOQTWrapper(fileId, fileNameEnd, oqtWrapper);
        TVerifyFileAssociates tVerifyFileAssociates = new TVerifyFileAssociates();
        tVerifyFileAssociates.setTVerifyFileId(id.toString());
        tVerifyFileAssociates.setTaskId(taskId);
        tVerifyFileAssociates.setCreateTime(new Date());
        tVerifyFileAssociates.setUpdateTime(new Date());
        // 设置状态完成
        tVerifyFileAssociates.setCompleteTag(0);
        tVerifyFileAssociates.setStatus(0);
        // 设置合并标志为非合并数据
        tVerifyFileAssociates.setMergeTag(0);
        tVerifyFileAssociates.insert();
    }

    private String processOQTWrapper(String fileId, String fileName, OQTWrapper oqtWrapper) {
        Map<String, Object> wordTemplateParams = WordTemplatePlusParamsUtil.buildWordTemplateParams(fileId, fileName, toJson(oqtWrapper));
        return verifyDocumentService.wordTemplatePlusHandle(wordTemplateParams);

    }


    /**
     * 获取测试计划下面的所有周期的所有测试运行
     *
     * @param jiraToken         jiraToken
     * @param verifyDocumentDTO 验证文档参数
     * @return 测试运行列表
     */
    private List<TestRunForCycle> getTestRuns(String jiraToken, VerifyDocumentDTO verifyDocumentDTO) {
        Set<String> cyclesToInclude = (verifyDocumentDTO.getCycles() != null && !verifyDocumentDTO.getCycles().isEmpty())
                ? new HashSet<>(verifyDocumentDTO.getCycles())
                : Collections.emptySet();

        // 过滤测试周期
        return verifyDocumentDTO.getTestPlanIssueKey().stream()
                .flatMap(testPlanIssueKey -> getTestPlanCycles(jiraToken, testPlanIssueKey).stream()
                        .filter(testCycle -> cyclesToInclude.isEmpty() || cyclesToInclude.contains(testCycle.getId()))
                        .flatMap(testCycle -> getTestRuns(jiraToken, testPlanIssueKey, testCycle.getName()).stream()))
                .collect(Collectors.toList());
    }

    private void OQRVerifyDocument(Long id, String jiraToken, VerifyDocumentDTO verifyDocumentDTO) {
        VerifyDocument document = getVerifyDocument(verifyDocumentDTO.getFileType());
        String fileId = getTemplateFileId(verifyDocumentDTO.getFileType());
        String fileName = verifyDocumentDTO.getCodeVersion() + document.getName() + verifyDocumentDTO.getVersion();
        OQRWrapper oqrWrapper = new OQRWrapper();
        List<TestRunForCycle> testRuns = getTestRuns(jiraToken, verifyDocumentDTO);
        OQRTestCaseListWrapper oqrTestCaseListWrapper = new OQRTestCaseListWrapper();
        OQRBugListWrapper oqrBugListWrapper = new OQRBugListWrapper();
        Map<String, String> nameMap = new HashMap<>();
        int bugTotal = 0;
        for (TestRunForCycle testRun : testRuns) {
            OQRTestCaseListParams oqrTestCaseListParams = new OQRTestCaseListParams();

            // 设置测试用例编号
            oqrTestCaseListParams.setTestCaseId(testRun.getTestCaseKey());

            // 设置测试用例标题
            oqrTestCaseListParams.setTestCaseName(testRun.getSummary());

            // 设置测试执行人
            String testCaseAssignee = testRun.getExecutedBy();
            String tester = StrUtil.isEmpty(testCaseAssignee) ? "" :
                    nameMap.computeIfAbsent(testCaseAssignee, key -> getUserInfoByName(jiraToken, key));

            oqrTestCaseListParams.setTester(tester);

            // 设置测试用例状态
            String status = testRun.getStatus();
            oqrTestCaseListParams.setConclusion(status);

            // 设置测试用例审核人
            oqrTestCaseListParams.setAuditor(verifyDocumentDTO.getAuditor());

            // 组装测试用例参数
            oqrTestCaseListWrapper.getValue().add(oqrTestCaseListParams);
        }
        // 如果没有bug，添加一个空的bug
        if (oqrBugListWrapper.getValue().isEmpty()) {
            OQRBugListParams oqrBugListParams = new OQRBugListParams();
            oqrBugListWrapper.getValue().add(oqrBugListParams);
        }
        // 设置bug列表
        oqrWrapper.setBugInfo(oqrBugListWrapper);
        // 设置用例列表
        oqrWrapper.setTestCaseInfo(oqrTestCaseListWrapper);
        // 设置文档版本号
        oqrWrapper.setVersion(verifyDocumentDTO.getVersion());
        // 设置测试者
        oqrWrapper.setTester(getDisplayNameByJiraToken(jiraToken));
        // 设置所有测试执行者的姓名
        String testers = String.join("、", nameMap.values());
        oqrWrapper.setTesters(testers);
        // 设置代码版本
        oqrWrapper.setCode_version(verifyDocumentDTO.getCodeVersion());
        // 设置审核人
        oqrWrapper.setAuditor(verifyDocumentDTO.getAuditor());
        // 设置审批人
        oqrWrapper.setApprover(verifyDocumentDTO.getApprover());
        // 设置修改时间
        oqrWrapper.setDate(verifyDocumentDTO.getUpdateDate());
        // 设置bug总数
        oqrWrapper.setBugTotal(String.valueOf(bugTotal));
        Map<String, Object> wordTemplateParams = WordTemplatePlusParamsUtil.buildWordTemplateParams(fileId, fileName, toJson(oqrWrapper));
        String taskId = verifyDocumentService.wordTemplatePlusHandle(wordTemplateParams);
        processTVerifyFile(id, taskId);
    }

    /**
     * 处理时间格式
     *
     * @param executionTimeStamp 执行时间
     * @return yyyy/MM/dd
     */
    private String getDate(String executionTimeStamp) {
        if (StrUtil.isEmpty(executionTimeStamp)) {
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm");
        LocalDateTime dateTime = LocalDateTime.parse(executionTimeStamp, formatter);
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 组装测试用例的附件参数
     *
     * @param attachments 附件信息列表
     * @param jiraToken   jiraToken
     * @return 测试用例的附件信息
     */
    private OQTPictureWrapperResult getTestCasePicture(List<AttachmentDTO> attachments, String jiraToken) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        OQTTestCasePictureWrapper wrapper = new OQTTestCasePictureWrapper();
        // 图片名称与图片存储路径
        Map<String, String> pictureMap = new LinkedHashMap<>();
        String testTestCasePictureDirectory = uploadFilePath + File.separator + IdUtil.simpleUUID();
        for (AttachmentDTO attachment : attachments) {
            // 只处理图片类型的附件
            if (!attachment.getMimeType().startsWith("image")) {
                log.warn("附件{}不是图片类型", attachment.getFileName());
                //打印attachment信息
                log.warn("attachment:{}", attachment);
                continue;
            }
            String filePath = downloadFile(attachment.getFilePath(), headers, testTestCasePictureDirectory, attachment.getFileName());
            pictureMap.put(attachment.getFileName(), filePath);
        }
        log.info("获取到的图片信息:{}", pictureMap);

        if (pictureMap.isEmpty()) {
            return new OQTPictureWrapperResult(wrapper, 0);
        }
        // 压缩图片
        CompressResult compressResult = compressImages(pictureMap);
        // 图片大小
        long imageSize = compressResult.getTotalSize();

        pictureMap = fileService.batchUploadFileToFS(compressResult.getPictureMap());
        for (Map.Entry<String, String> entry : pictureMap.entrySet()) {
            String description = entry.getKey();
            String fileId = entry.getValue();
            // check都不能为空
            if (StrUtil.isEmpty(description) || StrUtil.isEmpty(fileId)) {
                log.error("description:{},fileId:{}", description, fileId);
                throw new RuntimeException("图片信息不完整");
            }
            OQTTestCasePictureParams params = new OQTTestCasePictureParams();
            params.setValue(fileId);
            // 创建 OQTTestCasePictureListParams 并设置 description 和 image
            OQTTestCasePictureListParams pictureParams = new OQTTestCasePictureListParams();
            pictureParams.setDescription(description);
            pictureParams.setImage(params);
            // 将 pictureParams 添加到 wrapper 的 value 列表中
            wrapper.getValue().add(pictureParams);
        }
        // 删除临时文件夹
        FileUtil.del(testTestCasePictureDirectory);
        return new OQTPictureWrapperResult(wrapper, imageSize);
    }

    public byte[] downloadFile(String fileUrl, HttpHeaders headers) {
        HttpEntity<String> requestEntity = new HttpEntity<>(headers);
        ResponseEntity<byte[]> responseEntity = restTemplate.exchange(fileUrl, HttpMethod.GET, requestEntity, byte[].class);
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            throw new RuntimeException("Failed to download file: HTTP error code " + responseEntity.getStatusCode());
        }
        return responseEntity.getBody();
    }

    public String downloadFile(String fileUrl, HttpHeaders headers, String saveDirectory, String fileName) {
        RestTemplate restTemplate = new RestTemplate();
        HttpEntity<String> requestEntity = new HttpEntity<>(headers);
        ResponseEntity<byte[]> responseEntity = restTemplate.exchange(fileUrl, HttpMethod.GET, requestEntity, byte[].class);

        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            throw new RuntimeException("Failed to download file: HTTP error code " + responseEntity.getStatusCode());
        }
        byte[] fileData = responseEntity.getBody();
        if (fileData == null) {
            throw new RuntimeException("No data received from the server");
        }

        try {
            Path directory = Paths.get(saveDirectory);
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
            }
            Path filePath = directory.resolve(fileName);
            Files.write(filePath, fileData);

            return filePath.toString();
        } catch (IOException e) {
            throw new RuntimeException("Failed to save file: " + e.getMessage(), e);
        }
    }

    /**
     * 获取测试运行的附件信息
     *
     * @param jiraToken jiraToken
     * @param testRunId 测试运行id
     * @return 附件信息
     */
    private List<AttachmentDTO> getAttachment(String jiraToken, String testRunId) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<List<AttachmentDTO>> response = restTemplate.exchange(buildUrl(GET_TEST_RUN_ATTACHMENT_DETAILS, testRunId), HttpMethod.GET, request, new ParameterizedTypeReference<>() {
        });
        return response.getBody();
    }

    /**
     * 查询测试用例等级
     *
     * @param testCaseKey 测试用例key
     * @return 测试用例等级
     */
    private String getCaseLevel(String jiraToken, String testCaseKey) {
        JiraIssueFieldsDTO issueField = getIssueField(jiraToken, testCaseKey, List.of(JiraIssueFieldEnum.level));
        return issueField.getFields().get(JiraIssueFieldEnum.level);
    }

    /**
     * 查询用例的前置条件
     *
     * @param testCaseKey 测试用例key
     * @return 测试用例前置条件
     */
    private String getPrecondition(String jiraToken, String testCaseKey) {
        JiraIssueFieldsDTO issueField = getIssueField(jiraToken, testCaseKey, List.of(JiraIssueFieldEnum.precondition));
        return issueField.getFields().get(JiraIssueFieldEnum.precondition);
    }

    /**
     * 获取Issue的指定字段
     *
     * @return Issue的指定字段
     */
    private JiraIssueFieldsDTO getIssueField(String jiraToken, String issueKey, List<JiraIssueFieldEnum> fields) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        HttpEntity<String> request = new HttpEntity<>(headers);
        log.info("请求IssueField的url:{}", buildUrl(fields, issueKey));
        try {
            ResponseEntity<String> response = restTemplate.exchange(buildUrl(fields, issueKey), HttpMethod.GET, request, String.class);
            String responseBody = response.getBody();

            // 使用工具类解析 JSON 对象
            JsonNode jsonObject = JsonUtil.fromJson(responseBody, JsonNode.class);
            if (jsonObject == null) {
                return new JiraIssueFieldsDTO();
            }

            JiraIssueFieldsDTO jiraIssueFieldsDTO = new JiraIssueFieldsDTO();

            for (JiraIssueFieldEnum field : fields) {
                switch (field) {
                    case fixVersions:
                        // 获取 fixVersions 数组
                        JsonNode fixVersionsArray = jsonObject.path("fields").path(field.getField());
                        StringBuilder fixVersions = new StringBuilder();
                        if (fixVersionsArray.isArray()) {
                            for (JsonNode fixVersion : fixVersionsArray) {
                                if (!fixVersions.isEmpty()) {
                                    fixVersions.append(",");
                                }
                                fixVersions.append(fixVersion.path("name").asText());
                            }
                        }
                        jiraIssueFieldsDTO.getFields().put(field, fixVersions.toString());
                        break;

                    case components:
                        // 获取 components 数组
                        JsonNode componentsArray = jsonObject.path("fields").path(field.getField());
                        StringBuilder components = new StringBuilder();
                        if (componentsArray.isArray()) {
                            for (JsonNode component : componentsArray) {
                                if (!components.isEmpty()) {
                                    components.append(",");
                                }
                                components.append(component.path("name").asText());
                            }
                        }
                        jiraIssueFieldsDTO.getFields().put(field, components.toString());
                        break;

                    case assignee:
                        // 获取 assignee 对象
                        JsonNode assigneeObject = jsonObject.path("fields").path(field.getField());
                        String assignee = assigneeObject != null ? assigneeObject.path("displayName").asText() : "";
                        jiraIssueFieldsDTO.getFields().put(field, assignee);
                        break;

                    case tester:
                        // 自定义字段 tester
                        JsonNode testerObject = jsonObject.path("fields").path("customfield_12383");
                        String tester = testerObject != null ? testerObject.path("displayName").asText() : "";
                        jiraIssueFieldsDTO.getFields().put(field, tester);
                        break;

                    case status:
                        // 获取 status 对象
                        JsonNode statusObject = jsonObject.path("fields").path(field.getField());
                        String status = statusObject != null ? statusObject.path("name").asText() : "";
                        jiraIssueFieldsDTO.getFields().put(field, status);
                        break;

                    case level:
                        // 自定义字段 level
                        JsonNode levelObject = jsonObject.path("fields").path("customfield_12851");
                        String level = levelObject != null ? levelObject.path("value").asText() : "";
                        jiraIssueFieldsDTO.getFields().put(field, level);
                        break;

                    case precondition:
                        // 自定义字段 precondition
                        String precondition = jsonObject.path("fields").path("customfield_12859").asText("");
                        jiraIssueFieldsDTO.getFields().put(field, precondition);
                        break;

                    case summary:
                        // 获取 summary
                        String summary = jsonObject.path("fields").path(field.getField()).asText("");
                        jiraIssueFieldsDTO.getFields().put(field, summary);
                        break;
                    case issueType:
                        // 获取 issuetype
                        String issuetype = jsonObject.path("fields").path(field.getField()).path("name").asText("");
                        jiraIssueFieldsDTO.getFields().put(field, issuetype);
                        break;

                    default:
                        // 默认处理
                        String value = jsonObject.path("fields").path(field.getField()).asText("");
                        jiraIssueFieldsDTO.getFields().put(field, value);
                        break;
                }
            }

            return jiraIssueFieldsDTO;
        } catch (HttpClientErrorException.NotFound e) {
            throw new RuntimeException("Issue未找到: " + issueKey);
        } catch (Exception e) {
            log.error("获取Issue字段信息时发生异常: {}", e.getMessage(), e);
            return new JiraIssueFieldsDTO();
        }
    }


    @Override
    public List<JiraRequirementDTO> getRequirement(String jiraToken, String planKey, String cycleId) {
        Map<String, JiraRequirementDTO> requirementMap = new ConcurrentHashMap<>();
        Map<String, JiraIssueFieldsDTO> issueFieldCache = new ConcurrentHashMap<>(); // 缓存字段信息

        // 获取测试周期下的所有测试运行
        List<TestRunForCycle> testRuns = getTestRunsForCycleId(jiraToken, planKey, cycleId);

        // 使用线程池限制并发数
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        testRuns.forEach(testRun -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                // 获得测试用例的需求信息
                List<JiraTestCaseForRequirementDTO> requirementInfo = getRequirement(jiraToken, testRun.getTestCaseKey());

                if (requirementInfo.isEmpty()) {
                    log.warn("测试用例 {} 没有关联的需求信息", testRun.getTestCaseKey());
                    return;
                }

                JiraTestCaseForRequirementDTO jiraTestCaseForRequirement = requirementInfo.get(0);
                String key = jiraTestCaseForRequirement.getKey();

                // 使用 computeIfAbsent 确保去重
                JiraRequirementDTO existingRequirementDTO = requirementMap.computeIfAbsent(key, k -> {
                    JiraRequirementDTO dto = new JiraRequirementDTO();
                    dto.setKey(key);
                    dto.setSummary(jiraTestCaseForRequirement.getSummary());
                    return dto;
                });
                // 获取缓存中的字段信息
                JiraIssueFieldsDTO requirementFields = getCachedRequirementIssueFields(jiraToken, key, issueFieldCache);
                existingRequirementDTO.setTester(requirementFields.getFields().get(JiraIssueFieldEnum.tester));
                existingRequirementDTO.setStatus(requirementFields.getFields().get(JiraIssueFieldEnum.status));
                existingRequirementDTO.setAssignee(requirementFields.getFields().get(JiraIssueFieldEnum.assignee));
                String version = requirementFields.getFields().get(JiraIssueFieldEnum.fixVersions);
                List<String> versions = StrUtil.splitTrim(version, ',');
                existingRequirementDTO.setFixVersions(versions);
                String components = requirementFields.getFields().get(JiraIssueFieldEnum.components);
                List<String> componentList = StrUtil.splitTrim(components, ',');
                existingRequirementDTO.setModules(componentList);
            }, executorService);
            futures.add(future);
        });

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        executorService.shutdown();

        // 返回结果
        log.info("需求数量: {}，需求详情: {}", requirementMap.size(), requirementMap.keySet());
        return new ArrayList<>(requirementMap.values());
    }

    @Override
    public List<JiraTestCaseRunStepDTO> getFlattenedTestRunSteps(String jiraToken, String planKey, String cycleId, String requirementKey) {
        // 获取测试周期下的所有测试运行
        List<TestRunForCycle> testRuns = getTestRunsForCycleId(jiraToken, planKey, cycleId);
        log.info("测试运行详细: {}", testRuns);

        // 创建一个与testRuns大小相同的列表，用于存储结果
        List<List<JiraTestCaseRunStepDTO>> resultList = new ArrayList<>(Collections.nCopies(testRuns.size(), null));

        // 创建自定义线程池，控制并发线程数
        ExecutorService executor = Executors.newFixedThreadPool(10);  // 根据需要调整线程数

        // 使用 IntStream 并行处理，每个线程根据索引处理对应的 testRun
        IntStream.range(0, testRuns.size()).parallel().forEach(i -> {
            TestRunForCycle testRun = testRuns.get(i);
            try {
                // 处理单个 testRun，返回结果列表
                List<JiraTestCaseRunStepDTO> dtos = processTestRunForRequirement(jiraToken, requirementKey, testRun);
                // 将结果存储在 resultList 中对应的位置
                resultList.set(i, dtos);
            } catch (Exception e) {
                log.error("处理测试运行 {} 时发生异常: {}", testRun.getId(), e.getMessage(), e);
            }
        });

        // 关闭线程池
        executor.shutdown();

        // 将结果列表展平为单个列表，同时保持顺序
        List<JiraTestCaseRunStepDTO> jiraTestCaseRunSteps = resultList.stream()
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        return jiraTestCaseRunSteps;
    }

    // 处理单个 testRun，返回 JiraTestCaseRunStepDTO 的列表
    private List<JiraTestCaseRunStepDTO> processTestRunForRequirement(String jiraToken, String requirementKey, TestRunForCycle testRun) {
        List<JiraTestCaseRunStepDTO> dtos = new ArrayList<>();
        // 获得测试用例的需求信息
        List<JiraTestCaseForRequirementDTO> requirementInfo = getRequirement(jiraToken, testRun.getTestCaseKey());

        // 如果 requirementInfo 为空，直接跳过
        if (requirementInfo.isEmpty()) {
            log.warn("测试用例 {} 没有关联的需求信息", testRun.getTestCaseKey());
            return dtos;
        }

        // 判断 requirementKey 是否与任何一个需求的 key 匹配，如果没有匹配的就舍弃
        boolean matchesRequirement = requirementInfo.stream()
                .anyMatch(req -> req.getKey().equals(requirementKey));
        if (!matchesRequirement) {
//            log.info("测试用例 {} 关联的需求与指定的需求 {} 不匹配", testRun.getTestCaseKey(), requirementKey);
            return dtos;
        }

        // 获取测试运行详细信息
        TestRun testRunInfo = getTestRun(jiraToken, testRun.getId());
        // 获取测试用例的等级和前置条件
        JiraIssueFieldsDTO testCaseFields = getIssueField(jiraToken, testRun.getTestCaseKey(), List.of(
                JiraIssueFieldEnum.level,
                JiraIssueFieldEnum.precondition
        ));

        if (testRunInfo.getTestRunDetails().getTestRunBugs() != null) {
            // 设置测试运行上的bug状态
            testRunInfo.getTestRunDetails().getTestRunBugs().forEach(bug -> {
                JiraIssueFieldsDTO issueFields = getIssueField(jiraToken, bug.getKey(), List.of(JiraIssueFieldEnum.status));
                bug.setStatus(issueFields.getFields().get(JiraIssueFieldEnum.status));
            });
        }
        // 获取测试步骤列表，处理可能的空指针情况
        List<TestRunStep> testRunSteps = null;
        if (testRunInfo.getTestRunDetails() != null) {
            testRunSteps = testRunInfo.getTestRunDetails().getTestRunSteps();
        }

        int testRunStepCount = (testRunSteps != null) ? testRunSteps.size() : 0;

        if (testRunStepCount == 0) {
            // 创建不包含步骤的 DTO
            JiraTestCaseRunStepDTO dto = createDTO(testRunInfo, testCaseFields, null);
            dto.setRowspan(1); // 只有一个条目
            dto.setSortNumber(1); // 设置排序号
            dtos.add(dto);
        } else {
            for (int j = 0; j < testRunStepCount; j++) {
                TestRunStep testRunStep = testRunSteps.get(j);
                JiraTestCaseRunStepDTO dto = createDTO(testRunInfo, testCaseFields, testRunStep);
                dto.setRowspan(j == 0 ? testRunStepCount : 0); // 只有第一个步骤设置 rowspan
                dto.setSortNumber(j + 1); // 设置排序号
                dtos.add(dto);
            }
        }
        return dtos;
    }

    // 处理单个 testRun，返回 JiraTestCaseRunStepDTO 的列表
    private List<JiraTestCaseRunStepDTO> processTestRun(String jiraToken, TestRun testRun) {
        List<JiraTestCaseRunStepDTO> dtos = new ArrayList<>();

        // 获取测试运行详细信息
        TestRun testRunInfo = getTestRun(jiraToken, testRun.getId());
        // 获取测试用例的等级和前置条件
        JiraIssueFieldsDTO testCaseFields = getIssueField(jiraToken, testRun.getTestCaseKey(), List.of(
                JiraIssueFieldEnum.level,
                JiraIssueFieldEnum.precondition
        ));

        // 获取测试步骤列表，处理可能的空指针情况
        List<TestRunStep> testRunSteps = null;
        if (testRunInfo.getTestRunDetails() != null) {
            testRunSteps = testRunInfo.getTestRunDetails().getTestRunSteps();
        }

        int testRunStepCount = (testRunSteps != null) ? testRunSteps.size() : 0;

        if (testRunStepCount == 0) {
            // 创建不包含步骤的 DTO
            JiraTestCaseRunStepDTO dto = createDTO(testRunInfo, testCaseFields, null);
            dto.setRowspan(1); // 只有一个条目
            dto.setSortNumber(1); // 设置排序号
            dtos.add(dto);
        } else {
            for (int j = 0; j < testRunStepCount; j++) {
                TestRunStep testRunStep = testRunSteps.get(j);
                JiraTestCaseRunStepDTO dto = createDTO(testRunInfo, testCaseFields, testRunStep);
                log.info("dto: {}", dto);
                dto.setRowspan(j == 0 ? testRunStepCount : 0); // 只有第一个步骤设置 rowspan
                dto.setSortNumber(j + 1); // 设置排序号
                dtos.add(dto);
            }
        }
        return dtos;
    }

    // 创建 JiraTestCaseRunStepDTO 对象的辅助方法
    private JiraTestCaseRunStepDTO createDTO(TestRun testRunInfo, JiraIssueFieldsDTO testCaseFields, TestRunStep testRunStep) {
        JiraTestCaseRunStepDTO dto = new JiraTestCaseRunStepDTO();
        dto.setTestCaseKey(testRunInfo.getTestCaseKey());
        dto.setLevel(testCaseFields.getFields().get(JiraIssueFieldEnum.level));
        dto.setSummary(testRunInfo.getSummary());
        dto.setPrecondition(testCaseFields.getFields().get(JiraIssueFieldEnum.precondition));
        dto.setTestPlanKey(testRunInfo.getTestPlanKey());
        dto.setTestCycleId(testRunInfo.getTestCycleId());
        dto.setTestCaseId(testRunInfo.getTestCaseId());
        dto.setParentId(testRunInfo.getId());
        dto.setParentStatus(testRunInfo.getStatus());
        dto.setTestRunAttachments(testRunInfo.getTestRunDetails().getTestRunAttachments() == null ? new ArrayList<>() : testRunInfo.getTestRunDetails().getTestRunAttachments());
        // 测试运行上的缺陷
        dto.setTestRunBugsWrapper(testRunInfo.getTestRunDetails().getTestRunBugs());
        dto.setStepProperty("update"); // 默认为 update

        if (testRunStep != null) {
            dto.setExpectedResult(testRunStep.getExpectedResult());
            dto.setActualResult(testRunStep.getActualResult());
            dto.setStep(testRunStep.getStep());
            dto.setId(testRunStep.getId());
            dto.setStatus(testRunStep.getStatus());

        }

        return dto;
    }


    @Override
    public TestRun updateTestRunResult(String jiraToken, UpdateTestRunResultRequest request) {
        String runId = request.getRunId();
        String result = request.getResult();
        updateTestRunResult(jiraToken, runId, result, null);
        return getTestRun(jiraToken, runId);
    }

    @Override
    public TestRun updateTestStepResult(String jiraToken, UpdateTestStepResultRequest request) {
        String runId = request.getRunId();
        String runStepId = request.getRunStepId();
        String result = request.getResult();
        updateTestStepResult(jiraToken, runStepId, result, null);
        return getTestRun(jiraToken, runId);
    }

    @Override
    public void deleteTestCase(String jiraToken, DeleteTestCaseRequest request) {
        //遍历request.getTestRunIds()，解除关联
        request.getRemoveTestCaseKeys().forEach(testCaseKey -> CompletableFuture.runAsync(() -> deleteIssue(jiraToken, testCaseKey)));
    }

    private List<JiraTestCaseForRequirementDTO> getRequirement(String jiraToken, String testCaseKey) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<List<JiraTestCaseForRequirementDTO>> response = restTemplate.exchange(buildUrl(GET_REQUIREMENTS_FOR_TEST_CASE, testCaseKey), HttpMethod.GET, request, new ParameterizedTypeReference<>() {
        });
        return response.getBody();
    }

    /**
     * 修改测试周期的状态
     *
     * @param jiraToken        jiraToken
     * @param testPlanIssueKey 测试计划key
     * @param cycleName        测试周期名称
     * @param action           状态
     */
    private void updateCycleStatus(String jiraToken, String testPlanIssueKey, String cycleName, String action) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(buildUrl(UPDATE_CYCLE_STATUS, testPlanIssueKey, cycleName, action), HttpMethod.PUT, request, String.class);
        log.info("修改测试周期状态返回结果:{}", response.getBody());
    }

    @Override
    public void deleteTestRunAttachment(String jiraToken, String testRunId, String attachmentId) {
        deleteAttachment(jiraToken, testRunId, attachmentId);
    }

    @Override
    public void downloadTestRunAttachment(String jiraToken, String jiraUrl, HttpServletResponse response) {
        try {
            HttpHeaders headers = buildJiraHeader(jiraToken);

            // 创建 HttpEntity，带上请求头
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 通过 RestTemplate 发起 GET 请求，获取附件数据
            ResponseEntity<byte[]> responseEntity = restTemplate.exchange(jiraUrl, HttpMethod.GET, entity, byte[].class);

            // 判断响应状态
            if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.hasBody()) {
                // 获取文件数据
                byte[] fileData = responseEntity.getBody();

                // 从响应头中获取 Content-Disposition，用于提取文件名
                String contentDisposition = responseEntity.getHeaders().getFirst(HttpHeaders.CONTENT_DISPOSITION);
                String fileName = "downloaded-file";

                // 优先处理 filename*=UTF-8'' 格式
                if (contentDisposition != null) {
                    if (contentDisposition.contains("filename*=")) {
                        fileName = contentDisposition.split("filename\\*=UTF-8''")[1];
                        fileName = URLDecoder.decode(fileName, StandardCharsets.UTF_8); // 解码 UTF-8 编码的文件名
                    } else if (contentDisposition.contains("filename=")) {
                        // 如果没有 filename*，则处理普通的 filename=
                        fileName = contentDisposition.split("filename=")[1].replace("\"", "");
                    }
                }

                log.info("下载文件名: {}", fileName);

                // 使用 URLEncoder 对文件名进行 UTF-8 编码，保证在设置响应头时格式正确
                String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");

                // 设置响应头，只使用 filename* 格式
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
                response.setContentLength(fileData.length);

                // 写入响应输出流并确保关闭资源
                try (OutputStream outputStream = response.getOutputStream()) {
                    outputStream.write(fileData);
                    outputStream.flush();
                }
            } else {
                // 处理非200响应状态码的情况
                response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
                response.getWriter().write("文件下载失败，状态码: " + responseEntity.getStatusCode());
            }
        } catch (Exception e) {
            // 异常处理
            try {
                response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
                response.getWriter().write("文件下载失败: " + e.getMessage());
            } catch (Exception ex) {
                log.error("文件下载失败: " + ex.getMessage(), ex);
            }
        }
    }

    @Override
    public List<JiraTestCaseRunStepDTO> updateTestStep(String jiraToken, List<JiraTestCaseRunStepDTO> steps) {
        // 检查步骤列表是否为空
        if (steps == null || steps.isEmpty()) {
            throw new IllegalArgumentException("步骤列表不能为空");
        }

        JiraTestCaseRunStepDTO firstStep = steps.get(0);
        String testPlanKey = firstStep.getTestPlanKey();
        String testCycleId = firstStep.getTestCycleId();
        String testCaseKey = firstStep.getTestCaseKey();
        String runId = firstStep.getParentId();

        // 过滤掉被删除的步骤
        List<JiraTestCaseRunStepDTO> nonDeletedSteps = steps.stream()
                .filter(step -> !StepProperty.DELETE.getValue().equals(step.getStepProperty()))
                .toList();

        // 获取测试运行信息
        TestRun testRun = getTestRun(jiraToken, runId);
        List<TestRunStep> existingTestRunSteps = Optional.ofNullable(testRun.getTestRunDetails())
                .map(TestRunDetails::getTestRunSteps)
                .orElse(new ArrayList<>());

        // 如果没有现有的测试运行步骤，将所有步骤属性设置为“添加”
        if (existingTestRunSteps.isEmpty()) {
            steps.forEach(step -> step.setStepProperty(StepProperty.ADD.getValue()));
        }

        // 处理每个步骤
        for (int i = 0; i < steps.size(); i++) {
            JiraTestCaseRunStepDTO currentStep = steps.get(i);
            String stepProperty = currentStep.getStepProperty();

            if (stepProperty == null) {
                throw new IllegalArgumentException("步骤属性不能为空");
            }

            String stepText = Optional.ofNullable(currentStep.getStep()).orElse("");
            String expectedResult = Optional.ofNullable(currentStep.getExpectedResult()).orElse("");

            if (StepProperty.UPDATE.getValue().equals(stepProperty)) {
                // 检查索引是否越界
                if (i >= existingTestRunSteps.size()) {
                    throw new IndexOutOfBoundsException("更新步骤时索引越界，索引：" + i);
                }
                handleUpdateStep(jiraToken, currentStep, existingTestRunSteps.get(i), i + 1);
            } else if (StepProperty.ADD.getValue().equals(stepProperty)) {
                handleAddStep(jiraToken, testCaseKey, stepText, expectedResult);
            } else if (StepProperty.DELETE.getValue().equals(stepProperty)) {
                handleDeleteStep(jiraToken, currentStep, i + 1);
            } else {
                throw new IllegalArgumentException("未知的步骤属性：" + stepProperty);
            }
        }
        // 获取测试运行关联的bug
        String[] bugs = extractBugIds(testRun.getTestRunDetails().getTestRunBugs());

        // 重新加载测试运行
        reload(jiraToken, testPlanKey, testCycleId, runId);

        // 获取最新的测试运行信息
        testRun = getTestRun(jiraToken, runId);
        List<TestRunStep> updatedTestRunSteps = Optional.ofNullable(testRun.getTestRunDetails())
                .map(TestRunDetails::getTestRunSteps)
                .orElse(new ArrayList<>());

        // 检查步骤数量是否匹配
        if (nonDeletedSteps.size() != updatedTestRunSteps.size()) {
            throw new IllegalStateException("重载后的步骤数量与未删除的原始步骤数量不匹配");
        }

        // 更新每个步骤的结果和关联的bug
        for (int i = 0; i < nonDeletedSteps.size(); i++) {
            JiraTestCaseRunStepDTO originalStep = nonDeletedSteps.get(i);
            TestRunStep runStep = updatedTestRunSteps.get(i);

            String runStepId = runStep.getId();
            String stepStatus = originalStep.getStatus();
            updateTestStepResult(jiraToken, runStepId, stepStatus, null);
        }

        // 更新测试运行的结果和关联的bug
        String runStatus = firstStep.getParentStatus();
        updateTestRunResult(jiraToken, runId, runStatus, bugs);

        // 处理并返回更新后的测试运行信息
        testRun = getTestRun(jiraToken, runId);
        return processTestRun(jiraToken, testRun);
    }

    private void handleUpdateStep(String jiraToken, JiraTestCaseRunStepDTO currentStep, TestRunStep existingStep, int stepIndex) {
        String newStepText = Optional.ofNullable(currentStep.getStep()).orElse("");
        String newExpectedResult = Optional.ofNullable(currentStep.getExpectedResult()).orElse("");

        String existingStepText = Optional.ofNullable(existingStep.getStep()).orElse("");
        String existingExpectedResult = Optional.ofNullable(existingStep.getExpectedResult()).orElse("");

        // 只有当步骤或预期结果发生变化时才更新
        if (!newStepText.equals(existingStepText) || !newExpectedResult.equals(existingExpectedResult)) {
            updateTestStep(jiraToken, currentStep.getTestCaseKey(), String.valueOf(stepIndex), newStepText, newExpectedResult);
        }
    }

    private void handleDeleteStep(String jiraToken, JiraTestCaseRunStepDTO currentStep, int stepIndex) {
        deleteTestStep(jiraToken, currentStep.getTestCaseKey(), String.valueOf(stepIndex));
    }

    private String[] extractBugIds(List<BugDTO> bugs) {
        return Optional.ofNullable(bugs)
                .orElse(Collections.emptyList())
                .stream()
                .map(bug -> String.valueOf(bug.getKey()))
                .toArray(String[]::new);
    }

    private void handleAddStep(String jiraToken, String testCaseKey, String stepText, String expectedResult) {
        TestCaseStep testCaseStep = new TestCaseStep();
        testCaseStep.setStep(stepText);
        testCaseStep.setExpectedResult(expectedResult);

        List<TestCaseStep> testCaseSteps = new ArrayList<>();
        testCaseSteps.add(testCaseStep);

        addTestStep(buildJiraHeader(jiraToken), testCaseKey, testCaseSteps);
    }


    @Override
    public List<AttachmentDTO> getTestRunAttachments(String jiraToken, String testRunId) {
        return getAttachment(jiraToken, testRunId);
    }

    @Override
    public void createTestCase(String jiraToken, CreateTestCaseRequest request) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        TestCaseIssue testCaseIssue = mapToTestCaseIssue(request);
        log.info("创建测试用例: {}", testCaseIssue);
        // 创建测试用例
        CreateTestCaseResponse response = createTestCase(headers, testCaseIssue);

        // 获得测试用例编号
        String testCaseKey = response.getKey();

        // 关联计划
        addTestCaseToPlan(jiraToken, request.getTestPlanKey(), testCaseKey);

        // 关联测试周期
        addTestCaseToCycle(headers, request.getTestPlanKey(), request.getCycleId(), request.getCycleName(), testCaseKey);

        // 关联需求
        linkTestCaseToRequirement(headers, request.getRequirementKey(), testCaseKey);

        // 关联测试集
        if (!request.getTestSuitePath().isEmpty()) {
            LinkToTestSuite linkToTestSuite = new LinkToTestSuite();
            linkToTestSuite.setProjectKey(request.getProjectKey());
            linkToTestSuite.setTestSuitePath(request.getTestSuitePath());
            linkToTestSuite.setTestCaseKeys(List.of(testCaseKey));
            linkTestCaseToSuite(headers, linkToTestSuite);
        }
    }

    @Override
    public BugDTO createBug(String jiraToken, CreateBugRequest request) {
        BugIssue bugIssue = mapToBugIssue(request);
        CreateBugResponse response = createBug(buildJiraHeader(jiraToken), bugIssue);
        String bugKey = response.getKey();
        JiraIssueFieldsDTO issueField = getIssueField(jiraToken, bugKey, List.of(JiraIssueFieldEnum.status));
        String status = issueField.getFields().get(JiraIssueFieldEnum.status);
        BugDTO bugDTO = new BugDTO();
        bugDTO.setKey(bugKey);
        bugDTO.setStatus(status);
        // 关联缺陷上的附件，异步上传
        List<String> attachmentIds = request.getAttachmentFileIds();
        if (attachmentIds != null) {
            attachmentIds.forEach(attachmentId -> CompletableFuture.runAsync(() -> {
                try {
                    TFile file = new TFile();
                    file.setId(attachmentId);
                    TFile fileInfo = file.selectById();
                    addAttachment(buildJiraHeaderMultiPart(jiraToken), bugKey, fileInfo.getFilePath(), fileInfo.getFileName());
                } catch (Exception e) {
                    log.error("上传附件失败: {}", e.getMessage(), e);
                }
            }));
        }
        linkBugsToTestRunStep(jiraToken, request.getRunId(), request.getStepId(), List.of(bugKey));
        return bugDTO;
    }

    private CreateBugResponse createBug(HttpHeaders headers, BugIssue bugIssue) {
        HttpEntity<BugIssue> request = new HttpEntity<>(bugIssue, headers);
        ResponseEntity<CreateBugResponse> response = restTemplate.exchange(CREATE_ISSUE, HttpMethod.POST, request, CreateBugResponse.class);
        return response.getBody();
    }

    @Override
    public List<String> getAllTestSuiteForProject(String jiraToken, String projectKey) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        return getAllTestSuiteForProject(headers, projectKey).stream()
                .map(GetTestSuitesResponse::getName)
                .collect(Collectors.toList());
    }

    /**
     * 获取测试计划的版本
     *
     * @param jiraToken   jiraToken
     * @param testPlanKey 测试计划key
     * @return 版本列表
     */
    @Override
    public List<String> getTestPlanVersions(String jiraToken, String testPlanKey) {
        JiraIssueFieldsDTO issueField = getIssueField(jiraToken, testPlanKey, List.of(JiraIssueFieldEnum.fixVersions));
        String fixVersions = issueField.getFields().get(JiraIssueFieldEnum.fixVersions);
        return Arrays.asList(fixVersions.split(","));
    }

    @Override
    public JiraUserDTO getJiraUserInfo(String jiraToken) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        return getUserInfoByJiraToken(headers);
    }

    @Override
    public List<BugInfoDTO> getJiraBugsInfo(String jiraToken, List<String> issueKeys) {
        if (issueKeys == null || issueKeys.isEmpty()) {
            return List.of();
        }
        Map<String, JiraIssueFieldsDTO> issueFieldCache = new ConcurrentHashMap<>();

        return issueKeys.parallelStream()
                .map(issueKey -> {
                    try {
                        JiraIssueFieldsDTO issueFields = getCachedBugIssueFields(jiraToken, issueKey, issueFieldCache);
                        BugInfoDTO bugInfo = new BugInfoDTO();
                        bugInfo.setKey(issueKey);
                        bugInfo.setAssignee(issueFields.getFields().get(JiraIssueFieldEnum.assignee));
                        bugInfo.setStatus(issueFields.getFields().get(JiraIssueFieldEnum.status));
                        bugInfo.setSummary(issueFields.getFields().get(JiraIssueFieldEnum.summary));
                        return bugInfo;
                    } catch (Exception e) {
                        log.error("获取缺陷信息时发生异常: {}", e.getMessage(), e);
                        return null;
                    }
                })
                // 过滤掉处理失败的结果
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<BugInfoDTO> getTestRunBugsInfo(String jiraToken, String testRunId) {
        List<BugDTO> bugs = getTestRunBugs(jiraToken, testRunId);
        if (bugs != null && !bugs.isEmpty()) {
            List<String> issueKeys = bugs.stream().map(BugDTO::getKey).collect(Collectors.toList());
            return getJiraBugsInfo(jiraToken, issueKeys);
        }
        return List.of();
    }

    @Override
    public void linkTestRunStepBugs(String jiraToken, LinkTestRunStepBugsRequest request) {
        log.info("关联测试运行步骤的BUG: {}", request);
        linkBugsToTestRunStep(jiraToken, request.getRunId(), request.getStepId(), request.getIssueKeys());
    }

    @Override
    public String getIssueSummary(String jiraToken, String issueKey) {
        JiraIssueFieldsDTO issueFields = getIssueField(jiraToken, issueKey, List.of(JiraIssueFieldEnum.summary));
        return issueFields.getFields().get(JiraIssueFieldEnum.summary);
    }

    @Override
    public String getTestPlanSummary(String jiraToken, String issueKey) {
        try {
            JiraIssueFieldsDTO issueFields = getIssueField(jiraToken, issueKey, List.of(JiraIssueFieldEnum.summary, JiraIssueFieldEnum.issueType));
            if (issueFields.getFields().get(JiraIssueFieldEnum.issueType).equals("测试计划")) {
                return issueFields.getFields().get(JiraIssueFieldEnum.summary);
            }
        } catch (Exception ignored) {
        }
        throw new IllegalArgumentException(issueKey + "不是测试计划");
    }

    @Override
    public List<String> getComponentsForProject(String jiraToken, String projectKey) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        List<ComponentDTO> components = getComponentsForProject(headers, projectKey);
        // 将components根据ID进行倒叙排序，然后返回name
        return components.stream()
                .sorted(Comparator.comparing(ComponentDTO::getId).reversed())
                .map(ComponentDTO::getName)
                .collect(Collectors.toList());
    }

    @Override
    public List<JiraUserDTO> getAllJiraUsers(String jiraToken) {
        return getAllUserInfos(buildJiraHeader(jiraToken));
    }

    @Override
    public void uploadTestRunAttachment(String jiraToken, String testRunId, MultipartFile file) {
        addAttachmentToTestRun(jiraToken, testRunId, file);
    }

    @Override
    public List<JiraTestCaseRunStepDTO> deleteBug(String jiraToken, String runId, String bugKey) {
        deleteIssue(jiraToken, bugKey);
        // 处理并返回更新后的测试运行信息
        TestRun testRun = getTestRun(jiraToken, runId);
        return processTestRun(jiraToken, testRun);
    }

    /**
     * 获取测试运行的所有缺陷
     */
    private List<BugDTO> getTestRunBugs(String jiraToken, String testRunId) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<List<BugDTO>> response = restTemplate.exchange(buildUrl(GET_TEST_RUN_DEFECTS, testRunId), HttpMethod.GET, request, new ParameterizedTypeReference<>() {
        });
        return response.getBody();
    }

    /**
     * 向测试运行添加附件
     */
    private void addAttachmentToTestRun(String jiraToken, String testRunId, MultipartFile file) {
        HttpHeaders headers = buildJiraHeaderMultiPart(jiraToken);
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        try {
            ByteArrayResource resource = new ByteArrayResource(file.getBytes()) {
                @Override
                public String getFilename() {
                    return file.getOriginalFilename();
                }
            };
            body.add("file", resource);
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            ResponseEntity<String> response = restTemplate.exchange(buildUrl(ADD_TEST_RUN_ATTACHMENT, testRunId), HttpMethod.POST, requestEntity, String.class);
            log.info("添加运行附件返回结果:{}", response.getBody());
        } catch (IOException e) {
            log.error("添加运行附件失败: {}", e.getMessage(), e);
            throw new RuntimeException("添加运行附件失败", e);
        }
    }

    /**
     * 查询jira的所有taimei用户
     */
    private List<JiraUserDTO> getAllUserInfos(HttpHeaders headers) {
        final int MAX_RESULTS = 1000;
        List<JiraUserDTO> allUsers = new ArrayList<>();
        int startAt = 0;

        while (true) {
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(GET_ALL_USERS)
                    .queryParam("username", ".")
                    .queryParam("maxResults", MAX_RESULTS)
                    .queryParam("startAt", startAt);
            HttpEntity<String> request = new HttpEntity<>(headers);
            ResponseEntity<List<JiraUserDTO>> response = restTemplate.exchange(
                    uriBuilder.toUriString(),
                    HttpMethod.GET,
                    request,
                    new ParameterizedTypeReference<>() {
                    }
            );
            List<JiraUserDTO> users = response.getBody();
            if (users == null || users.isEmpty()) {
                break;
            }

            // 过滤满足条件的用户
            List<JiraUserDTO> validUsers = users.stream()
                    .filter(user -> isValidEmail(user.getEmailAddress()))
                    .toList();

            // 为过滤后的用户生成 displayNamePinyin
            validUsers.forEach(user -> {
                if (user.getDisplayName() != null && !user.getDisplayName().isEmpty()) {
                    String pinyin = PinyinUtil.getPinyin(user.getDisplayName(), "");
                    user.setDisplayNamePinyin(pinyin);
                } else {
                    user.setDisplayNamePinyin("");
                }
            });

            allUsers.addAll(validUsers);

            if (users.size() < MAX_RESULTS) {
                // 最后一页
                break;
            }
            startAt += MAX_RESULTS;
        }

        return allUsers;
    }

    private boolean isValidEmail(String email) {
        if (email == null) {
            return false;
        }

        int atIndex = email.indexOf('@');
        if (atIndex <= 0) { // 没有@或者@在第一个字符
            return false;
        }

        String localPart = email.substring(0, atIndex);
        String domainPart = email.substring(atIndex + 1);

        // 本地部分包含 '.'
        if (!localPart.contains(".")) {
            return false;
        }

        // 域名不为 'ecr-global.com'
        return !domainPart.equalsIgnoreCase("ecr-global.com");
    }

    /**
     * 获取项目下所有模块
     */
    private List<ComponentDTO> getComponentsForProject(HttpHeaders headers, String projectKey) {
        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<List<ComponentDTO>> response = restTemplate.exchange(buildUrl(GET_COMPONENTS, projectKey), HttpMethod.GET, request, new ParameterizedTypeReference<>() {
        });
        return response.getBody();
    }

    /**
     * 获取项目下所有的测试用例集
     */
    private List<GetTestSuitesResponse> getAllTestSuiteForProject(HttpHeaders headers, String projectKey) {
        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<List<GetTestSuitesResponse>> response = restTemplate.exchange(buildUrl(GET_TEST_SUITES, projectKey), HttpMethod.GET, request, new ParameterizedTypeReference<>() {
        });
        return response.getBody();
    }

    /**
     * 关联bugs到测试运行步骤
     */
    private void linkBugsToTestRunStep(String jiraToken, String runId, String stepId, List<String> issueKeys) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("runId", runId);
        requestBody.put("stepId", stepId);
        requestBody.put("issueKeys", issueKeys);
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
        ResponseEntity<String> response = restTemplate.exchange(ADD_TEST_RUN_STEP_BUGS, HttpMethod.PUT, request, String.class);
        log.info("关联bugs到测试运行步骤返回结果:{}", response.getBody());
    }

    /**
     * 创建测试用例
     */
    private CreateTestCaseResponse createTestCase(HttpHeaders headers, TestCaseIssue testCaseIssue) {
        HttpEntity<TestCaseIssue> request = new HttpEntity<>(testCaseIssue, headers);
        try {

            ResponseEntity<CreateTestCaseResponse> response = restTemplate.exchange(buildUrl(CREATE_TEST_CASE), HttpMethod.POST, request, CreateTestCaseResponse.class);
            if (response.getStatusCode() != HttpStatus.OK) {
                throw new RuntimeException("创建测试用例失败: HTTP error code " + response.getStatusCode());
            }
            return response.getBody();
        } catch (Exception e) {
            log.error("创建测试用例失败:{}", e.getMessage());
            throw new RuntimeException("创建测试用例失败", e);
        }
    }

    /**
     * 映射 CreateTestCaseRequest 到 TestCaseIssue
     */
    private TestCaseIssue mapToTestCaseIssue(CreateTestCaseRequest request) {
        TestCaseIssue testCaseIssue = new TestCaseIssue();

        TestCaseIssue.Fields fields = new TestCaseIssue.Fields();

        // 设置项目
        TestCaseIssue.Fields.Project project = new TestCaseIssue.Fields.Project();
        project.setKey(request.getProjectKey());
        fields.setProject(project);

        // 设置摘要
        fields.setSummary(request.getSummary());

        // 设置问题类型
        TestCaseIssue.Fields.IssueType issueType = new TestCaseIssue.Fields.IssueType();
        issueType.setName("测试用例");
        fields.setIssuetype(issueType);

        // 设置修复版本
        List<TestCaseIssue.Fields.FixVersion> fixVersions = request.getFixVersions().stream()
                .map(versionName -> {
                    TestCaseIssue.Fields.FixVersion fixVersion = new TestCaseIssue.Fields.FixVersion();
                    fixVersion.setName(versionName);
                    return fixVersion;
                })
                .collect(Collectors.toList());
        fields.setFixVersions(fixVersions);

        // 设置指派人
        if (request.getAssignee() != null && !request.getAssignee().isEmpty()) {
            TestCaseIssue.Fields.Assignee assignee = new TestCaseIssue.Fields.Assignee();
            assignee.setName(request.getAssignee());
            fields.setAssignee(assignee);
        }

        // 设置用例等级
        TestCaseIssue.Fields.Customfield12851 customField = new TestCaseIssue.Fields.Customfield12851();
        customField.setValue(request.getLevel());
        fields.setCustomfield_12851(customField);

        // 设置前提
        fields.setCustomfield_12859(request.getPrecondition());

        testCaseIssue.setFields(fields);

        // 设置用例步骤
        if (request.getTestCaseSteps() != null && !request.getTestCaseSteps().isEmpty()) {
            List<TestCaseIssue.TestCaseStep> testCaseSteps = request.getTestCaseSteps().stream().map(step -> {
                TestCaseIssue.TestCaseStep testCaseStep = new TestCaseIssue.TestCaseStep();
                testCaseStep.setStep(step.getStep());
                testCaseStep.setExpectedResult(step.getExpectedResult());
                return testCaseStep;
            }).collect(Collectors.toList());
            testCaseIssue.setTestcasesteps(testCaseSteps);
        }

        return testCaseIssue;
    }

    private BugIssue mapToBugIssue(CreateBugRequest request) {
        BugIssue bugIssue = new BugIssue();
        BugIssue.Fields fields = new BugIssue.Fields();

        // 设置项目
        BugIssue.Fields.Project project = new BugIssue.Fields.Project();
        project.setKey(request.getProjectKey());
        fields.setProject(project);

        // 设置摘要
        fields.setSummary(request.getSummary());

        // 设置问题类型
        BugIssue.Fields.IssueType issueType = new BugIssue.Fields.IssueType();
        issueType.setName("缺陷");
        fields.setIssuetype(issueType);

        // 设置修复版本
        if (request.getFixVersions() != null && !request.getFixVersions().isEmpty()) {
            List<BugIssue.Fields.FixVersion> fixVersions = request.getFixVersions().stream()
                    .map(versionName -> {
                        BugIssue.Fields.FixVersion fixVersion = new BugIssue.Fields.FixVersion();
                        fixVersion.setName(versionName);
                        return fixVersion;
                    })
                    .collect(Collectors.toList());
            fields.setFixVersions(fixVersions);
        }

        // 设置重现步骤
        if (request.getReproductionSteps() != null) {
            fields.setCustomfield_10103(request.getReproductionSteps());
        }

        // 设置期望结果
        if (request.getExpectedResult() != null) {
            fields.setCustomfield_10104(request.getExpectedResult());
        }

        // 设置实际结果
        if (request.getActualResult() != null) {
            fields.setCustomfield_10105(request.getActualResult());
        }

        // 设置严重程度
        if (request.getSeverity() != null) {
            BugIssue.Fields.Customfield10109 severity = new BugIssue.Fields.Customfield10109();
            severity.setId(String.valueOf(request.getSeverity()));
            fields.setCustomfield_10109(severity);
        }

        // 设置优先级
        if (request.getPriority() != null) {
            BugIssue.Fields.IssuePriority priority = new BugIssue.Fields.IssuePriority();
            priority.setId(String.valueOf(request.getPriority()));
            fields.setPriority(priority);
        }

        // 设置模块
        if (request.getComponents() != null && !request.getComponents().isEmpty()) {
            List<BugIssue.Fields.Components> components = request.getComponents().stream()
                    .map(componentName -> {
                        BugIssue.Fields.Components component = new BugIssue.Fields.Components();
                        component.setName(componentName);
                        return component;
                    })
                    .collect(Collectors.toList());
            fields.setComponents(components);
        }

        // 设置缺陷实际负责人
        if (request.getDefectAssignee() != null && !request.getDefectAssignee().isEmpty()) {
            List<BugIssue.Fields.Customfield11769> defectAssignees = request.getDefectAssignee().stream()
                    .map(assigneeName -> {
                        BugIssue.Fields.Customfield11769 assignee = new BugIssue.Fields.Customfield11769();
                        assignee.setName(assigneeName);
                        return assignee;
                    })
                    .collect(Collectors.toList());
            fields.setCustomfield_11769(defectAssignees);
        }

        // 设置研发负责人
        if (request.getDeveloperInCharge() != null) {
            BugIssue.Fields.customfield12958 customfield12958 = new BugIssue.Fields.customfield12958();
            customfield12958.setName(request.getDeveloperInCharge());
            fields.setCustomfield_12958(customfield12958);
        }

        // 设置描述
        if (request.getDescription() != null) {
            fields.setDescription(request.getDescription());
        }

        bugIssue.setFields(fields);
        return bugIssue;
    }


    /**
     * Issue添加附件
     */
    private void addAttachment(HttpHeaders headers, String issueKey, String filePath, String fileName) {
        headers.add("X-Atlassian-Token", "no-check");
        File file = new File(filePath);
        if (!file.exists() || !file.isFile()) {
            throw new IllegalArgumentException("Invalid file path provided: " + filePath);
        }
        FileSystemResource resource = new FileSystemResource(file) {
            @Override
            public String getFilename() {
                return fileName;
            }
        };
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", resource);
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        ResponseEntity<String> response = restTemplate.exchange(buildUrl(ADD_ATTACHMENT, issueKey), HttpMethod.POST, requestEntity, String.class);
        log.info("添加附件返回结果:{}", response.getBody());

    }


    /**
     * 删除测试运行附件
     *
     * @param jiraToken    jiraToken
     * @param testRunId    测试运行id
     * @param attachmentId 附件id
     */
    private void deleteAttachment(String jiraToken, String testRunId, String attachmentId) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(buildUrl(DELETE_TEST_RUN_ATTACHMENT, testRunId, attachmentId), HttpMethod.DELETE, request, String.class);
        log.info("删除测试运行{}的附件返回结果:{}", testRunId, response.getBody());
    }

    /**
     * 通过步骤序号 更新测试用例
     */
    private void updateTestStep(String jiraToken, String testCaseKey, String sequenceNumber, String step, String expectedResult) {
        log.info("更新测试用例{}步骤{}的信息:步骤序号:{}，预期结果:{}", testCaseKey, sequenceNumber, step, expectedResult);
        HttpHeaders headers = buildJiraHeader(jiraToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("sequenceNumber", sequenceNumber);
        requestBody.put("step", step);
        requestBody.put("expectedResult", expectedResult);
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
        try {
            ResponseEntity<String> response = restTemplate.exchange(buildUrl(UPDATE_TEST_CASE_STEP, testCaseKey), HttpMethod.PUT, request, String.class);
            log.info("更新测试步骤序号{}的结果返回结果:{}", sequenceNumber, response.getBody());
        } catch (Exception e) {
            log.error("更新测试步骤序号{}的信息失败:{}", sequenceNumber, e.getMessage());
            throw new RuntimeException("更新测试步骤信息失败", e);
        }
    }

    /**
     * 通过ID删除测试用例的步骤
     */
    private void deleteTestStep(String jiraToken, String testCaseKey, String sequenceNumber) {
        HttpHeaders headers = buildJiraHeader(jiraToken);
        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(buildUrl(DELETE_TEST_STEP_BY_INDEX, testCaseKey, sequenceNumber), HttpMethod.DELETE, request, String.class);
        log.info("删除测试{}步骤序号{}的结果返回结果:{}", testCaseKey, sequenceNumber, response.getBody());
    }

    // 缓存字段获取
    private JiraIssueFieldsDTO getCachedRequirementIssueFields(String jiraToken, String key, Map<String, JiraIssueFieldsDTO> issueFieldCache) {
        return issueFieldCache.computeIfAbsent(key, k -> getIssueField(jiraToken, key, List.of(
                JiraIssueFieldEnum.tester,
                JiraIssueFieldEnum.status,
                JiraIssueFieldEnum.fixVersions,
                JiraIssueFieldEnum.assignee,
                JiraIssueFieldEnum.components
        )));
    }

    private JiraIssueFieldsDTO getCachedBugIssueFields(String jiraToken, String key, Map<String, JiraIssueFieldsDTO> issueFieldCache) {
        return issueFieldCache.computeIfAbsent(key, k -> getIssueField(jiraToken, key, List.of(
                JiraIssueFieldEnum.status,
                JiraIssueFieldEnum.summary,
                JiraIssueFieldEnum.assignee
        )));
    }

    private String buildUrl(String path, Object... params) {
        return String.format(path, params);
    }

    // 构建Jira查询URL
    private String buildUrl(List<JiraIssueFieldEnum> fields, Object... params) {
        String url = buildUrl(JiraServiceImpl.GET_FIELD, params);
        // 使用fields参数来构建查询字段
        String fieldsQuery = fields.stream()
                .map(field -> "fields=" + field.getField())
                .collect(Collectors.joining("&"));
        return url + "?" + fieldsQuery;
    }

}
