package com.kf.baosi.dto.verifyDocument.OQT;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class OQTTestCaseParams {

    // 步骤id
    @JsonProperty("id")
    private String id;

    // 步骤
    @JsonProperty("step")
    private String step;

    // 预期结果
    @JsonProperty("result")
    private String result;

    // 备注
    @JsonProperty("remark")
    private String remark;

    // 结论
    @JsonProperty("conclusion")
    private String conclusion;

}
