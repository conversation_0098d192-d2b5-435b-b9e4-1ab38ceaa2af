package com.kf.uitest.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.Map;

@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ui_test_step")
public class UiTestStep extends Model<UiTestStep> {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @TableField("block_id")
    private String blockId;

    @TableField("action_type")
    private String actionType;

    @TableField("selector")
    private String selector;

    @TableField("input_data")
    private String inputData;

    @TableField("step_order")
    private Integer stepOrder;

    @TableField("wait_time")
    private Integer waitTime;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;

}
