package com.kf.aitest.integration;

import com.kf.aitest.service.impl.AiEvaluationServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ARK API集成测试
 * 注意：这些测试会调用真实的API，需要有效的API Key
 */
@Slf4j
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
class ArkApiIntegrationTest {

    @Autowired
    private AiEvaluationServiceImpl aiEvaluationService;

    @Test
    @Disabled("需要真实API Key才能运行")
    void testCallAiService_TextOnly() {
        // 准备测试数据
        String prompt = "请简单介绍一下人工智能的发展历史，不超过100字。";

        try {
            // 执行测试
            String result = aiEvaluationService.callAiServiceWithImage(prompt, null);

            // 验证结果
            assertNotNull(result);
            assertFalse(result.trim().isEmpty());
            log.info("AI响应: {}", result);

        } catch (Exception e) {
            log.error("测试失败: {}", e.getMessage(), e);
            fail("API调用失败: " + e.getMessage());
        }
    }

    @Test
    @Disabled("需要真实API Key和图像URL才能运行")
    void testCallAiService_WithImage() {
        // 准备测试数据
        String prompt = "图片主要讲了什么?";
        String imageUrl = "https://ark-project.tos-cn-beijing.ivolces.com/images/view.jpeg";

        try {
            // 执行测试
            String result = aiEvaluationService.callAiServiceWithImage(prompt, imageUrl);

            // 验证结果
            assertNotNull(result);
            assertFalse(result.trim().isEmpty());
            log.info("AI响应: {}", result);

        } catch (Exception e) {
            log.error("测试失败: {}", e.getMessage(), e);
            fail("API调用失败: " + e.getMessage());
        }
    }

    @Test
    void testImageUrlValidation() {
        // 测试有效的图像URL
        String validImageUrl = "https://example.com/test.jpg";
        assertDoesNotThrow(() -> {
            // 这里只测试URL验证，不实际调用API
            log.info("测试有效图像URL: {}", validImageUrl);
        });

        // 测试无效的图像URL
        String invalidImageUrl = "not-a-url";
        assertThrows(Exception.class, () -> {
            aiEvaluationService.callAiServiceWithImage("test", invalidImageUrl);
        });
    }

    @Test
    void testPromptLengthHandling() {
        // 测试长提示词处理
        StringBuilder longPrompt = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longPrompt.append("这是一个很长的测试提示词。");
        }

        String prompt = longPrompt.toString();
        log.info("测试长提示词，长度: {} 字符", prompt.length());

        // 验证不会因为长度而直接抛出异常
        assertDoesNotThrow(() -> {
            // 这里只测试参数处理，不实际调用API
            assertTrue(prompt.length() > 10000);
        });
    }
}
