package com.kf.baosi.entity;

import lombok.Data;

import java.util.Date;

@Data
public class DataToJiraParam {
    /**
     * 主键
     */
    private String id;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件存储路径
     */
    private String filePath;
    /**
     * 文件大小
     */
    private Long fileSize;
    /**
     * 文件MD5
     */
    private String fileMd5;
    /**
     * 文件后缀名
     */
    private String fileSuffix;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除  0未删除 1删除
     */
    private Integer isDeleted;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 是否完成  0未完成 1完成
     * 进度是否完成，如果没完成则需要使用taskId继续请求进度
     */
    private Integer isFinished;

}
