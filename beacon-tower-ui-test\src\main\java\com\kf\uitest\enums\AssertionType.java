package com.kf.uitest.enums;

import lombok.Getter;

@Getter
public enum AssertionType {
    // 元素相关断言
    ELEMENT_PRESENT("元素存在"),
    ELEMENT_NOT_PRESENT("元素不存在"),
    ELEMENT_VISIBLE("元素可见"),
    ELEMENT_NOT_VISIBLE("元素不可见"),
    ELEMENT_ENABLED("元素可用"),
    ELEMENT_DISABLED("元素不可用"),
    ELEMENT_SELECTED("元素已选中"),
    ELEMENT_NOT_SELECTED("元素未选中"),
    ELEMENT_TEXT("元素文本"),
    ELEMENT_VALUE("元素值"),
    ELEMENT_ATTRIBUTE("元素属性"),
    ELEMENT_CSS("元素CSS属性"),
    ELEMENT_COUNT("元素数量"),
    
    // 页面相关断言
    PAGE_TITLE("页面标题"),
    PAGE_URL("页面URL"),
    PAGE_SOURCE("页面源码"),
    
    // 网络相关断言
    NETWORK_REQUEST("网络请求"),
    NETWORK_RESPONSE("网络响应"),
    NETWORK_STATUS("网络状态"),
    
    // 数据库相关断言
    DB_RECORD_EXISTS("数据库记录存在"),
    DB_RECORD_NOT_EXISTS("数据库记录不存在"),
    DB_RECORD_COUNT("数据库记录数量"),
    DB_FIELD_VALUE("数据库字段值"),
    
    // 变量相关断言
    VARIABLE_VALUE("变量值"),
    VARIABLE_TYPE("变量类型"),
    VARIABLE_EXISTS("变量存在"),
    
    // JSON相关断言
    JSON_PATH("JSON路径"),
    JSON_SCHEMA("JSON Schema"),
    
    // 文件相关断言
    FILE_EXISTS("文件存在"),
    FILE_NOT_EXISTS("文件不存在"),
    FILE_CONTENT("文件内容"),
    FILE_SIZE("文件大小"),
    
    // 性能相关断言
    PERFORMANCE_TIMING("性能计时"),
    RESPONSE_TIME("响应时间"),
    MEMORY_USAGE("内存使用"),
    CPU_USAGE("CPU使用");

    private final String description;
    
    AssertionType(String description) {
        this.description = description;
    }

} 