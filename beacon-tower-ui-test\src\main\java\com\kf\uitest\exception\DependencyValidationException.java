package com.kf.uitest.exception;

/**
 * 依赖关系验证异常
 * 当依赖关系验证失败时抛出此异常
 */
public class DependencyValidationException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public DependencyValidationException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     * @param cause   原始异常
     */
    public DependencyValidationException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 使用依赖节点信息构造异常
     *
     * @param nodeType 节点类型
     * @param nodeId   节点ID
     * @param nodeName 节点名称
     * @param detail   详细错误信息
     * @return 格式化的异常信息
     */
    public static DependencyValidationException forNode(String nodeType, Long nodeId, String nodeName, String detail) {
        String message = String.format("Dependency validation failed for %s[id=%d, name=%s]: %s",
                nodeType, nodeId, nodeName, detail);
        return new DependencyValidationException(message);
    }

    /**
     * 创建循环依赖异常
     *
     * @param path 依赖路径
     * @return 循环依赖异常
     */
    public static DependencyValidationException circularDependency(String path) {
        return new DependencyValidationException("Circular dependency detected: " + path);
    }

    /**
     * 创建无效依赖异常
     *
     * @param sourceNode 源节点
     * @param targetNode 目标节点
     * @return 无效依赖异常
     */
    public static DependencyValidationException invalidDependency(String sourceNode, String targetNode) {
        return new DependencyValidationException(
                String.format("Invalid dependency: %s cannot depend on %s", sourceNode, targetNode));
    }

    /**
     * 创建依赖交叉异常
     *
     * @param preNode  前置节点
     * @param postNode 后置节点
     * @return 依赖交叉异常
     */
    public static DependencyValidationException crossingDependency(String preNode, String postNode) {
        return new DependencyValidationException(
                String.format("Cross dependency detected between pre-dependency %s and post-dependency %s",
                        preNode, postNode));
    }

    /**
     * 创建缺失依赖异常
     *
     * @param nodeInfo   节点信息
     * @param missingDep 缺失的依赖信息
     * @return 缺失依赖异常
     */
    public static DependencyValidationException missingDependency(String nodeInfo, String missingDep) {
        return new DependencyValidationException(
                String.format("Missing dependency for %s: required dependency %s not found",
                        nodeInfo, missingDep));
    }
}