package com.kf.baosi.utils;

import com.kf.baosi.dto.CompressResult;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriter;
import javax.imageio.plugins.jpeg.JPEGImageWriteParam;
import javax.imageio.stream.ImageOutputStream;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
public class ImageCompressor {

    /**
     * 压缩图片工具方法
     *
     * @param pictureMap Map<图片名称, 图片路径>，例如：{"pic1":"C:/images/1.png", "pic2":"C:/images/2.jpg"}
     * @return CompressResult 其中包含保持原顺序的压缩结果Map和总大小
     */
    public static CompressResult compressImages(Map<String, String> pictureMap) {
        // 使用有序的Map以保持顺序
        Map<String, String> orderedResultMap = new LinkedHashMap<>();

        // 创建线程池（根据需求调整线程数量）
        int threadCount = Runtime.getRuntime().availableProcessors();
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        // 使用CompletableFuture并行处理
        List<Map.Entry<String, String>> entries = new ArrayList<>(pictureMap.entrySet());
        List<CompletableFuture<AbstractMap.SimpleEntry<String, String>>> futures = new ArrayList<>();

        for (Map.Entry<String, String> entry : entries) {
            CompletableFuture<AbstractMap.SimpleEntry<String, String>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return new AbstractMap.SimpleEntry<>(entry.getKey(), compressSingleImage(entry.getValue()));
                } catch (Exception e) {
                    // 异常时可根据需要处理，这里简单返回原路径
                    log.error("压缩图片失败：" + entry.getValue(), e);
                    return new AbstractMap.SimpleEntry<>(entry.getKey(), entry.getValue());
                }
            }, executor);
            futures.add(future);
        }

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 按原顺序收集结果
        for (Map.Entry<String, String> entry : entries) {
            // 找出对应的Future结果
            AbstractMap.SimpleEntry<String, String> result = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(e -> e.getKey().equals(entry.getKey()))
                    .findFirst().orElse(new AbstractMap.SimpleEntry<>(entry.getKey(), entry.getValue()));
            // 将名字改为jpeg格式
            String newFileName = result.getKey().substring(0, result.getKey().lastIndexOf('.')) + ".jpeg";
            orderedResultMap.put(newFileName, result.getValue());
        }

        long totalSizeInBytes = orderedResultMap.values().stream()
                .mapToLong(path -> {
                    File f = new File(path);
                    return f.exists() ? f.length() : 0;
                }).sum();

        // 转换为KB（1 KB = 1024 bytes）
        long totalSizeInKB = totalSizeInBytes / 1024;
        executor.shutdown();
        return new CompressResult(orderedResultMap, totalSizeInKB);
    }

    /**
     * 压缩单张图片的方法：
     * 1. 统一转为JPG格式。
     * 2. 尽可能移除元数据（EXIF、ICC Profile等）。
     * 3. 根据分辨率动态调整图片质量：
     *    - 若宽高均在1920x1080及以下，则在原基础质量上稍微再降低一点质量（比如0.75基础上再降至0.7）。
     *    - 若宽高超出1920x1080，则根据超出比例线性降低质量。
     * 返回压缩后的文件路径。
     */
    private static String compressSingleImage(String srcPath) throws Exception {
        File srcFile = new File(srcPath);
        if (!srcFile.exists()) {
            throw new RuntimeException("源文件不存在：" + srcPath);
        }

        // 读取图片为RGB格式以便后续写出为JPEG
        BufferedImage inputImage = ImageIO.read(srcFile);
        if (inputImage == null) {
            throw new RuntimeException("无法读取图片：" + srcPath);
        }

        int width = inputImage.getWidth();
        int height = inputImage.getHeight();

        // 基准质量
        float baseQuality = 0.75f;
        float finalQuality = calculateQuality(width, height, baseQuality);

        // 将输出文件命名为：原名_压缩后.jpg
        String outputPath = getCompressedFilePath(srcFile);

        // 使用Thumbnails进行基本压缩
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Thumbnails.of(inputImage)
                .scale(1.0)
                .outputFormat("jpeg")
                .outputQuality(finalQuality)
                .toOutputStream(baos);

        // 使用ImageIO去除元数据（再写出一次），确保无关信息被移除。
        BufferedImage bufferedImage = ImageIO.read(new java.io.ByteArrayInputStream(baos.toByteArray()));
        if (bufferedImage == null) {
            throw new RuntimeException("中间转换过程出现问题：" + srcPath);
        }

        // 使用JPEGImageWriteParam进行写出，确保无元数据，采用Huffman优化表。
        try (FileOutputStream fos = new FileOutputStream(outputPath);
             ImageOutputStream ios = ImageIO.createImageOutputStream(fos)) {
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpeg");
            if (!writers.hasNext()) {
                throw new RuntimeException("无JPEG格式的ImageWriter");
            }
            ImageWriter writer = writers.next();

            JPEGImageWriteParam jpegParams = new JPEGImageWriteParam(Locale.getDefault());
            jpegParams.setCompressionMode(JPEGImageWriteParam.MODE_EXPLICIT);
            jpegParams.setCompressionQuality(finalQuality);
            jpegParams.setOptimizeHuffmanTables(true);

            // 创建空元数据，确保无EXIF等信息
            writer.setOutput(ios);
            writer.write(null, new IIOImage(bufferedImage, null, null), jpegParams);
            writer.dispose();
        }

        return outputPath;
    }

    /**
     * 根据图像分辨率和基准质量计算最终质量。
     * 逻辑举例：
     * - 若图片分辨率 <= 1920x1080，则在baseQuality基础上稍微降低一点，如0.75 -> 0.7。
     * - 若图片分辨率 > 1920x1080，则根据尺寸相对于1920x1080的比例线性降低质量。
     *   可简单按面积比例来降质：finalQuality = baseQuality * (参考面积 / 实际面积)
     *   限制最低质量不低于0.4
     */
    private static float calculateQuality(int width, int height, float baseQuality) {
        int baseWidth = 1920;
        int baseHeight = 1080;
        int baseArea = baseWidth * baseHeight;
        int actualArea = width * height;

        // 当分辨率不超过1920x1080时，稍微降一点
        if (width <= baseWidth && height <= baseHeight) {
            return Math.max(0.4f, baseQuality - 0.05f);
        } else {
            // 当超过1920x1080时
            // ratio小于1表示超出基准分辨率(因为baseArea/actualArea会<1)
            float ratio = (float) baseArea / actualArea;

            // 使用幂函数使得当ratio越小（即图片越大），最终质量下降更明显
            // ratio^(1.5) 比 ratio 本身更快接近0，从而减少质量
            // 可以尝试ratio^(2.0)或其它指数，以实现更高阶的惩罚
            float nonlinearFactor = (float) Math.pow(ratio, 1.5);

            // 最终质量 = 基础质量 * 非线性因子
            float adjustedQuality = baseQuality * nonlinearFactor;

            // 可根据需要对最终值进行适当再微调，比如再减去一小点：
            // adjustedQuality -= 0.02f; // 这是可选的微调

            // 最终加上上下限保护，防止质量过差或变化不明显
            return Math.max(0.3f, adjustedQuality);
        }
    }


    /**
     * 根据原图文件生成压缩后文件路径
     */
    private static String getCompressedFilePath(File srcFile) {
        String parent = srcFile.getParent();
        String name = srcFile.getName();
        int dotIndex = name.lastIndexOf('.');
        String baseName = (dotIndex == -1) ? name : name.substring(0, dotIndex);
        return parent + File.separator + baseName + ".jpeg";
    }

    public static void main(String[] args) {
        Map<String, String> inputMap = new LinkedHashMap<>();
        inputMap.put("OQ-TESTING-204-1-1 AAA.png", "D:\\file\\file\\OQ-TESTING-204-1-1 AAA.png");
        inputMap.put("PQ-EBALANCE6-3794-1-4.png", "D:\\file\\file\\PQ-EBALANCE6-3794-1-4.png");
        inputMap.put("PQ-EBALANCE6-3627-3-1.jpeg", "D:\\file\\file\\PQ-EBALANCE6-3627-3-1.jpeg");
        inputMap.put("PQ-EBALANCE6-3792-1-1.png", "D:\\file\\file\\PQ-EBALANCE6-3792-1-1.png");
        inputMap.put("PQ-EBALANCE6-3611-1-3.jpeg", "D:\\file\\file\\PQ-EBALANCE6-3611-1-3.jpeg");
        inputMap.put("PQ-EBALANCE6-3611-1-5.jpeg", "D:\\file\\file\\PQ-EBALANCE6-3611-1-5.jpeg");
        inputMap.put("PQ-EBALANCE6-3627-2-1.jpeg", "D:\\file\\file\\PQ-EBALANCE6-3627-2-1.jpeg");

        CompressResult result = compressImages(inputMap);
        System.out.println("压缩结果Map：" + result.getPictureMap());
        System.out.println("总大小：" + result.getTotalSize() + " 字节");
    }

}