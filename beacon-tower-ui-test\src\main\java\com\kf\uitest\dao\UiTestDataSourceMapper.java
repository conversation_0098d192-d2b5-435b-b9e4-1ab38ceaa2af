package com.kf.uitest.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kf.uitest.entity.UiTestDataSource;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface UiTestDataSourceMapper extends BaseMapper<UiTestDataSource> {

    @Select("SELECT * FROM ui_test_datasource WHERE name = #{name} AND project_id = #{projectId} AND user_id = #{userId}")
    UiTestDataSource findByNameAndProjectIdAndUserId(
            @Param("name") String name,
            @Param("projectId") Long projectId,
            @Param("userId") Long userId
    );
}
