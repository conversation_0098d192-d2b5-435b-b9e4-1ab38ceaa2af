package com.kf.baosi.utils;

import org.apache.poi.xwpf.usermodel.*;
import com.github.javafaker.Faker;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Random;

public class WordDocumentGenerator {

    private static final Faker faker = new Faker();
    private static final Random random = new Random();

    public static void main(String[] args) {
        // 配置参数
        int totalPages = 70; // 指定页数
        int tablesPerPage = 3; // 每页表格数量
        String outputPath = "D:\\file\\test.docx"; // 输出路径

        try {
            createWordDocument(totalPages, tablesPerPage, outputPath);
            System.out.println("成功创建了 " + totalPages + " 页的文档，保存为 " + outputPath);
        } catch (IOException e) {
            System.err.println("文档生成失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建包含指定页数、随机内容和表格的Word文档
     *
     * @param totalPages    总页数
     * @param tablesPerPage 每页的表格数量
     * @param outputPath    输出文件路径
     * @throws IOException
     */
    public static void createWordDocument(int totalPages, int tablesPerPage, String outputPath) throws IOException {
        try (XWPFDocument document = new XWPFDocument()) {

            for (int page = 1; page <= totalPages; page++) {
                addPageTitle(document, page);
                addRandomParagraphs(document, 5 + random.nextInt(6)); // 每页5-10段落
                addTables(document, tablesPerPage, 3 + random.nextInt(3), 3 + random.nextInt(4)); // 每表3-5行，3-6列

                if (page < totalPages) {
                    addPageBreak(document);
                }
            }

            try (FileOutputStream out = new FileOutputStream(outputPath)) {
                document.write(out);
            }
        }
    }

    /**
     * 添加页面标题
     *
     * @param document Word文档对象
     * @param page     当前页数
     */
    private static void addPageTitle(XWPFDocument document, int page) {
        XWPFParagraph title = document.createParagraph();
        title.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun run = title.createRun();
        run.setText("第 " + page + " 页");
        run.setBold(true);
        run.setFontSize(16);
        run.addBreak();
    }

    /**
     * 添加随机段落
     *
     * @param document    Word文档对象
     * @param paragraphCount 段落数量
     */
    private static void addRandomParagraphs(XWPFDocument document, int paragraphCount) {
        for (int i = 0; i < paragraphCount; i++) {
            XWPFParagraph paragraph = document.createParagraph();
            XWPFRun run = paragraph.createRun();
            run.setFontSize(12);
            run.setText(faker.lorem().paragraph(3));
        }
    }

    /**
     * 添加指定数量的表格
     *
     * @param document Word文档对象
     * @param tablesPerPage 每页表格数量
     * @param rows     每个表格的行数
     * @param cols     每个表格的列数
     */
    private static void addTables(XWPFDocument document, int tablesPerPage, int rows, int cols) {
        for (int t = 0; t < tablesPerPage; t++) {
            XWPFTable table = document.createTable(rows, cols);
            setTableStyle(table);

            // 填充表格内容
            for (int r = 0; r < rows; r++) {
                XWPFTableRow row = table.getRow(r);
                for (int c = 0; c < cols; c++) {
                    XWPFTableCell cell = row.getCell(c);
                    cell.setText(faker.lorem().word());
                }
            }

            // 添加表格下的空行
            XWPFParagraph emptyParagraph = document.createParagraph();
            emptyParagraph.createRun().addBreak();
        }
    }

    /**
     * 设置表格样式
     *
     * @param table XWPFTable对象
     */
    private static void setTableStyle(XWPFTable table) {
        table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, 1, 0, "000000");
        table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, 1, 0, "000000");
        table.setTableAlignment(TableRowAlign.CENTER);
    }

    /**
     * 添加分页符
     *
     * @param document Word文档对象
     */
    private static void addPageBreak(XWPFDocument document) {
        XWPFParagraph pageBreakParagraph = document.createParagraph();
        pageBreakParagraph.setPageBreak(true);
    }
}
