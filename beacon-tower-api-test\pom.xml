<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.kf</groupId>
        <artifactId>beacon-tower</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>beacon-tower-api-test</artifactId>
    <packaging>jar</packaging>

    <name>beacon-tower-api-test</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
<!--        <dependency>-->
<!--            <groupId>com.kf</groupId>-->
<!--            <artifactId>sdk</artifactId>-->
<!--            <version>${revision}</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>poi</artifactId>-->
<!--                    <groupId>org.apache.poi</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>poi-ooxml</artifactId>-->
<!--                    <groupId>org.apache.poi</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>poi-ooxml-schemas</artifactId>-->
<!--                    <groupId>org.apache.poi</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.kf</groupId>-->
<!--            <artifactId>xpack-interface</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <!-- 将ribbon排除 -->
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.json</include>
                    <include>**/*.tpl</include>
                    <include>**/*.js</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
<!--            <plugin>-->
<!--                <artifactId>maven-clean-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <filesets>-->
<!--                        <fileset>-->
<!--                            <directory>src/main/resources/static</directory>-->
<!--                            <includes>-->
<!--                                <include>**</include>-->
<!--                            </includes>-->
<!--                            <followSymlinks>false</followSymlinks>-->
<!--                        </fileset>-->
<!--                        <fileset>-->
<!--                            <directory>src/main/resources/public</directory>-->
<!--                            <includes>-->
<!--                                <include>**</include>-->
<!--                            </includes>-->
<!--                            <followSymlinks>false</followSymlinks>-->
<!--                        </fileset>-->
<!--                    </filesets>-->
<!--                </configuration>-->
<!--            </plugin>-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <artifactItems>
                        <artifactItem>
                            <groupId>org.apache.jmeter</groupId>
                            <artifactId>ApacheJMeter_functions</artifactId>
                            <version>${jmeter.version}</version>
                            <type>jar</type>
                            <overWrite>true</overWrite>
                            <outputDirectory>src/main/resources/jmeter/lib/ext</outputDirectory>
                            <destFileName>ApacheJMeter_functions.jar</destFileName>
                        </artifactItem>
                        <artifactItem>
                            <groupId>io.metersphere</groupId>
                            <artifactId>metersphere-jmeter-functions</artifactId>
                            <version>${metersphere-jmeter-functions.version}</version>
                            <type>jar</type>
                            <overWrite>true</overWrite>
                            <outputDirectory>src/main/resources/jmeter/lib/ext</outputDirectory>
                            <destFileName>metersphere-jmeter-functions.jar</destFileName>
                        </artifactItem>
                        <artifactItem>
                            <groupId>org.python</groupId>
                            <artifactId>jython-standalone</artifactId>
                            <version>${jython.version}</version>
                            <type>jar</type>
                            <overWrite>true</overWrite>
                            <outputDirectory>src/main/resources/jmeter/lib/ext</outputDirectory>
                            <destFileName>jython-standalone.jar</destFileName>
                        </artifactItem>
                    </artifactItems>
                    <outputDirectory>${project.build.directory}/wars</outputDirectory>
                    <overWriteReleases>false</overWriteReleases>
                    <overWriteSnapshots>true</overWriteSnapshots>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
