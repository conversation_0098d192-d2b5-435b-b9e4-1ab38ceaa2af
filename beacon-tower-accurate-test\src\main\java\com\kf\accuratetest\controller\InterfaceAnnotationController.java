package com.kf.accuratetest.controller;

import com.kf.accuratetest.common.ResponseDoMain;
import com.kf.accuratetest.service.InterfaceAnnotationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/interface")
@Tag(name = "接口注解相关接口", description = "接口注解相关接口")
public class InterfaceAnnotationController {

    @Resource
    private InterfaceAnnotationService interfaceAnnotationService;

    /**
     * 获取受影响的接口
     *
     * @param taskId 主键
     * @return 单条数据
     */
    @GetMapping("/getAnnotation")
    @Operation(summary = "获取受影响的接口", description = "通过taskId获取受影响的接口")
    public ResponseDoMain queryById(@RequestParam("taskId") @Parameter(description="任务ID") String taskId) {
        return ResponseDoMain.custom("请求成功", true, this.interfaceAnnotationService.queryById(taskId), 200);
    }

}

