package com.kf.userservice.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TEmailVerificationCode implements Serializable {
    private static final long serialVersionUID = 909746752330614774L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 验证码
     */
    private String code;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 过期时间
     */
    private Date expireTime;
    /**
     * sign
     */
    private String sign;
    /**
     * 是否删除  0未删除 1删除
     */
    private int isDeleted;

}

