server:
  port: 8005
  tomcat:
    max-swallow-size: -1
spring:
  application:
    name: beacon-tower-ai-test
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
  datasource:
    # 主数据库 - 标准Spring Boot数据源配置
    url: *******************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 600000
      connection-timeout: 30000
      max-lifetime: 1800000
    # 其他数据源配置（如果需要多数据源，需要创建对应的配置类）
    pv-test:
      url: ********************************************************************************************************************************************
      username: root
      password: Mysql#r2368
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        idle-timeout: 600000
        connection-timeout: 30000
        max-lifetime: 1800000

    # PMP数据库
    pmp:
      jdbc-url: ******************************************************************************************************************************************
      username: root
      password: Mysql#r2368
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        idle-timeout: 600000
        connection-timeout: 30000
        max-lifetime: 1800000

    # 知识库数据库
    kb:
      jdbc-url: ********************************************************************************************************************************
      username: root
      password: Mysql#r2368
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        idle-timeout: 600000
        connection-timeout: 30000
        max-lifetime: 1800000
        
    # 生产数据库连接
    pv-prod:
      jdbc-url: ***************************************************************************************************************************************
      username: root
      password: Mysql#r2368
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        idle-timeout: 600000
        connection-timeout: 30000
        max-lifetime: 1800000
        
    # PMC医学编码数据库
    pmc:
      jdbc-url: ************************************************************************************************************************************
      username: dicReader
      password: reader@123!
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        maximum-pool-size: 10
        minimum-idle: 2
        idle-timeout: 600000
        connection-timeout: 30000
        max-lifetime: 1800000

# AI服务配置
ai:
  azure:
    gpt:
      concurrent: 500
  claude:
    concurrent: 500
  reason-claude:
    concurrent: 500
  anthropic:
    version: bedrock-2023-05-31
  # 火山引擎ARK API配置
  ark:
    # API端点
    api-url: https://ark.cn-beijing.volces.com/api/v3/chat/completions
    # API密钥
    api-key: c854450c-60c6-451c-8bd7-d0ada82a7519
    # 模型ID
    model: ep-20250723134959-88zhw
    # 请求超时时间（毫秒）- 增加到2分钟避免超时
    timeout: 120000
    # 最大重试次数
    max-retries: 3
  # AI评估配置
  evaluation:
    # 单次AI调用的最大token限制
    max-tokens: 8000
    # 提示词模板目录
    prompt-template-path: classpath:prompts/
    # 是否启用详细的控制台打印
    enable-console-print: true
    # 是否默认禁用数据分片（全局设置）
    disable-chunking-by-default: true

# Langfuse 配置
langfuse:
  public-key: pk-lf-6eeb334d-ce34-454a-ba99-706d2b71b737
  secret-key: sk-lf-165294ce-06de-4b63-8dce-2b91f601c542
  host: http://**********:3000

# 文件上传配置
upload:
  file:
    path: /home/<USER>/uploadFile/pmp

# Python相关配置
python:
  malloc: malloc
  malloc-trim-threshold: 100000

# 环境标识
environment: test

# 数据对比配置
data:
  comparison:
    uat:
      url: https://copilot-uat.pharmaronclinical.com
    test:
      url: https://copilot-test.pharmaronclinical.com

# SSE配置
sse:
  connection:
    timeout: 600000  # 连接超时时间（毫秒），默认10分钟
  message:
    expire-minutes: 30  # 消息过期时间（分钟）
  cleanup:
    interval-minutes: 5  # 清理任务执行间隔（分钟）



# 日志配置
logging:
  level:
    com.kf.aitest: DEBUG
    org.springframework: INFO
    root: INFO