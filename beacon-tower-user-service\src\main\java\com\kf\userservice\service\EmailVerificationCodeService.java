package com.kf.userservice.service;

import com.kf.userservice.entity.TEmailVerificationCode;

public interface EmailVerificationCodeService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TEmailVerificationCode queryById(Long id);

    /**
     * 通过ID查询单条数据
     *
     * @param email 邮箱
     * @return 实例对象
     */
    TEmailVerificationCode queryByEmail(String email);

    /**
     * 新增数据
     *
     * @param tEmailVerificationCode 实例对象
     * @return 实例对象
     */
    TEmailVerificationCode insert(TEmailVerificationCode tEmailVerificationCode);

    /**
     * 修改数据
     *
     * @param tEmailVerificationCode 实例对象
     * @return 实例对象
     */
    TEmailVerificationCode update(TEmailVerificationCode tEmailVerificationCode);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

}
