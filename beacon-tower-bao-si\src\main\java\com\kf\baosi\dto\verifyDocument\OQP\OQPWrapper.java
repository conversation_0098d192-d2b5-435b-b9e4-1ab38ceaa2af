package com.kf.baosi.dto.verifyDocument.OQP;

import lombok.Data;

/**
 * OQP参数组装总类
 */
@Data
public class OQPWrapper {
    // 对应word模板的list（填充时的key的名称）
    private OQPTestCaseListWrapper testCaseInfo;

    // 对应word模板的文本（填充时的key的名称）
    private String version;

    private String tester;

    private String code_version;

    // 审核人
    private String auditor;

    // 批准人
    private String approver;

    private String date;
}
