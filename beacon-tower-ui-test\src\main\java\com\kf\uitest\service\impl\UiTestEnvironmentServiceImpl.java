package com.kf.uitest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kf.uitest.dao.UiTestEnvironmentMapper;
import com.kf.uitest.dto.UiTestEnvironmentDTO;
import com.kf.uitest.dto.UiTestEnvironmentCreateRequest;
import com.kf.uitest.entity.UiTestEnvironment;
import com.kf.uitest.entity.UiTestEnvironmentVariable;
import com.kf.uitest.service.UiTestEnvironmentService;
import com.kf.uitest.service.UiTestEnvironmentVariableService;
import com.kf.uitest.dto.UiTestEnvironmentWithVariablesDTO;
import com.kf.uitest.dto.UiTestEnvironmentVariableDTO;
import com.kf.uitest.dto.UiTestEnvironmentUpdateRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.AbstractMap;

@Slf4j
@Service
public class UiTestEnvironmentServiceImpl implements UiTestEnvironmentService {

    @Resource
    private UiTestEnvironmentMapper environmentMapper;

    @Resource
    private UiTestEnvironmentVariableService environmentVariableService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UiTestEnvironmentWithVariablesDTO WithVariables(Long userId, UiTestEnvironmentCreateRequest request) {
        // 创建环境
        UiTestEnvironment environment = new UiTestEnvironment();
        BeanUtils.copyProperties(request, environment);
        environment.setUserId(userId);
        environment.setProjectId(request.getProjectId());
        environment.setCreateTime(LocalDateTime.now());
        environment.setUpdateTime(LocalDateTime.now());
        environment.insert();
        
        List<UiTestEnvironmentVariable> variables = List.of();
        // 创建环境变量
        if (request.getVariables() != null && !request.getVariables().isEmpty()) {
            variables = request.getVariables().stream()
                .map(var -> {
                    UiTestEnvironmentVariable variable = new UiTestEnvironmentVariable();
                    variable.setEnvironmentId(environment.getId());
                    variable.setVariableName(var.getVariableName());
                    variable.setVariableValue(var.getVariableValue());
                    variable.setDescription(var.getDescription());
                    variable.setCreateTime(LocalDateTime.now());
                    variable.setUpdateTime(LocalDateTime.now());
                    return variable;
                })
                .collect(Collectors.toList());
            environmentVariableService.saveBatch(variables);
        }
        
        // 构造返回的DTO
        UiTestEnvironmentDTO envDTO = new UiTestEnvironmentDTO();
        BeanUtils.copyProperties(environment, envDTO);
        
        // 转换变量为DTO
        List<UiTestEnvironmentVariableDTO> variableDTOs = variables.stream()
            .map(var -> {
                UiTestEnvironmentVariableDTO varDTO = new UiTestEnvironmentVariableDTO();
                BeanUtils.copyProperties(var, varDTO);
                return varDTO;
            })
            .collect(Collectors.toList());
        
        // 组装最终返回的DTO
        UiTestEnvironmentWithVariablesDTO resultDTO = new UiTestEnvironmentWithVariablesDTO();
        resultDTO.setEnvironment(envDTO);
        resultDTO.setVariables(variableDTOs);
        
        return resultDTO;
    }

    @Override
    public List<UiTestEnvironmentWithVariablesDTO> getAllEnvironmentsWithVariables(Long userId,Long projectId) {
        // 获取所有环境
        List<UiTestEnvironment> environments = environmentMapper.selectList(
                new LambdaQueryWrapper<UiTestEnvironment>()
                        .eq(UiTestEnvironment::getUserId, userId)
                        .eq(UiTestEnvironment::getProjectId, projectId)
        );

        // 转换为DTO
        return environments.stream()
                .map(env -> {
                    // 转换环境信息
                    UiTestEnvironmentDTO envDTO = new UiTestEnvironmentDTO();
                    BeanUtils.copyProperties(env, envDTO);

                    // 获取该环境的所有变量
                    List<UiTestEnvironmentVariable> variables = environmentVariableService.findByEnvironmentId(env.getId());
                    List<UiTestEnvironmentVariableDTO> variableDTOs = variables.stream()
                            .map(var -> {
                                UiTestEnvironmentVariableDTO varDTO = new UiTestEnvironmentVariableDTO();
                                BeanUtils.copyProperties(var, varDTO);
                                return varDTO;
                            })
                            .collect(Collectors.toList());

                    // 组装DTO
                    UiTestEnvironmentWithVariablesDTO resultDTO = new UiTestEnvironmentWithVariablesDTO();
                    resultDTO.setEnvironment(envDTO);
                    resultDTO.setVariables(variableDTOs);

                    return resultDTO;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWithVariables(UiTestEnvironmentUpdateRequest request) {
        // 更新环境基本信息
        UiTestEnvironment environment = new UiTestEnvironment();
        environment.setId(request.getId());
        environment.setEnvironmentName(request.getEnvironmentName());
        environment.setBrowserType(request.getBrowserType());
        environment.setIsHeadless(request.getIsHeadless());
        environment.setDescription(request.getDescription());
        environment.setUpdateTime(LocalDateTime.now());
        environment.updateById();

        if (request.getVariables() != null) {
            // 处理新增变量
            if (request.getVariables().getCreate() != null) {
                List<UiTestEnvironmentVariable> newVariables = request.getVariables().getCreate().stream()
                    .map(var -> {
                        UiTestEnvironmentVariable variable = new UiTestEnvironmentVariable();
                        variable.setEnvironmentId(request.getId());
                        variable.setVariableName(var.getVariableName());
                        variable.setVariableValue(var.getVariableValue());
                        variable.setDescription(var.getDescription());
                        variable.setCreateTime(LocalDateTime.now());
                        variable.setUpdateTime(LocalDateTime.now());
                        return variable;
                    })
                    .collect(Collectors.toList());
                environmentVariableService.saveBatch(newVariables);
            }

            // 处理更新变量
            if (request.getVariables().getUpdate() != null) {
                request.getVariables().getUpdate().forEach(var -> {
                    UiTestEnvironmentVariable variable = new UiTestEnvironmentVariable();
                    variable.setId(var.getId());
                    variable.setVariableName(var.getVariableName());
                    variable.setVariableValue(var.getVariableValue());
                    variable.setDescription(var.getDescription());
                    variable.setUpdateTime(LocalDateTime.now());
                    environmentVariableService.update(variable);
                });
            }

            // 处理删除变量
            if (request.getVariables().getDelete() != null) {
                request.getVariables().getDelete()
                    .forEach(environmentVariableService::delete);
            }
        }
        if(request.getDelete() != null) {
            request.getDelete().forEach(id -> environmentMapper.deleteById(id));
        }
    }
} 