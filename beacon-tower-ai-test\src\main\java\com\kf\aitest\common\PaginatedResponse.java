package com.kf.aitest.common;

import lombok.Data;
import java.util.List;

/**
 * 分页响应结果封装类
 */
@Data
public class PaginatedResponse<T> {
    
    /**
     * 数据列表
     */
    private List<T> records;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 当前页码
     */
    private Long current;
    
    /**
     * 每页大小
     */
    private Long size;
    
    /**
     * 总页数
     */
    private Long pages;
    
    public PaginatedResponse() {
    }
    
    public PaginatedResponse(List<T> records, Long total, Long current, Long size) {
        this.records = records;
        this.total = total;
        this.current = current;
        this.size = size;
        this.pages = (total + size - 1) / size;
    }
    
    /**
     * 创建分页响应
     */
    public static <T> PaginatedResponse<T> of(List<T> records, Long total, Long current, Long size) {
        return new PaginatedResponse<>(records, total, current, size);
    }
    
    /**
     * 创建空分页响应
     */
    public static <T> PaginatedResponse<T> empty(Long current, Long size) {
        return new PaginatedResponse<>(List.of(), 0L, current, size);
    }
}
