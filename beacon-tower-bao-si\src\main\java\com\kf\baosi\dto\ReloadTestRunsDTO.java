package com.kf.baosi.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class ReloadTestRunsDTO {

    @NotNull(message = "测试用例ID不能为空")
    @Size(min = 1, message = "测试用例ID至少有一个")
    private String[] testCaseIds;

    //是否保留测试运行结果
    private boolean keepTestResults = true;

    //是否保留每一步测试结果
    private boolean keepStepResults = true;

    //是否保留关联的测试缺陷
    private boolean keepDefects = true;

}
