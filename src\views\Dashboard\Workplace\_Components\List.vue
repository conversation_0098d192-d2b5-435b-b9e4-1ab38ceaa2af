<template>
    <List type='card' :data='cardList'>
        <template #header>
            <div class='card-header flex justify-between items-center'>
                <span>静态分析-简报</span>
                <!-- <el-link type='primary' :underline='false' href='javascript:;'>全部</el-link> -->
            </div>
        </template>
    </List>
</template>
<script setup lang='ts'>
import { ref } from 'vue'
import List, { type IList } from '@/components/List/index.vue'
import { getTaskList } from '@/api/layout'

let cardList = ref([] as IList[])
const getCardList = async () => {
    const res = await getTaskList()
    cardList.value = res.data.data
}
getCardList()

</script>