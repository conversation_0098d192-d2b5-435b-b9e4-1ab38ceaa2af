package com.kf.uitest.service.impl;

import com.kf.uitest.dao.UiTestExecutionRecordMapper;
import com.kf.uitest.dto.TestExecutionRecordDTO;
import com.kf.uitest.entity.UITestExecutionRecord;
import com.kf.uitest.enums.TestStatus;
import com.kf.uitest.model.StepExecutionResult;
import com.kf.uitest.model.TestResult;
import com.kf.uitest.service.UiTestExecutionRecordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class UiTestExecutionRecordServiceImpl implements UiTestExecutionRecordService {

    @Resource
    private UiTestExecutionRecordMapper recordMapper;

    @Override
    public TestExecutionRecordDTO create(String executionId, List<String> sceneIds) {
        TestExecutionRecordDTO dto = new TestExecutionRecordDTO();
        dto.setExecutionId(executionId);
        dto.setTestCaseIds(sceneIds);
        dto.setStatus(TestStatus.RUNNING.name());
        dto.setCreateTime(LocalDateTime.now());
        dto.setUpdateTime(LocalDateTime.now());

        UITestExecutionRecord entity = dto.toEntity();
        recordMapper.insert(entity);

        return dto;
    }

    @Override
    public void updateStatus(String executionId, TestStatus status) {
        TestExecutionRecordDTO dto = getRecord(executionId);
        dto.setStatus(status.name());
        dto.setUpdateTime(LocalDateTime.now());

        updateById(dto.toEntity());
    }

    @Override
    public void saveResult(String executionId, TestResult result) {
        TestExecutionRecordDTO dto = getRecord(executionId);
        dto.setResult(result);
        dto.setUpdateTime(LocalDateTime.now());
        if (result != null) {
            dto.setEndTime(LocalDateTime.now());
        }

        updateById(dto.toEntity());
    }

    @Override
    public TestExecutionRecordDTO getRecord(String executionId) {
        UITestExecutionRecord entity = recordMapper.selectById(executionId);
        if (entity == null) {
            return null;
        }
        return TestExecutionRecordDTO.fromEntity(entity);
    }

    @Override
    public TestResult getResult(String executionId) {
        TestExecutionRecordDTO dto = getRecord(executionId);
        return dto != null ? dto.getResult() : null;
    }

    @Override
    public void updateById(UITestExecutionRecord record) {
        recordMapper.updateById(record);
    }

    @Override
    public void saveStepResult(String executionId, StepExecutionResult stepResult) {

    }
}