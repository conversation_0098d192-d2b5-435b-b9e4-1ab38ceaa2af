package com.kf.baosi.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_xmind")
public class TXMind extends Model<TXMind> {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    private String userId;

    private String taskId;

    private String fileId;

    private String testCaseId;

    private Date createTime;

    private Date updateTime;

    private Integer isComplete;

    @TableField(value = "error_message")
    private String errorMsg;

    private Integer status;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
