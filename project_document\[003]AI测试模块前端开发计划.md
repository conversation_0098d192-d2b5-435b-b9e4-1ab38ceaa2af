# [003] AI测试模块前端开发计划

**创建时间**: 2025-07-25T14:33:31+08:00  
**项目**: beacon-tower-ai-test  
**模块**: AI测试前端页面和功能  

## 任务目标

基于beacon-tower-ai-test工程为AI测试模块创建完整的前端页面和功能。采用增强实现方案，最大化复用现有架构组件（SSE连接、页面布局、API接口等），新增Markdown渲染和AI测试管理功能，确保与现有系统完美集成。

## 核心需求

1. **页面结构设计**: 创建符合现有前端架构的Vue 3页面组件，页面布局分为查询区域和列表区域，添加"开始AI测试"按钮
2. **路由配置**: 提供完整的路由配置对象，包含路由路径、组件名称、页面标题、权限标识、图标配置
3. **SSE实时通信**: 基于ComparisonProgressManager的SSE推送机制，实现前端接收代码，支持progress、ai-evaluation、ai-result、stage-complete等事件类型
4. **Markdown渲染支持**: 集成Markdown渲染库，对接收到的SSE消息中的Markdown内容进行渲染显示，支持代码高亮、表格、列表等常见语法
5. **技术要求**: 使用Vue 3 Composition API + TypeScript，setup语法糖，方法规范使用箭头函数，遵循项目现有代码规范，使用Element Plus组件库，集成Pinia状态管理，确保响应式设计和错误处理

## 技术方案：增强实现方案

### 架构设计原则
1. **最大化复用现有架构**: 复用ComparisonProgressManager的SSE实现、CardList等UI组件、现有的request.ts和路由系统
2. **渐进式开发策略**: 基础设施 → 核心功能 → 用户交互 → 系统集成 → 优化完善
3. **技术栈一致性**: Vue 3 + TypeScript + Composition API + Element Plus + Tailwind CSS + Pinia状态管理

### 核心技术栈
- **前端框架**: Vue 3.4.3 + TypeScript
- **UI组件库**: Element Plus 2.8.4
- **状态管理**: Pinia 2.1.7
- **路由管理**: Vue Router 4.2.5
- **HTTP客户端**: Axios 1.6.3
- **构建工具**: Vite 5.0.10
- **样式框架**: Tailwind CSS
- **新增依赖**: markdown-it@^14.0.0, highlight.js@^11.9.0, @types/markdown-it@^13.0.7

## 详细实施计划

### 任务1: 项目依赖和环境准备
**预估时间**: 0.5天
**状态**: [ ] 待开始
**任务ID**: 007d13dd-6efc-4d00-ba30-837fc5b88081

#### 实施内容
- 使用npm/yarn添加依赖：markdown-it@^14.0.0, highlight.js@^11.9.0, @types/markdown-it@^13.0.7
- 验证package.json中现有依赖版本兼容性
- 更新tsconfig.json如需要（通常不需要）
- 验证Vite配置是否需要调整
- 运行npm install确保依赖安装成功
- 创建基础目录结构：src/views/AITest/, src/api/aitest/

#### 验收标准
1. 依赖安装成功，无版本冲突警告
2. 项目能正常启动和构建
3. TypeScript类型检查通过
4. 目录结构创建完成

### 任务2: Markdown渲染组件开发
**预估时间**: 1.5天
**状态**: [ ] 待开始
**任务ID**: c01f2c64-6140-463a-a469-49aa9cb11720
**依赖**: 项目依赖和环境准备

#### 实施内容
- 创建MarkdownRenderer.vue组件，使用Composition API + setup语法糖
- 集成markdown-it库，配置基础插件（表格、代码块等）
- 集成highlight.js，支持常见编程语言高亮
- 设计props接口：content(string), theme(string), loading(boolean)
- 添加样式适配Element Plus主题
- 实现按需加载highlight.js语言包优化性能
- 添加错误处理和loading状态
- 编写TypeScript类型定义

#### 验收标准
1. 组件能正确渲染Markdown内容
2. 代码高亮功能正常
3. 样式与Element Plus主题一致
4. TypeScript类型检查通过
5. 性能测试：大内容渲染流畅

### 任务3: AI测试API接口封装
**预估时间**: 1天
**状态**: [ ] 待开始
**任务ID**: 35d78af2-edf9-47e8-a8b0-e981efd73ce8
**依赖**: 项目依赖和环境准备

#### 实施内容
- 在src/api/目录下创建aitest模块
- 复用现有的request.ts HTTP封装
- 定义AI测试相关接口：
  - startAITest: POST /data-comparison/start
  - getAITestList: 基于现有分页查询接口
  - getAITestDetail: 根据taskId查询详情
  - createSSEConnection: GET /data-comparison/progress/{taskId}
- 定义TypeScript接口类型：AITestTask, AITestRequest, AITestResponse
- 添加错误处理和响应拦截
- 编写接口文档注释

#### 验收标准
1. API接口调用成功
2. TypeScript类型检查通过
3. 错误处理机制正常
4. 接口响应格式符合预期
5. 与现有request.ts集成无冲突

### 任务4: SSE连接管理组件
**预估时间**: 2天
**状态**: [ ] 待开始
**任务ID**: e78f1561-e891-4530-8ba6-27ff7c767e4f
**依赖**: AI测试API接口封装

#### 实施内容
- 创建SSEConnection.vue组件或composable函数
- 复用现有ComparisonProgressManager的SSE实现
- 实现事件监听：connected, progress, ai-evaluation, ai-result, stage-complete
- 添加连接状态管理：PENDING, CONNECTED, DISCONNECTED
- 实现智能重连机制（指数退避算法）
- 添加消息缓存和离线支持
- 实现错误处理和用户友好的错误提示
- 组件销毁时正确关闭连接
- 提供事件回调接口供父组件使用

#### 验收标准
1. SSE连接建立成功
2. 能正确接收各种事件类型
3. 重连机制工作正常
4. 错误处理用户友好
5. 组件销毁时连接正确关闭

### 任务5: AI测试状态管理Store
**预估时间**: 1天
**状态**: [ ] 待开始
**任务ID**: 4ee2b3f1-b31d-4b1e-8a7c-037ccaffb8c8
**依赖**: SSE连接管理组件

#### 实施内容
- 创建useAITestStore，使用Pinia Composition API风格
- 定义状态：taskList, currentTask, sseConnection, loading, error
- 实现actions：fetchTaskList, startTask, updateTaskStatus, clearCache
- 添加getters：filteredTasks, taskStatistics, connectionStatus
- 集成localStorage持久化（参考现有layoutStore模式）
- 实现离线缓存机制
- 添加状态变更的响应式监听
- 与SSE连接组件集成，实时更新任务状态

#### 验收标准
1. Store状态管理正常
2. 数据持久化功能正常
3. 与SSE连接集成成功
4. 响应式更新正常
5. TypeScript类型安全

### 任务6: AI测试主页面开发
**预估时间**: 2.5天
**状态**: [ ] 待开始
**任务ID**: 7074ab5f-2ea9-45c8-88e1-f84618b8e4bb
**依赖**: AI测试状态管理Store, Markdown渲染组件开发

#### 实施内容
- 创建AITestManagement.vue主页面，使用setup语法糖
- 复用现有布局模式：CardList查询区域 + el-table列表区域
- 实现查询表单：任务状态、创建时间范围、任务类型等筛选条件
- 实现数据列表：任务ID、状态、创建时间、进度、操作等列
- 添加分页组件（复用现有分页模式）
- 添加"开始AI测试"按钮，触发模态框
- 集成AI测试Store进行状态管理
- 实现列表项点击查看详情功能
- 添加loading状态和错误处理
- 确保响应式设计

#### 验收标准
1. 页面布局符合设计要求
2. 查询和分页功能正常
3. 数据列表展示正确
4. 响应式设计良好
5. 与Store集成正常

### 任务7: 开始测试模态框组件
**预估时间**: 1.5天
**状态**: [ ] 待开始
**任务ID**: 0c728d20-8013-47e0-bf57-c7859bdb9de1
**依赖**: AI测试主页面开发

#### 实施内容
- 创建AITestModal.vue组件，使用el-dialog
- 设计表单：文件MD5输入（必填）、测试类型选择、高级配置等
- 集成Element Plus表单验证（参考现有formExtend.ts）
- 实现提交逻辑：调用startAITest API
- 添加loading状态和进度指示
- 实现错误处理和用户友好提示
- 模态框关闭时重置表单状态
- 支持键盘快捷键（ESC关闭、Enter提交）
- 添加表单数据的本地缓存（防止意外关闭丢失）

#### 验收标准
1. 模态框显示和关闭正常
2. 表单验证功能正确
3. 任务创建成功
4. 错误处理用户友好
5. 键盘交互正常

### 任务8: 实时进度查看页面
**预估时间**: 2.5天
**状态**: [ ] 待开始
**任务ID**: f780c1f5-fbd2-4c00-9f1b-778e72ff0532
**依赖**: 开始测试模态框组件

#### 实施内容
- 创建AITestDetail.vue详情页面
- 根据任务状态判断显示模式：处理中显示实时SSE进度，已完成显示最终结果
- 集成SSE连接组件，实时接收进度更新
- 使用MarkdownRenderer组件渲染AI评估结果
- 实现进度条和阶段指示器
- 添加任务基本信息展示
- 实现错误状态处理和重试机制
- 添加结果导出功能（可选）
- 支持全屏查看模式
- 实现页面刷新时的状态恢复

#### 验收标准
1. 实时进度显示正常
2. Markdown渲染效果良好
3. 任务状态判断准确
4. SSE连接稳定
5. 页面性能良好

### 任务9: 路由配置和权限集成
**预估时间**: 1天
**状态**: [ ] 待开始
**任务ID**: ab068f81-64c3-4eed-99a5-2e27ef6a82a9
**依赖**: 实时进度查看页面

#### 实施内容
- 在router/index.ts中添加AI测试相关路由配置
- 配置路由对象：path: /seigneur/AITest/Management, name: AITestManagement, component: 动态导入AITestManagement组件, meta: title, icon, permission等
- 添加详情页路由（支持taskId参数）
- 配置权限标识：ai:test:view, ai:test:create等
- 集成到现有的动态路由生成机制
- 更新菜单配置（如需要）
- 测试路由跳转和权限控制
- 确保面包屑导航正确

#### 验收标准
1. 路由配置正确
2. 页面跳转正常
3. 权限控制有效
4. 面包屑导航正确
5. 动态路由加载成功

### 任务10: 性能优化和测试
**预估时间**: 2天
**状态**: [ ] 待开始
**任务ID**: 79445dfe-920b-4b20-9ff3-08217f377b7e
**依赖**: 路由配置和权限集成

#### 实施内容
- 实现虚拟滚动优化（当任务列表超过1000条时）
- 添加组件懒加载和代码分割
- 优化Markdown渲染性能（大内容分片渲染）
- 实现智能缓存策略（API响应缓存、图片缓存等）
- 添加性能监控和错误上报
- 编写关键功能的单元测试
- 编写SSE连接的集成测试
- 进行压力测试和性能基准测试
- 优化首屏加载时间
- 添加PWA支持（可选）

#### 验收标准
1. 性能指标达标（首屏<3s，列表滚动流畅）
2. 测试覆盖率>80%
3. 内存使用合理
4. 错误处理完善
5. 用户体验良好

---

**最后更新时间**: 2025-07-25T14:33:31+08:00
