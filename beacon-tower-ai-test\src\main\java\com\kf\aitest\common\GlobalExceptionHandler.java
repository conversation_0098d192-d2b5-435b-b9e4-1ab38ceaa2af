package com.kf.aitest.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseDoMain<Void> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常: ", e);
        return ResponseDoMain.error("系统内部错误: " + e.getMessage());
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseDoMain<Void> handleIllegalArgumentException(IllegalArgumentException e) {
        log.error("参数异常: ", e);
        return ResponseDoMain.error(400, "参数错误: " + e.getMessage());
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseDoMain<Void> handleNullPointerException(NullPointerException e) {
        log.error("空指针异常: ", e);
        return ResponseDoMain.error("系统内部错误");
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseDoMain<Void> handleException(Exception e) {
        log.error("系统异常: ", e);
        return ResponseDoMain.error("系统异常，请联系管理员");
    }
}
