package com.kf.uitest.dto;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kf.uitest.entity.UITestExecutionRecord;
import com.kf.uitest.model.TestResult;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class TestExecutionRecordDTO {
    private String executionId;
    private List<String> testCaseIds;
    private String status;
    private TestResult result;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private LocalDateTime endTime;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static TestExecutionRecordDTO fromEntity(UITestExecutionRecord entity) {
        TestExecutionRecordDTO dto = new TestExecutionRecordDTO();
        dto.setExecutionId(entity.getExecutionId());
        dto.setStatus(entity.getStatus());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setEndTime(entity.getEndTime());

        try {
            // 转换 sceneIds
            if (entity.getSceneIdsJson() != null) {
                dto.setTestCaseIds(objectMapper.readValue(entity.getSceneIdsJson(),
                        new TypeReference<>() {
                        }));
            }
            // 转换 result
            if (entity.getResultJson() != null) {
                dto.setResult(objectMapper.readValue(entity.getResultJson(), TestResult.class));
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON转换失败", e);
        }
        return dto;
    }

    public UITestExecutionRecord toEntity() {
        UITestExecutionRecord entity = new UITestExecutionRecord();
        entity.setExecutionId(this.executionId);
        entity.setStatus(this.status);
        entity.setCreateTime(this.createTime);
        entity.setUpdateTime(this.updateTime);
        entity.setEndTime(this.endTime);

        try {
            // 转换 testCaseIds
            if (this.testCaseIds != null) {
                entity.setSceneIdsJson(objectMapper.writeValueAsString(this.testCaseIds));
            }
            // 转换 result
            if (this.result != null) {
                entity.setResultJson(objectMapper.writeValueAsString(this.result));
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON转换失败", e);
        }
        return entity;
    }
} 