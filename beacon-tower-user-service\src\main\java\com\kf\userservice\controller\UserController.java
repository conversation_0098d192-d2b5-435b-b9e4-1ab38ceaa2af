package com.kf.userservice.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONObject;
import com.kf.userservice.common.RequireHeader;
import com.kf.userservice.common.ResponseDoMain;
import com.kf.userservice.dto.ResetUserPassWordDTO;
import com.kf.userservice.dto.VerificationCodeDTO;
import com.kf.userservice.entity.TEmailVerificationCode;
import com.kf.userservice.entity.TUser;
import com.kf.userservice.entity.TUserRole;
import com.kf.userservice.service.EmailVerificationCodeService;
import com.kf.userservice.service.UserRoleService;
import com.kf.userservice.service.UserService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import static com.kf.userservice.utils.MD5Util.string2MD5;

@Slf4j
@RestController
@RequestMapping("/user")
public class UserController {

    @Resource
    private UserService userService;

    @Resource
    private UserRoleService userRoleService;

    @Resource
    private EmailVerificationCodeService emailVerificationCodeService;

    @Resource
    private JavaMailSender javaMailSender;

    @Value("${spring.mail.username}")
    private String from;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public ResponseDoMain register(@RequestBody JSONObject json) {
        log.info("用户注册信息：{}", json);
        String userName = json.getStr("username");
        TUser selectUser = userService.queryByUserName(userName);
        if (selectUser != null) {
            return ResponseDoMain.custom("用户名已存在", false, "", 200);
        }
        TEmailVerificationCode tEmailVerificationCode = emailVerificationCodeService.queryByEmail(json.get("email").toString());
        if (tEmailVerificationCode == null) {
            return ResponseDoMain.custom("验证码错误", false, "", 200);
        }
        String code = json.get("code").toString();
        if (!code.equals(tEmailVerificationCode.getCode())) {
            return ResponseDoMain.custom("验证码错误", false, "", 200);
        }
        Date date = new Date();
        //验证码过期时间
        Date expireTime = tEmailVerificationCode.getExpireTime();
        if (date.after(expireTime)) {
            return ResponseDoMain.custom("验证码已过期", false, "", 200);
        }

        TUser user = new TUser();
        user.setUsername(userName);
        user.setPassword(string2MD5(json.get("password").toString()));
        user.setEmail(json.get("email").toString());
        user.setStatus("1");
        user.setCreateTime(new Date());
        user.setModifyTime(new Date());
        TUser tuser = userService.insertUser(user);
        if (tuser != null) {
            TUserRole tUserRole = new TUserRole();
            tUserRole.setUserId(tuser.getUserId());
            tUserRole.setRoleId(2);
            userRoleService.insert(tUserRole);
        }
        TEmailVerificationCode delCode = new TEmailVerificationCode();
        delCode.setId(tEmailVerificationCode.getId());
        delCode.setIsDeleted(1);
        //将验证码删除
        emailVerificationCodeService.update(delCode);
        return ResponseDoMain.custom("注册成功", true, "", 200);
    }

    /**
     * 注册时获得验证码
     */
    @GetMapping("/getVerificationCode")
    public ResponseDoMain getVerificationCode(@RequestParam("email") String email) {
        //判断邮箱是否存在
        TUser tUser = userService.queryByEmail(email);
        if (tUser != null) {
            return ResponseDoMain.custom("邮箱已被注册", false, "", 200);
        }
        String randomNumber = RandomUtil.randomNumbers(4);
        log.info("发送{}验证码:{}", email, randomNumber);
        SimpleMailMessage smm = new SimpleMailMessage();
        smm.setFrom("质效平台" + " <" + from + ">");
        smm.setTo(email);
        smm.setSubject("质效平台验证码:" + randomNumber);
        smm.setText("您的验证码为：" + randomNumber + "，请在5分钟内完成注册，如非本人操作，请忽略此邮件。");
        javaMailSender.send(smm);
        TEmailVerificationCode tEmailVerificationCode = new TEmailVerificationCode();
        tEmailVerificationCode.setEmail(email);
        tEmailVerificationCode.setCode(randomNumber);
        tEmailVerificationCode.setCreateTime(new Date());
        tEmailVerificationCode.setUpdateTime(new Date());
        tEmailVerificationCode.setExpireTime(new Date(new Date().getTime() + 5 * 60 * 1000));
        tEmailVerificationCode.setIsDeleted(0);
        emailVerificationCodeService.insert(tEmailVerificationCode);
        return ResponseDoMain.success("验证码发送成功");
    }

    /**
     * 找回密码时获取验证码
     */
    @PostMapping("/gerResetPassWordVerificationCode")
    public ResponseDoMain getFindPasswordCode(@RequestParam("email") String email) {
        //判断邮箱是否存在
        TUser tUser = userService.queryByEmail(email);
        if (tUser == null) {
            return ResponseDoMain.custom("邮箱不存在", false, "", 200);
        }
        String randomNumber = RandomUtil.randomNumbers(4);
        log.debug("密码找回操作，发送{}验证码:{}", email, randomNumber);
        SimpleMailMessage smm = new SimpleMailMessage();
        smm.setFrom("质效平台" + " <" + from + ">");
        smm.setTo(email);
        smm.setSubject("质效平台验证码:" + randomNumber);
        smm.setText("您的验证码为：" + randomNumber + "，请在5分钟将内完成操作，如非本人操作，请忽略此邮件。");
        javaMailSender.send(smm);
        TEmailVerificationCode tEmailVerificationCode = new TEmailVerificationCode();
        tEmailVerificationCode.setEmail(email);
        tEmailVerificationCode.setCode(randomNumber);
        tEmailVerificationCode.setCreateTime(new Date());
        tEmailVerificationCode.setUpdateTime(new Date());
        tEmailVerificationCode.setExpireTime(new Date(new Date().getTime() + 5 * 60 * 1000));
        tEmailVerificationCode.setIsDeleted(0);
        emailVerificationCodeService.insert(tEmailVerificationCode);
        return ResponseDoMain.success("验证码发送成功");
    }

    /**
     * 重置密码,检查验证码是否正确
     */
    @PostMapping("/checkVerificationCode")
    public ResponseDoMain checkVerificationCode(@RequestBody VerificationCodeDTO dto) {
        String email = dto.getEmail();
        String code = dto.getCode();
        TEmailVerificationCode tEmailVerificationCode = emailVerificationCodeService.queryByEmail(email);
        if (tEmailVerificationCode == null) {
            return ResponseDoMain.custom("验证码错误", false, "", 200);
        }
        if (!code.equals(tEmailVerificationCode.getCode())) {
            return ResponseDoMain.custom("验证码错误", false, "", 200);
        }
        Date date = new Date();
        //验证码过期时间
        Date expireTime = tEmailVerificationCode.getExpireTime();
        if (date.after(expireTime)) {
            return ResponseDoMain.custom("验证码已过期", false, "", 200);
        }
        //生成一个uuid作为sign
        String uuid = IdUtil.simpleUUID();
        tEmailVerificationCode.setSign(uuid);
        emailVerificationCodeService.update(tEmailVerificationCode);
        TUser user = new TUser();
        user.setEmail(email);
        List<TUser> userList = userService.queryByUser(user);
        VerificationCodeDTO verificationCodeDTO = new VerificationCodeDTO();
        verificationCodeDTO.setEmail(email);
        verificationCodeDTO.setSign(uuid);
        verificationCodeDTO.setUserName(userList.get(0).getUsername());
        log.info("{}重置密码，验证码正确，返回sign:{}", email, uuid);
        return ResponseDoMain.success(verificationCodeDTO, "");
    }

    @PostMapping("/resetPassWord")
    public ResponseDoMain resetPassWord(@RequestBody VerificationCodeDTO dto) {
        String userName = dto.getUserName();
        String password = dto.getPassWord();
        TUser user = userService.queryByUserName(userName);
        if (user == null) {
            return ResponseDoMain.custom("用户不存在", false, "", 200);
        }
        String email = user.getEmail();
        TEmailVerificationCode emailVerificationCode = emailVerificationCodeService.queryByEmail(email);
        if (emailVerificationCode == null) {
            return ResponseDoMain.custom("验证码错误", false, "", 200);
        }
        if (!dto.getSign().equals(emailVerificationCode.getSign())) {
            return ResponseDoMain.custom("验证码错误", false, "", 200);
        }
        user.setPassword(string2MD5(password));
        int num = userService.update(user);
        if (num == 0) {
            return ResponseDoMain.custom("重置密码失败", false, "", 200);
        }
        //将验证码删除
        TEmailVerificationCode delCode = new TEmailVerificationCode();
        delCode.setId(emailVerificationCode.getId());
        delCode.setIsDeleted(1);
        emailVerificationCodeService.update(delCode);
        return ResponseDoMain.success("", "重置密码成功");
    }

    @RequireHeader("userId")
    @PostMapping("/resetUserPassword")
    public ResponseDoMain resetUserPassword(HttpServletRequest request,@RequestBody ResetUserPassWordDTO dto) {
        log.info("用户：{}，修改密码", request.getHeader("userId"));
        TUser user = userService.queryById(request.getHeader("userId"));
        String oldPassword = dto.getOldPassWord();
        //判断旧密码是否正确
        if (!string2MD5(oldPassword).equals(user.getPassword())) {
            return ResponseDoMain.custom("旧密码错误", false, "", 200);
        }
        String newPassWord = dto.getNewPassWord();
        user.setPassword(string2MD5(newPassWord));
        user.setModifyTime(new Date());
        int num = userService.update(user);
        if (num == 0) {
            return ResponseDoMain.custom("重置密码失败", false, "", 200);
        }
        return ResponseDoMain.success("", "修改密码成功");
    }
}