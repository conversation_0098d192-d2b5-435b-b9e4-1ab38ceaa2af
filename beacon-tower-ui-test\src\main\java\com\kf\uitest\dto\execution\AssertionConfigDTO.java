package com.kf.uitest.dto.execution;

import com.kf.uitest.enums.AssertionType;
import com.kf.uitest.enums.AssertionOperator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssertionConfigDTO {
    /**
     * 断言类型
     * @see com.kf.uitest.enums.AssertionType
     */
    private AssertionType type;
    
    /**
     * 实际值获取方式
     * 支持：元素文本、元素属性、变量值等
     */
    private ValueExtractorDTO valueExtractor;
    
    /**
     * 比较操作符
     * 定义如何比较预期值和实际值
     * @see com.kf.uitest.enums.AssertionOperator
     */
    private AssertionOperator operator;
    
    /**
     * 预期值
     * 与实际值进行比较的期望值
     * 对于范围比较、集合操作等，可以是数组或列表格式的字符串
     * 示例：
     * - 普通值: "期望的文本"
     * - 范围值: "[1, 100]"
     * - 集合值: "['值1', '值2', '值3']"
     * - 时间范围: "['2024-01-01', '2024-12-31']"
     */
    private String expected;
    
    /**
     * 断言消息
     * 断言失败时的提示信息
     */
    private String message;
    
    /**
     * 是否为软断言
     * true: 断言失败继续执行
     * false: 断言失败立即终止
     */
    private boolean soft;
    
    /**
     * 超时时间（秒）
     * 等待断言条件满足的最大时间
     * 默认：10
     */
    private Integer timeout;
    
    /**
     * 重试间隔（毫秒）
     * 断言失败后重试的间隔时间
     * 默认：500
     */
    private Long retryInterval;
} 