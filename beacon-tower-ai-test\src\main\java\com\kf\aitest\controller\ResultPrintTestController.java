package com.kf.aitest.controller;

import com.kf.aitest.common.ResponseDoMain;
import com.kf.aitest.dto.DataComparisonResultDTO;
import com.kf.aitest.dto.StageDataDTO;
import com.kf.aitest.service.AiEvaluationService;
import com.kf.aitest.service.ResultPrintService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 结果打印测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/test/print")
public class ResultPrintTestController {

    @Autowired
    private ResultPrintService resultPrintService;

    @Autowired
    private AiEvaluationService aiEvaluationService;

    /**
     * 测试阶段结果打印
     */
    @GetMapping("/stage")
    public ResponseDoMain<String> testStagePrint() {
        // 创建测试数据
        StageDataDTO stageData = new StageDataDTO();
        stageData.setStageName("recognize");
        stageData.setDataType("json");
        stageData.setStatus("SUCCESS");
        stageData.setFetchTime(LocalDateTime.now());
        stageData.setDuration(1500L);
        stageData.setAiScore(85);
        stageData.setAiEvaluation("数据识别阶段表现良好，文档结构清晰，字段识别准确率较高。建议优化部分边缘字段的识别精度。");

        // 打印结果
        resultPrintService.printStageResult(stageData, "TEST001");

        return ResponseDoMain.success("阶段结果打印测试完成");
    }

    /**
     * 测试整体结果打印
     */
    @GetMapping("/overall")
    public ResponseDoMain<String> testOverallPrint() {
        // 创建测试数据
        DataComparisonResultDTO result = new DataComparisonResultDTO();
        result.setId("TEST001");
        result.setTaskId("TASK-123");
        result.setOverallStatus("SUCCESS");
        result.setSuccessStageCount(4);
        result.setTotalStageCount(4);
        result.setStartTime(LocalDateTime.now().minusMinutes(5));
        result.setEndTime(LocalDateTime.now());
        result.setTotalDuration(300000L); // 5分钟
        result.setOverallScore(88);
        result.setOverallAiEvaluation("整体数据处理质量优秀，各阶段表现稳定。数据一致性良好，符合临床试验标准。建议继续保持当前处理流程。");

        // 创建阶段结果
        List<StageDataDTO> stageResults = new ArrayList<>();
        
        // 识别阶段
        StageDataDTO stage1 = new StageDataDTO();
        stage1.setStageName("recognize");
        stage1.setStatus("SUCCESS");
        stage1.setAiScore(85);
        stageResults.add(stage1);

        // 提取阶段
        StageDataDTO stage2 = new StageDataDTO();
        stage2.setStageName("extraction");
        stage2.setStatus("SUCCESS");
        stage2.setAiScore(90);
        stageResults.add(stage2);

        // 结构化阶段
        StageDataDTO stage3 = new StageDataDTO();
        stage3.setStageName("structured");
        stage3.setStatus("SUCCESS");
        stage3.setAiScore(87);
        stageResults.add(stage3);

        // 转换阶段
        StageDataDTO stage4 = new StageDataDTO();
        stage4.setStageName("transformer");
        stage4.setStatus("SUCCESS");
        stage4.setAiScore(92);
        stageResults.add(stage4);

        result.setStageResults(stageResults);

        // 打印结果
        resultPrintService.printOverallResult(result);

        return ResponseDoMain.success("整体结果打印测试完成");
    }

    /**
     * 测试任务流程打印
     */
    @GetMapping("/task-flow")
    public ResponseDoMain<String> testTaskFlowPrint() {
        String taskId = "TASK-DEMO-" + System.currentTimeMillis();
        
        // 打印任务开始
        resultPrintService.printTaskStart(taskId, 2);

        // 模拟处理过程
        try {
            Thread.sleep(1000); // 模拟处理时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 打印任务完成
        resultPrintService.printTaskComplete(taskId);

        return ResponseDoMain.success("任务流程打印测试完成");
    }

    /**
     * 测试完整流程打印
     */
    @GetMapping("/full-demo")
    public ResponseDoMain<String> testFullDemo() {
        String taskId = "DEMO-TASK-" + System.currentTimeMillis();
        String testId = "DEMO001";

        // 1. 任务开始
        resultPrintService.printTaskStart(taskId, 1);

        // 2. 处理各个阶段
        String[] stages = {"recognize", "extraction", "structured", "transformer"};
        int[] scores = {85, 90, 87, 92};
        String[] evaluations = {
            "文档识别阶段表现良好，结构清晰，字段识别准确。",
            "信息提取阶段优秀，关键数据提取完整，格式规范。",
            "结构化处理良好，数据组织合理，层次分明。",
            "数据转换阶段表现出色，格式转换准确，数据完整性保持良好。"
        };

        List<StageDataDTO> stageResults = new ArrayList<>();

        for (int i = 0; i < stages.length; i++) {
            // 阶段开始
            resultPrintService.printStageSeparator(stages[i], testId);

            // 创建阶段数据
            StageDataDTO stageData = new StageDataDTO();
            stageData.setStageName(stages[i]);
            stageData.setDataType("json");
            stageData.setStatus("SUCCESS");
            stageData.setFetchTime(LocalDateTime.now());
            stageData.setDuration(1000L + i * 500);
            stageData.setAiScore(scores[i]);
            stageData.setAiEvaluation(evaluations[i]);

            stageResults.add(stageData);

            // 打印阶段结果
            resultPrintService.printStageResult(stageData, testId);

            // 模拟处理时间
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // 3. 整体评估
        DataComparisonResultDTO overallResult = new DataComparisonResultDTO();
        overallResult.setId(testId);
        overallResult.setTaskId(taskId);
        overallResult.setOverallStatus("SUCCESS");
        overallResult.setSuccessStageCount(4);
        overallResult.setTotalStageCount(4);
        overallResult.setStartTime(LocalDateTime.now().minusMinutes(2));
        overallResult.setEndTime(LocalDateTime.now());
        overallResult.setTotalDuration(120000L);
        overallResult.setOverallScore(88);
        overallResult.setOverallAiEvaluation("整体数据处理质量优秀，各阶段协调配合良好。数据一致性和完整性均达到预期标准，建议继续保持当前处理流程。");
        overallResult.setStageResults(stageResults);

        resultPrintService.printOverallResult(overallResult);

        // 4. 任务完成
        resultPrintService.printTaskComplete(taskId);

        return ResponseDoMain.success("完整流程演示完成");
    }

    /**
     * 测试评分解析功能
     */
    @GetMapping("/score-parsing")
    public ResponseDoMain<String> testScoreParsing() {
        // 测试不同格式的AI响应
        String[] testResponses = {
            "**综合评估**: 各阶段数据处理整体质量存在显著缺陷\n**综合评分**: 38",
            "**评分**: 85",
            "评分：90",
            "综合评分：75",
            "**整体评估**: 数据质量良好\n**评分**: 88",
            "评估结果：数据处理完成\n评分：92"
        };

        StringBuilder result = new StringBuilder();
        result.append("评分解析测试结果：\n");

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method extractScoreMethod =
                aiEvaluationService.getClass().getDeclaredMethod("extractScore", String.class);
            extractScoreMethod.setAccessible(true);

            java.lang.reflect.Method extractEvaluationMethod =
                aiEvaluationService.getClass().getDeclaredMethod("extractEvaluation", String.class);
            extractEvaluationMethod.setAccessible(true);

            for (int i = 0; i < testResponses.length; i++) {
                String response = testResponses[i];
                Integer score = (Integer) extractScoreMethod.invoke(aiEvaluationService, response);
                String evaluation = (String) extractEvaluationMethod.invoke(aiEvaluationService, response);

                result.append(String.format("\n测试 %d:\n", i + 1));
                result.append(String.format("输入: %s\n", response));
                result.append(String.format("解析评分: %s\n", score));
                result.append(String.format("解析评估: %s\n",
                    evaluation.length() > 50 ? evaluation.substring(0, 50) + "..." : evaluation));
                result.append("---\n");
            }

        } catch (Exception e) {
            result.append("测试失败: ").append(e.getMessage());
        }

        System.out.println(result.toString());
        return ResponseDoMain.success(result.toString());
    }
}
