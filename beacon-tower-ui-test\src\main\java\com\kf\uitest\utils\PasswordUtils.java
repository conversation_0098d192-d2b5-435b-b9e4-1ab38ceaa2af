package com.kf.uitest.utils;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Component
public class PasswordUtils {

    private static final String ALGORITHM = "AES";
    private static final String SECRET_KEY = System.getenv("DB_PASSWORD_SECRET_KEY");

    private final BCryptPasswordEncoder bcryptEncoder = new BCryptPasswordEncoder();

    /**
     * 加密数据库密码
     */
    public String encryptDbPassword(String password) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(
                    SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);

            byte[] encrypted = cipher.doFinal(password.getBytes());
            return Base64.getEncoder().encodeToString(encrypted);

        } catch (Exception e) {
            throw new SecurityException("Failed to encrypt database password", e);
        }
    }

    /**
     * 解密数据库密码
     */
    public String decryptDbPassword(String encryptedPassword) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(
                    SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, keySpec);

            byte[] decrypted = cipher.doFinal(
                    Base64.getDecoder().decode(encryptedPassword));
            return new String(decrypted, StandardCharsets.UTF_8);

        } catch (Exception e) {
            throw new SecurityException("Failed to decrypt database password", e);
        }
    }
}