package com.kf.uitest.manager;

import com.microsoft.playwright.*;
import lombok.Data;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.kf.uitest.enums.BrowserEngine;

@Slf4j
@Component
public class BrowserManager {
    private final Playwright playwright = Playwright.create();
    private final Map<String, Browser> browserInstances = new ConcurrentHashMap<>();

    /**
     * 获取浏览器实例
     */
    public Browser getBrowser(String executionId, BrowserEngine browserEngine, BrowserOptions options) {
        String key = generateBrowserKey(executionId, options.isShareBrowser());
        return browserInstances.computeIfAbsent(key, id -> createBrowser(browserEngine, options));
    }

    /**
     * 关闭浏览器实例
     */
    public void closeBrowser(String executionId, boolean shareBrowser) {
        String key = generateBrowserKey(executionId, shareBrowser);
        Browser browser = browserInstances.remove(key);
        if (browser != null) {
            try {
                browser.close();
            } catch (Exception e) {
                log.error("Failed to close browser for execution: {}", executionId, e);
            }
        }
    }

    private String generateBrowserKey(String executionId, boolean shareBrowser) {
        return shareBrowser ? "shared:" + executionId : executionId;
    }

    private Browser createBrowser(BrowserEngine browserEngine, BrowserOptions options) {
        BrowserType type = getBrowserType(browserEngine);
        return type.launch(new BrowserType.LaunchOptions()
                .setHeadless(options.isHeadless())
                .setSlowMo(options.getExecutionSpeed()));
    }

    private BrowserType getBrowserType(BrowserEngine browserEngine) {
        return switch (browserEngine.getPlaywrightEngine()) {
            case "chromium" -> playwright.chromium();
            case "firefox" -> playwright.firefox();
            case "webkit" -> playwright.webkit();
            default -> throw new IllegalArgumentException("Unsupported browser engine: " + browserEngine);
        };
    }

    @Data
    @Builder
    public static class BrowserOptions {
        private boolean headless;
        private int executionSpeed;
        private boolean shareBrowser;
    }
}