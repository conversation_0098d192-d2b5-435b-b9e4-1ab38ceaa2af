package com.kf.uitest.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kf.uitest.entity.UiTestHook;
import com.kf.uitest.enums.HookParameterKey;
import com.kf.uitest.model.TestExecutionContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class HookParameterUtils {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 获取参数Map
     */
    public static Map<String, Object> getParameterMap(UiTestHook hook) {
        if (ObjectUtils.isEmpty(hook.getParameters())) {
            return new HashMap<>();
        }
        try {
            return OBJECT_MAPPER.readValue(hook.getParameters(),
                    new TypeReference<>() {
                    });
        } catch (Exception e) {
            log.error("Failed to parse parameters: {}", hook.getParameters(), e);
            return new HashMap<>();
        }
    }

    /**
     * 设置参数Map
     */
    public static void setParameterMap(UiTestHook hook, Map<String, Object> paramMap) {
        try {
            hook.setParameters(paramMap != null ?
                    OBJECT_MAPPER.writeValueAsString(paramMap) : null);
        } catch (Exception e) {
            log.error("Failed to serialize parameters: {}", paramMap, e);
        }
    }

    /**
     * 获取指定参数值
     */
    public static <T> T getParameter(UiTestHook hook, HookParameterKey key) {
        Map<String, Object> paramMap = getParameterMap(hook);
        return (T) paramMap.get(key.getKey());
    }

    /**
     * 设置指定参数值
     */
    public static void setParameter(UiTestHook hook, HookParameterKey key, Object value) {
        Map<String, Object> paramMap = getParameterMap(hook);
        paramMap.put(key.getKey(), value);
        setParameterMap(hook, paramMap);
    }

    /**
     * 获取参数Map（带上下文）
     */
    public static Map<String, Object> getParameterMap(UiTestHook hook, HookParameterKey key, TestExecutionContext context) {
        Object value = getParameter(hook, key);
        if (value == null) {
            return new HashMap<>();
        }

        try {
            if (value instanceof Map) {
                Map<String, Object> paramMap = (Map<String, Object>) value;
                // 处理变量替换
                return processVariables(paramMap, context);
            } else if (value instanceof String) {
                Map<String, Object> paramMap = OBJECT_MAPPER.readValue((String) value,
                        new TypeReference<Map<String, Object>>() {
                        });
                // 处理变量替换
                return processVariables(paramMap, context);
            }
            return new HashMap<>();
        } catch (Exception e) {
            log.error("Failed to parse parameters: {}", value, e);
            return new HashMap<>();
        }
    }

    /**
     * 处理参数中的变量
     */
    private static Map<String, Object> processVariables(Map<String, Object> params, TestExecutionContext context) {
        Map<String, Object> result = new HashMap<>();
        params.forEach((key, value) -> {
            if (value instanceof String strValue) {
                if (strValue.startsWith("${") && strValue.endsWith("}")) {
                    String varName = strValue.substring(2, strValue.length() - 1);
                    result.put(key, context.getVariable(varName));
                } else {
                    result.put(key, value);
                }
            } else {
                result.put(key, value);
            }
        });
        return result;
    }
}