package com.kf.aitest.service;

import com.kf.aitest.dto.DataComparisonResultDTO;
import com.kf.aitest.dto.StageDataDTO;

/**
 * 结果打印服务接口
 */
public interface ResultPrintService {
    
    /**
     * 打印单个阶段的评估结果
     * 
     * @param stageData 阶段数据
     * @param id 数据ID
     */
    void printStageResult(StageDataDTO stageData, String id);
    
    /**
     * 打印整体评估结果
     * 
     * @param comparisonResult 对比结果
     */
    void printOverallResult(DataComparisonResultDTO comparisonResult);
    
    /**
     * 打印阶段分隔线
     * 
     * @param stageName 阶段名称
     * @param id 数据ID
     */
    void printStageSeparator(String stageName, String id);
    
    /**
     * 打印任务开始信息
     * 
     * @param taskId 任务ID
     * @param totalIds 总ID数量
     */
    void printTaskStart(String taskId, int totalIds);
    
    /**
     * 打印任务完成信息
     * 
     * @param taskId 任务ID
     */
    void printTaskComplete(String taskId);
}
