package com.kf.aitest.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应码枚举
 */
@Getter
@AllArgsConstructor
public enum ResponseCodeEnum implements BaseEnum<Integer> {
    
    SUCCESS(200, "操作成功"),
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    INTERNAL_SERVER_ERROR(500, "系统内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用");
    
    private final Integer value;
    private final String description;
    
    @Override
    public Integer getValue() {
        return value;
    }
    
    @Override
    public String getDescription() {
        return description;
    }
}
