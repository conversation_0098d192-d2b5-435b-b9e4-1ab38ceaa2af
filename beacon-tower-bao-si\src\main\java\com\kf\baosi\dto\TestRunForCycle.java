package com.kf.baosi.dto;

import lombok.Data;

import java.util.List;
/**
 * 测试周期中的测试用例
 */
@Data
public class TestRunForCycle {

    // 测试用例编号 CA-4802
    private String testCaseKey;

    // 测试用例执行时间 2024/05/24 15:40
    private String executionTimeStamp;

    // 测试用例执行人 xuewen.wang
    private String executedBy;

    // 测试用例关联的bug列表
    private List<BugDTO> bugs;

    // 测试用例标题
    private String summary;

    // 测试用例附件，比如图片
    private List<AttachmentDTO> attachments;

    // 测试用例运行id
    private String id;

    private String comment;

    // 测试用例执行状态
    private String status;
}

