package com.kf.userservice;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@MapperScan({"com.kf.userservice.dao"})
@EnableDiscoveryClient
@SpringBootApplication
public class BeaconTowerUserServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(BeaconTowerUserServiceApplication.class, args);
    }
}
