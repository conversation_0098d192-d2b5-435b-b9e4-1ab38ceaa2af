# kf

### 介绍
测试平台后端，目前完成的功能：精准测试-推荐接口
### 后端技术栈
Java
SpringBoot
SpringSecurity
Mybatis
Maven
RabbitMQ
### 前端技术栈
typescript
vue3
ES2015+
element-plus
pinia
vite
postcss
### 精准测试实现思路
#### 1.代码克隆
分别将基准分支与被测分支的代码拉取到本地
#### 2.项目编译
分别编译两个项目
#### 3.方法对比
遍历两个项目的所有方法，降噪之后进行对比，获得新增与修改方法，这些方法即为受影响的方法
#### 4.调用链分析
#### 4.1静态调用链分析
分析被测分支的Class文件，以Controller类为根节点，获得所有方法之间的调用关系（调用链）
#### 4.2动态调用链分析
静态分析无法分析代码中存在的AOP和多态，采用动态分析进行补充：使用javaagent对被测工程进行插桩，在工程运行时，获得更完整的调用链，将这些调用链进行去重入库
#### 5.推荐接口
将受影响的方法带入调用链中进行匹配，如果一条调用链中包含受影响的方法，那么这个调用链对应的接口即为受影响的接口
#### 6.代码绑定测试用例
使用javaagent插桩获得方法的入参，入参即视为一个测试用例，将这个测试用例与所有经过的方法（动态分析的调用链）进行绑定
#### 7.推荐测试用例
查找受影响的调用链，找到受影响的接口与测试用例进行推荐，可以采用钉钉消息的方式进行通知
### 未来畅想
可以做一个丐版的流量回放功能，在得到接口与请求入参之后，直接拉起接口自动化测试，得到测试结果，免去人工回归的时间。