package com.kf.uitest.service.impl;

import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.kf.uitest.dao.UiTestBlockMapper;
import com.kf.uitest.entity.UiTestBlock;
import com.kf.uitest.entity.UiTestBlockStep;
import com.kf.uitest.service.UiTestBlockService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;


@Service
@Slf4j
public class UiTestBlockServiceImpl implements UiTestBlockService {
    
    @Resource
    private UiTestBlockMapper blockMapper;
    
    @Override
    public List<UiTestBlock> findByCaseId(String caseId) {
        return blockMapper.findByCaseId(caseId);
    }
    
    @Override
    public UiTestBlock getById(String blockId) {
        return blockMapper.selectById(blockId);
    }
    
    @Override
    public UiTestBlock create(UiTestBlock block) {
        initializeBlock(block);
        blockMapper.insert(block);
        return block;
    }
    
    @Override
    public boolean update(UiTestBlock block) {
        block.setUpdateTime(LocalDateTime.now());
        return blockMapper.updateById(block) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String blockId) {
        // 删除块的所有步骤关联
        Db.lambdaUpdate(UiTestBlockStep.class)
                .eq(UiTestBlockStep::getBlockId, blockId)
                .remove();
        
        return blockMapper.deleteById(blockId) > 0;
    }
    
    @Override
    public List<UiTestBlock> findChildBlocks(String blockId) {
        return blockMapper.findChildBlocks(blockId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBlockOrder(String blockId, Integer newOrder) {
        UiTestBlock block = blockMapper.selectById(blockId);
        if (block == null) {
            return false;
        }
        
        Integer oldOrder = block.getBlockOrder();
        if (oldOrder.equals(newOrder)) {
            return true;
        }
        
        // 使用Db工具类更新顺序
        if (newOrder > oldOrder) {
            // 向后移动
            Db.lambdaUpdate(UiTestBlock.class)
                    .eq(UiTestBlock::getCaseId, block.getCaseId())
                    .gt(UiTestBlock::getBlockOrder, oldOrder)
                    .le(UiTestBlock::getBlockOrder, newOrder)
                    .setSql("block_order = block_order - 1")
                    .update();
        } else {
            // 向前移动
            Db.lambdaUpdate(UiTestBlock.class)
                    .eq(UiTestBlock::getCaseId, block.getCaseId())
                    .ge(UiTestBlock::getBlockOrder, newOrder)
                    .lt(UiTestBlock::getBlockOrder, oldOrder)
                    .setSql("block_order = block_order + 1")
                    .update();
        }
        
        block.setBlockOrder(newOrder);
        return update(block);
    }
    
    private void initializeBlock(UiTestBlock block) {
        LocalDateTime now = LocalDateTime.now();
        block.setCreateTime(now);
        block.setUpdateTime(now);
        
        if (block.getBlockOrder() == null) {
            // 使用Db工具类查询最大顺序
            Integer maxOrder = Db.lambdaQuery(UiTestBlock.class)
                    .eq(UiTestBlock::getCaseId, block.getCaseId())
                    .orderByDesc(UiTestBlock::getBlockOrder)
                    .last("LIMIT 1")
                    .oneOpt()
                    .map(UiTestBlock::getBlockOrder)
                    .orElse(0);
            block.setBlockOrder(maxOrder + 1);
        }
    }
} 