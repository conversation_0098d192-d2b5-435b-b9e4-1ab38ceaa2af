import com.microsoft.playwright.*;
import com.microsoft.playwright.options.WaitForSelectorState;

public class PlaywrightTest2 {
    public static void main(String[] args) {
        // 初始化Playwright
        try (Playwright playwright = Playwright.create()) {
            BrowserType browserType = playwright.chromium();
            Browser browser = browserType.launch(new BrowserType.LaunchOptions().setHeadless(false));
            BrowserContext context = browser.newContext();
            Page page = context.newPage();

            // 设置要监听的接口关键字（根据实际情况修改）
            String targetApi = "/api/sso-web/sso/sso/doLogin"; // 例如接口A的路径为 /api/endpointA

            // 添加监听器，在请求发出和响应接收时打印信息
            context.onRequest(request -> {
                if (request.url().contains(targetApi)) {
                    System.out.println("--------- 请求信息 ---------");
                    System.out.println("请求URL: " + request.url());
                    System.out.println("请求方法: " + request.method());
                    System.out.println("请求头: " + request.headers());
                    if (request.postData() != null) {
                        System.out.println("请求Body: " + request.postData());
                    }
                }
            });

            context.onResponse(response -> {
                if (response.url().contains(targetApi)) {
                    System.out.println("--------- 响应信息 ---------");
                    System.out.println("响应URL: " + response.url());
                    System.out.println("响应状态码: " + response.status());
                    System.out.println("响应头: " + response.headers());
                    String body = "";
                    try {
                        body = response.text();
                    } catch (Exception e) {
                        System.out.println("获取响应文本失败: " + e.getMessage());
                    }
                    System.out.println("响应Body: " + body);
                }
            });

            // 打开目标页面（请替换为实际URL）
            page.navigate("https://www.trialos.com.cn/login/");

            // 定位输入框并输入内容（请根据实际DOM结构与选择器修改）
            page.fill("#username", "kfur77");
            page.fill("#password", "123@Qaz#020609");

            // 点击确定按钮（请根据实际选择器修改）
            page.click("text=登 录");

            // 等待一会儿，让请求与响应有时间捕获
            page.waitForTimeout(3000);

            page.click("text=eCollege");

            // 新增部分：检查“进入培训”按钮并点击第一个
            try {
                // 创建一个定位器，查找包含“进入培训”文本的元素
                Locator enterTrainingLocator = page.locator("text=进入培训");

                // 检查是否有任何匹配的元素
                if (enterTrainingLocator.count() > 0) {
                    System.out.println("找到 " + enterTrainingLocator.count() + " 个“进入培训”按钮。准备点击第一个。");

                    enterTrainingLocator.first().waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE));

                    // 点击第一个匹配的“进入培训”按钮
                    enterTrainingLocator.first().click();

                    System.out.println("已点击第一个“进入培训”按钮。");
                } else {
                    System.out.println("页面上没有找到“进入培训”按钮。");
                }
            } catch (Exception e) {
                System.out.println("在查找或点击“进入培训”按钮时发生错误: " + e.getMessage());
            }

            // 关闭浏览器
            browser.close();
        }
    }
}
