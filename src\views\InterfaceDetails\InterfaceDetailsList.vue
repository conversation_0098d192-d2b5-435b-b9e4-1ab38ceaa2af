<template >
    <div class="el-table">
        <el-table :data="tableData" stripe height="100%">
            <el-table-column :label="objInfo">
                <el-table-column prop="interfaceName" label="接口" />
                <el-table-column prop="interfaceDesc" label="接口描述" />
                <el-table-column prop="interfaceNotes" label="详细描述">
                    <template #default="scope">
                        <div v-html="scope.row.interfaceNotes"></div>
                    </template>
                </el-table-column>
                <el-table-column prop="pageUrl" label="页面入口">
                    <template #default="scope">
                        <div v-for="item in scope.row.pageUrl" :key="item">{{ item }}</div>
                    </template>
                </el-table-column>
            </el-table-column>
        </el-table>
    </div>
</template>

<script lang="ts" setup>
import { useRoute } from 'vue-router'
import { getAnnotation } from '@/api/layout'
import { reactive, ref } from 'vue'
interface data {
    interfaceName: string
    interfaceDesc: string
    interfaceNotes: string
    pageUrl: Array<string>
}
const objInfo = ref('项目信息')
const tableData: Array<data> = reactive([])

const route = useRoute()
const taskId = reactive({
    taskId: route.params.taskId
})
const getAnnotations = async () => {
    const res = await getAnnotation(taskId)
    // 将数据赋值给tableData
    res.data.data.forEach((item: any) => {
        tableData.push({
            interfaceName: item.interfaceName,
            interfaceDesc: item.interfaceDesc,
            interfaceNotes: item.interfaceNotes.replace(/\\n/g, '<br/>'),
            pageUrl: item.pageUrl
        })
    })
}
getAnnotations()
</script>
<style scoped lang='postcss'>
.el-table {
    display: flex;
    height: 100vh;
    width: 100%;
}

/* :deep(.el-table__inner-wrapper) {
    width: 100%;
} */
</style>

