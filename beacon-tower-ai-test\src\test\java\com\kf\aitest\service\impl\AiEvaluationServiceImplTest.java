package com.kf.aitest.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kf.aitest.configuration.ArkApiConfig;
import com.kf.aitest.dto.ark.ArkApiResponse;
import com.kf.aitest.exception.AiServiceException;
import com.kf.aitest.service.DataChunkService;
import com.kf.aitest.service.PromptTemplateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AI评估服务测试
 */
@ExtendWith(MockitoExtension.class)
class AiEvaluationServiceImplTest {

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private PromptTemplateService promptTemplateService;

    @Mock
    private DataChunkService dataChunkService;

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private ArkApiConfig arkApiConfig;

    @InjectMocks
    private AiEvaluationServiceImpl aiEvaluationService;

    @BeforeEach
    void setUp() {
        // 配置模拟对象
        when(arkApiConfig.getApiUrl()).thenReturn("https://ark.cn-beijing.volces.com/api/v3/chat/completions");
        when(arkApiConfig.getApiKey()).thenReturn("test-api-key");
        when(arkApiConfig.getModel()).thenReturn("test-model");
        when(arkApiConfig.getDefaultMaxTokens()).thenReturn(2000);
        when(arkApiConfig.getDefaultTemperature()).thenReturn(0.7);
    }

    @Test
    void testCallAiServiceWithImage_Success() {
        // 准备测试数据
        String prompt = "测试提示词";
        String imageUrl = "https://example.com/test.jpg";

        // 模拟成功响应
        ArkApiResponse mockResponse = createMockSuccessResponse();
        ResponseEntity<ArkApiResponse> responseEntity = new ResponseEntity<>(mockResponse, HttpStatus.OK);

        when(restTemplate.exchange(anyString(), any(), any(), eq(ArkApiResponse.class)))
                .thenReturn(responseEntity);

        // 执行测试
        String result = aiEvaluationService.callAiServiceWithImage(prompt, imageUrl);

        // 验证结果
        assertNotNull(result);
        assertEquals("测试AI响应内容", result);

        // 验证调用
        verify(restTemplate, times(1)).exchange(anyString(), any(), any(), eq(ArkApiResponse.class));
    }

    @Test
    void testCallAiServiceWithImage_InvalidImageUrl() {
        // 准备测试数据
        String prompt = "测试提示词";
        String invalidImageUrl = "invalid-url";

        // 执行测试并验证异常
        AiServiceException exception = assertThrows(AiServiceException.class, () -> {
            aiEvaluationService.callAiServiceWithImage(prompt, invalidImageUrl);
        });

        assertEquals("INVALID_IMAGE_URL", exception.getErrorCode());
        assertEquals("VALIDATION_ERROR", exception.getErrorType());
    }

    @Test
    void testCallAiService_ApiError() {
        // 准备测试数据
        String prompt = "测试提示词";

        // 模拟API错误响应
        ArkApiResponse mockResponse = createMockErrorResponse();
        ResponseEntity<ArkApiResponse> responseEntity = new ResponseEntity<>(mockResponse, HttpStatus.OK);

        when(restTemplate.exchange(anyString(), any(), any(), eq(ArkApiResponse.class)))
                .thenReturn(responseEntity);

        // 执行测试并验证异常
        AiServiceException exception = assertThrows(AiServiceException.class, () -> {
            aiEvaluationService.callAiServiceWithImage(prompt, null);
        });

        assertEquals("test_error_code", exception.getErrorCode());
        assertEquals("test_error_type", exception.getErrorType());
    }

    /**
     * 创建模拟成功响应
     */
    private ArkApiResponse createMockSuccessResponse() {
        ArkApiResponse response = new ArkApiResponse();
        response.setId("test-id");
        response.setModel("test-model");

        ArkApiResponse.Choice choice = new ArkApiResponse.Choice();
        choice.setIndex(0);
        choice.setFinishReason("stop");

        ArkApiResponse.Message message = new ArkApiResponse.Message();
        message.setRole("assistant");
        message.setContent("测试AI响应内容");
        choice.setMessage(message);

        response.setChoices(List.of(choice));

        ArkApiResponse.Usage usage = new ArkApiResponse.Usage();
        usage.setPromptTokens(100);
        usage.setCompletionTokens(50);
        usage.setTotalTokens(150);
        response.setUsage(usage);

        return response;
    }

    /**
     * 创建模拟错误响应
     */
    private ArkApiResponse createMockErrorResponse() {
        ArkApiResponse response = new ArkApiResponse();

        ArkApiResponse.Error error = new ArkApiResponse.Error();
        error.setMessage("测试错误消息");
        error.setCode("test_error_code");
        error.setType("test_error_type");
        response.setError(error);

        return response;
    }
}
