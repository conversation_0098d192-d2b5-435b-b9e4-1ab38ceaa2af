package com.kf.accuratetest.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.kf.accuratetest.common.ExtendedResponseDoMain;
import com.kf.accuratetest.common.RequireHeader;
import com.kf.accuratetest.common.ResponseDoMain;
import com.kf.accuratetest.common.ResultHandler;
import com.kf.accuratetest.dto.CodeParamDTO;
import com.kf.accuratetest.dto.TInterfaceAnnotationDTO;
import com.kf.accuratetest.entity.RepositoryInfo;
import com.kf.accuratetest.entity.UserInform;
import com.kf.accuratetest.service.*;
import com.kf.accuratetest.service.impl.WebSocketScheduled;
import com.kf.accuratetest.utils.DingDingUtil;
import com.kf.accuratetest.utils.JGitUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.maven.shared.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
@RestController
@RequestMapping("/code")
@Tag(name = "代码分析", description = "目前只针对SpringBoot项目进行分析")
public class CodeController {

    @Value("${kf.git.local.directory}")
    private String directory;

    @Resource
    private CodeProcessService codeProcessService;

    @Resource
    private ChainService chainService;

    @Resource
    private UserInformService userInformService;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private UserInterfaceAnnotationTaskService userInterfaceAnnotationTaskService;

    @Resource
    private InterfaceAnnotationService interfaceAnnotationService;

    @Resource
    private WebSocketScheduled webSocketScheduled;

    @Value("${kf.pa.interface}")
    private String paInterfaceDetailsList;

    /**
     * <p>
     * 1.克隆两个项目
     * </p>
     * <p>
     * 2.编译得到两个分支的所有class文件
     * </p>
     * <p>
     * 3.对比两个分支的每个方法的特征，提取新增与修改的方法
     * </p>
     * <p>
     * 4.以Controller为根节点，使用ASM对class文件进行字节码分析,得到所有调用链,将方法带入调用链中遍历，得到推荐的接口
     * </p>
     * <p>注意事项：</p>
     * <p>1.静态分析有局限性，无法判断的场景：AOP、多态等需要动态运行才能知道的方法调用</p>
     * <p>2.目前只对本工程下的class文件进行分析，不包括第三方jar包</p>
     * <p>3.如果修改了基础方法，可能会追溯到大量接口</p>
     * <p>4.功能还在优化中，结果供参考</p>
     */
    @RequireHeader("userId")
    @PostMapping("/analysis")
    @Operation(summary = "代码分析", description =
            """
                    注意事项：
                    1.静态分析有局限性，无法判断的场景：AOP、多态等需要动态运行才能知道的方法调用
                    2.目前只对本工程下的class文件进行分析，不包括第三方jar包
                    3.如果修改了基础方法，可能会追溯到大量接口
                    4.功能还在优化中，结果供参考""")
    public ResponseDoMain analysis(HttpServletRequest request, @RequestBody CodeParamDTO param) {
        String userId = request.getHeader("userId");
        log.info("用户{}开始分析代码", userId);
        String taskId = IdUtil.simpleUUID();
        //加入定时任务map，会定时清理
        webSocketScheduled.setTask(taskId);
        ResultHandler.createQueue(taskId);
        if (!param.getGitPath().contains("http") || param.getGitPath().lastIndexOf(".git") == -1) {
            ResultHandler.endAdd(taskId, "git地址不合法，请填写http://xxxx/xxx.git地址");
            return ResponseDoMain.custom("Git地址不合法", false, "", 400);
        }
        CompletableFuture.runAsync(() -> {
            paramTrim(param);
            String masterBranch = param.getMasterBranch();
            String devBranch = param.getDevBranch();
            //如果branch为空，会使用HEAD指向的分支（默认分支）
            if (masterBranch.isEmpty()) {
                try {
                    masterBranch = JGitUtil.getHEADToBranch(param.getGitPath(), param.getUserName(), param.getPassWord());
                    param.setMasterBranch(masterBranch);
                    log.info("masterBranch is null,use HEAD branch:{}", masterBranch);
                    ResultHandler.add(taskId, "基准分支名称为空，使用默认分支:" + masterBranch);
                } catch (Exception e) {
                    ResultHandler.endAdd(taskId, "获取默认分支" + masterBranch + "失败,错误信息: " + e.getMessage());
                    log.error("获取HEAD指向的分支失败:{}", e.getMessage());
                    return;
                }
            }
            if (devBranch.isEmpty()) {
                try {
                    devBranch = JGitUtil.getHEADToBranch(param.getGitPath(), param.getUserName(), param.getPassWord());
                    param.setDevBranch(devBranch);
                    log.info("devBranch is null,use HEAD branch:{}", masterBranch);
                    ResultHandler.add(taskId, "对比分支名称为空，使用默认分支:" + devBranch);
                } catch (Exception e) {
                    ResultHandler.endAdd(taskId, "获取默认分支" + devBranch + "失败,错误信息: " + e.getMessage());
                    log.error("获取HEAD指向的分支失败:{}", e.getMessage());
                    return;
                }
            }
            //创建任务ID
            userInterfaceAnnotationTaskService.insert(param, userId, taskId);
            String finalBranch = masterBranch;
            try {
                ResultHandler.add(taskId, "正在下载代码...");
                //克隆项目
                List<RepositoryInfo> repositoryList = codeProcessService.codeDownload(param, directory);
                if (repositoryList == null || CollUtil.contains(repositoryList, "") || repositoryList.size() != 2) {
                    ResultHandler.endAdd(taskId, "代码克隆失败!");
                    return;
                }
                ResultHandler.add(taskId, "代码下载完成！");
                ResultHandler.add(taskId, "编译项目...");
                boolean isCompiled = codeProcessService.codeCompile(userId, repositoryList, param.getJdkVersion());
                if (!isCompiled) {
                    ResultHandler.endAdd(taskId, "编译项目失败!");
                    return;
                }
                ResultHandler.add(taskId, "项目编译完成！");
                ResultHandler.add(taskId, "对比分析中...");
                List<String> diff = codeProcessService.codeDiff(repositoryList.get(0).getDirectoryPath(), repositoryList.get(1).getDirectoryPath());
                if (diff.isEmpty()) {
                    ResultHandler.endAdd(taskId, "没有找到受影响的方法,分析结束!");
                    return;
                }
                ResultHandler.add(taskId, "受影响的方法：");
                diff.forEach(t -> ResultHandler.add(taskId, t));
                ResultHandler.add(taskId, "分析调用链...");
                List<List<String>> result = codeProcessService.codeProcess(repositoryList.get(1).getDirectoryPath(), diff);
                if (result.isEmpty()) {
                    ResultHandler.endAdd(taskId, "没有找到受影响调用链!");
                    return;
                }
//        TChain tChain = new TChain();
//        tChain.setProjectId("FS");
//        tChain.setSource("static");
//        tChain.setCreateTime(new Date());
//        tChain.setUpdateTime(new Date());
//        tChainService.delete(tChain);
//        //将静态分析的调用链入库
//        result.forEach(list->{
//            tChain.setInterfaceName(list.get(0));
//            tChain.setChain(list.toString());
//            tChainService.insert(tChain);
//        });

                //遍历数据库的调用链，对比每个调用链的方法，得到推荐的接口
//        List<TChain> chainList = tChainService.queryByProjectId("FS");
//        List<String> MethodList = new ArrayList<>();
//        diff.forEach(methodName -> {
//            chainList.forEach(tChain -> {
//                List messageList = JSON.parseObject(tChain.getChain(), List.class);
//                if (CollUtil.safeContains(messageList, methodName)) {
//                    MethodList.add(tChain.getInterfaceName());
//                }
//            });
//        });

                Map<String, List<String>> map = new HashMap<>();
                String subObj = param.getGitPath().substring(param.getGitPath().lastIndexOf("/") + 1, param.getGitPath().lastIndexOf(".git") == -1 ? param.getGitPath().length() : param.getGitPath().lastIndexOf(".git"));
                StringBuilder DingDingString = new StringBuilder("### 受影响的接口\n > [" + subObj + "]项目的[" + param.getDevBranch() + "]分支对比[" + finalBranch + "]分支");
                for (List<String> list : result) {
                    map.put(list.get(0), list.subList(1, 3));
                }
                interfaceAnnotationService.insert(map, taskId);
                ResultHandler.add(taskId, "建议回归的接口：");
                map.forEach((k, v) -> ResultHandler.add(taskId, k + "  " + v.get(0)));
                ResultHandler.add(taskId, "报告详情：" + paInterfaceDetailsList + taskId);
                //发送钉钉消息
                DingDingMessage(userId, map, DingDingString, taskId, taskId);
            } catch (Exception e) {
                log.error("代码分析失败:{}", e.getMessage());
            }
            ResultHandler.endAdd(taskId, "分析完成！");
        });
        return ResponseDoMain.custom("开始分析", true, taskId, 200);
    }

    /**
     * cicd专用接口
     */
    @PostMapping("/codeAnalysis")
    @Operation(summary = "代码分析", description =
            """
                    CICD专用接口！
                    注意事项：
                    1.静态分析有局限性，无法判断的场景：AOP、多态等需要动态运行才能知道的方法调用
                    2.目前只对本工程下的class文件进行分析，不包括第三方jar包
                    3.如果修改了基础方法，可能会追溯到大量接口
                    4.功能还在优化中，结果供参考""")
    public ExtendedResponseDoMain codeAnalysis(@RequestBody @Valid CodeParamDTO param) {
        //写死的用户ID
        String userId = "93";
        log.info("用户{}开始分析代码", userId);
        if (!param.getGitPath().contains("http") || param.getGitPath().lastIndexOf(".git") == -1) {
            log.info("Git地址不合法,请填写http://xxxx/xxx.git地址");
            return ExtendedResponseDoMain.failure("Git地址不合法,请填写http://xxxx/xxx.git地址", null);
        }
        if (StrUtil.isBlank(param.getCallbackUrl())) {
            log.info("回调地址不能为空");
            return ExtendedResponseDoMain.failure("回调地址不能为空", null);
        }
        String taskId = IdUtil.simpleUUID();
        CompletableFuture.runAsync(() -> {
            paramTrim(param);
            String masterBranch = param.getMasterBranch();
            String devBranch = param.getDevBranch();
            //如果branch为空，会使用HEAD指向的分支（默认分支）
            if (StrUtil.isBlank(masterBranch)) {
                try {
                    masterBranch = JGitUtil.getHEADToBranch(param.getGitPath(), param.getUserName(), param.getPassWord());
                    param.setMasterBranch(masterBranch);
                    log.info("masterBranch is null,use HEAD branch:{}", masterBranch);
                } catch (Exception e) {
                    log.error("获取HEAD指向的分支失败:{}", e.getMessage());
                    sendResponse(taskId, "[" + param.getMasterBranch() + "]分支获取HEAD指向的分支失败", false, null, param.getCallbackUrl());
                    return;
                }
            }
            if (StrUtil.isBlank(devBranch)) {
                try {
                    devBranch = JGitUtil.getHEADToBranch(param.getGitPath(), param.getUserName(), param.getPassWord());
                    param.setDevBranch(devBranch);
                    log.info("devBranch is null,use HEAD branch:{}", masterBranch);
                } catch (Exception e) {
                    log.error("获取HEAD指向的分支失败:{}", e.getMessage());
                    sendResponse(taskId, "[" + param.getDevBranch() + "]分支获取HEAD指向的分支失败", false, null, param.getCallbackUrl());
                    return;
                }
            }
            //创建任务ID
            userInterfaceAnnotationTaskService.insert(param, userId, taskId);
            try {
                //克隆项目
                List<RepositoryInfo> repositoryList = codeProcessService.codeDownload(param, directory);
                if (repositoryList == null || CollUtil.contains(repositoryList, "") || repositoryList.size() != 2) {
                    sendResponse(taskId, "代码克隆失败", false, null, param.getCallbackUrl());
                    return;
                }
                boolean isCompiled = codeProcessService.codeCompile(userId, repositoryList, param.getJdkVersion());
                if (!isCompiled) {
                    sendResponse(taskId, "编译项目失败，请检查项目完整性", false, null, param.getCallbackUrl());
                    return;
                }
                List<String> diff = codeProcessService.codeDiff(repositoryList.get(0).getDirectoryPath(), repositoryList.get(1).getDirectoryPath());
                if (diff.isEmpty()) {
                    sendResponse(taskId, "没有找到受影响的方法,分析结束!", false, null, param.getCallbackUrl());
                    return;
                }
                List<List<String>> result = codeProcessService.codeProcess(repositoryList.get(1).getDirectoryPath(), diff);
                if (result.isEmpty()) {
                    sendResponse(taskId, "没有找到受影响调用链,分析结束!", false, null, param.getCallbackUrl());
                    return;
                }
                Map<String, List<String>> map = new HashMap<>();
                for (List<String> list : result) {
                    map.put(list.get(0), list.subList(1, 3));
                }
                interfaceAnnotationService.insert(map, taskId);
            } catch (Exception e) {
                log.error("代码分析失败:{}", e.getMessage());
                sendResponse(taskId, "代码分析失败:" + e.getMessage(), false, null, param.getCallbackUrl());
                return;
            }
            List<TInterfaceAnnotationDTO> interfaceAnnotationDTOList = interfaceAnnotationService.queryById(taskId);
            sendResponse(taskId, "分析完成", true, interfaceAnnotationDTOList, param.getCallbackUrl());
        });
        return new ExtendedResponseDoMain("开始分析", true, null, 200, taskId);
    }

    private void sendResponse(String taskId, String message, boolean isSuccess, List<TInterfaceAnnotationDTO> interfaceAnnotationDTOList, String callbackUrl) {
        ExtendedResponseDoMain response = new ExtendedResponseDoMain(message, isSuccess, interfaceAnnotationDTOList, 200, taskId);
        try {
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(callbackUrl, response, String.class);
            log.info("任务:{},CICD回调结果:{}", taskId, responseEntity.getBody());
        } catch (Exception e) {
            log.error("任务:{},CICD回调失败:{}", taskId, e.getMessage());
        }
    }

    private void paramTrim(CodeParamDTO param) {
        param.setGitPath(StringUtils.trim(param.getGitPath()));
        param.setUserName(StringUtils.trim(param.getUserName()));
        param.setPassWord(StringUtils.trim(param.getPassWord()));
        param.setMasterBranch(StringUtils.trim(param.getMasterBranch()));
        param.setMasterBranchCommitId(StringUtils.trim(param.getMasterBranchCommitId()));
        param.setDevBranch(StringUtils.trim(param.getDevBranch()));
        param.setDevBranchCommitId(StringUtils.trim(param.getDevBranchCommitId()));
        param.setJdkVersion(StringUtils.trim(param.getJdkVersion()));
    }

    /**
     * 组装钉钉消息
     */
    public void DingDingMessage(String userId, Map<String, List<String>> map, StringBuilder DingDingString, String taskId, String webSocketTaskId) {
        boolean isDingDing = !StrUtil.isBlank(webSocketTaskId);
        List<String> DingDingUrl = new ArrayList<>();
        List<UserInform> userInFromList = this.userInformService.queryDingDingById(userId);
        userInFromList.forEach(TUserInFrom -> DingDingUrl.add(TUserInFrom.getObjectId()));
        if (!DingDingUrl.isEmpty()) {
            log.info("用户{}匹配到{}个钉钉消息接收人，开始发送钉钉消息...", userId, DingDingUrl.size());
            if (isDingDing)
                ResultHandler.add(webSocketTaskId, "匹配到" + DingDingUrl.size() + "个钉钉消息接收人，开始发送钉钉消息...");
            //遍历map，只添加10条
            int i = 0;
            for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                if (i < 10) {
                    DingDingString.append("\n - ").append(entry.getKey()).append("  ").append(entry.getValue().get(0));
                }
                i++;
                if (i == 10) {
                    DingDingString.append("\n - ...");
                    break;
                }
            }
            DingDingString.append("\n -  [查看详情](").append(paInterfaceDetailsList).append(taskId).append(")");
            if (isDingDing) {
                DingDingUrl.forEach(url -> ResultHandler.add(webSocketTaskId, DingDingUtil.postMarkdownMsg(url, "接口分析结果", DingDingString.toString())));
            }
        }
    }
}
