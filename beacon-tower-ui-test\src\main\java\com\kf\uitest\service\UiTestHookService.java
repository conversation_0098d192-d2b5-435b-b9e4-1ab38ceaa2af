package com.kf.uitest.service;

import com.kf.uitest.entity.UiTestHook;
import com.kf.uitest.enums.HookOwnerType;
import com.kf.uitest.enums.HookTiming;

import java.util.List;

public interface UiTestHookService {
    /**
     * 根据所属对象类型和ID获取所有钩子
     */
    List<UiTestHook> findByOwnerId(HookOwnerType ownerType, String ownerId);

    /**
     * 获取指定类型和时机的钩子
     */
    List<UiTestHook> findHooks(HookOwnerType ownerType, String ownerId, HookTiming timing);

    /**
     * 保存钩子
     */
    UiTestHook save(UiTestHook hook);

    /**
     * 批量保存钩子
     */
    List<UiTestHook> saveBatch(List<UiTestHook> hooks);

    /**
     * 删除钩子
     */
    void delete(Long id);

    /**
     * 批量删除钩子
     */
    void deleteBatch(List<Long> ids);

    
    /**
     * 根据所属对象删除所有钩子
     */
    void deleteByOwner(HookOwnerType ownerType, Long ownerId);
    
    /**
     * 更新钩子顺序
     */
    void updateOrder(List<UiTestHook> hooks);

    /**
     * 根据步骤ID查找所有钩子
     * @param stepId 步骤ID
     * @return 钩子列表
     */
    List<UiTestHook> findByStepId(String stepId);
}