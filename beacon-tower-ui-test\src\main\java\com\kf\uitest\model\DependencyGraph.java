package com.kf.uitest.model;

import com.kf.uitest.enums.NodeType;
import lombok.Getter;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Getter
public class DependencyGraph {
    // 前置依赖
    private final Map<DependencyNode, Set<DependencyNode>> preDeps = new HashMap<>();

    // 后置依赖
    private final Map<DependencyNode, Set<DependencyNode>> postDeps = new HashMap<>();

    // 节点映射（用于快速查找）
    private final Map<String, DependencyNode> nodeMap = new HashMap<>();

    /**
     * 添加节点
     */
    public void addNode(DependencyNode node) {
        nodeMap.put(node.getKey(), node);
    }

    /**
     * 根据类型和ID获取节点
     */
    public DependencyNode getNode(NodeType type, Long id) {
        return nodeMap.get(type.name() + ":" + id);
    }

    /**
     * 添加前置依赖
     */
    public void addPreDependency(DependencyNode source, DependencyNode target) {
        preDeps.computeIfAbsent(source, k -> new HashSet<>()).add(target);
    }

    /**
     * 添加后置依赖
     */
    public void addPostDependency(DependencyNode source, DependencyNode target) {
        postDeps.computeIfAbsent(source, k -> new HashSet<>()).add(target);
    }

    /**
     * 获取节点的前置依赖
     */
    public Set<DependencyNode> getPreDependencies(DependencyNode node) {
        return preDeps.getOrDefault(node, new HashSet<>());
    }

    /**
     * 获取节点的后置依赖
     */
    public Set<DependencyNode> getPostDependencies(DependencyNode node) {
        return postDeps.getOrDefault(node, new HashSet<>());
    }

    /**
     * 获取所有节点
     */
    public Collection<DependencyNode> getAllNodes() {
        return nodeMap.values();
    }

    /**
     * 检查是否存在循环依赖
     */
    public boolean hasCycle(boolean isPreDependency) {
        Set<DependencyNode> visited = new HashSet<>();
        Set<DependencyNode> recursionStack = new HashSet<>();
        Map<DependencyNode, Set<DependencyNode>> depsToCheck = isPreDependency ? preDeps : postDeps;

        for (DependencyNode node : getAllNodes()) {
            if (hasCycleUtil(node, visited, recursionStack, depsToCheck)) {
                return true;
            }
        }
        return false;
    }

    private boolean hasCycleUtil(DependencyNode node, Set<DependencyNode> visited,
                                 Set<DependencyNode> recursionStack, Map<DependencyNode, Set<DependencyNode>> deps) {
        if (recursionStack.contains(node)) {
            return true;
        }
        if (visited.contains(node)) {
            return false;
        }

        visited.add(node);
        recursionStack.add(node);

        Set<DependencyNode> dependencies = deps.getOrDefault(node, Collections.emptySet());
        for (DependencyNode dependency : dependencies) {
            if (hasCycleUtil(dependency, visited, recursionStack, deps)) {
                return true;
            }
        }

        recursionStack.remove(node);
        return false;
    }

    /**
     * 获取所有前置依赖关系
     */
    public Map<DependencyNode, Set<DependencyNode>> getAllPreDependencies() {
        return Collections.unmodifiableMap(preDeps);
    }

    /**
     * 获取所有后置依赖关系
     */
    public Map<DependencyNode, Set<DependencyNode>> getAllPostDependencies() {
        return Collections.unmodifiableMap(postDeps);
    }
}