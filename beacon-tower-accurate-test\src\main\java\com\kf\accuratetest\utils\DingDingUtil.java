package com.kf.accuratetest.utils;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

import static com.kf.accuratetest.enums.DingTalkMessageResultEnum.getMessage;

@Slf4j
public class DingDingUtil {
    static String url = "https://oapi.dingtalk.com/robot/send?access_token=7b7ef48a486371899e2f6242de60ac4f0df8f67060231c13d4bfb2720cdefd67";//闪亮阿聪的钉钉机器人

    public static void postMsg(String msg) {
        Map<String, String> map = new HashMap<>();
        map.put("msgtype", "text");
        map.put("text", "{'content':'" + msg + "'}");
        ResponseEntity<String> entity = new RestTemplate().postForEntity(url, map, String.class);
        //打印返回的结果
        System.out.println(entity.getBody());
    }

    /**
     * 发送markdown格式的消息
     */
    public static String postMarkdownMsg(String url, String title, String text) {
        Map<String, String> map = new HashMap<>();
        map.put("msgtype", "markdown");
        map.put("markdown", "{'title':'" + title + "','text':'" + text + "'}");
        ResponseEntity<String> entity = new RestTemplate().postForEntity(url, map, String.class);
        JSONObject jsonObject = JSONUtil.parseObj(entity.getBody());
        log.info("钉钉机器人返回结果：{}", jsonObject);
        String errCode = jsonObject.get("errcode").toString();
        if (errCode.equals("0")) {
            return "钉钉消息发送成功";
        }
        if (errCode.equals("310000")) {
            return "钉钉消息发送失败：" + jsonObject.get("errmsg").toString();
        }
        return "钉钉消息发送失败：" + getMessage(errCode);
    }

    public static void main(String[] args) {
//        postMarkdownMsg("测试接口"," ### 测试 \n - 测试啊 \n - 测试123 qwe来一段非常长的文字we来一段非常长的文字we来一段非常长的文字we来一段非常长的文字we来一段非常长的文字 \n - asdasdasdasd ceshi ");
        System.out.println(postMarkdownMsg(url, "123", " ### 测试 \n > 测试啊 \n - 测试123 qwe来一字 \n - asdasdasdasd ceshi "));
    }
}
