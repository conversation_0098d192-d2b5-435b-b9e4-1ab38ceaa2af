package com.kf.uitest.service;

import com.kf.uitest.entity.UiTestCase;

import java.util.List;
import java.util.Collection;
import java.util.Set;

public interface UiTestCaseService {
    /**
     * 根据ID获取测试用例
     */
    UiTestCase getById(String id);

    /**
     * 根据场景ID获取测试用例列表
     */
    List<UiTestCase> findBySuiteId(Long suiteId);

    /**
     * 保存测试用例
     */
    UiTestCase save(UiTestCase testCase);

    /**
     * 批量保存测试用例
     */
    List<UiTestCase> saveBatch(List<UiTestCase> testCases);

    /**
     * 删除测试用例
     */
    boolean removeById(Long id);

    /**
     * 更新测试用例
     */
    boolean updateById(UiTestCase testCase);

    /**
     * 获取测试用例总数
     */
    long count();

    /**
     * 根据ID列表批量获取测试用例
     *
     * @param ids 测试用例ID列表
     * @return 测试用例列表
     */
    List<UiTestCase> findByIds(List<String> ids);

    /**
     * 批量获取测试用例
     */
    List<UiTestCase> listByIds(Collection<String> ids);

    /**
     * 检查测试用例是否存在循环依赖
     * @param caseId 测试用例ID
     * @param visited 已访问的测试用例ID集合
     * @return 是否存在循环依赖
     */
    boolean isCircularDependency(String caseId, Set<String> visited);

    /**
     * 获取测试用例的前置依赖
     * @param caseId 测试用例ID
     * @return 前置依赖ID列表
     */
    List<String> getPreDependencies(String caseId);

    /**
     * 获取测试用例的后置依赖
     * @param caseId 测试用例ID
     * @return 后置依赖ID列表
     */
    List<String> getPostDependencies(String caseId);
}