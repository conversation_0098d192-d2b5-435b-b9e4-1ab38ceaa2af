{"name": "seigneur", "version": "1.0.0", "private": true, "scripts": {"dev": "vite --open", "build": "npm run build-only", "preview": "vite preview --open", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@tiptap/extension-image": "^2.9.1", "@tiptap/extension-link": "^2.9.1", "@tiptap/starter-kit": "^2.9.1", "@tiptap/vue-3": "^2.9.1", "@types/markdown-it": "^14.1.2", "axios": "^1.6.3", "echarts": "^5.4.3", "element-plus": "^2.8.4", "fuse.js": "^7.0.0", "highlight.js": "^11.9.0", "lodash": "^4.17.21", "markdown-it": "^14.0.0", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinyin": "^2.11.2", "screenfull": "^6.0.2", "sortablejs": "^1.15.6", "vue": "^3.4.3", "vue-router": "^4.2.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.6.1", "@tsconfig/node20": "^20.1.2", "@types/node": "^20.10.6", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-vue": "^5.0.2", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "npm-run-all2": "^6.1.1", "tailwindcss": "^3.4.0", "typescript": "~5.3.3", "vite": "^5.0.10", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^1.8.27"}, "browserslist": [">1%", "last 4 versions", "not ie < 9"]}