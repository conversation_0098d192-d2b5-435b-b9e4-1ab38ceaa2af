/**
 * 宝思服务类型定义（工具服务）
 */

import type { AxiosResponse } from 'axios'

// ==================== 宝思服务类型定义 ====================

export interface XMindToExcelFileList {
    fileName?: string
    createTime?: string
    isComplete?: string
    current: number
    size: number
}

export interface FileIdParam {
    fileId: string
}

export interface LoginJiraData {
    userName: string
    passWord: string
}

export interface CsvToJiraData {
    fileId: string
    jiraToken: string
    projectKey?: string
    fixVersion?: string[]
    testPlan?: string
    testCycle?: string
    mergeStepAndResult?: boolean
}

export interface JiraParams {
    userName?: string
    passWord?: string
    projectKey?: string
    planKey?: string
}

export interface ReloadTestRuns {
    testCaseIds: string[]
    keepTestResults: boolean
    keepStepResults: boolean
    keepDefects: boolean
}

export interface UpdateTestRunResultRequest {
    runId: string
    result: string
}

export interface UpdateTestStepResultRequest {
    runId: string
    runStepId: string
    result: string
}

export interface DeleteTestCaseRequest {
    testPlanKey: string
    testCycleId: string
    testCycleName: string
    removeTestCaseKeys: string[]
}

export interface JiraTestCaseRunStep {
    testCaseKey: string
    level: string
    summary: string
    precondition: string
    testPlanKey: string
    testCycleId: string
    testCaseId: string
    parentId: string
    parentStatus: string
    testRunBugsWrapper: Array<{
        id: string
        key: string
        summary: string
        assignee: string
        status: string
    }>
    testRunStepBugsWrapper: Array<{
        id: string
        key: string
        summary: string
        assignee: string
        status: string
    }>
    expectedResult: string
    stepData: string
    testRunAttachments: Array<{
        id: string
        mimeType: string
        fileName: string
        filePath: string
    }>
    actualResult: string
    step: string
    id: string
    status: string
    rowspan: number
    stepProperty: string
    mergeGroupId: string
    sortNumber?: number
}

export interface LinkTestRunStepBugsRequest {
    runId: string
    stepId: string
    issueKeys: string[]
}

export interface VerifyFileList {
    fileName?: string
    fileType?: string
    createTime?: string
    isComplete?: string
    current: number
    size: number
}

export interface FileTypeData {
    [key: string]: string
}
