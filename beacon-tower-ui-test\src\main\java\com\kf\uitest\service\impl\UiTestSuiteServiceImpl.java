package com.kf.uitest.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.kf.uitest.dao.UiTestSuiteMapper;
import com.kf.uitest.entity.UiTestSuite;
import com.kf.uitest.exception.EntityNotFoundException;
import com.kf.uitest.exception.TestPlanBuildException;
import com.kf.uitest.service.UiTestSuiteService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
public class UiTestSuiteServiceImpl implements UiTestSuiteService {

    @Resource
    private UiTestSuiteMapper suiteMapper;

    @Override
    public UiTestSuite getById(String id) {
        if (id == null) {
            throw new IllegalArgumentException("Suite id cannot be null");
        }
        UiTestSuite suite = suiteMapper.selectById(id);
        if (suite == null) {
            throw new EntityNotFoundException("Test suite not found with id: " + id);
        }
        return suite;
    }

    @Override
    public List<UiTestSuite> findAll() {
        return suiteMapper.selectList(null);
    }

    @Override
    @Transactional
    public UiTestSuite save(UiTestSuite suite) {
        if (suite == null) {
            throw new IllegalArgumentException("Suite cannot be null");
        }

        try {
            if (suite.getId() == null) {
                // 新增
                suite.setCreateTime(LocalDateTime.now());
                suite.setUpdateTime(LocalDateTime.now());
                suiteMapper.insert(suite);
            } else {
                // 更新
                UiTestSuite existingSuite = getById(suite.getId());
                suite.setCreateTime(existingSuite.getCreateTime());
                suite.setUpdateTime(LocalDateTime.now());
                suiteMapper.updateById(suite);
            }
            return suite;
        } catch (Exception e) {
            log.error("Failed to save test suite", e);
            throw new TestPlanBuildException("Failed to save test suite", e);
        }
    }

    @Override
    @Transactional
    public void delete(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("Suite id cannot be null");
        }

        try {
            suiteMapper.deleteById(id);
        } catch (Exception e) {
            log.error("Failed to delete test suite: {}", id, e);
            throw new TestPlanBuildException("Failed to delete test suite", e);
        }
    }

    @Override
    public List<UiTestSuite> findByStatus(String status) {
        if (StringUtils.isBlank(status)) {
            throw new IllegalArgumentException("Status cannot be empty");
        }

        QueryWrapper<UiTestSuite> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status);
        return suiteMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional
    public boolean updateStatus(String id, Integer status) {
        if (id == null) {
            throw new IllegalArgumentException("Suite id cannot be null");
        }
        if (status == null) {
            throw new IllegalArgumentException("Status cannot be null");
        }

        try {
            UiTestSuite suite = getById(id);
            suite.setStatus(status);
            suite.setUpdateTime(LocalDateTime.now());
            return suiteMapper.updateById(suite) > 0;
        } catch (Exception e) {
            log.error("Failed to update suite status: {}", id, e);
            throw new RuntimeException("Failed to update suite status", e);
        }
    }
}