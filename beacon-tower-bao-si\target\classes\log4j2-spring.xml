<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="30">
    <Properties>
        <Property name="LOG_HOME">logs</Property>
        <Property name="LOG_DATE_FORMAT">yyyy-MM-dd'T'HH:mm:ss.SSSXXX</Property>
        <Property name="LOG_PATTERN">
            %d{${LOG_DATE_FORMAT}} %highlight{%-5level} %style{%X{PID}}{#800080} --- [%t] %style{%logger{36}}{#ADD8E6} : %msg%n
        </Property>
    </Properties>

    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}"
                           charset="UTF-8">
                <PatternSelector defaultPattern="${LOG_PATTERN}">
                    <PatternMatch key="INFO" pattern="%d{${LOG_DATE_FORMAT}} %highlight{%-5level}{green} %style{%X{PID}}{#800080} --- [%t] %style{%logger{36}}{#ADD8E6} : %msg%n"/>
                    <PatternMatch key="DEBUG" pattern="%d{${LOG_DATE_FORMAT}} %highlight{%-5level}{cyan} %style{%X{PID}}{#800080} --- [%t] %style{%logger{36}}{#ADD8E6} : %msg%n"/>
                    <PatternMatch key="WARN" pattern="%d{${LOG_DATE_FORMAT}} %highlight{%-5level}{yellow} %style{%X{PID}}{#800080} --- [%t] %style{%logger{36}}{#ADD8E6} : %msg%n"/>
                    <PatternMatch key="ERROR" pattern="%d{${LOG_DATE_FORMAT}} %highlight{%-5level}{red} %style{%X{PID}}{#800080} --- [%t] %style{%logger{36}}{#ADD8E6} : %msg%n"/>
                    <PatternMatch key="FATAL" pattern="%d{${LOG_DATE_FORMAT}} %highlight{%-5level}{magenta} %style{%X{PID}}{#800080} --- [%t] %style{%logger{36}}{#ADD8E6} : %msg%n"/>
                </PatternSelector>
            </PatternLayout>
        </Console>

        <RollingFile name="RollingFile" fileName="${LOG_HOME}/app.log"
                     filePattern="${LOG_HOME}/app-%d{yyyy-MM-dd}.log.gz">
            <PatternLayout pattern="%d{${LOG_DATE_FORMAT}} %-5level %X{PID} --- [%t] %logger{36} : %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
        </RollingFile>
    </Appenders>

    <Loggers>
        <Logger name="org.springframework" level="INFO" additivity="false">
            <AppenderRef ref="RollingFile"/>
        </Logger>

        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
        </Root>
    </Loggers>
</Configuration>
