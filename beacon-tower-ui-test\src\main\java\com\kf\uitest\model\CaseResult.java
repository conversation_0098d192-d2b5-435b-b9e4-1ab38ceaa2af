package com.kf.uitest.model;

import com.kf.uitest.enums.StepStatus;
import com.kf.uitest.enums.TestStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CaseResult {
    private String caseId;
    private TestStatus status;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Long duration;
    private String errorMessage;
    private Integer retryCount;
    
    @Builder.Default
    private List<StepExecutionResult> stepResults = new ArrayList<>();
    
    public CaseResult(String caseId) {
        this.caseId = caseId;
        this.status = TestStatus.RUNNING;
        this.startTime = LocalDateTime.now();
        this.retryCount = 0;
    }
    
    public void addStepResult(StepExecutionResult stepResult) {
        this.stepResults.add(stepResult);
        updateStatus();
    }
    
    public void complete() {
        this.endTime = LocalDateTime.now();
        this.duration = ChronoUnit.MILLIS.between(startTime, endTime);
        updateStatus();
    }
    
    private void updateStatus() {
        if (stepResults.isEmpty()) {
            return;
        }

        boolean hasFailures = stepResults.stream()
            .anyMatch(result -> result.getStatus() == StepStatus.FAILED);
            
        this.status = hasFailures ? TestStatus.FAILED : TestStatus.SUCCESS;
    }
} 