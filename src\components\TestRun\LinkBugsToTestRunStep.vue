<template>
    <el-dialog
        v-model="localVisible"
        :title="`${testCaseKey} 关联缺陷`"
        width="30%"
        @close="handleClose"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
        <el-select
            v-model="selectedIssueKeys"
            multiple
            filterable
            remote
            clearable
            :loading="loading"
            :remote-method="handleInputChange"
            placeholder="请输入缺陷的IssueKey进行搜索"
            @remove-tag="handleRemove"
            @change="handleSelectChange"
            @clear="handleClear"
            :reserve-keyword="false"
            default-first-option
            no-data-text="无匹配数据"
            style="width: 100%;"
        >
            <!-- 下拉框中显示key和summary -->
            <el-option
                v-for="item in options"
                :key="item.key"
                :label="item.key"
                :value="item.key"
            >
                {{ item.key }} - {{ item.summary }}
            </el-option>
        </el-select>
        <template #footer>
            <el-button type="text" @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="confirmSelection">确定</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { getJiraBugsInfo, linkTestRunStepBugs } from '@/api/layout'
import { debounce } from 'lodash'

// 定义接收的 props
const props = defineProps<{
    jiraToken: string
    requirementKey: string
    runId: string
    stepId: string
    testCaseKey : string
    visible: boolean
}>()
const localVisible = ref(props.visible)

const emit = defineEmits<{
    (e: 'close'): void
    (e: 'save', payload: {
        requirementKey: string
        testCaseKey: string
        selectedIssues: BugInfo[]
    }): void
}>()

interface BugInfo {
    key: string
    summary: string
    status: string
    assignee: string
}

// 存储已选择的缺陷
const selectedIssueKeys = ref<string[]>([])
const bugInfoMap = ref<Record<string, BugInfo>>({})
// 存储选项
const options = ref<BugInfo[]>([])
const loading = ref(false)

// 处理输入框内容自动转大写
const handleInputChange = (query: string) => {
    const upperQuery = query.toUpperCase()  // 将输入内容转为大写
    remoteSearch(upperQuery)
}
// 远程搜索
const remoteSearch = debounce(async (query: string) => {
    if (query.trim() === '') {
        options.value = []
        return
    }
    loading.value = true
    try {
        const response = await getJiraBugsInfo(props.jiraToken, query.split(','))
        options.value = response.data.data.map((bug: BugInfo) => {
            const info = {
                key: bug.key,
                summary: bug.summary,
                status: bug.status,
                assignee: bug.assignee
            }
            bugInfoMap.value[bug.key] = info
            return info
        })
    } catch (error) {
        console.error('获取Jira缺陷信息失败:', error)
        options.value = []
    } finally {
        loading.value = false
    }
}, 300)

const selectedIssues = computed(() => selectedIssueKeys.value.map(key => bugInfoMap.value[key]))

// 处理删除标签
const handleRemove = (item: any) => {
    const index = selectedIssueKeys.value.indexOf(item)
    if (index !== -1) {
        selectedIssueKeys.value.splice(index, 1)
    }
}

// 处理清空
const handleClear = () => {
    selectedIssueKeys.value = []
}

// 确认选择
const confirmSelection = async () => {
    if (selectedIssueKeys.value.length === 0) {
        ElMessage.warning('至少要选择一个缺陷')
        return
    }

    try {
        const payload = {
            runId: props.runId,
            stepId: props.stepId,
            issueKeys: selectedIssueKeys.value
        }
        const response = await linkTestRunStepBugs(props.jiraToken, payload)
        if (!response.data.isSuccess) {
            ElMessage.error('关联失败，请重试')
            return
        }
        ElMessage.success('关联成功')
        emit('save', {
            requirementKey: props.requirementKey,
            testCaseKey: props.testCaseKey,
            selectedIssues: selectedIssues.value
        })
        console.log('selectedIssues:', selectedIssues.value)
        emit('close')
        // 清空选择
        selectedIssueKeys.value = []
        options.value = []
    } catch (error) {
        ElMessage.error('请求失败，请检查网络或重试:' + error)
    }
}

// 处理对话框关闭事件
const handleClose = () => {
    emit('close')
    selectedIssueKeys.value = []
}

// 每次选择后，输入框中只显示key
const handleSelectChange = () => {
    // 保证每次选择后下拉框不再显示，清空options
    options.value = []
}
// 处理取消
const handleCancel = () => {
    localVisible.value = false
}
</script>

<style scoped>

</style>
