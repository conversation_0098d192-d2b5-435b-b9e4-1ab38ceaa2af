package com.kf.baosi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kf.baosi.dto.XMindToExcelListDTO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

public interface XMindService {
    /**
     * 查询xmind转csv列表
     */
    IPage<XMindToExcelListDTO> getXMindList(String userId, String fileName, String isComplete, int current, int size);

    void xMindToExcelFileUpload(MultipartFile file, String userId);

    /**
     * 通过主键删除数据
     *
     * @param fileId 文件ID
     */
    void deleteFileByFileId(String fileId);

}
