package com.kf.uitest.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ui_test_case")
public class UiTestCase extends Model<UiTestCase> {

    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @TableField("suite_id")
    private String suiteId;

    @TableField("user_id")
    private Long userId;

    @TableField("case_name")
    private String caseName;

    @TableField("description")
    private String description;

    @TableField("browser_type")
    private String browserType = "chromium";  // 默认使用 chromium

    @TableField("resolution")
    private String resolution = "1920x1080";  // 默认分辨率

    @TableField("timeout")
    private Integer timeout = 30000;  // 默认超时时间 30 秒

    @TableField("priority")
    private Integer priority = 0;  // 优先级，默认为0，用于控制用例执行顺序

    @TableField("status")
    private Integer status = 1;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;
}
