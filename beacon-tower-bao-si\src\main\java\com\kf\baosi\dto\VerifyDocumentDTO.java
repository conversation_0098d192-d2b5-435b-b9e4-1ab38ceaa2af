package com.kf.baosi.dto;

import com.kf.baosi.validation.NotBlankArray;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@Data
public class VerifyDocumentDTO {

    // 测试计划id
    @Size(min = 1, message = "测试计划不能为空")
    private List<String> testPlanIssueKey;

    private List<String> cycles;

    // 验证文档类型
    @NotBlank(message = "文档类型不能为空")
    private String fileType;

    // 文档版本
    private String version = "1.0";

    // 项目+版本
    private String codeVersion = "【产品代码+产品版本】";

    // 审核者姓名
    private String auditor = "【审核人】";

    // 审批者 目前暂时写死
    private String approver = "季静贤";

    // 审核时间 默认为当前时间
    private String auditDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

    // 修改时间 默认为当前时间
    private String updateDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

    // 图片质量
    private int qualityLevel;

    // 动态预设字段
    private String scope = "【范围】";
    private String documentNumber = "【文件编号】";
    private String systemOverview = "【系统概述】";
    private String testMethods = "【测试方法】";
    private String screenshotScopeAndRules = "【截图范围与规则】";
    private String projectDescription = "【项目描述】";
    private String recommendations = "【建议】";

}
