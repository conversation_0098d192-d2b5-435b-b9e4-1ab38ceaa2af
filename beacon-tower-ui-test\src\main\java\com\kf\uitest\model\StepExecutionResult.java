package com.kf.uitest.model;

import com.kf.uitest.enums.StepStatus;
import com.kf.uitest.enums.TestStatus;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
public class StepExecutionResult {
    private String stepId;
    private StepStatus status;
    private String errorMessage;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private int iterationNumber;  // 在循环中的迭代次数
    private Map<String, Object> variables;
} 