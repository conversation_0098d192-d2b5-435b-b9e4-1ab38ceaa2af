<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/beacon-tower-accurate-test/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/beacon-tower-ai-test/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/beacon-tower-api-test/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/beacon-tower-bao-si/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/beacon-tower-flow-playback/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/beacon-tower-gateway/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/beacon-tower-mcp-clint/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/beacon-tower-mcp/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/beacon-tower-sdk/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/beacon-tower-sdk/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/beacon-tower-ui-test/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/beacon-tower-user-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
  </component>
</project>