package com.kf.baosi.enums;

import lombok.Getter;

@Getter
public enum JiraIssueFieldEnum {

    // 修复版本
    fixVersions("fixVersions", "修复版本"),
    // 用例优先级
    level("customfield_12851", "用例优先级"),
    // 前置条件
    precondition("customfield_12859", "前置条件"),
    // 经办人
    assignee("assignee", "经办人"),
    // 测试负责人
    tester("customfield_12383", "测试负责人"),
    // Issue状态
    status("status", "Issue状态"),
    // 模块
    components("components", "模块"),
    // 标题
    summary("summary", "标题"),
    // 类型
    issueType("issuetype", "类型");


    private final String field;
    private final String desc;

    JiraIssueFieldEnum(String field, String desc) {
        this.field = field;
        this.desc = desc;
    }

    public static JiraIssueFieldEnum getEnumByDesc(String desc) {
        for (JiraIssueFieldEnum value : JiraIssueFieldEnum.values()) {
            if (value.desc.equals(desc)) {
                return value;
            }
        }
        throw new IllegalArgumentException("未找到对应的类型");
    }
}
