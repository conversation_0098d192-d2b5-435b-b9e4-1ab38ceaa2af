package com.kf.baosi.controller;

import com.kf.baosi.common.RequireHeader;
import com.kf.baosi.common.ResponseDoMain;
import com.kf.baosi.dto.CreateBugRequest;
import com.kf.baosi.dto.CreateTestCaseRequest;
import com.kf.baosi.dto.LoginJiraDTO;
import com.kf.baosi.dto.ReloadTestRunsDTO;
import com.kf.baosi.dto.jiraTestRun.*;
import com.kf.baosi.dto.JiraSynapseRT.TestCycle;
import com.kf.baosi.entity.TUserToken;
import com.kf.baosi.service.JiraService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/jira")
public class JiraController {

    @Resource
    JiraService jiraService;

    // SSE接口发送进度
    @GetMapping("/progress/{taskId}")
    public SseEmitter handleSse(@PathVariable String taskId) {
        SseEmitter emitter = jiraService.getEmitter(taskId);
        if (emitter == null) {
            emitter = new SseEmitter(600000L);// 设置超时时间10分钟
            emitter.complete();
        }
        return emitter;
    }

    /**
     * 登录前校验是否需要登录
     */
    @GetMapping("/loginCheck")
    @RequireHeader("userId")
    public ResponseDoMain loginCheck(HttpServletRequest request, HttpServletResponse response) {
        TUserToken userToken = jiraService.getJiraUserToken(request.getHeader("userId"));
        boolean checkJiraToken = jiraService.checkJiraToken(userToken);
        if (checkJiraToken) {
            response.setHeader("jiraToken", userToken.getJiraToken());
            return ResponseDoMain.custom("不需要登录", true, userToken, 200);
        } else {
            return ResponseDoMain.custom("需要登录", false, null, 200);
        }
    }

    /**
     * jira登录成功后返回文件中的项目
     */
    @PostMapping("/login")
    @RequireHeader("userId")
    public ResponseDoMain loginJira(@RequestBody @Valid LoginJiraDTO loginJiraDTO, HttpServletRequest request, HttpServletResponse response) {
        String jiraToken = jiraService.login(loginJiraDTO, request.getHeader("userId"));
        //添加自定义表头 值是token
        response.setHeader("jiraToken", jiraToken);
        return ResponseDoMain.custom("登录成功", true, "", 200);
    }

    /**
     * 获取xMind的文件信息
     */
    @GetMapping("/getXMindFileInfo")
    public ResponseDoMain getXMindFileInfo(@RequestParam String fileId) {
        if (fileId == null || fileId.isEmpty()) {
            throw new RuntimeException("文件id不能为空");
        }
        Map<String, Object> xMindFileInfo = jiraService.getXMindFileInfo(fileId);
        return ResponseDoMain.custom("成功", true, xMindFileInfo, 200);
    }

//    @PostMapping("/customDataCheck")
//    public ResponseDoMain customDataCheck(@RequestBody @Valid CustomDataCheckDTO customDataCheckDTO) {
//        Map<String, Boolean> map = jiraSynapseRTService.customDataCheck(customDataCheckDTO);
//        return ResponseDoMain.custom("成功", true, map, 200);
//    }

    /**
     * 获取所有项目
     */
    @GetMapping("/getAllProject")
    public ResponseDoMain getAllProject(HttpServletRequest request) {
        return ResponseDoMain.custom("成功", true, jiraService.getAllProject(request.getHeader("jiraToken")), 200);
    }

    /**
     * 获取项目下所有的版本
     */
    @GetMapping("/getAllVersionForProject")
    public ResponseDoMain getAllVersionForProject(HttpServletRequest request, @RequestParam String projectKey) {
        return ResponseDoMain.custom("成功", true, jiraService.getAllVersionForProject(request.getHeader("jiraToken"), projectKey), 200);
    }

    /**
     * 获取项目下所有的测试计划
     */
    @GetMapping("/getAllPlanForProject")
    public ResponseDoMain getAllPlanForProject(HttpServletRequest request, @RequestParam String projectKey) {
        List<Map<String, String>> allPlanForProject = jiraService.getAllPlanForProject(request.getHeader("jiraToken"), projectKey);
        Map<String, String> allPlanForProjectMap = allPlanForProject.stream()
                .collect(Collectors.toMap(
                        plan -> plan.get("summary"),
                        plan -> plan.get("key"),
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new // 保持插入顺序
                ));
        return ResponseDoMain.custom("成功", true, allPlanForProjectMap, 200);
    }

    /**
     * 获取计划下所有的测试周期
     */
    @GetMapping("/getAllCycleForPlan")
    public ResponseDoMain getAllCycleForPlan(@RequestParam String planKey, HttpServletRequest request) {
        List<TestCycle> allCycleForPlan = jiraService.getTestPlanCycles(request.getHeader("jiraToken"), planKey);
        allCycleForPlan = allCycleForPlan.stream().distinct().toList();
        Map<String, String> allCycleForPlanMap = allCycleForPlan.stream().collect(Collectors.toMap(TestCycle::getName, TestCycle::getId));
        return ResponseDoMain.custom("成功", true, allCycleForPlanMap, 200);
    }

    /**
     * 重载测试周期下的测试运行
     * 可以保留测试运行结果状态、每一步的结果状态
     * 可以关联测试缺陷
     */
    @PostMapping("/reloadTestRuns")
    public ResponseDoMain reloadTestRuns(@RequestBody @Valid ReloadTestRunsDTO reloadTestRunsDTO, HttpServletRequest request) {
        boolean reloadTestRuns = jiraService.reloadTestRuns(reloadTestRunsDTO, request.getHeader("jiraToken"));
        return ResponseDoMain.custom("重新载入成功", reloadTestRuns, null, 200);
    }

    /**
     * 获取测试需求列表
     */
    @GetMapping("/getTestRequirementList")
    public ResponseDoMain getTestRequirementList(@RequestParam String planKey, @RequestParam String cycleId, HttpServletRequest request) {
        return ResponseDoMain.custom("成功", true, jiraService.getRequirement(request.getHeader("jiraToken"), planKey, cycleId), 200);
    }

    /**
     * 获取测试用例列表
     */
    @GetMapping("/getTestCaseList")
    public ResponseDoMain getTestCaseList(@RequestParam String planKey, @RequestParam String cycleId, @RequestParam String requirementKey, HttpServletRequest request) {
        return ResponseDoMain.custom("成功", true, jiraService.getFlattenedTestRunSteps(request.getHeader("jiraToken"), planKey, cycleId, requirementKey), 200);
    }

    /**
     * 更新测试运行总结果
     */
    @PostMapping("/updateTestRunStatus")
    public ResponseDoMain updateTestRunStatus(@RequestHeader("jiraToken") String jiraToken, @RequestBody UpdateTestRunResultRequest request) {
        return ResponseDoMain.custom("运行结果更新成功", true, jiraService.updateTestRunResult(jiraToken, request), 200);
    }

    /**
     * 更新测试步骤结果
     */
    @PostMapping("/updateTestStepStatus")
    public ResponseDoMain updateTestStepStatus(@RequestHeader("jiraToken") String jiraToken, @RequestBody UpdateTestStepResultRequest request) {
        return ResponseDoMain.custom("步骤结果更新成功", true, jiraService.updateTestStepResult(jiraToken, request), 200);
    }

    /**
     * 删除测试用例
     */
    @DeleteMapping("/deleteTestCase")
    public ResponseDoMain deleteTestCase(@RequestHeader("jiraToken") String jiraToken, @RequestBody DeleteTestCaseRequest request) {
        jiraService.deleteTestCase(jiraToken, request);
        return ResponseDoMain.custom("删除成功", true, null, 200);
    }

    /**
     * 删除测试附件
     */
    @DeleteMapping("/deleteRunAttachment")
    public ResponseDoMain deleteAttachment(@RequestHeader("jiraToken") String jiraToken, @RequestParam String runId, @RequestParam String attachmentId) {
        jiraService.deleteTestRunAttachment(jiraToken, runId, attachmentId);
        return ResponseDoMain.custom("删除成功", true, null, 200);
    }

    /**
     * 下载测试附件
     */
    @GetMapping("/downloadRunAttachment")
    public void downloadAttachment(@RequestHeader("jiraToken") String jiraToken, @RequestParam String jiraUrl, HttpServletResponse response) {
        jiraService.downloadTestRunAttachment(jiraToken, jiraUrl, response);
    }

    /**
     * 更新测试步骤
     */
    @PostMapping("/updateTestStep")
    public ResponseDoMain updateTestStep(@RequestHeader("jiraToken") String jiraToken, @RequestBody List<JiraTestCaseRunStepDTO> steps) {
        return ResponseDoMain.custom("步骤更新成功", true, jiraService.updateTestStep(jiraToken, steps), 200);
    }

    /**
     * 获取测试运行的附件信息
     */
    @GetMapping("/getTestRunAttachments")
    public ResponseDoMain getTestRunAttachments(@RequestHeader("jiraToken") String jiraToken, @RequestParam String testRunId) {
        return ResponseDoMain.custom("成功", true, jiraService.getTestRunAttachments(jiraToken, testRunId), 200);
    }

    /**
     * 创建测试用例
     */
    @PostMapping("/createTestCase")
    public ResponseDoMain createTestCase(@RequestHeader("jiraToken") String jiraToken, @RequestBody @Valid CreateTestCaseRequest request) {
        jiraService.createTestCase(jiraToken, request);
        return ResponseDoMain.custom("创建成功", true,"" , 200);
    }

    /**
     * 创建缺陷
     */
    @PostMapping("/createBug")
    public ResponseDoMain createBug(@RequestHeader("jiraToken") String jiraToken, @RequestBody @Valid CreateBugRequest request) {
        return ResponseDoMain.custom("创建成功", true, jiraService.createBug(jiraToken, request), 200);
    }

    /**
     * 获取项目中所有的测试用例集
     */
    @GetMapping("/getAllTestSuiteForProject")
    public ResponseDoMain getAllTestSuiteForProject(@RequestHeader("jiraToken") String jiraToken, @RequestParam String projectKey) {
        return ResponseDoMain.custom("成功", true, jiraService.getAllTestSuiteForProject(jiraToken, projectKey), 200);
    }

    /**
     * 获取测试计划的版本信息
     */
    @GetMapping("/getTestPlanFixVersion")
    public ResponseDoMain getTestPlanFixVersion(@RequestHeader("jiraToken") String jiraToken, @RequestParam String planKey) {
        return ResponseDoMain.custom("成功", true, jiraService.getTestPlanVersions(jiraToken, planKey), 200);
    }

    /**
     * 根据jiraToken获取用户信息
     */
    @GetMapping("/getJiraUserInfo")
    public ResponseDoMain getUserInfo(@RequestHeader("jiraToken") String jiraToken) {
        return ResponseDoMain.custom("成功", true, jiraService.getJiraUserInfo(jiraToken), 200);
    }

    /**
     * 获取缺陷详情
     */
    @PostMapping("/getJiraBugsInfo")
    public ResponseDoMain getJiraBugs(@RequestHeader("jiraToken") String jiraToken, @RequestBody List<String> bugKeys) {
        log.info("获取jira缺陷详情:" + bugKeys);
        return ResponseDoMain.custom("成功", true, jiraService.getJiraBugsInfo(jiraToken, bugKeys), 200);
    }
    /**
     * 获取缺陷详情
     */
    @GetMapping("/getTestRunBugsInfo")
    public ResponseDoMain getTestRunBugs(@RequestHeader("jiraToken") String jiraToken, @RequestParam String testRunId) {
        log.info("获取测试运行：{}的缺陷详情", testRunId);
        return ResponseDoMain.custom("成功", true, jiraService.getTestRunBugsInfo(jiraToken, testRunId), 200);
    }

    /**
     * 将缺陷链接到运行步骤
     */
    @PostMapping("/linkTestRunStepBugs")
    public ResponseDoMain linkTestRunStepBugs(@RequestHeader("jiraToken") String jiraToken, @RequestBody LinkTestRunStepBugsRequest request) {
        jiraService.linkTestRunStepBugs(jiraToken, request);
        return ResponseDoMain.custom("成功", true, null, 200);
    }

    /**
     * 获取Issue的标题
     */
    @GetMapping("/getIssueSummary")
    public ResponseDoMain getIssueSummary(@RequestHeader("jiraToken") String jiraToken, @RequestParam String issueKey) {
        return ResponseDoMain.custom("成功", true, jiraService.getIssueSummary(jiraToken, issueKey), 200);
    }
    /**
     * 获取Issue的标题
     */
    @GetMapping("/getTestPlanSummary")
    public ResponseDoMain getTestPlanSummary(@RequestHeader("jiraToken") String jiraToken, @RequestParam String issueKey) {
        return ResponseDoMain.custom("成功", true, jiraService.getTestPlanSummary(jiraToken, issueKey), 200);
    }

    /**
     * 获取项目的所有模块
     */
    @GetMapping("/getComponentsForProject")
    public ResponseDoMain getComponentsForProject(@RequestHeader("jiraToken") String jiraToken, @RequestParam String projectKey) {
        return ResponseDoMain.custom("成功", true, jiraService.getComponentsForProject(jiraToken, projectKey), 200);
    }

    /**
     * 查询jira的所有用户
     */
    @GetMapping("/getAllUsers")
    public ResponseDoMain getAllUsers(@RequestHeader("jiraToken") String jiraToken) {
        log.info("jiraToken:{} 查询jira的所有用户", jiraToken);
        return ResponseDoMain.custom("成功", true, jiraService.getAllJiraUsers(jiraToken), 200);
    }

    /**
     * 上传测试运行的附件
     */
    @PostMapping("/uploadTestRunAttachment")
    public ResponseDoMain uploadTestRunAttachment(@RequestHeader("jiraToken") String jiraToken, @RequestParam String runId, @RequestParam("file") MultipartFile file) {
        jiraService.uploadTestRunAttachment(jiraToken, runId, file);
        return ResponseDoMain.custom("成功", true, null, 200);
    }

    /**
     * 删除测试缺陷
     */
    @DeleteMapping("/deleteBug")
    public ResponseDoMain deleteBug(@RequestHeader("jiraToken") String jiraToken, @RequestParam String runId, @RequestParam String issueKey) {
        log.info("删除缺陷：runId:{}, issueKey:{}", runId, issueKey);
        return ResponseDoMain.custom("成功", true, jiraService.deleteBug(jiraToken, runId, issueKey), 200);
    }

}
