package com.kf.accuratetest.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

@Data
public class InterfacePageBatchImportDTO {
    /**
     * 页面路径
     */
    @Schema(description = "页面路径")
    String pageUrl;
    /**
     * 接口名称
     */
    @Schema(description = "接口名称")
    String interfaceName;
    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    String projectName;
    /**
     * 消息
     */
    String msg;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        InterfacePageBatchImportDTO that = (InterfacePageBatchImportDTO) o;
        return Objects.equals(pageUrl, that.pageUrl) &&
                Objects.equals(interfaceName, that.interfaceName) &&
                Objects.equals(projectName, that.projectName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(pageUrl, interfaceName, projectName);
    }
}
