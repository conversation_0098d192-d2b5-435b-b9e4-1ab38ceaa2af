package com.kf.aitest.service;

import java.util.List;

/**
 * 数据分片处理服务接口
 */
public interface DataChunkService {
    
    /**
     * 数据分片结果
     */
    class ChunkResult {
        private final List<DataChunk> chunks;
        private final boolean needsChunking;
        
        public ChunkResult(List<DataChunk> chunks, boolean needsChunking) {
            this.chunks = chunks;
            this.needsChunking = needsChunking;
        }
        
        public List<DataChunk> getChunks() { return chunks; }
        public boolean isNeedsChunking() { return needsChunking; }
    }
    
    /**
     * 单个数据分片
     */
    class DataChunk {
        private final Object uatChunk;
        private final Object testChunk;
        private final int chunkIndex;
        private final int totalChunks;
        private final String chunkDescription;
        
        public DataChunk(Object uatChunk, Object testChunk, int chunkIndex, int totalChunks, String chunkDescription) {
            this.uatChunk = uatChunk;
            this.testChunk = testChunk;
            this.chunkIndex = chunkIndex;
            this.totalChunks = totalChunks;
            this.chunkDescription = chunkDescription;
        }
        
        public Object getUatChunk() { return uatChunk; }
        public Object getTestChunk() { return testChunk; }
        public int getChunkIndex() { return chunkIndex; }
        public int getTotalChunks() { return totalChunks; }
        public String getChunkDescription() { return chunkDescription; }
    }
    
    /**
     * 分片评估结果
     */
    class ChunkEvaluationResult {
        private final String evaluation;
        private final Integer score;
        private final int chunkIndex;
        private final String chunkDescription;
        
        public ChunkEvaluationResult(String evaluation, Integer score, int chunkIndex, String chunkDescription) {
            this.evaluation = evaluation;
            this.score = score;
            this.chunkIndex = chunkIndex;
            this.chunkDescription = chunkDescription;
        }
        
        public String getEvaluation() { return evaluation; }
        public Integer getScore() { return score; }
        public int getChunkIndex() { return chunkIndex; }
        public String getChunkDescription() { return chunkDescription; }
    }
    
    /**
     * 检查数据是否需要分片处理
     * 
     * @param uatData UAT数据
     * @param testData TEST数据
     * @param maxTokens 最大token限制
     * @return 是否需要分片
     */
    boolean needsChunking(Object uatData, Object testData, int maxTokens);
    
    /**
     * 将数据分片处理
     * 
     * @param uatData UAT数据
     * @param testData TEST数据
     * @param maxTokens 每个分片的最大token数
     * @return 分片结果
     */
    ChunkResult chunkData(Object uatData, Object testData, int maxTokens);
    
    /**
     * 合并分片评估结果
     * 
     * @param chunkResults 各分片的评估结果
     * @param stageName 阶段名称
     * @return 合并后的评估结果和综合评分
     */
    String mergeChunkResults(List<ChunkEvaluationResult> chunkResults, String stageName);
    
    /**
     * 计算综合评分
     * 
     * @param chunkResults 各分片的评估结果
     * @return 综合评分
     */
    Integer calculateOverallScore(List<ChunkEvaluationResult> chunkResults);
}
