# Sample user.properties file
#
##   Licensed to the Apache Software Foundation (ASF) under one or more
##   contributor license agreements.  See the NOTICE file distributed with
##   this work for additional information regarding copyright ownership.
##   The ASF licenses this file to You under the Apache License, Version 2.0
##   (the "License"); you may not use this file except in compliance with
##   the License.  You may obtain a copy of the License at
##
##       http://www.apache.org/licenses/LICENSE-2.0
##
##   Unless required by applicable law or agreed to in writing, software
##   distributed under the License is distributed on an "AS IS" BASIS,
##   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
##   See the License for the specific language governing permissions and
##   limitations under the License.

#---------------------------------------------------------------------------
# Classpath configuration
#---------------------------------------------------------------------------
#
# List of paths (separated by ;) to search for additional JMeter plugin classes,
# for example new GUI elements and samplers.
# A path item can either be a jar file or a directory.
# Any jar file in such a directory will be automatically included,
# jar files in sub directories are ignored.
# The given value is in addition to any jars found in the lib/ext directory.
# Do not use this for utility or plugin dependency jars.
#search_paths=/app1/lib;/app2/lib

# List of paths that JMeter will search for utility and plugin dependency classes.
# Use your platform path separator (java.io.File.pathSeparatorChar in Java) to separate multiple paths.
# A path item can either be a jar file or a directory.
# Any jar file in such a directory will be automatically included,
# jar files in sub directories are ignored.
# The given value is in addition to any jars found in the lib directory.
# All entries will be added to the class path of the system class loader
# and also to the path of the JMeter internal loader.
# Paths with spaces may cause problems for the JVM
#Example for windows (; separator)
#user.classpath=../classes;../lib;../app1/jar1.jar;../app2/jar2.jar
#Example for linux (:separator)
#user.classpath=../classes:../lib:../app1/jar1.jar:../app2/jar2.jar

# List of paths (separated by ;) that JMeter will search for utility
# and plugin dependency classes.
# A path item can either be a jar file or a directory.
# Any jar file in such a directory will be automatically included,
# jar files in sub directories are ignored.
# The given value is in addition to any jars found in the lib directory
# or given by the user.classpath property.
# All entries will be added to the path of the JMeter internal loader only.
# For plugin dependencies using plugin_dependency_paths should be preferred over
# user.classpath.
#plugin_dependency_paths=../dependencies/lib;../app1/jar1.jar;../app2/jar2.jar

#---------------------------------------------------------------------------
# Reporting configuration
#---------------------------------------------------------------------------

# Configure this property to change the report title
#jmeter.reportgenerator.report_title=Apache JMeter Dashboard

# Used to generate a report based on a date range if needed
# Default date format (from SimpleDateFormat Java API and Locale.ENGLISH)
#jmeter.reportgenerator.date_format=yyyyMMddHHmmss
# Date range start date using date_format property
#jmeter.reportgenerator.start_date=
# Date range end date using date_format property
#jmeter.reportgenerator.end_date=

# Change this parameter if you want to change the granularity of over time graphs.
#jmeter.reportgenerator.overall_granularity=60000

# Change this parameter if you want to change the granularity of Response time distribution
# Set to 100 ms by default
#jmeter.reportgenerator.graph.responseTimeDistribution.property.set_granularity=100

# Change this parameter if you want to keep only some samples.
# Regular Expression which Indicates which samples to keep for graphs and statistics generation.
# Empty value means no filtering
#jmeter.reportgenerator.sample_filter=

# Change this parameter if you want to override the APDEX satisfaction threshold.
#jmeter.reportgenerator.apdex_satisfied_threshold=500

# Change this parameter if you want to override the APDEX tolerance threshold.
#jmeter.reportgenerator.apdex_tolerated_threshold=1500

# Indicates which graph series are filtered (regular expression)
# In the below example we filter on Search and Order samples
# Note that the end of the pattern should always include (-success|-failure)?$
# TransactionsPerSecondGraphConsumer suffixes transactions with "-success" or "-failure" depending
# on the result
#jmeter.reportgenerator.exporter.html.series_filter=^(Search|Order)(-success|-failure)?$

# Indicates whether only controller samples are displayed on graphs that support it.
#jmeter.reportgenerator.exporter.html.show_controllers_only=false

# This property is used by menu item "Export transactions for report"
# It is used to select which transactions by default will be exported
#jmeter.reportgenerator.exported_transactions_pattern=[a-zA-Z0-9_\\-{}\\$\\.]*[-_][0-9]*


## Custom graph definition
#jmeter.reportgenerator.graph.custom_mm_hit.classname=org.apache.jmeter.report.processor.graph.impl.CustomGraphConsumer
#jmeter.reportgenerator.graph.custom_mm_hit.title=Graph Title
#jmeter.reportgenerator.graph.custom_mm_hit.property.set_Y_Axis=Response Time (ms)
#jmeter.reportgenerator.graph.custom_mm_hit.property.set_X_Axis=Over Time
#jmeter.reportgenerator.graph.custom_mm_hit.property.set_granularity=${jmeter.reportgenerator.overall_granularity}
#jmeter.reportgenerator.graph.custom_mm_hit.property.setSampleVariableName=VarName
#jmeter.reportgenerator.graph.custom_mm_hit.property.setContentMessage=Message for graph point label

########################################################################
################## DISTRIBUTED TESTING CONFIGURATION  ##################
########################################################################
# Type of keystore : JKS
#
#server.rmi.ssl.keystore.type=JKS
#
# Keystore file that contains private key
#
#server.rmi.ssl.keystore.file=rmi_keystore.jks
#
# Password of Keystore
#
#server.rmi.ssl.keystore.password=changeit
#
# Key alias
#
#server.rmi.ssl.keystore.alias=rmi
#
# Type of truststore : JKS
#
#server.rmi.ssl.truststore.type=JKS
#
# Keystore file that contains certificate
#
#server.rmi.ssl.truststore.file=rmi_keystore.jks
#
# Password of Trust store
#
#server.rmi.ssl.truststore.password=changeit
#
# Set this if you don't want to use SSL for RMI
#
#server.rmi.ssl.disable=false