package com.kf.baosi.configuration;

import com.kf.baosi.common.RabbitMQProperties;
import jakarta.annotation.PostConstruct;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class RabbitMQConfig {

    @Autowired
    private RabbitMQProperties rabbitMQProperties;

    private final Map<String, CachingConnectionFactory> connectionFactoryMap = new HashMap<>();

    @PostConstruct
    public void init() {
        for (RabbitMQProperties.Connection connection : rabbitMQProperties.getConnections()) {
            CachingConnectionFactory factory = new CachingConnectionFactory();
            factory.setHost(connection.getHost());
            factory.setPort(connection.getPort());
            factory.setUsername(connection.getUsername());
            factory.setPassword(connection.getPassword());
            factory.setVirtualHost(connection.getVirtualHost());
            connectionFactoryMap.put(connection.getName(), factory);
        }
    }

    public CachingConnectionFactory getConnectionFactory(String name) {
        return connectionFactoryMap.get(name);
    }

    @Bean(name = "testMq001RabbitTemplate")
    @Primary
    public RabbitTemplate testMq001RabbitTemplate() {
        return new RabbitTemplate(getConnectionFactory("test-mq-001"));
    }

    @Bean(name = "testMq001Factory")
    public SimpleRabbitListenerContainerFactory testMq001Factory() {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(getConnectionFactory("test-mq-001"));
        return factory;
    }
}
