package com.kf.accuratetest.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.kf.accuratetest.entity.Chain;
import com.kf.accuratetest.service.ChainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class ReceiveHandler {
    @Resource
    private ChainService chainService;

    @RabbitListener(queues = "${spring.rabbitmq.queue}")
    public void sendMessage(String message, Message msg) {
        try {
            log.info("接收到调用链消息:{}", message);
            List messageList = JSON.parseObject(message, List.class);
            String interfaceName = ((List) messageList.get(0)).get(0).toString();
            String[] messageStr = StrUtil.splitToArray(msg.getMessageProperties().getMessageId(), "-");
            String messageId = messageStr[0];
            String packageName = messageStr[1];

            Chain chain = new Chain();
            chain.setMessageId(messageId);
            chain.setProjectId(packageName);
            chain.setInterfaceName(interfaceName);
            chain.setSource("active");
            chain.setCreateTime(new Date());
            chain.setUpdateTime(new Date());

            List<Chain> chainList = chainService.queryByMessageId(messageId);

            //如果没有找到说明宿主服务器重启了，这里简单的认为有发布动作，需要删掉之前的调用链重新入库
            if (CollUtil.isEmpty(chainList)) {
                chainService.delete(chain);
            }

            List<String> list1 = new ArrayList<>();
            chainList.forEach(t -> list1.add(t.getChain()));
            List<String> list2 = new ArrayList<>();
            for (Object o : messageList) {
                list2.add(o.toString());
            }

            //将差集存入数据库
            for (Object o : CollUtil.subtract(list2, list1)) {
                chain.setChain(o.toString());
                chainService.insert(chain);
            }
        } catch (Exception e) {
            log.error("处理调用链消息异常:{}", e.getMessage());
        }
    }
}
