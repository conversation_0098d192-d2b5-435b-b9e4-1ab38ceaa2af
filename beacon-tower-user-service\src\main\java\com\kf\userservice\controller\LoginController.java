package com.kf.userservice.controller;

import com.kf.userservice.common.ResponseDoMain;
import com.kf.userservice.dto.LoginUserDTO;
import com.kf.userservice.entity.TUser;
import com.kf.userservice.service.UserService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

import static com.kf.userservice.utils.MD5Util.string2MD5;

@Slf4j
@RestController
public class LoginController {
    @Resource
    private UserService userService;

    @PostMapping("/login")
    public ResponseDoMain login(HttpServletRequest request, @RequestBody @Valid LoginUserDTO loginUserDTO) {
        TUser tUser = userService.queryByUserName(loginUserDTO.getUsername());
        if (tUser == null || !tUser.getPassword().equals(string2MD5(loginUserDTO.getPassword()))) {
            return ResponseDoMain.failure("账号或密码错误");
        }
        HttpSession session = request.getSession();
        log.info("用户登录，session id: {}", session.getId());
        String token = session.getId();
        session.setAttribute("userId", tUser.getUserId());
        session.setAttribute("token", token);
        session.setAttribute("userName", tUser.getUsername());
        session.setMaxInactiveInterval(60 * 60 * 24 * 30);
        Map<String, Object> map = new HashMap<>(4);
        map.put("token", token);
        map.put("userId", tUser.getUserId());
        map.put("userName", tUser.getUsername());
        return ResponseDoMain.success(map);
    }
}
