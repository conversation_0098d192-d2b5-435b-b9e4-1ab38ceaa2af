package com.kf.accuratetest.service.impl;

import com.kf.accuratetest.dao.UserInformDao;
import com.kf.accuratetest.dto.InFromDTO;
import com.kf.accuratetest.entity.UserInform;
import com.kf.accuratetest.service.UserInformService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class UserInformServiceImpl implements UserInformService {
    @Resource
    private UserInformDao userInformDao;

    /**
     * 通过用户ID查询多条钉钉机器人配置数据
     *
     * @return 实例对象list
     */
    @Override
    public List<UserInform> queryDingDingById(String userId) {
        UserInform userInform = new UserInform();
        userInform.setUserId(userId);
        userInform.setType(1);
        userInform.setStatus(0);
        userInform.setIsDeleted(0);
        return this.userInformDao.queryAllByLimit(userInform);
    }

    /**
     * 通过ID多条钉钉机器人配置数据
     *
     * @return 实例对象list
     */
    @Override
    public List<UserInform> queryAllByLimit(String userId) {
        UserInform userInform = new UserInform();
        userInform.setUserId(userId);
        userInform.setType(1);
        userInform.setIsDeleted(0);
        return this.userInformDao.queryAllByLimit(userInform);
    }

    /**
     * 新增数据
     *
     * @param fromList 实例对象list
     * @return 实例对象
     */
    @Override
    public int insert(List<InFromDTO> fromList, String userId) {
        List<UserInform> userInFromListOld = queryAllByLimit(userId);
        userInFromListOld.forEach(tUserInfrom -> {
            tUserInfrom.setIsDeleted(1);
            tUserInfrom.setUpdateTime(new Date());
            userInformDao.update(tUserInfrom);
        });
        List<UserInform> UserInFromList = new ArrayList<>();
        fromList.forEach(inFromDTO -> {
            UserInform userInform = new UserInform();
            userInform.setUserId(userId);
            userInform.setGitUrl(inFromDTO.getGitPath());
            userInform.setType(1);
            userInform.setObjectId(inFromDTO.getWebHook());
            userInform.setCreateTime(new Date());
            userInform.setUpdateTime(new Date());
            userInform.setStatus(inFromDTO.getStatus() ? 0 : 1);
            userInform.setIsDeleted(0);
            UserInFromList.add(userInform);
        });
        return this.userInformDao.insertBatch(UserInFromList);
    }

}
