package com.kf.accuratetest.service.impl;

import com.kf.accuratetest.common.ResultHandler;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 定时删除ResultHandler的队列
 */
@Service
public class WebSocketScheduled {
    public static final Map<String, Date> taskMap = new HashMap<>();

    public void setTask(String taskId) {
        taskMap.put(taskId, new Date());
    }

    @Scheduled(fixedRate = 3600000) // 1小时，单位为毫秒
    public void cleanupQueue() {
        //循环遍历map，如果超过1小时，就清除
        for (Map.Entry<String, Date> entry : taskMap.entrySet()) {
            if (new Date().getTime() - entry.getValue().getTime() > 7200000) {
                taskMap.remove(entry.getKey());
                ResultHandler.clear(entry.getKey());
            }
        }
    }

}
