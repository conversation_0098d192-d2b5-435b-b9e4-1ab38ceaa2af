package com.kf.baosi.common;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Configuration
@ConfigurationProperties(prefix = "rabbit")
public class RabbitMQProperties {
    private List<Connection> connections;

    @Data
    public static class Connection {
        private String name;
        private String host;
        private int port;
        private String username;
        private String password;
        private String virtualHost;
    }

}
