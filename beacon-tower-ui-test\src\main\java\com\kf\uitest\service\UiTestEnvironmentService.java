package com.kf.uitest.service;

import com.kf.uitest.dto.UiTestEnvironmentDTO;
import com.kf.uitest.dto.UiTestEnvironmentCreateRequest;
import com.kf.uitest.dto.UiTestEnvironmentWithVariablesDTO;
import com.kf.uitest.dto.UiTestEnvironmentUpdateRequest;

import java.util.List;
import java.util.Map;

public interface UiTestEnvironmentService {

    /**
     * 创建环境
     */
    UiTestEnvironmentWithVariablesDTO WithVariables(Long userId, UiTestEnvironmentCreateRequest request);

    /**
     * 获取用户所有环境及其环境变量
     * @param userId 用户ID
     * @param projectId 项目ID
     * @return 环境及其变量信息列表
     */
    List<UiTestEnvironmentWithVariablesDTO> getAllEnvironmentsWithVariables(Long userId, Long projectId);

    /**
     * 更新环境和变量
     */
    void updateWithVariables(UiTestEnvironmentUpdateRequest request);
} 