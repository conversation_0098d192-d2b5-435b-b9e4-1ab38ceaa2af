<template>
    <div>
        <div ref='searchEl' class='table-search-form'>
            <el-row :gutter='15' class='clear-both'>
                <el-col :span='24'>
                    <card-list :show-header='true' title='搜索' type='keyvalue'>
                        <template #btn>
                            <el-button-group>
                                <el-button v-prevent-default icon='el-icon-search' size='small' @click='selectData'>
                                    搜索
                                </el-button>
                            </el-button-group>
                        </template>
                        <template #keyvalue>
                            <el-form ref='refForm' :model='form' class='card-list-form' size='small'>
                                <el-row :gutter='15'>
                                    <card-list-item prop='objInfo' width='100px'>
                                        <template #key>文档名称</template>
                                        <template #value>
                                            <el-input v-model='form.fileName' placeholder='请输入文档名称'/>
                                        </template>
                                    </card-list-item>
                                    <card-list-item prop='pageUrl' width='100px'>
                                        <template #key>文档类型</template>
                                        <template #value>
                                            <el-select v-model='form.fileType' clearable filterable
                                                       placeholder='选择文档类型'>
                                                <el-option v-for='item in fileTypes' :key='item.name'
                                                           :label='item.value'
                                                           :value='item.name'/>
                                            </el-select>
                                        </template>
                                    </card-list-item>
                                </el-row>
                            </el-form>
                        </template>
                    </card-list>
                </el-col>
            </el-row>
        </div>

        <div class='flex justify-between items-center mb-2 '>
            <div>
                <el-button-group>
                    <el-button v-prevent-default type='primary' @click='createClick' :loading="createFileLoading">创建文档</el-button>
                    <input ref="fileInput" style="visibility: hidden" type="file" @change="uploadFile"
                           @dragenter.prevent @dragover.prevent @drop.prevent/>
                    <el-button v-prevent-default type="primary" @click="chooseFile">文件压缩</el-button>
                </el-button-group>
            </div>
            <el-button v-prevent-default type='text' @click='toggleSearch'>
                搜索
                <el-icon>
                    <el-icon-arrow-up v-if='isShow'/>
                    <el-icon-arrow-down v-else/>
                </el-icon>
            </el-button>
        </div>
        <el-table v-loading="tableLoading" :data='tableData.data' stripe style="width:100%">
            <el-table-column label='文档名称' prop='fileName' width="'calc(30% - 1350px)'"/>
            <el-table-column label='文档类型' prop='fileType' width="200"/>
            <el-table-column label="测试计划编号" prop="testPlanKey" width="'calc(20% - 200px)'">
                <template #default="{ row }">
                    <div class="tags-container">
                        <el-tag
                            v-for="(key, index) in parseTestPlanKey(row.testPlanKey)"
                            :key="index"
                            type="info"
                            effect="plain"
                        >
                            {{ key }}
                        </el-tag>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label='创建时间' prop='createTime' width='250'/>
            <el-table-column label="状态" prop="isComplete" width="150">
                <template #default="{ row }">
                    <div class="status-container">
                        {{ row.isComplete }}
                        <el-tooltip v-if="row.isComplete === '失败'" :content="row.errorMsg"
                                    :popper-style="{ width: '250px !important' }"
                                    effect="dark" hide-after="0" placement="top-start">
                            <el-icon-warning-filled class="el-icon-warning-filled"/>
                        </el-tooltip>
                    </div>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label='操作' prop='operation' width=" 150">
                <template #default="{ row }">
                    <div class="button-container">
                        <el-button v-if="row.isComplete === '已完成'" v-prevent-default plain size="small"
                                   type="primary" @click="() => downloadFile(row.id)">下载
                        </el-button>
                        <el-popconfirm cancel-button-text="取消" confirm-button-text="确定" title="确定删除吗？"
                                       width="220"
                                       @confirm="() => deleteInterface(row.id)">
                            <template #reference>
                                <el-button v-prevent-default plain size="small" type="danger">删除</el-button>
                            </template>
                        </el-popconfirm>
                    </div>

                </template>
            </el-table-column>
        </el-table>
        <el-dialog v-model="jiraDataVisible" :close-on-click-modal="false" :close-on-press-escape="false"
                   :width="'450px'" title="请先登录JIRA">
            <template #>
                <div class="body">您还没有登录JIRA或登录状态已失效，是否前往个人中心进行设置？</div>
            </template>
            <template #footer style="text-align:right;">
                <el-button @click="cancelImportJiraDataVisible">取消</el-button>
                <el-button v-prevent-default :loading="loading" type="primary" @click="jiraLogin">前往登录</el-button>
            </template>
        </el-dialog>
        <el-dialog v-model="addDialogVisible" :close-on-click-modal="false" :close-on-press-escape="false"
                   title="创建文档"
                   width="30%"
                   @close="resetForm">
            <el-form ref="addRef" :model="createForm" :rules="createRule" label-width="90px"
                     class="create-dialog">
                <el-form-item label="文档类型" prop="fileType">
                    <el-select v-model='createForm.fileType'
                               clearable
                               filterable
                               placeholder='选择文档类型'
                               @change="handleFileTypeChange">
                        <el-option v-for='item in fileTypes' :key='item.name' :label='item.value'
                                   :value='item.name'/>
                    </el-select>
                </el-form-item>
                <el-form-item label="计划周期" prop="testPlanIssueKey">
                    <el-tree-select
                        v-model="selectedValues"
                        :data="treeData"
                        :props="treeProps"
                        multiple
                        filterable
                        remote
                        :remote-method="remoteMethod"
                        :filter-node-method="filterNodeMethod"
                        :reserve-keyword="false"
                        placeholder="请输入计划的IssueKey进行搜索"
                        clearable
                        @change="handleSelectionChange"
                        @remove-tag="handleRemove"
                        @clear="handleClear"
                        default-expand-all
                    >
                    </el-tree-select>
                </el-form-item>
                <el-form-item label="文档版本">
                    <el-input v-model="createForm.version" placeholder="输入文档版本，默认为1.0"></el-input>
                </el-form-item>
                <el-form-item label="项目与版本">
                    <el-input v-model="createForm.codeVersion"
                              placeholder="输入项目与版本，如：电子签名V2.3"></el-input>
                </el-form-item>
                <el-form-item label="审核人">
                    <el-input v-model="createForm.auditor"
                              placeholder="输入审核人员的姓名"></el-input>
                </el-form-item>
                <el-form-item label="审核日期" v-if="showAuditDate">
                    <div class="date-picker">
                        <el-date-picker
                            v-model="createForm.auditDate"
                            placeholder="不选默认为当天"
                            value-format="YYYY-MM-DD"
                        />
                    </div>
                </el-form-item>
                <el-form-item label="修改日期" v-if="showUpdateDate">
                    <div class="date-picker">
                        <el-date-picker
                            v-model="createForm.updateDate"
                            placeholder="不选默认为当天"
                            value-format="YYYY-MM-DD"
                        />
                    </div>
                </el-form-item>
                <el-form-item v-for="(value, key) in createForm.dynamicFields"
                              :label="getLabelByKey(key)"
                              :key="key"
                              :prop="key">
                    <el-input
                        type="textarea"
                        v-model="createForm.dynamicFields[key]"
                        :placeholder="'请输入' + getLabelByKey(key)"
                        rows="2"
                    ></el-input>
                </el-form-item>
            </el-form>

            <template #footer style="text-align:right;">
                <el-button v-prevent-default @click="cancelCreateForm">取消</el-button>
                <el-button v-prevent-default type="primary" @click="submit" :loading="submitLoading">确定</el-button>
            </template>
        </el-dialog>

        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
                       :small="small" :total="total" layout="total, sizes, prev, pager, next, jumper"
                       @size-change="handleSizeChange"
                       @current-change="handleCurrentChange"/>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import CardList from '@/components/CardList/CardList.vue'
import CardListItem from '@/components/CardList/CardListItem.vue'
import {
    compressFile,
    createVerifyFile,
    deleteVerifyFile,
    downloadVerifyFile, getAllCycleForPlan, getIssueSummary, getPresetFields,
    getVerifyFileList,
    getVerifyFileTypeList, loginCheck,
    type VerifyFileList
} from '@/api/layout'
import { slide } from '@/utils/animate'
import { validateField } from '@/utils/formExtend'
import type { FormInstance } from 'element-plus/lib/components'
import router from '@/router'
import { debounce } from 'lodash'

const isShow = ref(false)
// 分页初始化参数
let currentPage = ref(1)
let pageSize = ref(10)

const total = ref(0)
const small = ref(false)
// 搜索表单初始化
const refForm = ref(null)
// 动画绑定
const searchEl = ref(null)

const addRef = ref<FormInstance | null>(null)
// 点击搜索按钮
const toggleSearch = () => {
    isShow.value = !isShow.value
    slide(searchEl, isShow.value)
}

// 设置分页
const handleSizeChange = (val: number) => {
    pageSize.value = val
    selectData()
}
const handleCurrentChange = (val: number) => {
    currentPage.value = val
    selectData()
}

interface ISearchForm {
    fileName: string
    fileType: string
    fileStatus: string
    createTime: string
    isComplete: string
}

// 搜索表单
const form: ISearchForm = reactive({
    fileName: '',
    fileType: '',
    fileStatus: '',
    createTime: '',
    isComplete: ''

})

const createRule = reactive({
    fileType: [{ required: true, message: '请选择文档类型' }],
    testPlanIssueKey: [{ required: true, message: '请输入计划编号，指定周期' }]
})

// 表单数据接口
interface data {
    id: number
    fileName: string
    fileType: string
    createTime: string
    isComplete: boolean
    errorMsg: string
    loading: boolean
}

// 表格数据
let tableData: ITable<data> = reactive({
    data: [],
    total: 0,
    page: 1,
    size: 10
})

// 查询数据
const selectData = async () => {
    try {
        tableLoading.value = true

        const { fileName, fileType, createTime, isComplete } = form
        const VerifyFile: VerifyFileList = {
            fileName,
            fileType,
            createTime,
            isComplete,
            current: currentPage.value,
            size: pageSize.value
        }

        const { data } = await getVerifyFileList(VerifyFile)

        if (!data.isSuccess) {
            ElMessage.error(data.message)
            return
        }

        total.value = data.data.total

        tableData.data = data.data.data.map((item: any) => ({
            id: item.id,
            fileName: item.fileName,
            fileType: item.fileType,
            testPlanKey: item.testPlanKey,
            createTime: item.createTime,
            isComplete: mapCompletionStatus(item.isComplete),
            errorMsg: item.errorMsg,
            loading: false
        }))
    } catch (error) {
        ElMessage.error('错误，请稍后再试')
    } finally {
        tableLoading.value = false
    }
}

const parseTestPlanKey = (testPlanKey:string) => {
    if (!testPlanKey) {
        return []
    }
    return testPlanKey
        .replace(/^\[|\]$/g, '')
        .split(',')
        .map((str) => str.trim())
}
const mapCompletionStatus = (status: number) => {
    switch (status) {
        case 0:
            return '未完成'
        case 1:
            return '已完成'
        case 2:
            return '失败'
        default:
            return '未知状态'
    }
}

// 表格loading初始化
const tableLoading = ref(false)
// 创建文档loading初始化
const createFileLoading = ref(false)
// 进入页面先查询一次列表
selectData()

interface FileType {
    name: string
    value: string
}

const fileTypes = ref<FileType[]>([])
const getFileTypeList = async () => {
    const res = await getVerifyFileTypeList()
    if (res.data.isSuccess) {
        fileTypes.value = Object.entries(res.data.data).map(([name, value]) => ({
            name,
            value
        }))
    }
}

// 是否显示审核日期和修改日期
const showAuditDate = ref(false)
const showUpdateDate = ref(false)

const handleFileTypeChange = (value: string) => {
    if (value === 'OQP'||value === 'OQR'||value === 'PQP'||value === 'PQR') {
        showAuditDate.value = false
        showUpdateDate.value = true
    } else if (value === 'OQT'||value ==='PQT'){
        showAuditDate.value = true
        showUpdateDate.value = false
    } else {
        showAuditDate.value = false
        showUpdateDate.value = false
    }
    if (createForm.testPlanIssueKey.length === 0) {
        createForm.dynamicFields = {}
    }
    if (value) {
        fetchDynamicFields(createForm.testPlanIssueKey, value)
    }
}

// 进入页面先查询一次文档类型列表
getFileTypeList()
// 登录框弹窗
const jiraDataVisible = ref(false)
// 创建文档弹窗初始化
const addDialogVisible = ref(false)
const createClick = async () => {
    createFileLoading.value = true
    const res = await loginCheck()
    if (!res.data.isSuccess) {
        // 如果登录失败，就打开登录框提示
        jiraDataVisible.value = true
        createFileLoading.value = false
        return
    }
    // 如果登录成功，就打开创建文档弹窗
    // 设置jiraToken
    createForm.jiraToken = res.headers['jiratoken']
    addRef.value?.resetFields()
    addDialogVisible.value = true
    createFileLoading.value = false
}

const loading = ref(false)

// 取消登录jira
const cancelImportJiraDataVisible = () => {
    jiraDataVisible.value = false
}
// 登录jira
const jiraLogin = async () => {
    // 跳转后关闭弹窗
    jiraDataVisible.value = false
    // 跳转到个人中心并带上 query 参数
    router.push({ path: '/seigneur/UserProfile', query: { from: 'jiraLogin' } })

}

// 创建文档表单初始化
const createForm: Record<string, any> =reactive({
    // 文档类型
    fileType: '',
    // 计划编号
    testPlanIssueKey: [] as string[],
    cycles: [] as string[],
    // 默认为1.0
    version: '',
    // 项目与版本
    codeVersion: '',
    // 审核者
    auditor: '',
    // 审核日期
    auditDate: '',
    // 修改日期
    updateDate: '',

    jiraToken: '',

    imageQualitySlider: 70,
    // 动态字段
    dynamicFields: {} as Record<string, string>
})

const fieldMapping: Record<string, { label: string, key: string }> = {
    'Scope': { label: '范围', key: 'scope' },
    'Document Number': { label: '文档编号', key: 'documentNumber' },
    'System Overview': { label: '系统概述', key: 'systemOverview' },
    'Test Methods': { label: '测试方法', key: 'testMethods' },
    'Screenshot Scope and Rules': { label: '范围与规则', key: 'screenshotScopeAndRules' },
    'Project Description': { label: '项目描述', key: 'projectDescription' },
    'Recommendations': { label: '建议', key: 'recommendations' }
}
const submitLoading = ref(false)
// 提交
const submit = async () => {
    try {
        // 校验必填项
        if (!await validateField(addRef, ['fileType', 'testPlanIssueKey'])) return
        submitLoading.value = true
        // 合并动态字段
        const submitData = {
            ...createForm,
            ...createForm.dynamicFields
        }
        delete submitData.dynamicFields

        // 处理表单数据，过滤掉空字符串的字段
        Object.keys(submitData).forEach(key => {
            if (submitData[key] === '') {
                delete submitData[key]
            }
        })

        const res = await createVerifyFile(submitData, createForm.jiraToken)
        if (res.data.isSuccess) {
            ElMessage.success('提交成功')
            addDialogVisible.value = false
            await selectData()
        } else {
            ElMessage.error(res.data.message)
        }
    } catch (error) {
        // 打印错误信息
        console.error(error)
        ElMessage.error('错误，请稍后再试')
    } finally {
        submitLoading.value = false
    }
}

const cancelCreateForm = () => {
    addDialogVisible.value = false
}

// 删除
const deleteInterface = async (id: number) => {
    const res = await deleteVerifyFile(id)
    if (res.data.isSuccess) {
        ElMessage.success('删除成功')
        await selectData()
    } else {
        ElMessage.error(res.data.message)
    }
}

const downloadFile = async (id: number) => {
    try {
        const res: any = await downloadVerifyFile(id)
        const contentDisposition = res.headers['content-disposition']
        const matches = contentDisposition.match(/filename\*=UTF-8''([^;\n\r"]+)/i) ||
            contentDisposition.match(/filename=['"]?([^;\n\r"]+)/i)
        let fileName = matches ? decodeURIComponent(matches[1].replace(/\+/g, ' ')) : 'unknown'
        // 创建Blob对象并下载
        const blob = new Blob([res.data], { type: res.headers['content-type'] })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
    } catch (err) {
        console.error('出错啦', err)
    }
}

const uploadFile = (event: Event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (file) {
        upload(file)
    }
}
const chooseFile = () => {
    const fileInput = document.querySelector('input[type=file]') as HTMLInputElement
    fileInput.click()
}
const upload = async (file: File) => {
    const formData = new FormData()
    formData.append('file', file)

    try {
        const res: any = await compressFile(formData)
        // 获取文件名并下载文件
        const contentDisposition = res.headers['content-disposition']
        const matches = contentDisposition.match('filename=([^;\n\r]+)')
        let fileName = matches ? matches[1] : 'unknown'
        fileName = decodeURIComponent(fileName)
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
    } catch (err) {
        console.error('出错啦', err)
    } finally {
        resetFileInput()
    }
}

// 重置文件输入框的方法
const resetFileInput = () => {
    const fileInput = document.querySelector('input[type=file]') as HTMLInputElement

    if (fileInput) {
        // 通过手动清空值来重置文件输入框的值
        fileInput.value = ''
    }
}

const selectLoading = ref(false)
interface testPlanInfo {
    key: string
    summary: string
}
// 存储选项
const options = ref<testPlanInfo[]>([])
// 处理清空
const handleClear = () => {
    createForm.testPlanIssueKey = []
    createForm.cycles = []
    createForm.dynamicFields = {}
    selectedValues.value = []
}
// 处理删除标签
const handleRemove = (item: any) => {
    const index =selectedValues.value.indexOf(item)
    if (index !== -1) {
        selectedValues.value.splice(index, 1)
    }
    handleSelectionChange(selectedValues.value)
}

const fetchDynamicFields = async (value: string[], fileType: string) => {
    try {
        if (createForm.testPlanIssueKey.length > 1) {
            return
        }
        if (!fileType) {
            return
        }
        const lastValue = value[value.length - 1]
        if (!lastValue) {
            return
        }
        const dashIndex = lastValue.indexOf('-')
        if (!lastValue) {
            return
        }
        const keyBeforeDash = dashIndex !== -1 ? lastValue.substring(0, dashIndex) : lastValue
        const res = await getPresetFields(keyBeforeDash, fileType)
        if (res.data.isSuccess) {
            const fields = res.data.data
            const newDynamicFields: Record<string, string> = {}
            Object.keys(fields).forEach(field => {
                if (fieldMapping[field]) {
                    newDynamicFields[fieldMapping[field].key] = fields[field] || ''
                }
            })
            createForm.dynamicFields = newDynamicFields
        } else {
            ElMessage.error(res.data.message)
        }
    } catch (error) {
        console.error('获取预设字段失败:', error)
        ElMessage.error('获取预设字段失败')
    }
}

const getLabelByKey = (key: string | number | symbol): string => {
    const keyStr = String(key)
    const entry = Object.values(fieldMapping).find(item => item.key === keyStr)
    return entry ? entry.label : keyStr
}
const resetForm = () => {
    Object.assign(createForm, {
        // 静态字段
        fileType: '',
        testPlanIssueKey: [],
        cycles: [],
        version: '',
        codeVersion: '',
        auditor: '',
        auditDate: '',
        updateDate: '',
        jiraToken: '',
        // 动态字段
        dynamicFields: {}
    })
    selectedValues.value = []
    treeData.value = []
}
// 监听 addDialogVisible 的变化
watch(addDialogVisible, (newVal) => {
    if (!newVal) {
        resetForm()
    }
})
const selectedValues = ref<string[]>([])
interface TreeNodeData {
    value: string
    label: string
    children?: TreeNodeData[]
}
const treeData = ref<TreeNodeData[]>([])
// 定义树的字段映射
const treeProps = {
    label: 'label',
    children: 'children',
    value: 'value',
    checkStrictly: false // 根据需要决定是否严格父子独立选择
}
/**
 * 远程搜索函数
 * 根据 query 获取计划label，再根据计划key获取对应周期，构造 treeData
 * 默认选中所有子节点
 */
const remoteMethod = async (query: string) => {
    if (!query) return
    selectLoading.value = true
    try {
        const planKey = query.toUpperCase().trim()
        const planLabelResponse = await getIssueSummary(createForm.jiraToken, planKey) // 请求A接口获取父节点label
        if (!planLabelResponse.data.isSuccess) {
            ElMessage.error(planLabelResponse.data.message)
            return
        }
        const cyclesDataResponse = await getAllCycleForPlan(planKey, createForm.jiraToken) // 请求B接口获取周期映射
        if (!cyclesDataResponse.data.isSuccess) {
            ElMessage.error(cyclesDataResponse.data.message)
            return
        }

        // 将cyclesData转换为children数组
        const children = Object.entries(cyclesDataResponse.data.data).map(([cycleLabel, cycleValue]) => {
            return { value: cycleValue as string, label: cycleLabel }
        })

        // 构造新的节点
        const newNode: TreeNodeData = {
            value: planKey,
            label: `${planKey} - ${planLabelResponse.data.data}`,
            children: children as TreeNodeData[]
        }

        // 检查是否已经存在相同的planKey，避免重复添加
        const existingNode = treeData.value.find(node => node.value === planKey)
        if (!existingNode) {
            treeData.value.push(newNode)
        } else {
            // 如果已经存在，可以选择更新现有节点的children
            existingNode.children = children
        }

    } catch (err) {
        ElMessage.error('搜索失败，请稍后再试')
    } finally {
        selectLoading.value = false
    }
}
const filterNodeMethod = (value: string, data : TreeNodeData) => {
    // 当父节点的 label 包含查询词时，返回true
    // 对子节点可以直接返回true，让其都显示
    return true
}
/**
 * 选择变化事件
 */
const handleSelectionChange = (values: string[]) => {
    const allSelectedNodes = findSelectedNodes(values, treeData.value)

    const selectedChildren: string[] = []
    const parentSet = new Set<string>()

    allSelectedNodes.forEach(node => {
        if (node.children && node.children.length > 0) {
            // 父节点
            parentSet.add(node.value)
        } else {
            // 子节点
            selectedChildren.push(node.value)
            const parentNode = findParentNode(node.value, treeData.value)
            if (parentNode) {
                parentSet.add(parentNode.value)
            }
        }
    })
    createForm.testPlanIssueKey = Array.from(parentSet)
    createForm.cycles = selectedChildren
    // 如果没有选中的 IssueKey，清空dynamicFields
    if (createForm.testPlanIssueKey.length === 0) {
        createForm.dynamicFields = {}
    }
    if (createForm.fileType && createForm.testPlanIssueKey.length > 0) {
        fetchDynamicFields(createForm.testPlanIssueKey, createForm.fileType)
    }
}
/**
 * 根据已选中的值在树中找到对应的节点对象
 */
function findSelectedNodes(values: string[], data: TreeNodeData[]): TreeNodeData[] {
    const result: TreeNodeData[] = []
    const mapValueToNode = (nodes: TreeNodeData[]) => {
        nodes.forEach(node => {
            if (values.includes(node.value)) {
                result.push(node)
            }
            if (node.children && node.children.length > 0) {
                mapValueToNode(node.children)
            }
        })
    }
    mapValueToNode(data)
    return values
        .map(value => result.find(node => node.value === value))
        .filter((node): node is TreeNodeData => node !== undefined)
}
/**
 * 寻找父节点的辅助函数
 */
function findParentNode(value: string, data: TreeNodeData[], parent?: TreeNodeData): TreeNodeData | null {
    for (const node of data) {
        if (node.value === value && parent) {
            return parent
        }
        if (node.children && node.children.length > 0) {
            const result = findParentNode(value, node.children, node)
            if (result) return result
        }
    }
    return null
}

</script>
<style lang="postcss" scoped>
.table-search-form {
    overflow: hidden;
    height: 0;
}

::v-deep(.el-card__header) {
    padding: 7px 15px;
}

::v-deep(.el-button) {
    padding: 4px 6px;
    border-radius: 3px;
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    flex: 1;
}


.input-tag ::v-deep(.el-input__wrapper) {
    box-shadow: none !important;
}

.status-container {
    display: flex;
    align-items: center;
}

.el-icon-warning-filled {
    color: red;
    margin-left: 5px;
    vertical-align: middle;
    width: 15px;
    height: 15px;
}

.button-container {
    display: flex;
    flex-wrap: wrap; /* 允许按钮换行 */
}

.button-container .el-button {
    margin: 2px 3px;
}
.body {
    font-size: 16px;
}
.date-picker {
    display: flex;
}
.create-dialog {
    max-height: 60vh;
    overflow-y: auto;
}
.el-tag {
    margin: 2px 3px 2px 3px;
}
</style>
