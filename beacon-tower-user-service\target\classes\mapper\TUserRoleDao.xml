<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kf.userservice.dao.TUserRoleDao">
    <resultMap type="com.kf.userservice.entity.TUserRole" id="TUserRoleMap">
        <result property="userId" column="USER_ID" jdbcType="INTEGER"/>
        <result property="roleId" column="ROLE_ID" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="TUserRoleMap">
        select USER_ID,
               ROLE_ID
        from t_user_role
        where USER_ID = #{userId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="TUserRoleMap">
        select USER_ID,
               ROLE_ID
        from t_user_role
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="TUserRoleMap">
        select USER_ID,
               ROLE_ID
        from t_user_role
        <where>
            <if test="userId != null">
                and USER_ID = #{userId}
            </if>
            <if test="roleId != null">
                and ROLE_ID = #{roleId}
            </if>
        </where>
    </select>

    <select id="queryAllById" resultMap="TUserRoleMap">
        select USER_ID,
               ROLE_ID
        from t_user_role
        where USER_ID = #{userId}
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.kf.userservice.entity.TUserRole">
        insert into t_user_role (USER_ID, ROLE_ID)
        values (#{userId}, #{roleId})
    </insert>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from t_user_role
        where USER_ID = #{userId}
    </delete>
</mapper>