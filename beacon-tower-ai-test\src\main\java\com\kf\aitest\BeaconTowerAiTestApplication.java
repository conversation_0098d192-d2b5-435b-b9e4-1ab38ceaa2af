package com.kf.aitest;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;

@MapperScan({"com.kf.aitest.dao"})
@EnableDiscoveryClient
@SpringBootApplication
@EnableRetry
@EnableScheduling
public class BeaconTowerAiTestApplication {

	public static void main(String[] args) {
		SpringApplication.run(BeaconTowerAiTestApplication.class, args);
	}

}
