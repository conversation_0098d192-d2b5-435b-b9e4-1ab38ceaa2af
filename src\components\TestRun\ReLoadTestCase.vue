<template>
    <el-dialog v-model="localVisible"
               :close-on-click-modal="false"
               :close-on-press-escape="false"
               :width="'500px'"
               class="dialogClass"
               title="重新载入测试用例"
               @closed="handleClosed"
    >
        <el-form ref="reLoadCaseRef" :model="reLoadCaseForm" :rules="reLoadCaseRules">
            <el-form-item label="用例编号" prop="testCaseIds">
                <el-input v-model="reLoadCaseForm.testCaseIds"
                          placeholder="填写用例编号，比如：CA-1234,多个编号使用,分割"></el-input>
            </el-form-item>
            <el-form-item prop="keepTestResults">
                <div class="flex-container">
                    <el-checkbox v-model="checkboxForLoadCase.keepTestResults"
                                 @change="clearInputOnCheckboxChangeForLoadCase('keepTestResults')"></el-checkbox>
                    <span class="no-wrap">保留测试运行状态
                            <el-tooltip placement="top">
                                <template #content>
                                    勾选此项，会保留之前的运行总状态<br/>
                                    <br/>
                                    如果不勾选此项，将使用jira中的默认逻辑:<br/>
                                    根据测试用例的步骤执行状态自动更新测试运行的状态
                                </template>
                                <el-icon>
                                    <el-icon-InfoFilled class="aligned-icon"></el-icon-InfoFilled>
                                </el-icon>
                            </el-tooltip>
                        </span>
                </div>
            </el-form-item>
            <el-form-item prop="keepStepResults">
                <div class="flex-container">
                    <el-checkbox v-model="checkboxForLoadCase.keepStepResults"
                                 @change="clearInputOnCheckboxChangeForLoadCase('keepStepResults')"></el-checkbox>
                    <span class="no-wrap">保留测试步骤状态
                            <el-tooltip placement="top">
                                <template #content>
                                    勾选此项，会保留每一个测试步骤的状态
                                </template>
                                <el-icon>
                                    <el-icon-InfoFilled class="aligned-icon"></el-icon-InfoFilled>
                                </el-icon>
                            </el-tooltip>
                        </span>
                </div>
            </el-form-item>
            <el-form-item prop="keepDefects">
                <div class="flex-container">
                    <el-checkbox v-model="checkboxForLoadCase.keepDefects" @change="clearInputOnCheckboxChangeForLoadCase('keepDefects')"></el-checkbox>
                    <span class="no-wrap">保留关联的测试缺陷
                            <el-tooltip placement="top">
                                <template #content>
                                    勾选此项，会保留之前已经关联的缺陷
                                </template>
                                <el-icon>
                                    <el-icon-InfoFilled class="aligned-icon"></el-icon-InfoFilled>
                                </el-icon>
                            </el-tooltip>
                        </span>
                </div>
            </el-form-item>
        </el-form>
        <template #footer style="text-align:right;">
            <el-button v-prevent-default type="text" @click="handleCancel">取消</el-button>
            <el-button v-prevent-default :loading="loading"
                       type="primary" @click="reloadTestRun">确定
            </el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import type { FormInstance } from 'element-plus/lib/components'
import { reloadTestRuns } from '@/api/layout'
import { ElMessage } from 'element-plus'

// 定义接收的 props
const props = defineProps<{
    visible: boolean
    jiraToken: string
}>()

// 定义发出的事件
const emit = defineEmits<{
    (e: 'close'): void
}>()
// 对话框的可见状态
const localVisible = ref(props.visible)
const reLoadCaseRules = reactive({
    testCaseIds: [{ required: true, message: '请输入用例编号', trigger: 'blur' }]
})
interface CheckboxInterfaceForLoadCase {
    keepTestResults: boolean
    keepStepResults: boolean
    keepDefects: boolean

    [key: string]: boolean
}
const checkboxForLoadCase = reactive<CheckboxInterfaceForLoadCase>({
    keepTestResults: true,
    keepStepResults: true,
    keepDefects: true
})
const reLoadCaseForm = reactive({
    // 测试用例ID集合
    testCaseIds: '',
    // 是否保留测试运行结果
    keepTestResults: true,
    // 是否保留每一步测试结果
    keepStepResults: true,
    // 是否保留关联的测试缺陷
    keepDefects: true,
    jiraToken: props.jiraToken
})
const clearInputOnCheckboxChangeForLoadCase = (propName: string) => {
    if (!checkboxForLoadCase[propName]) {
        (reLoadCaseForm as any)[propName] = ''
        switch (propName) {
            case 'keepTestResults':
                reLoadCaseForm.keepTestResults = false
                break
            case 'keepStepResults':
                reLoadCaseForm.keepStepResults = false
                break
            case 'keepDefects':
                reLoadCaseForm.keepDefects = false
                break
        }
    } else if (propName === 'keepTestResults') {
        reLoadCaseForm.keepTestResults = true
    } else if (propName === 'keepStepResults') {
        reLoadCaseForm.keepStepResults = true
    } else if (propName === 'keepDefects') {
        reLoadCaseForm.keepDefects = true
    }
}
const reLoadCaseRef = ref<FormInstance | null>(null)
// 初始化遮罩层不显示
const loading = ref(false)
const reloadTestRun = async () => {
    loading.value = true
    let { testCaseIds, keepTestResults, keepStepResults, keepDefects, jiraToken } = reLoadCaseForm
    // 使用正则表达式拆分字符串，允许逗号、分号、中文逗号、中文分号作为分隔符
    let testCaseIdsArray = testCaseIds.split(/[,;，；]/).map(id => id.trim()).filter(id => id !== '')
    try {
        const res = await reloadTestRuns({
            testCaseIds: testCaseIdsArray,
            keepTestResults: keepTestResults,
            keepStepResults: keepStepResults,
            keepDefects: keepDefects
        }, jiraToken)
        loading.value = false
        if (!res.data.isSuccess) {
            ElMessage.error(res.data.message)
            return
        } else {
            ElMessage.success(res.data.message)
        }
        // 关闭弹窗
        emit('close')
    } catch (error) {
        console.log(error)
    } finally {
        loading.value = false
    }
}
const handleCancel = () => {
    emit('close')
}
// 处理对话框关闭
const handleClosed = () => {
    emit('close')
}
</script>


