package com.kf.accuratetest.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.kf.accuratetest.common.ResponseDoMain;
import com.kf.accuratetest.dao.RepositoryInfoDao;
import com.kf.accuratetest.dto.CodeParamDTO;
import com.kf.accuratetest.entity.File;
import com.kf.accuratetest.entity.RepositoryInfo;
import com.kf.accuratetest.enums.JDKEnum;
import com.kf.accuratetest.feign.BaoSiService;
import com.kf.accuratetest.service.CodeProcessService;
import com.kf.accuratetest.utils.AsmControllerAnalyse;
import com.kf.accuratetest.utils.AsmDiffProjectMethod;
import com.kf.accuratetest.utils.JGitUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.maven.shared.invoker.*;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class CodeProcessServiceImpl implements CodeProcessService {

    // maven编译命令
    private static final String MAVEN_GOAL_CLEAN_COMPILE = "clean compile -Dmaven.test.skip=true";

    @Resource
    private RepositoryInfoDao repositoryInfoDao;

    @Resource
    private BaoSiService baoSiService;

    @Value("${kf.maven.settings:/opt/apache-maven-3.8.7/conf/settings.xml}")
    private String mavenSettings;

    @Value("${kf.compile.setJavaHome:true}")
    private boolean setJavaHome;

    /**
     * 下载成功则返回两个工程的实际存放路径
     */
    @Override
    public List<RepositoryInfo> codeDownload(CodeParamDTO param, String directoryPath) {
        List<RepositoryInfo> directoryList = new ArrayList<>();
        //获得master分支最新的版本提交记录
        String masterBranchCommitId;
        if (StrUtil.isNotEmpty(param.getMasterBranchCommitId())) {
            masterBranchCommitId = param.getMasterBranchCommitId();
        } else {
            masterBranchCommitId = JGitUtil.lastCommitRecord(param.getGitPath(), param.getMasterBranch(), param.getUserName(), param.getPassWord());
        }
        log.info("设置基准分支提交记录:{}", masterBranchCommitId);
        if (masterBranchCommitId == null) {
            log.error("获取{}分支最新提交记录失败!", param.getMasterBranch());
            return null;
        }
        RepositoryInfo info = new RepositoryInfo();
        info.setRepositoryUrl(param.getGitPath());
        info.setBranch(param.getMasterBranch());
        info.setObjectId(masterBranchCommitId);
        //查询数据库中是否有存在master的数据
        List<RepositoryInfo> masterRepositoryInfoList = repositoryInfoDao.selectByRepositoryAndBranch(info);
        //如果存在就直接添加到返回列表中
        if (!masterRepositoryInfoList.isEmpty() && masterRepositoryInfoList.get(0).getIsDone() == 1) {
            log.info("数据库中已存在{}分支的数据，直接返回", param.getMasterBranch());
            directoryList.add(masterRepositoryInfoList.get(0));
        } else {
            //如果不存在则下载
            log.info("数据库中不存在{}分支的数据，开始下载", param.getMasterBranch());
            RepositoryInfo masterInfo = new RepositoryInfo();
            masterInfo.setRepositoryUrl(param.getGitPath());
            masterInfo.setBranch(param.getMasterBranch());
            masterInfo.setObjectId(masterBranchCommitId);
            masterInfo.setCreateTime(new Date());
            masterInfo.setUpdateTime(new Date());
            masterInfo.setIsDone(0);
            masterInfo.setIsDeleted(0);
            repositoryInfoDao.insertSelective(masterInfo);
            String masterDirectoryPath = fileDownload(param.getGitPath(), param.getMasterBranch(), param.getUserName(), param.getPassWord(), directoryPath);
            if (masterDirectoryPath == null) {
                log.error("{}分支下载失败", param.getMasterBranch());
                return null;
            }
            try {
                //切换到master分支的提交记录
                JGitUtil.checkout(masterDirectoryPath, masterBranchCommitId);
            } catch (Exception e) {
                log.error("切换到提交记录{}失败:{}", masterBranchCommitId, e.getMessage());
                return null;
            }
            masterInfo.setDirectoryPath(masterDirectoryPath);
            masterInfo.setIsDone(1);
            masterInfo.setUpdateTime(new Date());
            repositoryInfoDao.updateByPrimaryKeySelective(masterInfo);
            directoryList.add(masterInfo);
        }

        //获得dev分支的版本提交记录
        String devBranchCommitId;
        //如果提交记录不为空，则使用提交记录
        if (StrUtil.isNotEmpty(param.getDevBranchCommitId())) {
            devBranchCommitId = param.getDevBranchCommitId();
        } else if (param.getMasterBranch().equals(param.getDevBranch())) {
            try {
                devBranchCommitId = JGitUtil.getPreviousCommitMessage(directoryList.get(0).getDirectoryPath()).getCommitId();
                if (devBranchCommitId == null) {
                    log.error("{}分支只有一次提交记录，无法对比！", param.getMasterBranch());
                    return null;
                }
            } catch (IOException | GitAPIException e) {
                log.error("{}分支处理异常:{}", param.getMasterBranch(), e.getMessage());
                throw new RuntimeException(e);
            }
        } else {
            devBranchCommitId = JGitUtil.lastCommitRecord(param.getGitPath(), param.getDevBranch(), param.getUserName(), param.getPassWord());
        }
        log.info("设置对比分支提交记录:{}", devBranchCommitId);
        info.setObjectId(devBranchCommitId);
        info.setBranch(param.getDevBranch());
        List<RepositoryInfo> devRepositoryInfoList = repositoryInfoDao.selectByRepositoryAndBranch(info);
        if (!devRepositoryInfoList.isEmpty() && devRepositoryInfoList.get(0).getIsDone() == 1) {
            log.info("数据库中已存在{}分支的数据，直接返回", param.getDevBranch());
            directoryList.add(devRepositoryInfoList.get(0));
        } else {
            //如果不存在则下载
            log.info("数据库中不存在{}分支的数据，开始下载", param.getDevBranch());
            RepositoryInfo devInfo = new RepositoryInfo();
            devInfo.setRepositoryUrl(param.getGitPath());
            devInfo.setBranch(param.getDevBranch());
            devInfo.setObjectId(devBranchCommitId);
            devInfo.setCreateTime(new Date());
            devInfo.setUpdateTime(new Date());
            devInfo.setIsDone(0);
            devInfo.setIsDeleted(0);
            repositoryInfoDao.insertSelective(devInfo);
            String devDirectoryPath;
            if (param.getMasterBranch().equals(param.getDevBranch())) {
                devDirectoryPath = fileCopy(directoryPath, directoryList.get(0).getDirectoryPath());
            } else {
                devDirectoryPath = fileDownload(param.getGitPath(), param.getDevBranch(), param.getUserName(), param.getPassWord(), directoryPath);
                if (devDirectoryPath == null) {
                    log.error("{}分支下载失败", param.getDevBranch());
                    return null;
                }
            }
            try {
                JGitUtil.checkout(devDirectoryPath, devBranchCommitId);
            } catch (Exception e) {
                log.error("切换到提交记录{}失败:{}", devBranchCommitId, e.getMessage());
                return null;
            }
            devInfo.setDirectoryPath(devDirectoryPath);
            devInfo.setIsDone(1);
            devInfo.setUpdateTime(new Date());
            repositoryInfoDao.updateByPrimaryKeySelective(devInfo);
            directoryList.add(devInfo);
        }
        return directoryList;
    }


    /**
     * 如果下载成功，则返回存储路径
     */
    private String fileDownload(String gitUrl, String branch, String userName, String PassWord, String directoryPath) {
        String directory = directoryPath + java.io.File.separator + IdUtil.simpleUUID();
        try {
            JGitUtil.downloadCode(gitUrl, branch, directory, userName, PassWord);
        } catch (Exception e) {
            log.error("下载{}分支失败:{}", branch, e.getMessage());
            return null;
        }
        return directory;
    }

    /**
     * 复制文件
     */
    private String fileCopy(String directoryPath, String srcPath) {
        String directory = directoryPath + java.io.File.separator + IdUtil.simpleUUID();
        FileUtil.copyContent(new java.io.File(srcPath), new java.io.File(directory), true);
        return directory;
    }

    /**
     * 编译项目
     */
    @Override
    public boolean codeCompile(String userId, List<RepositoryInfo> repositoryList, String jdkVersion) {
        ResponseDoMain responseDoMain = baoSiService.queryXmlByUserId(userId);
        //转换成List<TFile>类型
        List<File> files = (List<File>) responseDoMain.getData();
        String filePath;
        //如果files为空，直接返回
        if (files.isEmpty()) {
            filePath = mavenSettings;
            log.info("用户{}没有上传.xml文件，将使用默认xml编译项目", userId);
        } else {
            filePath = files.get(0).getFilePath();
            log.info("使用{}编译项目", files.get(0).getFileName());
        }
        String jdkHome = JDKEnum.getEnum(jdkVersion);
        for (RepositoryInfo repositoryInfo : repositoryList) {
            boolean compiled = compile(repositoryInfo, filePath, jdkHome, setJavaHome);
            if (!compiled) {
                return false;
            }
        }
        return true;
    }

    private boolean compile(RepositoryInfo repositoryInfo, String xmlPath, String jdkHome, boolean setJavaHome) {
        log.info("开始编译项目: {}", repositoryInfo.getDirectoryPath());
        if (repositoryInfo.getIsCompile() == 1) {
            log.info("项目 {} 已经编译过了", repositoryInfo.getDirectoryPath());
            return true;
        }
        String pomPath = repositoryInfo.getDirectoryPath() + "/pom.xml";
        InvocationRequest request = new DefaultInvocationRequest();
        java.io.File pomFile = new java.io.File(pomPath);
        if (!pomFile.exists()) {
            log.info("pom路径不存在: {}", pomPath);
            return false;
        }
        request.setPomFile(pomFile);

        java.io.File settingsFile = new java.io.File(xmlPath);
        if (settingsFile.exists()) {
            request.setGlobalSettingsFile(settingsFile);
        }

        java.io.File javaHomeFile = new java.io.File(setJavaHome ? jdkHome : System.getProperty("java.home"));
        log.info("设置jdkHome: {}", javaHomeFile.getPath());
        request.setJavaHome(javaHomeFile);

        request.setGoals(Collections.singletonList(MAVEN_GOAL_CLEAN_COMPILE));

        Invoker invoker = new DefaultInvoker();
        invoker.setMavenHome(new java.io.File(System.getenv("MAVEN_HOME")));
        invoker.setLogger(new PrintStreamLogger(System.err, InvokerLogger.ERROR));
        invoker.setOutputHandler(null); // 不打印日志

        try {
            InvocationResult result = invoker.execute(request);
            log.info("{}编译结果: {}", repositoryInfo.getDirectoryPath(), result.getExitCode());
            if (result.getExitCode() == 0) {
                log.info("{}编译成功", repositoryInfo.getDirectoryPath());
                repositoryInfo.setIsCompile(1);
                repositoryInfo.setUpdateTime(new Date());
                repositoryInfoDao.updateByPrimaryKeySelective(repositoryInfo);
                return true;
            } else {
                log.info("{}编译失败", repositoryInfo.getDirectoryPath());
                return false;
            }
        } catch (MavenInvocationException e) {
            log.error("编译项目失败: {}", repositoryInfo.getDirectoryPath(), e);
            return false;
        }
    }

    /**
     * 代码对比
     */
    @Override
    public List<String> codeDiff(String directory1, String directory2) {
        AsmDiffProjectMethod diffProjectLink = new AsmDiffProjectMethod(directory1, directory2);
        List<String> diff = diffProjectLink.diff();
//        log.info("受影响的方法：" + diff);
        return diff;
    }

    /**
     * 调用链分析
     */
    @Override
    public List<List<String>> codeProcess(String directory2, List<String> methodNameList) {
        //遍历开发分支的调用链，并把受影响的方法带进去比对，如果命中则认为当前调用链受影响了，会终止遍历（再遍历下去也没有意义了），并把这个调用链存起来
        AsmControllerAnalyse asmLinkAnalyse = new AsmControllerAnalyse(directory2, methodNameList);
        return asmLinkAnalyse.analyse();
    }
}