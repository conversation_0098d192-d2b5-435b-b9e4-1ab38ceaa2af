package com.kf.userservice.entity.router;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MenuTree {
    private static final long serialVersionUID = 132407007322321045L;
    /**
     * 菜单ID
     */
    private int id;

    /**
     * 菜单父ID
     */
    private int parentId;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 菜单URL
     */
    private String path;

    /**
     * 路由组件
     */
    private String component;

    /**
     * 路由重定向
     */
    private String redirect;

    private Meta meta;


}
