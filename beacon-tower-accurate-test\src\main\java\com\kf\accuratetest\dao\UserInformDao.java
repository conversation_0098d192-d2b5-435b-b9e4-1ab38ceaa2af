package com.kf.accuratetest.dao;

import com.kf.accuratetest.entity.UserInform;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserInformDao {

    /**
     * 查询指定行数据
     *
     * @param userInform 查询条件
     * @return 对象列表
     */
    List<UserInform> queryAllByLimit(UserInform userInform);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<TUserInfrom> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<UserInform> entities);


    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<TUserInfrom> 实例对象列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("entities") List<UserInform> entities);

    /**
     * 修改数据
     *
     * @param userInform 实例对象
     * @return 影响行数
     */
    int update(UserInform userInform);

}

