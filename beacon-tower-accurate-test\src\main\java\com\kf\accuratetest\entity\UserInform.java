package com.kf.accuratetest.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("t_user_infrom")
public class UserInform implements Serializable {
    private static final long serialVersionUID = -52206498245530454L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * git项目地址
     */
    private String gitUrl;
    /**
     * 通知类型 1.钉钉 2.邮件
     */
    private Integer type;
    /**
     * 通知标识 钉钉号或者邮箱
     */
    private String objectId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 是否生效  0生效 1未生效
     */
    private Integer status;
    /**
     * 是否删除  0未删除 1删除
     */
    private Integer isDeleted;
}

