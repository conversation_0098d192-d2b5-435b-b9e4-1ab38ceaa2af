package com.kf.uitest.dto.execution;

import com.kf.uitest.entity.UiTestHook;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecutionCaseDTO {
    /**
     * 用例ID
     * 关联：ui_test_case表的id字段
     */
    private String id;
    
    /**
     * 用例名称
     * 描述用例的功能
     */
    private String name;
    
    /**
     * 用例执行顺序
     * 在组内的执行顺序
     */
//    private Integer order;
    
    /**
     * 用例级别的前置钩子
     * 在用例开始执行前调用
     */
    private List<UiTestHook> preHooks;
    
    /**
     * 用例级别的后置钩子
     * 在用例执行完成后调用
     */
    private List<UiTestHook> postHooks;
    
    /**
     * 步骤组列表
     * 按顺序执行的步骤组
     */
    private List<StepBlockDTO> stepBlocks;
} 