package com.kf.accuratetest.service;

import com.kf.accuratetest.dto.CardListDTO;
import com.kf.accuratetest.dto.CodeParamDTO;
import com.kf.accuratetest.entity.UserInterfaceAnnotationTask;

import java.util.List;

public interface UserInterfaceAnnotationTaskService {

    /**
     * 通过ID查询数据
     *
     * @param userId 用户ID
     * @return 实例对象
     */
    List<CardListDTO> queryByUserId(String userId);

    /**
     * 通过ID查询数据
     *
     * @param userId 用户ID
     * @return 实例对象
     */
    List<UserInterfaceAnnotationTask> queryRepositoryListByUserId(String userId);

    /**
     * 新增数据
     *
     * @param param 实例对象
     * @param userId 用户ID
     * @param taskId 也是webSocketId，用于向前端推送消息
     * @return 实例对象
     */
    String insert(CodeParamDTO param, String userId,String taskId);

    /**
     * 通过主键删除数据
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteById(String userId);

}
