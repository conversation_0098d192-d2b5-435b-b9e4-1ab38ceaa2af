package com.kf.aitest.service;

import com.kf.aitest.dto.CachedMessage;
import com.kf.aitest.dto.ComparisonProgressDTO;
import com.kf.aitest.dto.ConnectionInfo;
import com.kf.aitest.dto.DataComparisonResultDTO;
import com.kf.aitest.enums.ConnectionStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.net.SocketException;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ConcurrentMap;

/**
 * 对比进度管理器 - 支持消息缓存和状态管理的SSE实现
 */
@Slf4j
@Component
public class ComparisonProgressManager {

    /**
     * 连接信息管理 - 包含SSE连接、状态和消息缓存
     */
    private final ConcurrentMap<String, ConnectionInfo> connections = new ConcurrentHashMap<>();

    /**
     * 进度数据存储
     */
    private final ConcurrentMap<String, ComparisonProgressDTO> progressMap = new ConcurrentHashMap<>();

    /**
     * SSE连接超时时间（毫秒）
     */
    @Value("${sse.connection.timeout:600000}")
    private long connectionTimeout;

    /**
     * 消息过期时间（分钟）
     */
    @Value("${sse.message.expire-minutes:30}")
    private int messageExpireMinutes;

    /**
     * 清理任务执行间隔（分钟）
     */
    @Value("${sse.cleanup.interval-minutes:5}")
    private int cleanupIntervalMinutes;
    
    /**
     * 创建SSE连接 - 支持消息缓存和状态管理
     */
    public SseEmitter createEmitter(String taskId) {
        ConnectionInfo connectionInfo = connections.get(taskId);

        if (connectionInfo != null && connectionInfo.getStatus() == ConnectionStatus.CONNECTED) {
            log.warn("SSE连接已存在且处于连接状态: taskId={}", taskId);
            return connectionInfo.getEmitter();
        }

        // 如果存在旧连接，先清理
        if (connectionInfo != null) {
            log.info("清理旧连接: taskId={}, status={}", taskId, connectionInfo.getStatus());
            cleanup(taskId);
        }

        // 创建新的连接信息
        connectionInfo = new ConnectionInfo();
        SseEmitter emitter = new SseEmitter(connectionTimeout);
        connectionInfo.setEmitter(emitter);
        connectionInfo.setStatus(ConnectionStatus.CONNECTED);

        connections.put(taskId, connectionInfo);

        // 设置连接关闭时的清理逻辑
        emitter.onCompletion(() -> {
            log.info("SSE连接正常关闭: taskId={}", taskId);
            updateConnectionStatus(taskId, ConnectionStatus.DISCONNECTED);
        });
        emitter.onTimeout(() -> {
            log.warn("SSE连接超时: taskId={}", taskId);
            updateConnectionStatus(taskId, ConnectionStatus.DISCONNECTED);
        });
        emitter.onError((throwable) -> {
            log.error("SSE连接错误: taskId={}, error={}", taskId, throwable.getMessage());
            updateConnectionStatus(taskId, ConnectionStatus.DISCONNECTED);
        });

        log.info("创建SSE连接: taskId={}, 缓存消息数量={}", taskId, connectionInfo.getMessageCount());

        try {
            // 发送连接成功事件
            emitter.send(SseEmitter.event().name("connected").data("SSE连接已建立"));

            // 发送所有缓存的消息
            flushCachedMessages(taskId, connectionInfo);

        } catch (Exception e) {
            log.error("发送连接成功事件或缓存消息失败: taskId={}, error={}", taskId, e.getMessage());
            updateConnectionStatus(taskId, ConnectionStatus.DISCONNECTED);
        }

        return emitter;
    }
    
    /**
     * 获取SSE连接
     */
    public SseEmitter getEmitter(String taskId) {
        ConnectionInfo connectionInfo = connections.get(taskId);
        return connectionInfo != null ? connectionInfo.getEmitter() : null;
    }

    /**
     * 初始化任务连接（在任务启动时调用）
     */
    public void initializeTask(String taskId) {
        ConnectionInfo connectionInfo = new ConnectionInfo();
        connectionInfo.setStatus(ConnectionStatus.PENDING);
        connections.put(taskId, connectionInfo);
        log.info("初始化任务连接: taskId={}, status=PENDING", taskId);
    }
    
    /**
     * 初始化进度
     */
    public void initProgress(String taskId, int totalIds) {
        // 确保任务连接已初始化
        if (!connections.containsKey(taskId)) {
            initializeTask(taskId);
        }

        ComparisonProgressDTO progress = new ComparisonProgressDTO();
        progress.setTaskId(taskId);
        progress.setTotalIds(totalIds);
        progress.setCompletedIds(0);
        progress.setOverallProgress(0.0);
        progress.setCurrentIdProgress(0.0);
        progress.setCompletedStages(0);
        progress.setUpdateTime(LocalDateTime.now());
        progress.setMessage("开始数据对比任务");

        progressMap.put(taskId, progress);
        sendProgress(taskId, progress);
    }
    
    /**
     * 更新当前处理的ID
     */
    public void updateCurrentId(String taskId, String currentId, int idIndex) {
        ComparisonProgressDTO progress = progressMap.get(taskId);
        if (progress != null) {
            progress.setCurrentId(currentId);
            progress.setCompletedIds(idIndex);
            progress.setCurrentIdProgress(0.0);
            progress.setCompletedStages(0);
            progress.setUpdateTime(LocalDateTime.now());
            progress.setMessage(String.format("正在处理ID: %s (%d/%d)", currentId, idIndex + 1, progress.getTotalIds()));
            
            // 计算总体进度
            double overallProgress = (double) idIndex / progress.getTotalIds() * 100;
            progress.setOverallProgress(overallProgress);
            
            sendProgress(taskId, progress);
        }
    }
    
    /**
     * 更新当前阶段
     */
    public void updateCurrentStage(String taskId, String stageName, int stageIndex) {
        ComparisonProgressDTO progress = progressMap.get(taskId);
        if (progress != null) {
            progress.setCurrentStage(stageName);
            progress.setCompletedStages(stageIndex);
            progress.setUpdateTime(LocalDateTime.now());
            progress.setMessage(String.format("正在处理阶段: %s", getStageDescription(stageName)));
            
            // 计算当前ID的进度
            double currentIdProgress = (double) stageIndex / progress.getTotalStages() * 100;
            progress.setCurrentIdProgress(currentIdProgress);
            
            // 更新总体进度
            double idProgress = (double) progress.getCompletedIds() / progress.getTotalIds();
            double stageProgress = currentIdProgress / 100.0 / progress.getTotalIds();
            progress.setOverallProgress((idProgress + stageProgress) * 100);
            
            sendProgress(taskId, progress);
        }
    }
    
    /**
     * 完成一个阶段
     */
    public void completeStage(String taskId, String stageName) {
        ComparisonProgressDTO progress = progressMap.get(taskId);
        if (progress != null) {
            int completedStages = progress.getCompletedStages() + 1;
            progress.setCompletedStages(completedStages);
            progress.setUpdateTime(LocalDateTime.now());
            progress.setMessage(String.format("完成阶段: %s", getStageDescription(stageName)));
            
            // 计算当前ID的进度
            double currentIdProgress = (double) completedStages / progress.getTotalStages() * 100;
            progress.setCurrentIdProgress(currentIdProgress);
            
            sendStageComplete(taskId, stageName);
        }
    }
    
    /**
     * 完成一个ID的处理
     */
    public void completeId(String taskId, String id, DataComparisonResultDTO result) {
        ComparisonProgressDTO progress = progressMap.get(taskId);
        if (progress != null) {
            int completedIds = progress.getCompletedIds() + 1;
            progress.setCompletedIds(completedIds);
            progress.setCurrentIdProgress(100.0);
            progress.setCompletedStages(progress.getTotalStages());
            progress.setUpdateTime(LocalDateTime.now());
            progress.setMessage(String.format("完成ID处理: %s (%d/%d)", id, completedIds, progress.getTotalIds()));
            
            // 计算总体进度
            double overallProgress = (double) completedIds / progress.getTotalIds() * 100;
            progress.setOverallProgress(overallProgress);
            
            sendComparisonResult(taskId, result);
        }
    }
    
    /**
     * 完成整个任务
     */
    public void completeTask(String taskId) {
        ComparisonProgressDTO progress = progressMap.get(taskId);
        if (progress != null) {
            progress.setCompleted(true);
            progress.setOverallProgress(100.0);
            progress.setUpdateTime(LocalDateTime.now());
            progress.setMessage("所有数据对比任务已完成");

            sendComplete(taskId);

            // 延迟关闭连接，确保客户端能收到完成事件
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            closeConnection(taskId);
        }
    }
    
    /**
     * 发送AI评估事件
     */
    public void sendAiEvent(String taskId, String eventType, Object data) {
        sendEvent(taskId, eventType, data);
    }

    /**
     * 发送错误信息并关闭连接
     */
    public void sendError(String taskId, String errorMessage) {
        ComparisonProgressDTO progress = progressMap.get(taskId);
        if (progress != null) {
            progress.setHasError(true);
            progress.setErrorMessage(errorMessage);
            progress.setUpdateTime(LocalDateTime.now());
            progress.setMessage("处理过程中发生错误");
            progress.setCompleted(true);
        }

        // 发送错误事件
        sendEvent(taskId, "error", errorMessage);

        // 关闭SSE连接
        closeConnection(taskId);
    }

    /**
     * 关闭SSE连接
     */
    public void closeConnection(String taskId) {
        ConnectionInfo connectionInfo = connections.get(taskId);
        if (connectionInfo != null) {
            SseEmitter emitter = connectionInfo.getEmitter();
            if (emitter != null) {
                try {
                    emitter.complete();
                    log.info("SSE连接已关闭: taskId={}", taskId);
                } catch (Exception e) {
                    log.warn("关闭SSE连接时发生异常: taskId={}, error={}", taskId, e.getMessage());
                } finally {
                    cleanup(taskId);
                }
            } else {
                cleanup(taskId);
            }
        }
    }
    
    /**
     * 发送进度更新
     */
    private void sendProgress(String taskId, ComparisonProgressDTO progress) {
        sendEvent(taskId, "progress", progress);
    }
    
    /**
     * 发送阶段完成事件
     */
    private void sendStageComplete(String taskId, String stageName) {
        sendEvent(taskId, "stage_complete", stageName);
    }
    
    /**
     * 发送对比结果
     */
    private void sendComparisonResult(String taskId, DataComparisonResultDTO result) {
        sendEvent(taskId, "comparison_result", result);
    }
    
    /**
     * 发送完成事件
     */
    private void sendComplete(String taskId) {
        sendEvent(taskId, "complete", "任务完成");
    }
    
    /**
     * 发送SSE事件 - 支持消息缓存
     */
    private void sendEvent(String taskId, String eventName, Object data) {
        ConnectionInfo connectionInfo = connections.get(taskId);
        if (connectionInfo == null) {
            log.warn("连接信息不存在: taskId={}, event={}", taskId, eventName);
            return;
        }

        // 根据连接状态决定是直接发送还是缓存
        if (connectionInfo.getStatus() == ConnectionStatus.CONNECTED) {
            // 连接已建立，直接发送
            SseEmitter emitter = connectionInfo.getEmitter();
            if (emitter != null) {
                try {
                    emitter.send(SseEmitter.event().name(eventName).data(data));
                    connectionInfo.updateLastActiveTime();
                    log.debug("发送SSE事件: taskId={}, event={}", taskId, eventName);
                } catch (IOException e) {
                    log.error("发送SSE事件失败: taskId={}, event={}, error={}", taskId, eventName, e.getMessage());
                    handleSendError(taskId, connectionInfo, e);
                }
            } else {
                log.warn("SSE连接为空: taskId={}, event={}", taskId, eventName);
                updateConnectionStatus(taskId, ConnectionStatus.DISCONNECTED);
            }
        } else {
            // 连接未建立或已断开，缓存消息
            connectionInfo.addMessage(eventName, data);
            log.debug("缓存SSE事件: taskId={}, event={}, status={}, 队列大小={}",
                     taskId, eventName, connectionInfo.getStatus(), connectionInfo.getMessageCount());
        }
    }
    
    /**
     * 发送缓存的消息 - 增强性能监控
     */
    private void flushCachedMessages(String taskId, ConnectionInfo connectionInfo) {
        ConcurrentLinkedQueue<CachedMessage> messages = connectionInfo.drainMessages();
        if (messages.isEmpty()) {
            log.debug("无缓存消息需要发送: taskId={}", taskId);
            return;
        }

        SseEmitter emitter = connectionInfo.getEmitter();
        if (emitter == null) {
            log.warn("SSE连接为空，无法发送缓存消息: taskId={}, 消息数量={}", taskId, messages.size());
            return;
        }

        long startTime = System.currentTimeMillis();
        int sentCount = 0;
        int totalMessages = messages.size();

        log.info("开始发送缓存消息: taskId={}, 消息数量={}", taskId, totalMessages);

        for (CachedMessage message : messages) {
            try {
                long messageStartTime = System.currentTimeMillis();
                emitter.send(SseEmitter.event().name(message.getEventName()).data(message.getData()));
                long messageEndTime = System.currentTimeMillis();

                sentCount++;

                // 记录慢消息发送
                long messageDuration = messageEndTime - messageStartTime;
                if (messageDuration > 100) { // 超过100ms的消息
                    log.warn("慢消息发送: taskId={}, event={}, 耗时={}ms",
                            taskId, message.getEventName(), messageDuration);
                }

            } catch (IOException e) {
                log.error("发送缓存消息失败: taskId={}, event={}, 已发送={}/{}, error={}",
                         taskId, message.getEventName(), sentCount, totalMessages, e.getMessage());
                handleSendError(taskId, connectionInfo, e);
                break;
            }
        }

        long totalDuration = System.currentTimeMillis() - startTime;

        if (sentCount == totalMessages) {
            log.info("缓存消息发送完成: taskId={}, 发送数量={}, 总耗时={}ms, 平均耗时={}ms",
                    taskId, sentCount, totalDuration, totalDuration / sentCount);
        } else {
            log.warn("缓存消息发送中断: taskId={}, 发送数量={}/{}, 总耗时={}ms",
                    taskId, sentCount, totalMessages, totalDuration);
        }

        connectionInfo.updateLastActiveTime();
    }

    /**
     * 处理发送错误 - 增强异常处理
     */
    private void handleSendError(String taskId, ConnectionInfo connectionInfo, IOException e) {
        String errorType = getErrorType(e);
        log.error("SSE发送错误: taskId={}, 错误类型={}, 错误信息={}", taskId, errorType, e.getMessage());

        try {
            SseEmitter emitter = connectionInfo.getEmitter();
            if (emitter != null) {
                // 根据错误类型决定处理方式
                if (isRecoverableError(e)) {
                    log.info("可恢复错误，标记连接为断开状态: taskId={}", taskId);
                    updateConnectionStatus(taskId, ConnectionStatus.DISCONNECTED);
                } else {
                    log.info("不可恢复错误，完成连接: taskId={}", taskId);
                    emitter.completeWithError(e);
                    updateConnectionStatus(taskId, ConnectionStatus.DISCONNECTED);
                }
            }
        } catch (Exception ex) {
            log.warn("处理SSE发送错误时发生异常: taskId={}, 原始错误={}, 处理异常={}",
                    taskId, e.getMessage(), ex.getMessage());
        }
    }

    /**
     * 获取错误类型描述
     */
    private String getErrorType(IOException e) {
        if (e instanceof SocketException) {
            return "网络连接异常";
        } else if (e.getMessage() != null) {
            String message = e.getMessage().toLowerCase();
            if (message.contains("broken pipe")) {
                return "连接中断";
            } else if (message.contains("connection reset")) {
                return "连接重置";
            } else if (message.contains("timeout")) {
                return "连接超时";
            }
        }
        return "IO异常";
    }

    /**
     * 判断是否为可恢复的错误
     */
    private boolean isRecoverableError(IOException e) {
        if (e instanceof SocketException) {
            return true; // 网络异常通常可以重连
        }

        String message = e.getMessage();
        if (message != null) {
            message = message.toLowerCase();
            // 这些错误通常表示客户端断开，可以等待重连
            return message.contains("broken pipe") ||
                   message.contains("connection reset") ||
                   message.contains("connection aborted");
        }

        return false;
    }

    /**
     * 更新连接状态
     */
    private void updateConnectionStatus(String taskId, ConnectionStatus status) {
        ConnectionInfo connectionInfo = connections.get(taskId);
        if (connectionInfo != null) {
            ConnectionStatus oldStatus = connectionInfo.getStatus();
            connectionInfo.setStatus(status);
            connectionInfo.updateLastActiveTime();
            log.info("连接状态变更: taskId={}, {} -> {}", taskId, oldStatus, status);

            if (status == ConnectionStatus.DISCONNECTED) {
                // 延迟清理，给重连机会
                scheduleCleanup(taskId);
            }
        }
    }

    /**
     * 调度清理任务
     */
    private void scheduleCleanup(String taskId) {
        // 这里可以实现延迟清理逻辑，暂时直接清理
        // 在实际应用中可以使用ScheduledExecutorService实现延迟清理
        log.info("调度清理任务: taskId={}", taskId);
    }

    /**
     * 清理资源
     */
    private void cleanup(String taskId) {
        ConnectionInfo connectionInfo = connections.remove(taskId);
        progressMap.remove(taskId);

        if (connectionInfo != null) {
            log.info("清理SSE资源: taskId={}, 状态={}, 缓存消息数量={}",
                    taskId, connectionInfo.getStatus(), connectionInfo.getMessageCount());
        } else {
            log.info("清理SSE资源: taskId={}", taskId);
        }
    }
    
    /**
     * 定时清理过期连接和消息
     */
    @Scheduled(fixedRateString = "#{${sse.cleanup.interval-minutes:5} * 60 * 1000}")
    public void cleanupExpiredConnections() {
        LocalDateTime now = LocalDateTime.now();
        int cleanedConnections = 0;
        int cleanedMessages = 0;

        for (String taskId : connections.keySet()) {
            ConnectionInfo connectionInfo = connections.get(taskId);
            if (connectionInfo == null) {
                continue;
            }

            // 检查连接是否过期
            LocalDateTime expireTime = connectionInfo.getCreateTime().plusMinutes(messageExpireMinutes);
            boolean isExpired = now.isAfter(expireTime);

            // 检查是否长时间未活跃
            LocalDateTime lastActiveExpireTime = connectionInfo.getLastActiveTime().plusMinutes(messageExpireMinutes / 2);
            boolean isInactive = now.isAfter(lastActiveExpireTime);

            if (isExpired || (connectionInfo.getStatus() == ConnectionStatus.DISCONNECTED && isInactive)) {
                log.info("清理过期连接: taskId={}, 状态={}, 创建时间={}, 最后活跃时间={}, 缓存消息数量={}",
                        taskId, connectionInfo.getStatus(), connectionInfo.getCreateTime(),
                        connectionInfo.getLastActiveTime(), connectionInfo.getMessageCount());

                cleanedMessages += connectionInfo.getMessageCount();
                cleanup(taskId);
                cleanedConnections++;
            } else if (connectionInfo.getStatus() == ConnectionStatus.PENDING && isInactive) {
                // 清理长时间未连接的PENDING状态连接中的部分消息
                int messageCount = connectionInfo.getMessageCount();
                if (messageCount > 100) { // 如果消息过多，清理一半
                    ConcurrentLinkedQueue<CachedMessage> messages = connectionInfo.drainMessages();
                    int keepCount = messageCount / 2;
                    int index = 0;
                    for (CachedMessage message : messages) {
                        if (index++ >= keepCount) {
                            connectionInfo.addMessage(message.getEventName(), message.getData());
                        }
                    }
                    cleanedMessages += keepCount;
                    log.info("清理部分缓存消息: taskId={}, 清理数量={}, 保留数量={}",
                            taskId, keepCount, connectionInfo.getMessageCount());
                }
            }
        }

        if (cleanedConnections > 0 || cleanedMessages > 0) {
            log.info("定时清理完成: 清理连接数={}, 清理消息数={}, 当前连接数={}",
                    cleanedConnections, cleanedMessages, connections.size());
        }
    }

    /**
     * 获取连接统计信息
     */
    public String getConnectionStats() {
        int totalConnections = connections.size();
        int pendingCount = 0;
        int connectedCount = 0;
        int disconnectedCount = 0;
        int totalMessages = 0;

        for (ConnectionInfo info : connections.values()) {
            totalMessages += info.getMessageCount();
            switch (info.getStatus()) {
                case PENDING:
                    pendingCount++;
                    break;
                case CONNECTED:
                    connectedCount++;
                    break;
                case DISCONNECTED:
                    disconnectedCount++;
                    break;
            }
        }

        return String.format("连接统计 - 总数:%d, 等待:%d, 已连接:%d, 已断开:%d, 缓存消息:%d",
                           totalConnections, pendingCount, connectedCount, disconnectedCount, totalMessages);
    }

    /**
     * 获取阶段描述
     */
    private String getStageDescription(String stageName) {
        switch (stageName) {
            case "recognize":
                return "文档识别";
            case "extraction":
                return "信息提取";
            case "structured":
                return "结构化处理";
            case "transformer":
                return "数据转换";
            default:
                return stageName;
        }
    }
}
