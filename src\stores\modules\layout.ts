import { defineStore } from 'pinia'
import { login, type LoginParam, register, type RegisterParam, verificationCode, type EmailParam, getRouterList, codeAnalysis, type AnalysisParam, saveInFrom } from '@/api/layout/index'
import { type ILayout, IMenubarStatus, type ITagsList, type IMenubarList, type ISetting, type IMenubar, type IStatus, type ITags, type IUserInfo } from '@/type/store/layout'
import router from '@/router/index'
import { allowRouter } from '@/router'
import { generatorDynamicRouter } from '@/router/asyncRouter'
import { setLocal, getLocal, decode, encode } from '@/utils/tools'
import { type RouteLocationNormalizedLoaded, type RouteRecordName } from 'vue-router'
import { type AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
// import { title } from 'process'

const setting = getLocal<ISetting>('setting')
const { ACCESS_TOKEN } = getLocal<IStatus>('token')
const seigneur = '/seigneur'

export const useLayoutStore = defineStore({
    id: 'layout',
    state: (): ILayout => ({
        menubar: {
            status: document.body.offsetWidth < 768 ? IMenubarStatus.PHN : IMenubarStatus.PCE,
            menuList: [],
            isPhone: document.body.offsetWidth < 768
        },
        // 用户信息
        userInfo: {
            name: '',
            role: []
        },
        // 标签栏
        tags: {
            tagsList: [],
            cachedViews: [],
            isNocacheView: false
        },
        setting: {
            theme: setting.theme !== undefined ? setting.theme : 0,
            showTags: setting.showTags !== undefined ? setting.showTags : true,
            color: {
                primary: setting.color !== undefined ? setting.color.primary : '#409eff'
            },
            usePinyinSearch: setting.usePinyinSearch !== undefined ? setting.usePinyinSearch : false,
            mode: setting.mode || 'vertical'
        },
        status: {
            isLoading: false,
            ACCESS_TOKEN: ACCESS_TOKEN || ''
        }
    }),
    getters: {
        getMenubar(): IMenubar {
            return this.menubar
        },
        getUserInfo(): IUserInfo {
            return this.userInfo
        },
        getTags(): ITags {
            return this.tags
        },
        getSetting(): ISetting {
            return this.setting
        },
        getStatus(): IStatus {
            return this.status
        }
    },
    actions: {
        changeCollapsed(): void {
            this.menubar.status = this.menubar.isPhone
                ? this.menubar.status === IMenubarStatus.PHN
                    ? IMenubarStatus.PHE
                    : IMenubarStatus.PHN
                : this.menubar.status === IMenubarStatus.PCN
                    ? IMenubarStatus.PCE
                    : IMenubarStatus.PCN
        },
        changeDeviceWidth(): void {
            this.menubar.isPhone = document.body.offsetWidth < 768
            this.menubar.status = this.menubar.isPhone ? IMenubarStatus.PHN : IMenubarStatus.PCE
        },
        // 切换导航，记录打开的导航
        changeTagNavList(cRouter: RouteLocationNormalizedLoaded): void {
            if (!this.setting.showTags) return // 判断是否开启多标签页
            // if(cRouter.meta.hidden && !cRouter.meta.activeMenu) return // 隐藏的菜单如果不是子菜单则不添加到标签
            if (new RegExp('^\\/redirect').test(cRouter.path)) return
            const index = this.tags.tagsList.findIndex((v: { path: string }) => v.path === cRouter.path)
            this.tags.tagsList.forEach((v: { isActive: boolean }) => v.isActive = false)
            // 判断页面是否打开过
            if (index !== -1) {
                this.tags.tagsList[index].isActive = true
                return
            }
            // 401 404 重定向页面不添加到面包屑
            if (cRouter.path.indexOf('404') !== -1 || cRouter.path.indexOf('401') !== -1 || cRouter.path.indexOf('/seigneur/redirect') !== -1) return
            const tagsList: ITagsList = {
                name: cRouter.name as string,
                title: cRouter.meta.title as string,
                path: cRouter.path,
                isActive: true
            }
            this.tags.tagsList.push(tagsList)
        },
        removeTagNav(obj: { tagsList: ITagsList, cPath: string }): void {
            const index = this.tags.tagsList.findIndex((v: { path: any }) => v.path === obj.tagsList.path)
            if (this.tags.tagsList[index].path === obj.cPath) {
                this.tags.tagsList.splice(index, 1)
                const i = index === this.tags.tagsList.length ? index - 1 : index
                this.tags.tagsList[i].isActive = true
                this.removeCachedViews({ name: obj.tagsList.name, index })
                router.push({ path: this.tags.tagsList[i].path })
            } else {
                this.tags.tagsList.splice(index, 1)
                this.removeCachedViews({ name: obj.tagsList.name, index })
            }
        },
        removeOtherTagNav(tagsList: ITagsList): void {
            const index = this.tags.tagsList.findIndex((v: { path: any }) => v.path === tagsList.path)
            this.tags.tagsList.splice(index + 1)
            this.tags.tagsList.splice(0, index)
            this.tags.cachedViews.splice(index + 1)
            this.tags.cachedViews.splice(0, index)
            router.push({ path: tagsList.path })
        },
        removeAllTagNav(): void {
            this.tags.tagsList.splice(0)
            this.tags.cachedViews.splice(0)
            router.push({ path: `${seigneur}/` })
        },
        // 添加缓存页面
        addCachedViews(obj: { name: string, noCache: boolean }): void {
            if (!this.setting.showTags) return // 判断是否开启多标签页
            if (obj.noCache || this.tags.cachedViews.includes(obj.name)) return
            this.tags.cachedViews.push(obj.name)
        },
        // 删除缓存页面
        removeCachedViews(obj: { name: string, index: number }): void {
            // 判断标签页是否还有该页面
            if (this.tags.tagsList.map((v: { name: any }) => v.name).includes(obj.name)) return
            this.tags.cachedViews.splice(obj.index, 1)
        },
        // 删除所有缓存页面并刷新当前页面
        removeAllCachedViews() {
            this.tags.cachedViews.splice(0)
            this.menubar.menuList = []
        },
        // 刷新页面，默认刷新当前页面
        refreshViews(type: 'push' | 'replace' = 'replace', path = router.currentRoute.value.fullPath, name = router.currentRoute.value.name) {
            this.changeNocacheViewStatus(true)
            // 删除页面的缓存
            const index = this.tags.cachedViews.findIndex((v: RouteRecordName | null | undefined) => v === name)
            index !== -1 && this.tags.cachedViews.splice(index, 1)
            if (type === 'push') {
                router.push(`${seigneur}/redirect${path}`)
            } else {
                router.replace(`${seigneur}/redirect${path}`)
            }
        },
        changeNocacheViewStatus(isNoCache: boolean) {
            this.tags.isNocacheView = isNoCache
        },
        logout(): void {
            this.status.ACCESS_TOKEN = ''
            localStorage.removeItem('token')
            // history.go(0)
            this.removeAllCachedViews()
            router.replace(`${seigneur}/Login` + (location.pathname ? `?from=${encode(location.pathname)}` : ''))
        },
        setToken(token: string): void {
            this.status.ACCESS_TOKEN = token
            setLocal('token', this.status, 1000 * 60 * 60)
        },
        setRoutes(data: Array<IMenubarList>): void {
            this.menubar.menuList = data
        },
        concatAllowRoutes(): void {
            allowRouter.reverse().forEach(v => this.menubar.menuList.unshift(v))
        },
        // 修改主题
        changeTheme(num?: number): void {
            if (num === this.setting.theme) return
            if (typeof num !== 'number') num = this.setting.theme
            this.setting.theme = num
            localStorage.setItem('setting', JSON.stringify(this.setting))
        },
        // 修改主题色
        changeThemeColor(color: string): void {
            this.setting.color.primary = color
            localStorage.setItem('setting', JSON.stringify(this.setting))
        },
        changeTagsSetting(showTags: boolean): void {
            this.setting.showTags = showTags
            localStorage.setItem('setting', JSON.stringify(this.setting))

            if (showTags) {
                const index = this.tags.tagsList.findIndex((v: { path: string }) => v.path === router.currentRoute.value.path)
                if (index !== -1) {
                    this.tags.tagsList.forEach((v: { isActive: boolean }) => v.isActive = false)
                    this.tags.tagsList[index].isActive = true
                } else {
                    this.changeTagNavList(router.currentRoute.value)
                }
            }
        },
        changePinSearchSetting(showPinyinSearch: boolean): void {
            this.setting.usePinyinSearch = showPinyinSearch
            localStorage.setItem('setting', JSON.stringify(this.setting))
        },
        // 下次进去该页面刷新该页面(解决子页面保存之后，回到父页面页面不刷新问题)
        refreshPage(path: string): void {
            const name = this.tags.tagsList.filter((v: { path: string }) => v.path === path)[0]?.name
            if (!name) return
            const index = this.tags.cachedViews.findIndex((v: any) => v === name)
            this.tags.cachedViews.splice(index, 1)
        },
        changemenubarMode(mode: 'horizontal' | 'vertical'): void {
            this.setting.mode = mode
            localStorage.setItem('setting', JSON.stringify(this.setting))
        },
        async login(data: LoginParam): Promise<void> {
            const res = await login(data)
            if (!res.data.isSuccess) {
                ElMessage.error(res.data.message)
                return
            }
            const { token } = res.data.data
            this.status.ACCESS_TOKEN = token
            setLocal('token', this.status)
            setLocal('userInfo', res.data.data)
            const userInfo = res.data.data
            this.userInfo.name = userInfo.userName
            this.userInfo.role = userInfo.roles
            const { query } = router.currentRoute.value
            location.replace(typeof query.from === 'string' ? decode(query.from) : '/seigneur')
        },
        async register(param: RegisterParam): Promise<void> {
            const res = await register(param)
            if (!res.data.isSuccess) {
                ElMessage.error(res.data.message)
                return
            }
            this.login({ username: param.username, password: param.password })
        },
        async GenerateRoutes(): Promise<void> {
            const res = await getRouterList()
            const { data } = res.data
            generatorDynamicRouter(data)
        },
        async codeAnalysis(param: AnalysisParam): Promise<void> {
            const res = await codeAnalysis(param)
        },
        async saveInFrom(param: any): Promise<AxiosResponse<IResponse>> {
            return await saveInFrom(param)
        }
    }
})