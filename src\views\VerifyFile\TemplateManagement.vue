<template>
    <div class="sortable-table">
        <el-button type="primary" @click="fetchData">搜索</el-button>

        <el-table
            ref="tableRef"
            :data="tableData"
            border
            style="width: 100%; margin-top: 16px;"
            row-key="id"
        >
            <!-- 左侧拖拽按钮列 -->
            <el-table-column
                label=""
                width="50"
            >
                <template #default>
                    <div
                        class="drag-handle drag-button"
                        @mousedown.stop
                    >
                        <el-icon><Sort /></el-icon>
                    </div>
                </template>
            </el-table-column>

            <el-table-column prop="date" label="日期" />
            <el-table-column prop="name" label="姓名" />
            <el-table-column prop="address" label="地址" />
        </el-table>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, watch } from 'vue'
import { ElTable } from 'element-plus'
import Sortable from 'sortablejs'
import { Sort } from '@element-plus/icons-vue'

interface TableItem {
    id: number
    name: string
    date: string
    address: string
}

const tableRef = ref<InstanceType<typeof ElTable> | null>(null)

// 模拟表格数据初始为空
const tableData = ref<TableItem[]>([])

// 存储 Sortable 实例，用于重复绑定前先销毁
let sortableInstance: Sortable | null = null

// 获取后端数据的模拟
async function fetchData() {
    // 你可以在这里发真实的 API 请求
    // 例如 const res = await axios.post('/api/search', searchParams)
    // 此处模拟异步请求后返回数据
    const newData: TableItem[] = [
        { id: 1, name: 'Tom',   date: '2023-01-01', address: 'Los Angeles' },
        { id: 2, name: 'Jerry', date: '2023-01-02', address: 'New York' },
        { id: 3, name: 'Alice', date: '2023-01-03', address: 'Chicago' },
        { id: 4, name: 'Bob',   date: '2023-01-04', address: 'Seattle' }
    ]

    // 更新表格数据
    tableData.value = newData

    // 数据更新后，DOM 也要重新渲染
    // 等 DOM 渲染完成后再绑定拖拽 (nextTick)
    await nextTick()
    initSortable()
}

/**
 * 初始化 / 重新初始化 Sortable
 */
function initSortable() {
    // 先销毁之前的实例，避免重复绑定
    if (sortableInstance) {
        sortableInstance.destroy()
        sortableInstance = null
    }

    // 找到 el-table 的真实 DOM，定位到 tbody
    const tbody = tableRef.value?.$el.querySelector('.el-table__body-wrapper tbody')
    if (!tbody) return

    // 创建新的 Sortable 实例
    sortableInstance = Sortable.create(tbody, {
        animation: 150,
        handle: '.drag-handle', // 仅允许按住指定 handle 拖拽
        onEnd: (evt) => {
            const { oldIndex, newIndex } = evt
            if (oldIndex == null || newIndex == null || oldIndex === newIndex) return

            // 交换数据源
            const [currentItem] = tableData.value.splice(oldIndex, 1)
            tableData.value.splice(newIndex, 0, currentItem)

            // 在这里执行你的业务逻辑 (调用接口或其他操作)
            console.log('拖拽结束，新的排序：', tableData.value)
            // saveToBackend(tableData.value)
        }
    })
}

// 解释“为什么要用 onMounted”:
// 因为只有组件挂载后，tableRef.value 才是真正的 DOM
onMounted(() => {
    // 如果初始就有数据，可以这里调用 initSortable()
    // 但本例开始时是空数据，所以不在这里强制调用
})

// 可选：如果你想在数据有变化时重新绑定 Sortable，也可以监听
// 这里仅做示例，若数据可能频繁更新，需要酌情考虑频率或其他逻辑
watch(
    () => tableData.value,
    async () => {
        // 当数据变为空或变成其他列表时，如果需要重新绑定，可以在这做
        // 例如 tableData.value 为空时，就销毁 sortableInstance
        // 这里仅做演示，需要按你的业务场景决定
        if (tableData.value.length === 0) {
            if (sortableInstance) {
                sortableInstance.destroy()
                sortableInstance = null
            }
        } else {
            // 数据非空 => 重新初始化
            await nextTick()
            initSortable()
        }
    },
    { deep: true }
)
</script>

<style scoped>
/*
  1. 默认隐藏“拖拽按钮”；
  2. 只有当鼠标悬浮到 .el-table__row 时，让 .drag-button 显示出来
*/
.drag-button {
    display: none;
    cursor: move; /* 鼠标形状变成可拖拽 */
}

/* Element Plus 的 .el-table__row 用于一行 */
.el-table__row:hover .drag-button {
    display: inline-block;
}
</style>
