package com.kf.uitest.service;

import com.kf.uitest.entity.UiTestAssertion;
import com.kf.uitest.model.HookExecutionResult;
import com.kf.uitest.model.TestExecutionContext;

import java.util.List;

public interface UiTestAssertionService {
    /**
     * 根据钩子ID查找所有断言
     */
    List<UiTestAssertion> findByHookId(String hookId);
    
    /**
     * 根据步骤ID查找所有断言
     * 通过关联的hook查找断言
     */
    List<UiTestAssertion> findByStepId(String stepId);
    
    /**
     * 创建断言
     */
    UiTestAssertion create(UiTestAssertion assertion);
    
    /**
     * 批量创建断言
     */
    List<UiTestAssertion> batchCreate(List<UiTestAssertion> assertions);
    
    /**
     * 更新断言
     */
    boolean update(UiTestAssertion assertion);
    
    /**
     * 删除断言
     */
    boolean delete(String id);
    
    /**
     * 删除钩子的所有断言
     */
    boolean deleteByHookId(String hookId);
    
    /**
     * 验证断言配置
     */
    boolean validateAssertion(UiTestAssertion assertion);

} 