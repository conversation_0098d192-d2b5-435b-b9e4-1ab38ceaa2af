package com.kf.baosi.dto.verifyDocument.OQR;

import lombok.Data;

@Data
public class OQRWrapper {

    private OQRTestCaseListWrapper testCaseInfo;

    private OQRBugListWrapper bugInfo;

    private String version;

    // 测试者
    private String tester;

    // 测试执行者（多个）
    private String testers;

    private String code_version;

    // 审核人
    private String auditor;

    // 批准者
    private String approver;

    // 修改日期
    private String date;

    // 测试缺陷总数
    private String bugTotal;
}
