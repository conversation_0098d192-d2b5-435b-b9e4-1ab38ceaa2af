package com.kf.accuratetest.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("t_interface_page")
public class InterfacePage implements Serializable {
    private static final long serialVersionUID = -72728273798244720L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 接口名称
     */
    private String interfaceName;
    /**
     * 页面入口
     */
    private String pageUrl;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 状态 0生效 1未生效
     */
    private Integer status;
    /**
     * 是否删除  0未删除 1删除
     */
    private Integer isDeleted;

}

