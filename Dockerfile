FROM images.taimei.com/library/node:20-alpine3.18 as builder

RUN echo -e 'https://mirrors.aliyun.com/alpine/v3.18/main/\nhttps://mirrors.aliyun.com/alpine/v3.18/community/' > /etc/apk/repositories && \
	apk update && \
	# 支持上传 CDN
	# 支持 node-sass 编译
	apk add --no-cache zip python3 py3-pip curl make g++

LABEL maintainers tm-cicd
ARG TARGET_PATH
ARG BUILD_ENV
ARG FRONT_BUILD="yarn build"
ARG PYTHON2="0"

ENV CHARSET=UTF-8 \
	LANG=C.UTF-8 \
	TZ=Asia/Shanghai
ENV BUILD_ENV=$BUILD_ENV

RUN echo "[DockerInDocker] 当前构建的环境为: $BUILD_ENV, 构建命令为：$FRONT_BUILD, 构建目录为：$TARGET_PATH"

WORKDIR /code

ADD $TARGET_PATH/package.json $TARGET_PATH/package-lock.json /code/
RUN npm config set registry 'http://npm.taimei.com' && \
	yarn config set registry 'http://npm.taimei.com' && \
	yarn config set cache-folder '/data/gitrunner/yarn.global'
RUN npm install

ADD $TARGET_PATH/ /code/
RUN ls -al 

# npm run uploadCdn 是把静态资源上传至 oss 上的脚本文件，将来会使用 cdn 对 oss 加速
RUN echo "require('child_process').execSync('${FRONT_BUILD}', { stdio: 'inherit' })" > build_script.js && node build_script.js

# 选择更小体积的基础镜像
FROM images.taimei.com/library/nginx:1.19.1-tm1.1

ARG ARTIFACT_DIST="dist"

COPY --from=builder /code/${ARTIFACT_DIST} ${APP_ARTIFACT_DIR}
EXPOSE 8080

