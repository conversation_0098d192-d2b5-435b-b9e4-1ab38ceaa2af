server:
  port: 8002
  tomcat:
    max-swallow-size: -1
spring:
  application:
    name: beacon-tower-user-service
  data:
    redis:
      host: test-redis-global.taimei.com
      port: 6379
      database: 6
      password: Taimei@2022
      timeout: 10000
      jedis:
        pool:
          max-active: 1000
          max-wait: 5000
          max-idle: 50
          min-idle: 10
  mail:
    host: smtp.163.com
    username: <EMAIL>
    password: JEZQOXATWSWKQGUL
    default-encoding: utf-8
  servlet:
    multipart:
      max-request-size: 5GB
      max-file-size: 20MB
  datasource:
    #   数据源基本配置
    username: fsuser
    password: TM@fs.456
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      validation-query: SELECT 1
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
management:
  endpoints:
    web:
      exposure:
        include: "*"
  metrics:
    # 下面选项建议打开，以监控 http 请求的 P99/P95 等，具体的时间分布可以根据实际情况设置
    distribution:
      percentiles:
        http:
          server:
            requests: 0.5, 0.75, 0.9, 0.95, 0.99
    # 在 Prometheus 中添加特别的 Labels
    tags:
      # 必须加上对应的应用名，因为需要以应用的维度来查看对应的监控
      application:  beacon-tower-user-service
  health:
    #忽略的健康检查
    mail:
      enabled: false