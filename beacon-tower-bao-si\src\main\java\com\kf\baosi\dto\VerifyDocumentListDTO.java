package com.kf.baosi.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class VerifyDocumentListDTO {

    String id;

    @Schema(description = "文档名称")
    String fileName;

    @Schema(description = "文档类型")
    String fileType;

    @Schema(description = "测试计划")
    String testPlanKey;

    @Schema(description = "创建时间")
    String createTime;

    @Schema(description = "更新时间")
    String updateTime;

    @Schema(description = "验证文档的状态 0:未完成 1:成功 2:失败")
    int isComplete;

    @Schema(description = "当状态为失败时的错误信息")
    String errorMsg;

}
