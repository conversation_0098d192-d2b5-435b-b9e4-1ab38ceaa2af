<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kf.accuratetest.dao.UserInformDao">
    <resultMap id="BaseResultMap" type="com.kf.accuratetest.entity.UserInform">
        <!--@Table t_user_infrom-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="gitUrl" column="git_url" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="objectId" column="object_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select id,
               user_id,
               git_url,
               type,
               object_id,
               create_time,
               update_time,
               status,
               is_deleted
        from t_user_infrom
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="gitUrl !=null ">
                and git_url = #{gitUrl}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="objectId != null and objectId != ''">
                and object_id = #{objectId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="isDeleted != null ">
                and is_deleted = #{isDeleted}
            </if>
        </where>
    </select>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into t_user_infrom(user_id, git_url, type, object_id, create_time, update_time, status,
                                         is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.gitUrl}, #{entity.type}, #{entity.objectId}, #{entity.createTime},
             #{entity.updateTime},
             #{entity.status}, #{entity.isDeleted})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update t_user_infrom
        <set>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="objectId != null and objectId != ''">
                object_id = #{objectId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
        </set>
        where id = #{id}
    </update>

    <insert id="insertOrUpdateBatch">
        insert into t_user_infrom(user_id, git_url, type, object_id, create_time, update_time, status,
                                         is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.gitUrl}, #{entity.type}, #{entity.objectId}, #{entity.createTime},
             #{entity.updateTime},
             #{entity.status}, #{entity.isDeleted})
        </foreach>
        on duplicate key update
        <foreach collection="entities" item="entity" separator=",">
            user_id = #{entity.userId},
            git_url = #{entity.gitUrl},
            type = #{entity.type},
            object_id = #{entity.objectId},
            create_time = #{entity.createTime},
            update_time = #{entity.updateTime},
            status = #{entity.status},
            is_deleted = #{entity.isDeleted}
        </foreach>
    </insert>


</mapper>

