package com.kf.accuratetest.service.impl;

import com.kf.accuratetest.common.ResultHandler;
import com.kf.accuratetest.entity.Result;
import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;

@Component
@ServerEndpoint(value = "/analysisLog/{taskId}")
@Slf4j
public class WebSocket {
    @Value("${kf.pa.interface:http://itaimei.test.taimei.com/seigneur/InterfaceDetailsList/}")
    private String paInterfaceDetailsList;
    public Session session;
    String taskId;

    @OnOpen
    public void onOpen(Session session, @PathParam("taskId") String taskId) {
        log.info("有新连接加入！taskId={}", taskId);
        this.taskId = taskId;
        this.session = session;
        LinkedBlockingQueue<Result> linkedBlockingQueue = ResultHandler.queue.get(taskId);
        //判断队列是否存在，如果不存在则说明任务已经超过1小时被清理，直接返回报告详情
        if (linkedBlockingQueue == null) {
            sendMessage("结果已经被清理，查看报告详情：" + paInterfaceDetailsList + taskId);
            try {
                session.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            return;
        }
        while (session.isOpen()) {
            Map<StringBuilder, Boolean> logMsg = ResultHandler.get(taskId);
            if (logMsg != null && !logMsg.isEmpty()) {
                //遍历logMsg
                logMsg.forEach((s, b) -> {
                    sendMessage(s.toString());
                    if (!b) {
                        try {
                            log.info("消息发送完成，同步关闭状态给客户端");
                            session.close();
                        } catch (IOException e) {
                            log.error("消息发送完成，服务端主动关闭连接失败：{}", e.getMessage());
                            throw new RuntimeException(e);
                        }
                    }
                });
            } else if (logMsg == null) {
                log.info("获取消息为null，服务端主动关闭连接");
                try {
                    session.close();
                } catch (IOException e) {
                    log.error("服务端主动关闭连接失败：{}", e.getMessage());
                    throw new RuntimeException(e);
                }
            }
        }
    }

    @OnClose
    public void onClose(Session session) {
        try {
            session.close();
            ResultHandler.clear(taskId);
            log.info("客户端消息：关闭连接并清空队列");
        } catch (IOException e) {
            log.error("客户端消息：关闭连接并清空队列失败：{}", e.getMessage());
        }

    }

    @OnError
    public void onError(Session session, Throwable error) {
        log.error("websocket连接发生错误:{}", error.getMessage());
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("来自客户端的消息:" + message);
    }

    public void sendMessage(String message) {
        try {
            this.session.getBasicRemote().sendText(message);
        } catch (IOException e) {
            try {
                this.session.close();
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
            log.error("消息发送异常：{}", e.getMessage());
        }
    }
}
