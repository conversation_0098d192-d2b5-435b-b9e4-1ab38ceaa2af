package com.kf.uitest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kf.uitest.dao.UiTestStepMapper;
import com.kf.uitest.entity.UiTestStep;
import com.kf.uitest.service.UiTestStepService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import com.kf.uitest.exception.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import com.kf.uitest.entity.UiTestBlock;
import com.kf.uitest.service.UiTestBlockService;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class UiTestStepServiceImpl implements UiTestStepService {

    private final UiTestStepMapper stepMapper;
    private final UiTestBlockService blockService;

    @Override
    public UiTestStep getById(String id) {
        if (id == null) {
            throw new IllegalArgumentException("Step id cannot be null");
        }
        UiTestStep step = stepMapper.selectById(id);
        if (step == null) {
            throw new EntityNotFoundException("Test step", id);
        }
        return step;
    }

    @Override
    public UiTestStep create(UiTestStep step) {
        return null;
    }

    @Override
    public boolean update(UiTestStep step) {
        return false;
    }

    @Override
    public boolean delete(String stepId) {
        return false;
    }

    @Override
    public boolean updateStepOrder(String stepId, Integer newOrder) {
        return false;
    }

    @Override
    public List<UiTestStep> findByBlockId(String blockId) {
        return stepMapper.selectList(
            new LambdaQueryWrapper<UiTestStep>()
                .eq(UiTestStep::getBlockId, blockId)
                .orderByAsc(UiTestStep::getStepOrder)
        );
    }

    @Override
    public List<UiTestStep> findByCaseId(String caseId) {
        List<UiTestBlock> blocks = blockService.findByCaseId(caseId);
        if (blocks.isEmpty()) {
            return Collections.emptyList();
        }
        
        List<String> blockIds = blocks.stream()
            .map(UiTestBlock::getId)
            .collect(Collectors.toList());
            
        return stepMapper.selectList(
            new LambdaQueryWrapper<UiTestStep>()
                .in(UiTestStep::getBlockId, blockIds)
                .orderByAsc(UiTestStep::getStepOrder)
        );
    }
}
