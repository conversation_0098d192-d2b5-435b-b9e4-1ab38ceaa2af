package com.kf.baosi.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kf.baosi.common.PaginatedResponse;
import com.kf.baosi.common.RequireHeader;
import com.kf.baosi.common.ResponseDoMain;
import com.kf.baosi.dto.VerifyDocumentDTO;
import com.kf.baosi.dto.VerifyDocumentListDTO;
import com.kf.baosi.entity.TVerifyFile;
import com.kf.baosi.enums.VerifyDocument;
import com.kf.baosi.service.JiraService;
import com.kf.baosi.service.verifyDocumentService;
import com.kf.baosi.utils.WordTemplatePlusParamsUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.CompletableFuture;

@Slf4j
@RestController
@RequestMapping("/verifyDocument")
public class verifyDocumentController {

    @Resource
    JiraService jiraService;

    @Resource
    verifyDocumentService verifyDocumentService;

    /**
     * 查询验证文档列表
     */
    @RequireHeader("userId")
    @GetMapping("/getVerifyFileList")
    public ResponseDoMain getVerifyDocumentList(HttpServletRequest request,
                                                @RequestParam(required = false) String fileName,
                                                @RequestParam(required = false) String fileType,
                                                @RequestParam(required = false) String isComplete,
                                                @RequestParam int current,
                                                @RequestParam int size) {
        String userId = request.getHeader("userId");
        IPage<VerifyDocumentListDTO> verifyDocumentList = verifyDocumentService.getVerifyDocumentList(userId, fileName, fileType, isComplete, current, size);
        PaginatedResponse<VerifyDocumentListDTO> response = new PaginatedResponse<>(verifyDocumentList);
        return ResponseDoMain.custom("", true, response, 200);
    }

    /**
     * 获取所有文档类型
     */
    @GetMapping("/getVerifyFileTypeList")
    public ResponseDoMain getVerifyDocumentTypeList() {
        List<String[]> verifyDocumentList = VerifyDocument.getVerifyDocumentTypeList();
        Map<String, String> verifyDocumentMap = new LinkedHashMap<>();
        verifyDocumentList.forEach(verifyDoc -> verifyDocumentMap.put(verifyDoc[0], verifyDoc[0] + " - " + verifyDoc[1]));
        return ResponseDoMain.custom("", true, verifyDocumentMap, 200);
    }

    /**
     * 1.生成sign
     * 2.请求获得OQT文档
     * 3.下载文档
     * 4.通过mq接受到的消息，更新数据库
     */
    // todo 验证token是否有效
    @PostMapping("/createVerifyFile")
    public ResponseDoMain getOQTDocument(HttpServletRequest request, @RequestBody @Valid VerifyDocumentDTO verifyDocumentDTO) {
        log.info("请求参数jiraToken:{}", request.getHeader("jiraToken"));
        // 获得对应的文档参数
        VerifyDocument document = VerifyDocument.getVerifyDocumentTypeList().stream()
                .filter(verifyDoc -> verifyDoc[0].equals(verifyDocumentDTO.getFileType()))
                .findFirst()
                .map(verifyDoc -> VerifyDocument.getVerifyDocument(verifyDocumentDTO.getFileType()))
                .orElseThrow(() -> new RuntimeException("文档类型不正确"));
        String userId = request.getHeader("userId");
        String fileName = verifyDocumentDTO.getCodeVersion() + document.getName() + verifyDocumentDTO.getVersion();
        TVerifyFile tVerifyFile = new TVerifyFile();
        tVerifyFile.setUserId(userId);
        tVerifyFile.setFileName(fileName);
        tVerifyFile.setFileType(document.getValue());
        List<String> testPlanIssueKey = verifyDocumentDTO.getTestPlanIssueKey();
        tVerifyFile.setTestPlanKey(testPlanIssueKey == null ? "" : String.join(",", testPlanIssueKey));
        tVerifyFile.setCreateTime(new Date());
        tVerifyFile.setUpdateTime(new Date());
        tVerifyFile.setIsComplete(0);
        tVerifyFile.setStatus(0);
        tVerifyFile.insert();
        CompletableFuture.runAsync(() -> {
            jiraService.verifyDocument(tVerifyFile.getId(), request.getHeader("jiraToken"), verifyDocumentDTO);
        }).exceptionally(e -> {
            log.error("生成文档失败,文件ID:{}，错误信息:{}", tVerifyFile.getFileId(), e.getMessage(), e);
            tVerifyFile.setIsComplete(2);
            tVerifyFile.setUpdateTime(new Date());
            tVerifyFile.setErrorMsg(e.getMessage());
            tVerifyFile.updateById();
            return null;
        });

        return ResponseDoMain.custom("开始处理", true, "", 200);

    }

    @GetMapping("/getTaskInfo")
    public ResponseDoMain getAfterOQTDocument(@RequestParam String taskId) {
        Map<String, Object> afterWordTemplateParams = WordTemplatePlusParamsUtil.buildTaskWordTemplateParams(taskId);
        String body = verifyDocumentService.wordTemplatePlusTaskInfo(afterWordTemplateParams);
        return ResponseDoMain.custom("", true, body, 200);
    }

    @GetMapping("/downloadVerifyFile")
    public void downloadFile(@RequestParam long id, HttpServletResponse response) {
        TVerifyFile verifyFile = new TVerifyFile();
        verifyFile.setId(id);
        TVerifyFile fileInfo = verifyFile.selectById(id);
        // 通过fileId下载文件
        verifyDocumentService.getFile(fileInfo.getFileId(), fileInfo.getFileName(),response);
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除是否成功
     */
    @DeleteMapping("/deleteVerifyFile")
    @Operation(summary = "删除验证文档", description = "通过id删除验证文档")
    public ResponseDoMain deleteById(@RequestParam(name = "id") @Parameter(description = "id") Long id) {
        TVerifyFile params = new TVerifyFile();
        params.setId(id);
        TVerifyFile verifyFile = params.selectById(id);
        verifyFile.setStatus(1);
        if (verifyFile.updateById()) {
            return ResponseDoMain.success("删除成功");
        } else {
            return ResponseDoMain.custom("删除失败", false, "", 400);
        }
    }

    /**
     * 文件压缩
     */
    @PostMapping("/compressFile")
    public void compressFile(HttpServletResponse request, @RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            throw new RuntimeException("文件是空的!");
        }
//        if (isSupportedZipFile(file)) {
//            throw new RuntimeException("不支持的文件类型");
//        }
        verifyDocumentService.fileCompress(file, request);

    }

    @GetMapping("/getPresetFields")
    public ResponseDoMain getField(@RequestParam String objectType, @RequestParam String verifyDocType) {
        return ResponseDoMain.custom("", true, verifyDocumentService.getVerifyField(objectType, verifyDocType), 200);
    }
}
