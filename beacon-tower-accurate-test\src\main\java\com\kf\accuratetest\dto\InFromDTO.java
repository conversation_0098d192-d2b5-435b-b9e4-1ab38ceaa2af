package com.kf.accuratetest.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "发送结果到钉钉消息入参")
public class InFromDTO {
    /**
     * git地址
    */
    @Schema(description = "git地址")
    String gitPath;
    /**
     * webHook地址
    */
    @Schema(description = "webHook地址")
    String webHook;
    /**
     * 是否有效
    */
    @Schema(description = "是否有效")
    Boolean status;
}
