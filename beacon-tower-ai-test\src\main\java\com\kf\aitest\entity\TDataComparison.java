package com.kf.aitest.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 数据对比主表实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_data_comparison")
public class TDataComparison extends Model<TDataComparison> {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 请求文件的MD5哈希值
     */
    @TableField("file_md5")
    private String fileMd5;

    /**
     * 对比ID列表(JSON格式)
     */
    @TableField("comparison_ids")
    private String comparisonIds;

    /**
     * 整体状态：SUCCESS、FAILED、PARTIAL_SUCCESS
     */
    @TableField("overall_status")
    private String overallStatus;

    /**
     * 整体评分(0-100)
     */
    @TableField("overall_score")
    private Integer overallScore;

    /**
     * 整体AI评估结果
     */
    @TableField("overall_ai_evaluation")
    private String overallAiEvaluation;

    /**
     * 总耗时(毫秒)
     */
    @TableField("total_duration")
    private Long totalDuration;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 成功的阶段数量
     */
    @TableField("success_stage_count")
    private Integer successStageCount;

    /**
     * 总阶段数量
     */
    @TableField("total_stage_count")
    private Integer totalStageCount;

    /**
     * 错误信息汇总(JSON格式)
     */
    @TableField("error_messages")
    private String errorMessages;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 状态：0正常，1删除
     */
    @TableField("status")
    private Integer status;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
