package com.kf.uitest.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public class ExecutionStepContext {
    private final Map<DependencyNode, Long> nodeToStepId = new HashMap<>();
    private long currentStepId = 1;

    public long generateStepId() {
        return currentStepId++;
    }

    public void recordStepId(DependencyNode node, Long stepId) {
        nodeToStepId.put(node, stepId);
    }

    public Long getStepId(DependencyNode node) {
        return nodeToStepId.get(node);
    }
}