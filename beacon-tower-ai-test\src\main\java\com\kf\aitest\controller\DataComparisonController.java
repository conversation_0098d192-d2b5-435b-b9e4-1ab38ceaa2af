package com.kf.aitest.controller;

import com.kf.aitest.common.ResponseDoMain;
import com.kf.aitest.dto.DataComparisonRequestDTO;
import com.kf.aitest.service.DataComparisonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.kf.aitest.common.ResponseDoMain.error;
import static com.kf.aitest.common.ResponseDoMain.success;

/**
 * 数据对比控制器
 */
@Slf4j
@RestController
@RequestMapping("/data-comparison")
public class DataComparisonController {
    
    @Autowired
    private DataComparisonService dataComparisonService;
    
    /**
     * 创建SSE连接，用于接收对比进度
     * 
     * @param taskId 任务ID
     * @return SSE连接
     */
    @GetMapping("/progress/{taskId}")
    public SseEmitter getProgress(@PathVariable String taskId) {
        log.info("创建SSE连接: taskId={}", taskId);
        return dataComparisonService.createSseConnection(taskId);
    }
    
    /**
     * 启动数据对比任务
     *
     * @param request 对比请求
     * @return 任务信息
     */
    @PostMapping("/start")
    public ResponseDoMain<Map<String, String>> startComparison(@Valid @RequestBody DataComparisonRequestDTO request,
                                                              @RequestHeader(value = "userId", required = false) String userId) {
        // 生成任务ID
        String taskId = UUID.randomUUID().toString();

        // 设置用户ID（从请求头获取，如果没有则使用默认值）
        if (request.getUserId() == null) {
            request.setUserId(userId != null ? userId : "system_user");
        }

        // 确保分片设置有默认值
        if (request.getDisableChunking() == null) {
            request.setDisableChunking(false);
        }

        log.info("启动数据对比任务: taskId={}, ids={}, userId={}, disableChunking={}",
                taskId, request.getIds(), request.getUserId(), request.getDisableChunking());

        try {
            // 直接启动任务，消息缓存机制会处理时序问题
            dataComparisonService.startComparison(taskId, request);

            // 返回任务信息
            Map<String, String> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("message", "数据对比任务已启动，请通过SSE接口获取进度");
            result.put("progressUrl", "/data-comparison/progress/" + taskId);

            return success("任务启动成功", result);

        } catch (Exception e) {
            log.error("启动数据对比任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return error("任务启动失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取环境配置信息
     * 
     * @return 环境配置
     */
    @GetMapping("/config")
    public ResponseDoMain<Map<String, String>> getConfig() {
        Map<String, String> config = new HashMap<>();
        config.put("defaultUatUrl", dataComparisonService.getDefaultUatUrl());
        config.put("defaultTestUrl", dataComparisonService.getDefaultTestUrl());
        config.put("supportedStages", "recognize,extraction,structured,transformer");
        
        return success("获取配置成功", config);
    }
    
    /**
     * 分页查询数据对比列表
     *
     * @param current 当前页
     * @param size 页大小
     * @param overallStatus 状态筛选
     * @param userId 用户ID
     * @return 分页结果
     */
    @GetMapping("/list")
    public ResponseDoMain<Object> getComparisonList(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String overallStatus,
            @RequestHeader(value = "userId", required = false) String userId) {

        try {
            // 调用服务层查询分页数据
            Object result = dataComparisonService.getComparisonList(current, size, overallStatus, userId);
            return success("查询成功", result);
        } catch (Exception e) {
            log.error("查询数据对比列表失败: error={}", e.getMessage(), e);
            return error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询数据对比详情
     *
     * @param id 对比记录ID
     * @return 详情数据
     */
    @GetMapping("/detail/{id}")
    public ResponseDoMain<Object> getComparisonDetail(@PathVariable Long id) {
        try {
            Object result = dataComparisonService.getComparisonDetail(id);
            return success("查询成功", result);
        } catch (Exception e) {
            log.error("查询数据对比详情失败: id={}, error={}", id, e.getMessage(), e);
            return error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 删除数据对比记录
     *
     * @param id 对比记录ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public ResponseDoMain<String> deleteComparison(@PathVariable Long id) {
        try {
            dataComparisonService.deleteComparison(id);
            return success("删除成功");
        } catch (Exception e) {
            log.error("删除数据对比记录失败: id={}, error={}", id, e.getMessage(), e);
            return error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     *
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseDoMain<String> health() {
        return success("数据对比服务运行正常");
    }
}
