package com.kf.uitest.common;

import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@Aspect
@Component
public class RequireHeaderAspect {

    @Around("@annotation(requireHeaderAnnotation)")
    public Object checkHeader(ProceedingJoinPoint joinPoint, RequireHeader requireHeaderAnnotation) throws Throwable {
        // 获取注解中定义的请求头名称
        String headerName = requireHeaderAnnotation.value();

        // 获取请求对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = null;
        if (attributes != null) {
            request = attributes.getRequest();
        }
        // 检查请求头是否存在
        String headerValue = null;
        if (request != null) {
            headerValue = request.getHeader(headerName);
        }
        if (headerValue == null || headerValue.isEmpty()) {
            // 请求头为空，可以根据业务逻辑返回错误响应或抛出异常
            throw new RuntimeException(headerName + "无效");
        }

        // 请求头存在，继续执行被拦截的方法
        return joinPoint.proceed();
    }
}









