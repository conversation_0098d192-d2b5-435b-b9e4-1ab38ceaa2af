package com.kf.baosi.entity;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_verify_file")
public class TVerifyFile extends Model<TVerifyFile> {
    @Serial
    private static final long serialVersionUID = 1L;
    // 主键
    @TableId(type = IdType.AUTO)
    private Long id;
    // 用户ID
    private String userId;
    // 文件id
    private String fileId;
    // 文件名称
    private String fileName;
    // 文件类型
    private String fileType;
    // 测试计划key
    private String testPlanKey;
    // 创建时间
    private Date createTime;
    // 修改时间
    private Date updateTime;
    // 是否完成 0未完成 1完成 2失败
    private Integer isComplete;
    // 错误原因
    @TableField(value = "error_message")
    private String errorMsg;
    // 状态 0正常 1禁用
    private Integer status;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

