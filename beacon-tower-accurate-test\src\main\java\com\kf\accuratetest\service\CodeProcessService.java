package com.kf.accuratetest.service;

import com.kf.accuratetest.dto.CodeParamDTO;
import com.kf.accuratetest.entity.RepositoryInfo;

import java.util.List;

public interface CodeProcessService {

    List<RepositoryInfo> codeDownload(CodeParamDTO param, String directoryPath);

    boolean codeCompile(String userId, List<RepositoryInfo> repositoryList, String jdkVersion);

    List<String> codeDiff(String directory1, String directory2);

    List<List<String>> codeProcess(String path,List<String> methodNameList);
}

