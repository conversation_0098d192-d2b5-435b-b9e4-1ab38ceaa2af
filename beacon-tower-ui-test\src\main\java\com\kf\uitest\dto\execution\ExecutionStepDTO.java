package com.kf.uitest.dto.execution;

import com.kf.uitest.entity.UiTestHook;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecutionStepDTO {
    /**
     * 步骤ID
     * 格式：UUID
     */
    private String id;
    
    /**
     * 步骤名称
     * 描述步骤的具体操作
     * 示例：输入用户名、点击登录按钮
     */
//    private String name;
    
    /**
     * 执行顺序
     * 在步骤组内的执行顺序
     */
    private Integer order;
    
    /**
     * 步骤级别的前置钩子
     * 在步骤执行前调用
     */
    private List<UiTestHook> preHooks;
    
    /**
     * 步骤级别的后置钩子
     * 在步骤执行后调用
     */
    private List<UiTestHook> postHooks;
    
    /**
     * 步骤配置
     * 包含：操作类型、目标元素、输入值等
     */
    private StepConfigDTO config;
} 