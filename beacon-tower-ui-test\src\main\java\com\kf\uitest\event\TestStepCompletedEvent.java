package com.kf.uitest.event;

import com.kf.uitest.entity.UiTestHook;
import com.kf.uitest.enums.TestExecutionStepType;
import com.kf.uitest.model.TestExecutionStep;
import com.kf.uitest.model.TestResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TestStepCompletedEvent extends BaseTestEvent {
    private TestExecutionStep step;
    private TestResult.StepExecutionResult result;

    public TestStepCompletedEvent(String executionId, TestExecutionStep step, TestResult.StepExecutionResult result) {
        super(executionId);
        this.step = step;
        this.result = result;
    }

    /**
     * 判断是否为钩子步骤
     */
    public boolean isHookStep() {
        return step != null && step.getType() == TestExecutionStepType.HOOK;
    }

    /**
     * 获取钩子信息（如果是钩子步骤）
     */
    public UiTestHook getHook() {
        if (isHookStep() && step.getContent() instanceof UiTestHook) {
            return (UiTestHook) step.getContent();
        }
        return null;
    }

    /**
     * 获取步骤详细描述
     */
    @Override
    public String toString() {
        if (step == null) {
            return String.format("TestStepCompletedEvent[executionId=%s, step=null]", getExecutionId());
        }

        StringBuilder sb = new StringBuilder();
        sb.append(String.format("TestStepCompletedEvent[executionId=%s, ", getExecutionId()));

        if (isHookStep()) {
            UiTestHook hook = getHook();
            if (hook != null) {
                sb.append(String.format("hook(id=%s, timing=%s, action=%s)",
                        hook.getId(),
                        hook.getHookTiming(),
                        hook.getActionType()
                ));
            }
        } else {
            sb.append(String.format("step=%s", step.getStepSummary()));
        }

        if (result != null) {
            sb.append(String.format(", status=%s", result.getStatus()));
            if (result.getErrorMessage() != null) {
                sb.append(String.format(", error=%s", result.getErrorMessage()));
            }
        }

        sb.append("]");
        return sb.toString();
    }
}