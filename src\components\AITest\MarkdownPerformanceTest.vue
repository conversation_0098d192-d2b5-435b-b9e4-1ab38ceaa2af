<template>
  <div class="performance-test">
    <el-card header="Markdown渲染性能测试">
      <div class="test-controls">
        <el-button @click="generateLargeContent">生成大内容测试</el-button>
        <el-button @click="generateCodeContent">生成代码高亮测试</el-button>
        <el-button @click="generateTableContent">生成表格测试</el-button>
        <el-button @click="clearContent">清空内容</el-button>
      </div>
      
      <div class="performance-info">
        <el-descriptions :column="4" border>
          <el-descriptions-item label="内容长度">{{ contentLength }}</el-descriptions-item>
          <el-descriptions-item label="渲染时间">{{ renderTime }}ms</el-descriptions-item>
          <el-descriptions-item label="HTML长度">{{ htmlLength }}</el-descriptions-item>
          <el-descriptions-item label="状态">{{ status }}</el-descriptions-item>
        </el-descriptions>
      </div>
      
      <div class="renderer-container">
        <el-input
          v-model="testContent"
          type="textarea"
          :rows="15"
          placeholder="性能测试内容预览区域"
          readonly
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElCard, ElButton, ElDescriptions, ElDescriptionsItem, ElMessage } from 'element-plus'
// 暂时注释掉MarkdownRenderer相关代码，因为我们主要关注架构重构
// import MarkdownRenderer from '@/components/MarkdownRenderer/index.vue'
// import type { MarkdownRendererInstance } from '@/components/MarkdownRenderer/types'

// 响应式数据
const testContent = ref('')
const isLoading = ref(false)
const contentLength = ref(0)
const renderTime = ref(0)
const htmlLength = ref(0)
const status = ref('就绪')
// const rendererRef = ref<MarkdownRendererInstance>()

// 生成大内容测试
const generateLargeContent = () => {
    const sections = []
  
    for (let i = 1; i <= 50; i++) {
        sections.push(`
## 第${i}节 - 大内容测试

这是第${i}节的内容。包含**粗体文本**、*斜体文本*和\`行内代码\`。

### 子标题 ${i}.1

这里是一些普通的段落文本，用于测试大量内容的渲染性能。Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.

### 子标题 ${i}.2

- 列表项 1
- 列表项 2
- 列表项 3

> 这是一个引用块，包含在第${i}节中。

---
`)
    }
  
    testContent.value = sections.join('\n')
    contentLength.value = testContent.value.length
    status.value = '大内容已生成'
}

// 生成代码高亮测试
const generateCodeContent = () => {
    testContent.value = `
# 代码高亮性能测试

## JavaScript代码
\`\`\`javascript
// 复杂的JavaScript代码示例
class PerformanceTest {
  constructor(name) {
    this.name = name
    this.startTime = null
    this.endTime = null
  }
  
  start() {
    this.startTime = performance.now()
    console.log(\`开始测试: \${this.name}\`)
  }
  
  end() {
    this.endTime = performance.now()
    const duration = this.endTime - this.startTime
    console.log(\`测试完成: \${this.name}, 耗时: \${duration}ms\`)
    return duration
  }
  
  async runAsyncTest() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve('异步测试完成')
      }, 1000)
    })
  }
}

const test = new PerformanceTest('Markdown渲染')
test.start()
// 执行测试...
test.end()
\`\`\`

## TypeScript代码
\`\`\`typescript
interface TestResult {
  name: string
  duration: number
  success: boolean
  errors?: string[]
}

type TestStatus = 'pending' | 'running' | 'completed' | 'failed'

class TestRunner<T extends TestResult> {
  private tests: Map<string, () => Promise<T>> = new Map()
  private results: T[] = []
  
  addTest(name: string, testFn: () => Promise<T>): void {
    this.tests.set(name, testFn)
  }
  
  async runAll(): Promise<T[]> {
    const promises = Array.from(this.tests.entries()).map(
      async ([name, testFn]) => {
        try {
          return await testFn()
        } catch (error) {
          return {
            name,
            duration: 0,
            success: false,
            errors: [error.message]
          } as T
        }
      }
    )
    
    this.results = await Promise.all(promises)
    return this.results
  }
}
\`\`\`
`
  
    contentLength.value = testContent.value.length
    status.value = '代码高亮测试已生成'
}

// 生成表格测试
const generateTableContent = () => {
    const tableRows = []
  
    for (let i = 1; i <= 100; i++) {
        tableRows.push(`| 项目${i} | 值${i} | 状态${i % 3 === 0 ? '完成' : '进行中'} | ${Math.random().toFixed(2)} |`)
    }
  
    testContent.value = `
# 表格渲染性能测试

## 大型数据表格

| 项目 | 值 | 状态 | 分数 |
|------|-----|------|------|
${tableRows.join('\n')}

## 表格说明

上述表格包含100行数据，用于测试表格渲染性能。
`
  
    contentLength.value = testContent.value.length
    status.value = '表格测试已生成'
}

// 清空内容
const clearContent = () => {
    testContent.value = ''
    contentLength.value = 0
    renderTime.value = 0
    htmlLength.value = 0
    status.value = '已清空'
}

// 渲染完成事件
const onRendered = (html: string) => {
    htmlLength.value = html.length
    status.value = '渲染完成'
    ElMessage.success(`渲染完成，HTML长度: ${html.length}`)
}

// 渲染错误事件
const onError = (error: Error) => {
    status.value = '渲染失败'
    ElMessage.error(`渲染失败: ${error.message}`)
}
</script>

<style scoped>
.performance-test {
  padding: 20px;
}

.test-controls {
  margin-bottom: 20px;
}

.test-controls .el-button {
  margin-right: 10px;
}

.performance-info {
  margin-bottom: 20px;
}

.renderer-container {
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
}
</style>
