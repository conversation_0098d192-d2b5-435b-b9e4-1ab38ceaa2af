package com.kf.baosi.dto.verifyDocument.OQT;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * OQT参数组装总类
 */
@Data
public class OQTWrapper {

    // 对应word模板的list（填充时的key的名称）
    private List<OQTTestCaseListParamsWrapper> testCaseList = new ArrayList<>();

    // 对应word模板的文本（填充时的key的名称）
    private String version;

    private String tester;

    // 测试执行者（多个）
    private String testers;

    private String code_version;

    private String auditor;

    // 批准者
    private String approver;
}
