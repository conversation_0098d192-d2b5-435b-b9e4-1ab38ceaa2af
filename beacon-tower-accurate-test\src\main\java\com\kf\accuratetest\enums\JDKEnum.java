package com.kf.accuratetest.enums;

import lombok.Getter;

/**
 * jdk版本枚举
 */
@Getter
public enum JDKEnum {

    /**
     * 镜像中写死的jdk存放位置
     */
    JDK_8("/opt/jdk8u382-b05", "8"),
    JDK_11("/opt/jdk-11.0.20+8", "11"),
    Jdk_17("/opt/jdk-17.0.8+7", "17");

    private final String value;
    private final String name;

    JDKEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    //获取枚举,默认为8
    public static String getEnum(String name) {
        for (JDKEnum jdkEnum : JDKEnum.values()) {
            if (jdkEnum.getName().equals(name)) {
                return jdkEnum.value;
            }
        }
        return JDKEnum.JDK_8.value;
    }

}
