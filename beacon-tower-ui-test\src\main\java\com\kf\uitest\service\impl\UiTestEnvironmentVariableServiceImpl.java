package com.kf.uitest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.kf.uitest.dao.UiTestEnvironmentVariableMapper;
import com.kf.uitest.entity.UiTestEnvironmentVariable;
import com.kf.uitest.service.UiTestEnvironmentVariableService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class UiTestEnvironmentVariableServiceImpl implements UiTestEnvironmentVariableService {

    @Resource
    private UiTestEnvironmentVariableMapper environmentVariableMapper;

    @Override
    public List<UiTestEnvironmentVariable> findByEnvironmentId(Long environmentId) {
        return environmentVariableMapper.selectList(
                new LambdaQueryWrapper<UiTestEnvironmentVariable>()
                        .eq(UiTestEnvironmentVariable::getEnvironmentId, environmentId)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(UiTestEnvironmentVariable variable) {
        variable.insert();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<UiTestEnvironmentVariable> variables) {
        if (variables == null || variables.isEmpty()) {
            return;
        }
        Db.saveBatch(variables);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(UiTestEnvironmentVariable variable) {
        variable.updateById();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        UiTestEnvironmentVariable variable = new UiTestEnvironmentVariable();
        variable.setId(id);
        variable.deleteById();
    }

    @Override
    public UiTestEnvironmentVariable getById(Long id) {
        return new UiTestEnvironmentVariable().selectById(id);
    }

    @Override
    public void updateVariables(String environmentId, Map<String, Object> variables) {

    }
}