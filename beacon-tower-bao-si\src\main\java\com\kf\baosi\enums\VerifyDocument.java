package com.kf.baosi.enums;

import lombok.Getter;

import java.util.*;

@Getter
public enum VerifyDocument {
//    SA("SA", "系统评估", "验证经理", ""),
//
//    VP("VP", "验证计划", "验证经理", ""),
//
//    URS("URS", "用户需求说明书", "产品", ""),
//
//    FDS("FDS", "功能设计说明", "产品", ""),
//
//    RiskAssessment("RiskAssessment", "风险分析评估表", "验证经理", ""),

//    CFRPart11Compliance("21CFRPart11Compliance","21 CFR Part 11合规性验证报告","验证经理",""),

//    TDS("TDS", "技术设计说明书", "研发", ""),
//
//    IQP("IQP", "安装确认方案", "运维", ""),

//    IQRforOQ("IQR for OQ", "测试环境安装确认报告", "运维", ""),

    OQP("OQP", "运行确认方案", "测试", Arrays.asList("Scope", "Document Number", "System Overview", "Test Methods", "Screenshot Scope and Rules")),

    OQT("OQT", "运行确认测试用例执行结果", "测试", List.of()),

    OQR("OQR", "运行确认报告", "测试", Arrays.asList("Project Description", "Recommendations")),

//    IQRforUAT("IQR for UAT", "UAT环境安装确认报告", "运维", ""),

    PQP("PQP", "性能确认方案", "测试", Arrays.asList("System Overview", "Document Number")),

    PQT("PQT", "性能确认执行结果", "测试", List.of()),

    PQR("PQR", "性能确认报告", "测试", List.of()),;

//    IQRforPROD("IQR for PROD", "生产环境安装确认报告", "运维", ""),
//
//    RTM("RTM", "需求跟踪矩阵", "产品", ""),
//
//    VR("VR", "验证报告", "验证经理", "");

    private final String value;
    private final String name;
    private final String followers;
    private final List<String> fieldNames;

    VerifyDocument(String value, String name, String followers, List<String> fieldNames) {
        this.value = value;
        this.name = name;
        this.followers = followers;
        this.fieldNames = fieldNames;
    }

    public static VerifyDocument getVerifyDocument(String value) {
        for (VerifyDocument verifyDocument : VerifyDocument.values()) {
            if (verifyDocument.getValue().equals(value)) {
                return verifyDocument;
            }
        }
        throw new IllegalArgumentException("未找到对应的文档类型");
    }

    public static List<String[]> getVerifyDocumentTypeList() {
        List<String[]> verifyDocumentList = new ArrayList<>();
        for (VerifyDocument verifyDocument : VerifyDocument.values()) {
            String[] verifyDocuments = new String[4];
            verifyDocuments[0] = verifyDocument.value;
            verifyDocuments[1] = verifyDocument.name;
            verifyDocuments[2] = verifyDocument.followers;
            verifyDocumentList.add(verifyDocuments);
        }
        return verifyDocumentList;
    }

}
