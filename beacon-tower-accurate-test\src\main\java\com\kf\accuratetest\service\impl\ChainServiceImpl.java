package com.kf.accuratetest.service.impl;

import com.kf.accuratetest.dao.ChainDao;
import com.kf.accuratetest.entity.Chain;
import com.kf.accuratetest.service.ChainService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ChainServiceImpl implements ChainService {
    @Resource
    private ChainDao chainDao;

    @Override
    public List<Chain> queryByProjectId(String projectId) {
        return chainDao.queryByProjectId(projectId);
    }

    @Override
    public List<Chain> queryByMessageId(String id) {
        return chainDao.queryByMessageId(id);
    }

    @Override
    public int insert(Chain chain) {
        return chainDao.insert(chain);
    }

    @Override
    public int update(Chain chain) {
        return chainDao.update(chain);
    }

    @Override
    public int delete(Chain chain) {
        return chainDao.delete(chain);
    }

}
